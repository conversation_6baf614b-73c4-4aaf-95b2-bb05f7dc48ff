<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Notification;
use App\Services\NotificationService;

class NotificationTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $notificationService = app(NotificationService::class);
        
        // الحصول على المستخدمين
        $jobSeekers = User::where('role', 'job_seeker')->get();
        $employers = User::where('role', 'employer')->get();
        $admins = User::where('role', 'admin')->get();
        
        // إشعارات للباحثين عن عمل
        foreach ($jobSeekers->take(5) as $jobSeeker) {
            // إشعار تحديث حالة طلب
            $notificationService->create(
                $jobSeeker,
                NotificationService::TYPE_APPLICATION_UPDATE,
                'تم قبول طلب التوظيف الخاص بك لوظيفة "مطور ويب" في شركة التقنية المتقدمة',
                route('job-seeker.applications'),
                ['application_id' => 1, 'status' => 'accepted']
            );
            
            // إشعار تقييم الملف الشخصي
            $notificationService->create(
                $jobSeeker,
                NotificationService::TYPE_PROFILE_RATING,
                'تم تقييم ملفك الشخصي بـ 5 نجوم من قبل شركة الابتكار التقني',
                route('job-seeker.profile'),
                ['rating' => 5, 'company' => 'شركة الابتكار التقني']
            );
            
            // إشعار توصية وظيفة
            $notificationService->create(
                $jobSeeker,
                NotificationService::TYPE_JOB_RECOMMENDATION,
                'وظيفة قد تهمك: مصمم جرافيك في شركة الإبداع الرقمي - راتب 4500 ريال',
                route('jobs.index'),
                ['job_id' => 2, 'salary' => 4500]
            );
            
            // إشعار جدولة مقابلة
            $notificationService->create(
                $jobSeeker,
                NotificationService::TYPE_INTERVIEW_SCHEDULED,
                'تم جدولة مقابلة للوظيفة "محلل بيانات" يوم الأحد 15 ديسمبر الساعة 10:00 صباحاً',
                route('job-seeker.applications'),
                ['interview_date' => '2024-12-15', 'interview_time' => '10:00', 'job_title' => 'محلل بيانات']
            );
            
            // إشعار نظام عام
            $notificationService->create(
                $jobSeeker,
                NotificationService::TYPE_SYSTEM_ANNOUNCEMENT,
                'تحديث جديد: تم إضافة ميزة البحث المتقدم للوظائف مع فلاتر محسنة',
                null,
                ['version' => '2.1.0', 'feature' => 'advanced_search']
            );
        }
        
        // إشعارات لأصحاب العمل
        foreach ($employers->take(3) as $employer) {
            // إشعار طلب توظيف جديد
            $notificationService->create(
                $employer,
                NotificationService::TYPE_JOB_APPLICATION,
                'تم استلام طلب توظيف جديد للوظيفة "مطور تطبيقات الجوال" من أحمد محمد',
                route('employer.applications.index'),
                ['applicant_name' => 'أحمد محمد', 'job_title' => 'مطور تطبيقات الجوال']
            );
            
            // إشعار نشر وظيفة
            $notificationService->create(
                $employer,
                NotificationService::TYPE_JOB_POSTED,
                'تم نشر وظيفتك "مدير مشاريع تقنية" بنجاح وهي متاحة الآن للمتقدمين',
                route('employer.jobs.index'),
                ['job_title' => 'مدير مشاريع تقنية']
            );
            
            // إشعار تقييم الشركة
            $notificationService->create(
                $employer,
                NotificationService::TYPE_COMPANY_RATING,
                'تم تقييم شركتك بـ 4 نجوم من قبل سارة أحمد مع تعليق إيجابي',
                route('employer.profile'),
                ['rating' => 4, 'reviewer' => 'سارة أحمد', 'comment' => 'شركة ممتازة وبيئة عمل رائعة']
            );
            
            // إشعار دفع ناجح
            $notificationService->create(
                $employer,
                NotificationService::TYPE_PAYMENT_SUCCESS,
                'تم الدفع بنجاح بمبلغ 150 ريال لنشر 3 وظائف جديدة',
                route('employer.profile'),
                ['amount' => 150, 'jobs_count' => 3, 'payment_method' => 'credit_card']
            );
        }
        
        // إشعارات للمديرين
        foreach ($admins->take(2) as $admin) {
            // إشعار نظام
            $notificationService->create(
                $admin,
                NotificationService::TYPE_SYSTEM_ANNOUNCEMENT,
                'تقرير يومي: تم تسجيل 25 مستخدم جديد و 15 وظيفة جديدة اليوم',
                route('admin.dashboard'),
                ['new_users' => 25, 'new_jobs' => 15, 'date' => now()->format('Y-m-d')]
            );
            
            // إشعار مراجعة مطلوبة
            $notificationService->create(
                $admin,
                NotificationService::TYPE_SYSTEM_ANNOUNCEMENT,
                'يوجد 5 تقييمات شركات تحتاج إلى مراجعة وموافقة',
                route('admin.reviews.index'),
                ['pending_reviews' => 5, 'type' => 'company_reviews']
            );
        }
        
        // إنشاء بعض الإشعارات القديمة (مقروءة)
        foreach ($jobSeekers->take(3) as $jobSeeker) {
            $oldNotification = $notificationService->create(
                $jobSeeker,
                NotificationService::TYPE_APPLICATION_UPDATE,
                'تم استلام طلب التوظيف الخاص بك لوظيفة "محاسب مالي"',
                route('job-seeker.applications'),
                ['job_title' => 'محاسب مالي', 'status' => 'received']
            );
            
            // تحديث الإشعار ليصبح مقروءاً وقديماً
            $oldNotification->update([
                'is_read' => true,
                'created_at' => now()->subDays(rand(3, 10)),
                'updated_at' => now()->subDays(rand(1, 3))
            ]);
        }
        
        // إنشاء إشعارات بأنواع مختلفة للاختبار
        $testUser = $jobSeekers->first();
        if ($testUser) {
            $testNotifications = [
                [
                    'type' => NotificationService::TYPE_JOB_EXPIRED,
                    'message' => 'انتهت صلاحية الوظيفة "مطور PHP" التي تقدمت إليها',
                    'data' => ['job_title' => 'مطور PHP']
                ],
                [
                    'type' => NotificationService::TYPE_ACCOUNT_VERIFICATION,
                    'message' => 'تم التحقق من حسابك بنجاح. يمكنك الآن التقدم للوظائف',
                    'data' => ['verification_status' => 'verified']
                ],
                [
                    'type' => NotificationService::TYPE_PASSWORD_RESET,
                    'message' => 'تم إعادة تعيين كلمة المرور الخاصة بك بنجاح',
                    'data' => ['reset_time' => now()->format('Y-m-d H:i:s')]
                ]
            ];
            
            foreach ($testNotifications as $notification) {
                $notificationService->create(
                    $testUser,
                    $notification['type'],
                    $notification['message'],
                    null,
                    $notification['data']
                );
            }
        }
        
        $this->command->info('تم إنشاء إشعارات تجريبية بنجاح!');
        $this->command->info('إجمالي الإشعارات المنشأة: ' . Notification::count());
    }
}
