@extends('layouts.app')

@section('title', 'إعدادات الإشعارات - Hire Me')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 dark:from-gray-900 dark:via-blue-900/20 dark:to-purple-900/20">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-5xl mx-auto space-y-8">
            <!-- Header -->
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                    <h1 class="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">إعدادات الإشعارات</h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-2 text-lg">تخصيص تفضيلات الإشعارات الخاصة بك</p>
                </div>

                <div class="flex gap-3 mt-6 md:mt-0">
                    <form action="{{ route('notifications.settings.reset') }}" method="POST" class="inline">
                        @csrf
                        <button type="submit" class="px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-xl hover:from-gray-700 hover:to-gray-800 transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform hover:-translate-y-1" onclick="return confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')">
                            <i class="fas fa-undo"></i>
                            إعادة تعيين
                        </button>
                    </form>

                    <a href="{{ route('notifications.index') }}" class="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                        <i class="fas fa-bell"></i>
                        عرض الإشعارات
                    </a>
                </div>
            </div>

            <!-- Settings Form -->
            <form action="{{ route('notifications.settings.update') }}" method="POST">
                @csrf
                @method('PUT')

                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                    <!-- Info Banner -->
                    <div class="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border-b border-gray-200 dark:border-gray-700 p-6">
                        <div class="flex items-start gap-4">
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-info-circle text-white text-lg"></i>
                                </div>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">معلومات مهمة</h3>
                                <p class="text-gray-600 dark:text-gray-400 mt-1 leading-relaxed">
                                    يمكنك تخصيص كيفية تلقي الإشعارات لكل نوع. الإشعارات الويب تظهر في المنصة، بينما إشعارات البريد الإلكتروني ترسل إلى بريدك.
                                </p>
                            </div>
                        </div>
                    </div>

                <!-- Settings List -->
                <div class="divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($notificationTypes as $type => $info)
                    @php
                        $setting = $settings[$type] ?? null;
                        $webEnabled = $setting ? $setting->web_enabled : true;
                        $emailEnabled = $setting ? $setting->email_enabled : true;
                        $pushEnabled = $setting ? $setting->push_enabled : false;
                    @endphp

                    <div class="p-6">
                        <div class="flex items-start gap-4">
                            <!-- Icon -->
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 rounded-full bg-{{ $info['color'] }}-100 dark:bg-{{ $info['color'] }}-900/30 flex items-center justify-center">
                                    <i class="fas fa-{{ $info['icon'] }} text-{{ $info['color'] }}-600 dark:text-{{ $info['color'] }}-400"></i>
                                </div>
                            </div>

                            <!-- Content -->
                            <div class="flex-1 min-w-0">
                                <div class="flex items-start justify-between">
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ $info['name'] }}</h3>
                                        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">{{ $info['description'] }}</p>
                                    </div>
                                </div>

                                <!-- Toggle Options -->
                                <div class="mt-4 space-y-3">
                                    <!-- Web Notifications -->
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-3">
                                            <i class="fas fa-desktop text-gray-400"></i>
                                            <span class="text-sm text-gray-700 dark:text-gray-300">إشعارات الويب</span>
                                        </div>
                                        <label class="relative inline-flex items-center cursor-pointer">
                                            <input type="checkbox" name="settings[{{ $type }}][web_enabled]" value="1" {{ $webEnabled ? 'checked' : '' }} class="sr-only peer">
                                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                                        </label>
                                    </div>

                                    <!-- Email Notifications -->
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-3">
                                            <i class="fas fa-envelope text-gray-400"></i>
                                            <span class="text-sm text-gray-700 dark:text-gray-300">إشعارات البريد الإلكتروني</span>
                                        </div>
                                        <label class="relative inline-flex items-center cursor-pointer">
                                            <input type="checkbox" name="settings[{{ $type }}][email_enabled]" value="1" {{ $emailEnabled ? 'checked' : '' }} class="sr-only peer">
                                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                                        </label>
                                    </div>

                                    <!-- Push Notifications (Future Feature) -->
                                    <div class="flex items-center justify-between opacity-50">
                                        <div class="flex items-center gap-3">
                                            <i class="fas fa-mobile-alt text-gray-400"></i>
                                            <span class="text-sm text-gray-700 dark:text-gray-300">الإشعارات المدفوعة</span>
                                            <span class="text-xs bg-gray-100 dark:bg-gray-700 text-gray-500 px-2 py-1 rounded">قريباً</span>
                                        </div>
                                        <label class="relative inline-flex items-center cursor-not-allowed">
                                            <input type="checkbox" name="settings[{{ $type }}][push_enabled]" value="1" {{ $pushEnabled ? 'checked' : '' }} disabled class="sr-only peer">
                                            <div class="w-11 h-6 bg-gray-200 rounded-full peer dark:bg-gray-700 after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600"></div>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>

                <!-- Save Button -->
                <div class="bg-gray-50 dark:bg-gray-900/50 px-6 py-4">
                    <div class="flex items-center justify-between">
                        <p class="text-sm text-gray-500 dark:text-gray-400">
                            سيتم حفظ التغييرات تلقائياً عند الضغط على "حفظ الإعدادات"
                        </p>
                        <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2">
                            <i class="fas fa-save"></i>
                            حفظ الإعدادات
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection
