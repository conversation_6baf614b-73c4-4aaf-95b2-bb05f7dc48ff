<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('jobs', function (Blueprint $table) {
            // إضافة حقول الراتب الجديدة
            $table->decimal('salary_min', 10, 2)->nullable()->after('salary_range');
            $table->decimal('salary_max', 10, 2)->nullable()->after('salary_min');
            $table->string('salary_currency', 3)->default('LYD')->after('salary_max');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('jobs', function (Blueprint $table) {
            $table->dropColumn(['salary_min', 'salary_max', 'salary_currency']);
        });
    }
};
