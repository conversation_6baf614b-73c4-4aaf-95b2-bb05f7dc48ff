<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

class SetupCandidatesTable extends Command
{
    protected $signature = 'setup:candidates-table';
    protected $description = 'Setup saved candidates table';

    public function handle()
    {
        $this->info('Setting up saved candidates table...');
        
        if (Schema::hasTable('saved_candidates')) {
            $this->info('Table already exists!');
            return 0;
        }
        
        try {
            Schema::create('saved_candidates', function (Blueprint $table) {
                $table->id();
                $table->foreignId('employer_id')->constrained('employers')->onDelete('cascade');
                $table->foreignId('job_seeker_id')->constrained('job_seekers')->onDelete('cascade');
                $table->text('notes')->nullable();
                $table->timestamps();
                $table->unique(['employer_id', 'job_seeker_id']);
            });
            
            $this->info('✅ Table created successfully!');
            
        } catch (\Exception $e) {
            $this->error('❌ Failed to create table: ' . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
}
