/**
 * نظام الإشعارات الفورية
 */
class NotificationSystem {
    constructor() {
        this.updateInterval = 30000; // 30 ثانية
        this.lastNotificationId = 0;
        this.isVisible = true;
        this.init();
    }

    init() {
        this.bindEvents();
        this.startPolling();
        this.checkPageVisibility();
        this.initAlpineEnhancements();
    }

    initAlpineEnhancements() {
        // تحسينات Alpine.js للنوافذ المنبثقة
        document.addEventListener('alpine:init', () => {
            console.log('Alpine.js initialized for notifications');

            // إضافة تأثيرات للنوافذ المنبثقة
            Alpine.directive('dropdown-enhance', (el, { expression }, { evaluate }) => {
                el.addEventListener('click', (e) => {
                    e.stopPropagation();
                });
            });
        });

        // تحسين الأداء للنوافذ المنبثقة
        document.addEventListener('DOMContentLoaded', () => {
            this.enhanceDropdowns();
        });
    }

    enhanceDropdowns() {
        const dropdowns = document.querySelectorAll('[x-data*="open"]');
        dropdowns.forEach(dropdown => {
            const button = dropdown.querySelector('button');
            const menu = dropdown.querySelector('[x-show="open"]');

            if (button && menu) {
                // إضافة keyboard navigation
                button.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        button.click();
                    }
                });

                // تحسين الوصولية
                button.setAttribute('aria-haspopup', 'true');
                button.setAttribute('aria-expanded', 'false');
                menu.setAttribute('role', 'menu');
            }
        });
    }

    bindEvents() {
        // التحقق من رؤية الصفحة
        document.addEventListener('visibilitychange', () => {
            this.isVisible = !document.hidden;
            if (this.isVisible) {
                this.checkForNewNotifications();
            }
        });

        // تحديث الإشعارات عند النقر على زر الإشعارات
        const notificationButton = document.querySelector('[data-notification-button]');
        if (notificationButton) {
            notificationButton.addEventListener('click', () => {
                this.checkForNewNotifications();
            });
        }
    }

    startPolling() {
        setInterval(() => {
            if (this.isVisible) {
                this.checkForNewNotifications();
            }
        }, this.updateInterval);
    }

    async checkForNewNotifications() {
        try {
            const response = await fetch('/notifications/unread', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.updateNotificationBadge(data.count);
                this.checkForNewNotificationSound(data.notifications);
            }
        } catch (error) {
            console.error('Error fetching notifications:', error);
        }
    }

    updateNotificationBadge(count) {
        const badge = document.querySelector('.notification-badge');
        if (badge) {
            badge.textContent = count > 99 ? '99+' : count;
            badge.style.display = count > 0 ? 'flex' : 'none';
            
            // إضافة تأثير النبض للإشعارات الجديدة
            if (count > 0) {
                badge.classList.add('notification-pulse');
            } else {
                badge.classList.remove('notification-pulse');
            }
        }
    }

    checkForNewNotificationSound(notifications) {
        if (notifications && notifications.length > 0) {
            const latestNotification = notifications[0];
            if (latestNotification.id > this.lastNotificationId) {
                this.lastNotificationId = latestNotification.id;
                this.playNotificationSound();
                this.showDesktopNotification(latestNotification);
            }
        }
    }

    playNotificationSound() {
        // تشغيل صوت الإشعار (اختياري)
        try {
            const audio = new Audio('/sounds/notification.mp3');
            audio.volume = 0.3;
            audio.play().catch(() => {
                // تجاهل الأخطاء إذا لم يكن الصوت متاحاً
            });
        } catch (error) {
            // تجاهل الأخطاء
        }
    }

    async showDesktopNotification(notification) {
        // طلب إذن الإشعارات المكتبية
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification('Hire Me - إشعار جديد', {
                body: notification.message,
                icon: '/images/logo.png',
                badge: '/images/logo.png',
                tag: 'hire-me-notification',
                requireInteraction: false,
                silent: false
            });
        } else if ('Notification' in window && Notification.permission !== 'denied') {
            const permission = await Notification.requestPermission();
            if (permission === 'granted') {
                this.showDesktopNotification(notification);
            }
        }
    }

    checkPageVisibility() {
        // التحقق من رؤية الصفحة لتوفير الموارد
        if (typeof document.hidden !== "undefined") {
            this.isVisible = !document.hidden;
        } else if (typeof document.webkitHidden !== "undefined") {
            this.isVisible = !document.webkitHidden;
        } else if (typeof document.mozHidden !== "undefined") {
            this.isVisible = !document.mozHidden;
        }
    }

    // إظهار إشعار مؤقت في الصفحة
    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transform transition-all duration-300 translate-x-full`;
        
        const bgColor = {
            'success': 'bg-green-500',
            'error': 'bg-red-500',
            'warning': 'bg-yellow-500',
            'info': 'bg-blue-500'
        }[type] || 'bg-blue-500';
        
        toast.classList.add(bgColor);
        toast.innerHTML = `
            <div class="flex items-center gap-3 text-white">
                <i class="fas fa-bell"></i>
                <span class="flex-1">${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        document.body.appendChild(toast);
        
        // إظهار التوست
        setTimeout(() => {
            toast.classList.remove('translate-x-full');
        }, 100);
        
        // إخفاء التوست تلقائياً بعد 5 ثوان
        setTimeout(() => {
            toast.classList.add('translate-x-full');
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 300);
        }, 5000);
    }
}

// تهيئة نظام الإشعارات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    if (typeof window.notificationSystem === 'undefined') {
        window.notificationSystem = new NotificationSystem();
    }
});

// إضافة دالة مساعدة لإظهار الإشعارات من الخادم
window.showNotification = function(message, type = 'info') {
    if (window.notificationSystem) {
        window.notificationSystem.showToast(message, type);
    }
};

// التحقق من الإشعارات الفورية من session
document.addEventListener('DOMContentLoaded', function() {
    // يمكن استخدام هذا لعرض الإشعارات من session flash
    const flashNotification = document.querySelector('[data-flash-notification]');
    if (flashNotification) {
        const message = flashNotification.dataset.message;
        const type = flashNotification.dataset.type || 'info';
        setTimeout(() => {
            window.showNotification(message, type);
        }, 1000);
    }
});
