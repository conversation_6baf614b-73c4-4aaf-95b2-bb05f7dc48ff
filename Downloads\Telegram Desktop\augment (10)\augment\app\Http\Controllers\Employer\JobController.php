<?php

namespace App\Http\Controllers\Employer;

use App\Http\Controllers\Controller;
use App\Models\Job;
use App\Models\Category;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Schema;

class JobController extends Controller
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->middleware('auth');
        // لا نحتاج role middleware هنا لأن route group يطبقه بالفعل
        $this->notificationService = $notificationService;
    }

    /**
     * عرض قائمة الوظائف
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $employer = $user->employer;

        // إنشاء ملف صاحب عمل إذا لم يكن موجوداً
        if (!$employer) {
            if ($user->role === 'employer') {
                $employer = \App\Models\Employer::create([
                    'user_id' => $user->id,
                    'company_name' => $user->company_name ?? 'شركة ' . $user->name,
                    'company_description' => $user->company_description ?? 'وصف الشركة',
                    'industry' => $user->industry ?? 'تقنية المعلومات',
                    'location' => $user->location ?? 'طرابلس',
                    'company_size' => $user->company_size ?? '1-10',
                ]);
            } else {
                return redirect()->route('home')->with('error', 'ليس لديك صلاحية للوصول إلى هذه الصفحة');
            }
        }

        $query = Job::where('employer_id', $employer->id)
                   ->where('is_active', true) // فقط الوظائف المنشورة
                   ->with(['category', 'applications']);

        // فلترة حسب الحالة
        if ($request->filled('status')) {
            switch ($request->status) {
                case 'active':
                    $query->where('expires_at', '>', now());
                    break;
                case 'expired':
                    $query->where('expires_at', '<=', now());
                    break;
            }
        }

        // البحث
        if ($request->filled('search')) {
            $query->where(function($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
            });
        }

        $jobs = $query->latest()->paginate(15);

        // إحصائيات سريعة
        $stats = [
            'total' => Job::where('employer_id', $employer->id)->where('is_active', true)->count(),
            'active' => Job::where('employer_id', $employer->id)->where('is_active', true)->where('expires_at', '>', now())->count(),
            'expired' => Job::where('employer_id', $employer->id)->where('is_active', true)->where('expires_at', '<=', now())->count(),
            'draft' => Job::where('employer_id', $employer->id)->where('is_active', false)->count(),
        ];

        return view('employer.jobs.index', compact('jobs', 'stats'));
    }

    /**
     * عرض قائمة المسودات
     */
    public function drafts(Request $request)
    {
        $user = Auth::user();
        $employer = $user->employer;

        if (!$employer) {
            return redirect()->route('home')->with('error', 'ليس لديك صلاحية للوصول إلى هذه الصفحة');
        }

        $query = Job::where('employer_id', $employer->id)
                   ->where('is_active', false) // المسودات غير نشطة
                   ->with(['category', 'applications']);

        // البحث
        if ($request->filled('search')) {
            $query->where(function($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
            });
        }

        $jobs = $query->latest()->paginate(15);

        return view('employer.jobs.drafts', compact('jobs'));
    }

    /**
     * نشر مسودة
     */
    public function publish(Job $job)
    {
        $employer = Auth::user()->employer;

        // التحقق من الملكية
        if ($job->employer_id !== $employer->id) {
            abort(403);
        }

        // التحقق من أن الوظيفة مسودة
        if ($job->is_active) {
            return redirect()->back()->with('error', 'هذه الوظيفة منشورة بالفعل');
        }

        // التحقق من اكتمال البيانات المطلوبة
        if (empty($job->title) || empty($job->description) || empty($job->category_id)) {
            return redirect()->route('employer.jobs.edit', $job)
                           ->with('error', 'يجب إكمال البيانات المطلوبة قبل النشر (العنوان، الوصف، التصنيف)');
        }

        // التحقق من حدود الباقة
        $subscription = $employer->currentSubscription();
        if (!$subscription) {
            $subscription = \App\Models\Subscription::createDefault($employer);
        }

        if (!$subscription->canPostJob()) {
            $packageDetails = $subscription->getPackageDetails();
            $upgradeMessage = $subscription->package_type === 'basic'
                ? 'يجب ترقية الباقة أولاً أو الانتظار للشهر القادم. تم حفظ الوظيفة في المسودات.'
                : 'تم الوصول للحد الأقصى من الوظائف لهذا الشهر. تم حفظ الوظيفة في المسودات.';

            return redirect()->route('employer.packages')
                           ->with('error', $upgradeMessage);
        }

        // نشر الوظيفة وتحديث العداد
        $job->update([
            'is_active' => true,
            'posted_at' => now(),
            'expires_at' => $job->expires_at ?? now()->addDays(30)
        ]);

        // زيادة عداد الوظائف المنشورة
        $subscription->incrementJobsPosted();

        return redirect()->route('employer.jobs.index')
                       ->with('success', 'تم نشر الوظيفة بنجاح');
    }

    /**
     * عرض نموذج إنشاء وظيفة جديدة
     */
    public function create()
    {
        $categories = Category::all();
        return view('employer.jobs.create', compact('categories'));
    }

    /**
     * حفظ وظيفة جديدة
     */
    public function store(Request $request)
    {


        // تحديد حالة الوظيفة (مسودة أم منشورة)
        $isDraft = $request->has('save_as_draft') && $request->save_as_draft;

        // التحقق من وجود الحقول الجديدة في قاعدة البيانات
        $hasNewSalaryFields = Schema::hasColumn('jobs', 'salary_min');

        // قواعد التحقق تختلف حسب ما إذا كانت مسودة أم لا
        if ($isDraft) {
            // للمسودة: جميع الحقول اختيارية
            $validationRules = [
                'title' => 'nullable|string|max:125',
                'description' => 'nullable|string',
                'requirements' => 'nullable|string',
                'category_id' => 'nullable|exists:categories,id',
                'location' => 'nullable|string|max:125',
                'job_type' => 'nullable|in:full_time,part_time,remote,freelance',
                'expires_at' => 'nullable|date',
                'is_featured' => 'boolean',
            ];
        } else {
            // للنشر: الحقول المطلوبة
            $validationRules = [
                'title' => 'required|string|max:125',
                'description' => 'required|string',
                'requirements' => 'required|string',
                'category_id' => 'nullable|exists:categories,id',
                'location' => 'nullable|string|max:125',
                'job_type' => 'required|in:full_time,part_time,remote,freelance',
                'expires_at' => 'required|date|after:today',
                'is_featured' => 'boolean',
            ];
        }

        if ($hasNewSalaryFields) {
            $validationRules['salary_min'] = 'nullable|numeric|min:0';
            $validationRules['salary_max'] = 'nullable|numeric|min:0|gte:salary_min';
            $validationRules['salary_currency'] = 'required|string|in:LYD,USD,EUR,GBP,SAR,AED,QAR,KWD,BHD,OMR,JOD,EGP,TND,MAD,DZD,TRY,CAD,AUD,CHF,JPY';
        } else {
            $validationRules['salary_range'] = 'nullable|string|max:125';
        }

        $request->validate($validationRules);

        $user = Auth::user();
        $employer = $user->employer;

        // إنشاء ملف صاحب عمل إذا لم يكن موجوداً
        if (!$employer) {
            if ($user->role === 'employer') {
                $employer = \App\Models\Employer::create([
                    'user_id' => $user->id,
                    'company_name' => $user->company_name ?? 'شركة ' . $user->name,
                    'company_description' => $user->company_description ?? 'وصف الشركة',
                    'industry' => $user->industry ?? 'تقنية المعلومات',
                    'location' => $user->location ?? 'الرياض',
                    'company_size' => $user->company_size ?? '1-10',
                ]);
            } else {
                return redirect()->route('home')->with('error', 'ليس لديك صلاحية لنشر الوظائف');
            }
        }

        // التحقق من حدود الباقة إذا كان النشر مباشر (وليس مسودة)
        if (!$isDraft) {
            $subscription = $employer->currentSubscription();
            if (!$subscription) {
                $subscription = \App\Models\Subscription::createDefault($employer);
            }

            if (!$subscription->canPostJob()) {
                $isDraft = true; // تحويل إلى مسودة
                $packageDetails = $subscription->getPackageDetails();
                $upgradeMessage = $subscription->package_type === 'basic'
                    ? 'تم الوصول للحد الأقصى من الوظائف (3 وظائف شهرياً). يجب ترقية الباقة أولاً أو الانتظار للشهر القادم. تم حفظ الوظيفة في المسودات.'
                    : 'تم الوصول للحد الأقصى من الوظائف لهذا الشهر (' . $packageDetails['job_limit'] . ' وظيفة). تم حفظ الوظيفة في المسودات.';

                session()->flash('warning', $upgradeMessage);
                session()->flash('show_upgrade', true);
            }
        }

        $jobData = [
            'title' => $request->title ?: ($isDraft ? 'مسودة وظيفة' : $request->title),
            'description' => $request->description ?: ($isDraft ? 'وصف الوظيفة' : $request->description),
            'requirements' => $request->requirements ?: ($isDraft ? 'متطلبات الوظيفة' : $request->requirements),
            'category_id' => $request->category_id,
            'employer_id' => $employer->id,
            'location' => $request->location,
            'job_type' => $request->job_type ?: ($isDraft ? 'full_time' : $request->job_type),
            'expires_at' => $request->expires_at ?: ($isDraft ? now()->addDays(30) : $request->expires_at),
            'is_featured' => $request->boolean('is_featured'),
            'is_active' => !$isDraft, // إذا كانت مسودة، تكون غير نشطة
            'views' => 0,
            'posted_at' => $isDraft ? now()->subYear() : now(), // للمسودات نستخدم تاريخ قديم
        ];

        // إضافة حقول الراتب حسب توفرها في قاعدة البيانات
        if ($hasNewSalaryFields) {
            $jobData['salary_min'] = $request->salary_min;
            $jobData['salary_max'] = $request->salary_max;
            $jobData['salary_currency'] = $request->salary_currency;
        } else {
            // استخدام الحقل القديم كحل مؤقت
            $salaryRange = '';
            if ($request->salary_min && $request->salary_max) {
                $salaryRange = $request->salary_min . ' - ' . $request->salary_max . ' ' . $request->salary_currency;
            } elseif ($request->salary_min) {
                $salaryRange = 'من ' . $request->salary_min . ' ' . $request->salary_currency;
            } elseif ($request->salary_max) {
                $salaryRange = 'حتى ' . $request->salary_max . ' ' . $request->salary_currency;
            }
            $jobData['salary_range'] = $salaryRange;
        }

        $job = Job::create($jobData);

        // إرسال إشعار وزيادة العداد فقط إذا تم نشر الوظيفة (وليس حفظها كمسودة)
        if (!$isDraft) {
            $this->notificationService->jobPosted($user, $job);

            // زيادة عداد الوظائف المنشورة
            $subscription = $employer->currentSubscription();
            if ($subscription) {
                $subscription->incrementJobsPosted();
            }
        }

        $message = $isDraft ? 'تم حفظ الوظيفة كمسودة بنجاح' : 'تم نشر الوظيفة بنجاح';
        $redirectRoute = $isDraft ? 'employer.jobs.drafts' : 'employer.jobs.index';

        return redirect()->route($redirectRoute)
                        ->with('success', $message);
    }

    /**
     * عرض تفاصيل وظيفة
     */
    public function show(Job $job)
    {
        $this->authorize('view', $job);

        $job->load(['category', 'applications.user']);

        // إحصائيات الوظيفة
        $stats = [
            'views' => $job->views,
            'applications' => $job->applications->count(),
            'pending' => $job->applications->where('status', 'pending')->count(),
            'reviewed' => $job->applications->where('status', 'reviewed')->count(),
            'accepted' => $job->applications->where('status', 'accepted')->count(),
            'rejected' => $job->applications->where('status', 'rejected')->count(),
        ];

        return view('employer.jobs.show', compact('job', 'stats'));
    }

    /**
     * عرض نموذج تعديل الوظيفة
     */
    public function edit(Job $job)
    {
        $this->authorize('update', $job);

        $categories = Category::all();
        return view('employer.jobs.edit', compact('job', 'categories'));
    }

    /**
     * تحديث الوظيفة
     */
    public function update(Request $request, Job $job)
    {
        $this->authorize('update', $job);

        // التحقق من وجود الحقول الجديدة في قاعدة البيانات
        $hasNewSalaryFields = Schema::hasColumn('jobs', 'salary_min');

        $validationRules = [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'requirements' => 'required|string',
            'category_id' => 'required|exists:categories,id',
            'location' => 'required|string|max:255',
            'job_type' => 'required|in:full_time,part_time,remote,freelance',
            'expires_at' => 'required|date|after:today',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
        ];

        if ($hasNewSalaryFields) {
            $validationRules['salary_min'] = 'nullable|numeric|min:0';
            $validationRules['salary_max'] = 'nullable|numeric|min:0|gte:salary_min';
            $validationRules['salary_currency'] = 'required|string|in:LYD,USD,EUR,GBP,SAR,AED,QAR,KWD,BHD,OMR,JOD,EGP,TND,MAD,DZD,TRY,CAD,AUD,CHF,JPY';
        } else {
            $validationRules['salary_range'] = 'nullable|string|max:255';
        }

        $request->validate($validationRules);

        $updateData = [
            'title' => $request->title,
            'description' => $request->description,
            'requirements' => $request->requirements,
            'category_id' => $request->category_id,
            'location' => $request->location,
            'job_type' => $request->job_type,
            'expires_at' => $request->expires_at,
            'is_featured' => $request->boolean('is_featured'),
            'is_active' => $request->boolean('is_active'),
        ];

        // إذا تم تغيير الحالة من مسودة إلى منشورة، نحدث posted_at
        if (!$job->is_active && $request->boolean('is_active')) {
            $updateData['posted_at'] = now();
        }

        // إضافة حقول الراتب حسب توفرها في قاعدة البيانات
        if ($hasNewSalaryFields) {
            $updateData['salary_min'] = $request->salary_min;
            $updateData['salary_max'] = $request->salary_max;
            $updateData['salary_currency'] = $request->salary_currency;
        } else {
            // استخدام الحقل القديم كحل مؤقت
            $salaryRange = '';
            if ($request->salary_min && $request->salary_max) {
                $salaryRange = $request->salary_min . ' - ' . $request->salary_max . ' ' . $request->salary_currency;
            } elseif ($request->salary_min) {
                $salaryRange = 'من ' . $request->salary_min . ' ' . $request->salary_currency;
            } elseif ($request->salary_max) {
                $salaryRange = 'حتى ' . $request->salary_max . ' ' . $request->salary_currency;
            }
            $updateData['salary_range'] = $salaryRange;
        }

        $job->update($updateData);

        return redirect()->route('employer.jobs.show', $job)
                        ->with('success', 'تم تحديث الوظيفة بنجاح');
    }

    /**
     * حذف الوظيفة
     */
    public function destroy(Job $job)
    {
        try {
            $this->authorize('delete', $job);

            $jobTitle = $job->title;
            $job->delete();

            return redirect()->route('employer.jobs.index')
                            ->with('success', "تم حذف الوظيفة '{$jobTitle}' بنجاح");
        } catch (\Exception $e) {
            return redirect()->route('employer.jobs.index')
                            ->with('error', 'حدث خطأ أثناء حذف الوظيفة: ' . $e->getMessage());
        }
    }

    /**
     * تفعيل/إلغاء تفعيل الوظيفة
     */
    public function toggleStatus(Job $job)
    {
        $this->authorize('update', $job);

        $job->update(['is_active' => !$job->is_active]);

        $status = $job->is_active ? 'تم تفعيل' : 'تم إلغاء تفعيل';

        return redirect()->back()->with('success', $status . ' الوظيفة بنجاح');
    }

    /**
     * نسخ الوظيفة
     */
    public function duplicate(Job $job)
    {
        $this->authorize('view', $job);

        $newJob = $job->replicate();
        $newJob->title = $job->title . ' - نسخة';
        $newJob->is_active = false;
        $newJob->expires_at = now()->addDays(30);
        $newJob->views = 0;
        $newJob->posted_at = now();
        $newJob->save();

        return redirect()->route('employer.jobs.edit', $newJob)
                        ->with('success', 'تم نسخ الوظيفة بنجاح. يمكنك تعديلها الآن');
    }
}
