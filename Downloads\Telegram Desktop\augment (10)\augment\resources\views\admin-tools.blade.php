<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أدوات الإدارة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body class="bg-gray-100 font-sans">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-center mb-8">أدوات الإدارة والتصحيح</h1>
        
        <!-- أزرار الإجراءات -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6 text-center">
                <i class="fas fa-database text-blue-500 text-3xl mb-4"></i>
                <h3 class="text-lg font-semibold mb-2">إنشاء بيانات تجريبية</h3>
                <p class="text-gray-600 mb-4">إنشاء مستخدمين ووظائف وطلبات للاختبار</p>
                <button onclick="createSampleData()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                    إنشاء البيانات
                </button>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6 text-center">
                <i class="fas fa-chart-bar text-green-500 text-3xl mb-4"></i>
                <h3 class="text-lg font-semibold mb-2">عرض الإحصائيات</h3>
                <p class="text-gray-600 mb-4">عرض الإحصائيات الحالية للنظام</p>
                <a href="{{ route('test.statistics') }}" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg inline-block">
                    عرض الإحصائيات
                </a>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6 text-center">
                <i class="fas fa-bug text-red-500 text-3xl mb-4"></i>
                <h3 class="text-lg font-semibold mb-2">تصحيح البيانات</h3>
                <p class="text-gray-600 mb-4">فحص البيانات والعلاقات في النظام</p>
                <button onclick="debugData()" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg">
                    فحص البيانات
                </button>
            </div>
        </div>
        
        <!-- منطقة النتائج -->
        <div id="results" class="bg-white rounded-lg shadow p-6 hidden">
            <h3 class="text-lg font-semibold mb-4">النتائج:</h3>
            <div id="results-content" class="bg-gray-50 p-4 rounded-lg">
                <!-- النتائج ستظهر هنا -->
            </div>
        </div>
        
        <!-- الإحصائيات السريعة -->
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mt-8">
            <div class="bg-white rounded-lg shadow p-4 text-center">
                <h4 class="font-semibold text-gray-600">المستخدمين</h4>
                <p class="text-2xl font-bold text-blue-600">{{ \App\Models\User::count() }}</p>
            </div>
            <div class="bg-white rounded-lg shadow p-4 text-center">
                <h4 class="font-semibold text-gray-600">أصحاب العمل</h4>
                <p class="text-2xl font-bold text-green-600">{{ \App\Models\Employer::count() }}</p>
            </div>
            <div class="bg-white rounded-lg shadow p-4 text-center">
                <h4 class="font-semibold text-gray-600">الباحثين</h4>
                <p class="text-2xl font-bold text-purple-600">{{ \App\Models\JobSeeker::count() }}</p>
            </div>
            <div class="bg-white rounded-lg shadow p-4 text-center">
                <h4 class="font-semibold text-gray-600">الوظائف</h4>
                <p class="text-2xl font-bold text-orange-600">{{ \App\Models\Job::count() }}</p>
            </div>
            <div class="bg-white rounded-lg shadow p-4 text-center">
                <h4 class="font-semibold text-gray-600">الطلبات</h4>
                <p class="text-2xl font-bold text-red-600">{{ \App\Models\Application::count() }}</p>
            </div>
        </div>
        
        <!-- روابط مفيدة -->
        <div class="mt-8 text-center">
            <h3 class="text-lg font-semibold mb-4">روابط مفيدة:</h3>
            <div class="space-x-4">
                <a href="{{ route('employer.dashboard') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                    لوحة تحكم صاحب العمل
                </a>
                <a href="{{ route('employer.statistics') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                    صفحة الإحصائيات الرسمية
                </a>
                <a href="{{ route('debug.statistics') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg" target="_blank">
                    JSON Debug
                </a>
            </div>
        </div>
    </div>

    <script>
        function createSampleData() {
            showLoading('إنشاء البيانات التجريبية...');
            
            fetch('{{ route("create.sample.data") }}')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showResults('تم إنشاء البيانات بنجاح!', data);
                        // إعادة تحميل الصفحة لتحديث الإحصائيات
                        setTimeout(() => location.reload(), 2000);
                    } else {
                        showResults('حدث خطأ: ' + data.message, data);
                    }
                })
                .catch(error => {
                    showResults('خطأ في الشبكة: ' + error.message, {});
                });
        }
        
        function debugData() {
            showLoading('فحص البيانات...');
            
            fetch('{{ route("debug.statistics") }}')
                .then(response => response.json())
                .then(data => {
                    showResults('نتائج فحص البيانات:', data);
                })
                .catch(error => {
                    showResults('خطأ في الشبكة: ' + error.message, {});
                });
        }
        
        function showLoading(message) {
            const results = document.getElementById('results');
            const content = document.getElementById('results-content');
            
            content.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin text-2xl text-blue-500"></i><p class="mt-2">' + message + '</p></div>';
            results.classList.remove('hidden');
        }
        
        function showResults(title, data) {
            const results = document.getElementById('results');
            const content = document.getElementById('results-content');
            
            let html = '<h4 class="font-semibold mb-2">' + title + '</h4>';
            html += '<pre class="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">' + JSON.stringify(data, null, 2) + '</pre>';
            
            content.innerHTML = html;
            results.classList.remove('hidden');
        }
    </script>
</body>
</html>
