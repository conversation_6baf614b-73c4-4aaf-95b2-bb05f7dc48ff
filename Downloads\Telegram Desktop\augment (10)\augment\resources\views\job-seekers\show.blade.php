@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-12">
    <div class="max-w-4xl mx-auto">
        <!-- Profile Header -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-lg p-8 mb-8">
            <div class="flex flex-col md:flex-row items-center md:items-start gap-6">
                <div class="w-32 h-32 bg-primary/10 rounded-full flex items-center justify-center">
                    @if($jobSeeker->user->avatar)
                        <img src="{{ asset('storage/' . $jobSeeker->user->avatar) }}" alt="{{ $jobSeeker->user->name }}" class="w-28 h-28 rounded-full object-cover">
                    @else
                        <i class="fas fa-user text-5xl text-primary"></i>
                    @endif
                </div>
                
                <div class="flex-1 text-center md:text-right">
                    <h1 class="text-3xl font-bold mb-2">{{ $jobSeeker->user->name }}</h1>
                    <p class="text-xl text-primary mb-4">{{ $jobSeeker->job_title ?: 'باحث عن عمل' }}</p>
                    
                    <div class="flex flex-wrap justify-center md:justify-start gap-4 mb-6">
                        @if($jobSeeker->location)
                            <div class="flex items-center text-gray-600 dark:text-gray-400">
                                <i class="fas fa-map-marker-alt ml-2"></i>
                                <span>{{ $jobSeeker->location }}</span>
                            </div>
                        @endif
                        
                        <div class="flex items-center text-gray-600 dark:text-gray-400">
                            <i class="fas fa-envelope ml-2"></i>
                            <span>{{ $jobSeeker->user->email }}</span>
                        </div>
                    </div>
                    
                    @auth
                        @if(auth()->user()->isEmployer())
                            <div class="flex flex-wrap justify-center md:justify-start gap-4">
                                <a href="mailto:{{ $jobSeeker->user->email }}" class="px-4 py-2 gradient-bg text-white rounded-lg hover:opacity-90 transition">
                                    <i class="fas fa-envelope ml-2"></i>تواصل
                                </a>
                                
                                @if($jobSeeker->resume)
                                    <a href="{{ asset('storage/' . $jobSeeker->resume) }}" target="_blank" class="px-4 py-2 border border-primary text-primary rounded-lg hover:bg-primary hover:text-white transition">
                                        <i class="fas fa-file-alt ml-2"></i>عرض السيرة الذاتية
                                    </a>
                                @endif
                            </div>
                        @endif
                    @endauth
                </div>
            </div>
        </div>
        
        <!-- Skills Section -->
        @if($jobSeeker->skills)
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-lg p-8 mb-8">
                <h2 class="text-2xl font-bold mb-6 flex items-center">
                    <i class="fas fa-lightbulb text-primary ml-3"></i>
                    المهارات
                </h2>
                
                <div class="flex flex-wrap gap-3">
                    @foreach(explode(',', $jobSeeker->skills) as $skill)
                        @if(trim($skill))
                            <span class="px-4 py-2 bg-gray-100 dark:bg-gray-800 rounded-lg text-gray-800 dark:text-gray-200">{{ trim($skill) }}</span>
                        @endif
                    @endforeach
                </div>
            </div>
        @endif
        
        <!-- Experience Section -->
        @if($jobSeeker->experience)
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-lg p-8 mb-8">
                <h2 class="text-2xl font-bold mb-6 flex items-center">
                    <i class="fas fa-briefcase text-primary ml-3"></i>
                    الخبرات
                </h2>
                
                <div class="prose max-w-none dark:prose-invert">
                    {!! nl2br(e($jobSeeker->experience)) !!}
                </div>
            </div>
        @endif
        
        <!-- Education Section -->
        @if($jobSeeker->education)
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-lg p-8">
                <h2 class="text-2xl font-bold mb-6 flex items-center">
                    <i class="fas fa-graduation-cap text-primary ml-3"></i>
                    التعليم
                </h2>
                
                <div class="prose max-w-none dark:prose-invert">
                    {!! nl2br(e($jobSeeker->education)) !!}
                </div>
            </div>
        @endif
    </div>
</div>
@endsection
