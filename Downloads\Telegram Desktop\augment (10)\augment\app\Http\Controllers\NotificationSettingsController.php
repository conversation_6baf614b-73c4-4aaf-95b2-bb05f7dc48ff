<?php

namespace App\Http\Controllers;

use App\Models\NotificationSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotificationSettingsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * عرض إعدادات الإشعارات
     */
    public function index()
    {
        $user = Auth::user();
        
        // إنشاء الإعدادات الافتراضية إذا لم تكن موجودة
        NotificationSetting::createDefaultSettingsForUser($user);
        
        // الحصول على جميع إعدادات المستخدم
        $settings = NotificationSetting::where('user_id', $user->id)
            ->get()
            ->keyBy('type');
        
        // أسماء أنواع الإشعارات بالعربية
        $notificationTypes = [
            'job_application' => [
                'name' => 'طلبات التوظيف',
                'description' => 'عند استلام طلب توظيف جديد',
                'icon' => 'file-alt',
                'color' => 'blue'
            ],
            'application_update' => [
                'name' => 'تحديثات الطلبات',
                'description' => 'عند تحديث حالة طلب التوظيف',
                'icon' => 'edit',
                'color' => 'green'
            ],
            'job_posted' => [
                'name' => 'نشر الوظائف',
                'description' => 'عند نشر وظيفة جديدة بنجاح',
                'icon' => 'briefcase',
                'color' => 'purple'
            ],
            'job_expired' => [
                'name' => 'انتهاء صلاحية الوظائف',
                'description' => 'عند انتهاء صلاحية إحدى الوظائف',
                'icon' => 'clock',
                'color' => 'orange'
            ],
            'payment_success' => [
                'name' => 'نجاح الدفع',
                'description' => 'عند إتمام عملية دفع بنجاح',
                'icon' => 'credit-card',
                'color' => 'green'
            ],
            'payment_failed' => [
                'name' => 'فشل الدفع',
                'description' => 'عند فشل عملية الدفع',
                'icon' => 'exclamation-triangle',
                'color' => 'red'
            ],
            'profile_rating' => [
                'name' => 'تقييم الملف الشخصي',
                'description' => 'عند تقييم ملفك الشخصي',
                'icon' => 'star',
                'color' => 'yellow'
            ],
            'company_rating' => [
                'name' => 'تقييم الشركة',
                'description' => 'عند تقييم شركتك',
                'icon' => 'building',
                'color' => 'indigo'
            ],
            'system_announcement' => [
                'name' => 'إعلانات النظام',
                'description' => 'الإعلانات والتحديثات المهمة',
                'icon' => 'bullhorn',
                'color' => 'red'
            ],
            'interview_scheduled' => [
                'name' => 'جدولة المقابلات',
                'description' => 'عند جدولة مقابلة عمل',
                'icon' => 'calendar-alt',
                'color' => 'blue'
            ],
            'job_recommendation' => [
                'name' => 'توصيات الوظائف',
                'description' => 'وظائف قد تهمك بناءً على ملفك الشخصي',
                'icon' => 'lightbulb',
                'color' => 'yellow'
            ],
        ];
        
        // تصفية الأنواع بناءً على دور المستخدم
        if ($user->role === 'job_seeker') {
            unset($notificationTypes['job_application']);
            unset($notificationTypes['company_rating']);
        } elseif ($user->role === 'employer') {
            unset($notificationTypes['application_update']);
            unset($notificationTypes['profile_rating']);
            unset($notificationTypes['job_recommendation']);
        }
        
        return view('notifications.settings', compact('settings', 'notificationTypes'));
    }

    /**
     * تحديث إعدادات الإشعارات
     */
    public function update(Request $request)
    {
        $user = Auth::user();
        
        $request->validate([
            'settings' => 'required|array',
            'settings.*.web_enabled' => 'boolean',
            'settings.*.email_enabled' => 'boolean',
            'settings.*.push_enabled' => 'boolean',
        ]);
        
        foreach ($request->settings as $type => $settings) {
            NotificationSetting::updateUserSetting($user, $type, [
                'web_enabled' => $settings['web_enabled'] ?? false,
                'email_enabled' => $settings['email_enabled'] ?? false,
                'push_enabled' => $settings['push_enabled'] ?? false,
            ]);
        }
        
        return redirect()->back()->with('success', 'تم حفظ إعدادات الإشعارات بنجاح');
    }

    /**
     * إعادة تعيين الإعدادات إلى الافتراضية
     */
    public function reset()
    {
        $user = Auth::user();
        
        // حذف الإعدادات الحالية
        NotificationSetting::where('user_id', $user->id)->delete();
        
        // إنشاء الإعدادات الافتراضية
        NotificationSetting::createDefaultSettingsForUser($user);
        
        return redirect()->back()->with('success', 'تم إعادة تعيين الإعدادات إلى الافتراضية');
    }
}
