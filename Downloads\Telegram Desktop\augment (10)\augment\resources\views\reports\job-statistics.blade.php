@extends('layouts.employer')

@section('title', 'إحصائيات الوظائف - Hire Me')
@section('header_title', 'إحصائيات الوظائف')

@section('content')
<div class="p-8 space-y-8">
        <!-- Header -->
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">إحصائيات الوظائف</h1>
                <p class="text-gray-600 dark:text-gray-400 mt-2">تحليل أداء وظائفك والطلبات المقدمة عليها</p>
            </div>
            <div class="mt-4 md:mt-0 flex gap-3">
                <button onclick="showExportModal()" class="px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl flex items-center gap-2">
                    <i class="fas fa-download"></i>
                    <span>تصدير التقرير</span>
                </button>
                <button onclick="printReport()" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors flex items-center gap-2">
                    <i class="fas fa-print"></i>
                    <span>طباعة</span>
                </button>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Jobs -->
            <div class="glass-card rounded-xl p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي الوظائف</p>
                        <p class="text-3xl font-bold text-gray-900 dark:text-white mt-2">{{ $totalJobs }}</p>
                    </div>
                    <div class="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                        <i class="fas fa-briefcase text-blue-600 dark:text-blue-400 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Active Jobs -->
            <div class="glass-card rounded-xl p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">الوظائف النشطة</p>
                        <p class="text-3xl font-bold text-green-600 dark:text-green-400 mt-2">{{ $activeJobs }}</p>
                    </div>
                    <div class="p-3 bg-green-100 dark:bg-green-900/30 rounded-full">
                        <i class="fas fa-check-circle text-green-600 dark:text-green-400 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Total Applications -->
            <div class="glass-card rounded-xl p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي الطلبات</p>
                        <p class="text-3xl font-bold text-purple-600 dark:text-purple-400 mt-2">{{ $totalApplications }}</p>
                    </div>
                    <div class="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-full">
                        <i class="fas fa-users text-purple-600 dark:text-purple-400 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Job Views -->
            <div class="glass-card rounded-xl p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">مشاهدات الوظائف</p>
                        <p class="text-3xl font-bold text-orange-600 dark:text-orange-400 mt-2">{{ number_format($jobViews) }}</p>
                    </div>
                    <div class="p-3 bg-orange-100 dark:bg-orange-900/30 rounded-full">
                        <i class="fas fa-eye text-orange-600 dark:text-orange-400 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- Applications by Status Chart -->
            <div class="glass-card rounded-xl p-6">
                <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">الطلبات حسب الحالة</h3>
                <div class="h-64">
                    <canvas id="statusChart"></canvas>
                </div>
            </div>

            <!-- Job Performance Chart -->
            <div class="glass-card rounded-xl p-6">
                <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">أداء الوظائف</h3>
                <div class="h-64">
                    <canvas id="performanceChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Recent Jobs Table -->
        <div class="glass-card rounded-xl overflow-hidden">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-bold text-gray-900 dark:text-white">الوظائف الحديثة</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">عنوان الوظيفة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">تاريخ النشر</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الطلبات</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">المشاهدات</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الحالة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        @forelse($recentJobs as $job)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900 dark:text-white">{{ $job->title }}</div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">{{ $job->location }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                {{ $job->created_at->format('Y-m-d') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                {{ $job->applications_count ?? 0 }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                {{ number_format($job->views ?? 0) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($job->is_active)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                        نشطة
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">
                                        غير نشطة
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="{{ route('jobs.show', $job) }}" class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 ml-3">
                                    عرض
                                </a>
                                <a href="{{ route('employer.jobs.edit', $job) }}" class="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300">
                                    تعديل
                                </a>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                                لا توجد وظائف حالياً
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Applications by Status Chart
const statusCtx = document.getElementById('statusChart').getContext('2d');
const statusChart = new Chart(statusCtx, {
    type: 'doughnut',
    data: {
        labels: [
            @foreach($applicationsByStatus as $status)
                '{{ \App\Models\Application::getStatusOptions()[$status->status] ?? $status->status }}',
            @endforeach
        ],
        datasets: [{
            data: [
                @foreach($applicationsByStatus as $status)
                    {{ $status->count }},
                @endforeach
            ],
            backgroundColor: [
                '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'
            ],
            borderWidth: 0
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 20,
                    usePointStyle: true
                }
            }
        }
    }
});

// Job Performance Chart (placeholder data)
const performanceCtx = document.getElementById('performanceChart').getContext('2d');
const performanceChart = new Chart(performanceCtx, {
    type: 'bar',
    data: {
        labels: [
            @foreach($recentJobs->take(5) as $job)
                '{{ Str::limit($job->title, 20) }}',
            @endforeach
        ],
        datasets: [{
            label: 'الطلبات',
            data: [
                @foreach($recentJobs->take(5) as $job)
                    {{ $job->applications_count ?? 0 }},
                @endforeach
            ],
            backgroundColor: '#3B82F6',
            borderRadius: 4
        }, {
            label: 'المشاهدات',
            data: [
                @foreach($recentJobs->take(5) as $job)
                    {{ $job->views ?? 0 }},
                @endforeach
            ],
            backgroundColor: '#10B981',
            borderRadius: 4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        },
        plugins: {
            legend: {
                position: 'top'
            }
        }
    }
});

function showExportModal() {
    console.log('Export button clicked!');
    document.getElementById('exportModal').classList.remove('hidden');
}

function hideExportModal() {
    document.getElementById('exportModal').classList.add('hidden');
}

// إغلاق modal عند النقر خارجه
document.getElementById('exportModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideExportModal();
    }
});

function startExport() {
    const selectedFormat = document.querySelector('input[name="export_format"]:checked').value;
    hideExportModal();

    console.log('Exporting as:', selectedFormat);

    let format = 'csv';
    if (selectedFormat === 'pdf') {
        format = 'pdf';
    } else if (selectedFormat === 'csv_filtered') {
        format = 'csv';
    }

    // استخدام window.open للتحميل المباشر
    const url = '/reports/job-statistics/export?format=' + format;
    console.log('Export URL:', url);

    window.open(url, '_blank');
}

// دالة لتحديد الخيار
function selectOption(value) {
    // إلغاء تحديد جميع الخيارات
    document.querySelectorAll('input[name="export_format"]').forEach(radio => {
        radio.checked = false;
    });

    // تحديد الخيار المطلوب
    document.querySelector(`input[name="export_format"][value="${value}"]`).checked = true;

    // تحديث المؤشرات البصرية
    updateRadioButtons();
}

// تحديث الـ radio buttons بصرياً
function updateRadioButtons() {
    const options = ['csv', 'csv_filtered', 'pdf'];

    options.forEach(option => {
        const radio = document.querySelector(`input[name="export_format"][value="${option}"]`);
        const indicator = document.getElementById(`indicator-${option}`);

        if (radio && indicator) {
            if (radio.checked) {
                indicator.className = 'w-4 h-4 rounded-full border-2 border-red-600 bg-red-600 flex items-center justify-center mr-3';
                indicator.innerHTML = '<div class="w-2 h-2 rounded-full bg-white"></div>';
            } else {
                indicator.className = 'w-4 h-4 rounded-full border-2 border-gray-300 dark:border-gray-600 mr-3';
                indicator.innerHTML = '';
            }
        }
    });
}

document.addEventListener('DOMContentLoaded', function() {
    // تحديث أولي
    updateRadioButtons();
});

function printReport() {
    window.print();
}
</script>

@push('styles')
<style>
@media print {
    .no-print {
        display: none !important;
    }
}
</style>
@endpush

<!-- Export Modal -->
<div id="exportModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-lg w-full mx-4" onclick="event.stopPropagation()">
        <!-- Header -->
        <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">تصدير تقرير إحصائيات الوظائف</h3>
            <button onclick="hideExportModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <!-- Body -->
        <div class="p-6">
            <div class="mb-6">
                <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4">اختر صيغة التصدير</h4>
                <div class="space-y-3">
                    <label class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors" onclick="selectOption('csv')">
                        <input type="radio" name="export_format" value="csv" checked class="hidden">
                        <div class="flex-1 text-right">
                            <div class="text-gray-900 dark:text-white font-medium">CSV (Excel) - جميع البيانات</div>
                        </div>
                        <div class="w-4 h-4 rounded-full border-2 border-red-600 bg-red-600 flex items-center justify-center mr-3" id="indicator-csv">
                            <div class="w-2 h-2 rounded-full bg-white"></div>
                        </div>
                    </label>

                    <label class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors" onclick="selectOption('csv_filtered')">
                        <input type="radio" name="export_format" value="csv_filtered" class="hidden">
                        <div class="flex-1 text-right">
                            <div class="text-gray-900 dark:text-white font-medium">CSV مع الفلاتر الحالية</div>
                        </div>
                        <div class="w-4 h-4 rounded-full border-2 border-gray-300 dark:border-gray-600 mr-3" id="indicator-csv_filtered"></div>
                    </label>

                    <label class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors" onclick="selectOption('pdf')">
                        <input type="radio" name="export_format" value="pdf" class="hidden">
                        <div class="flex-1 text-right">
                            <div class="text-gray-900 dark:text-white font-medium">PDF - تقرير مفصل</div>
                        </div>
                        <div class="w-4 h-4 rounded-full border-2 border-gray-300 dark:border-gray-600 mr-3" id="indicator-pdf"></div>
                    </label>
                </div>
            </div>

            <!-- Info Box -->
            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <div class="flex items-start">
                    <i class="fas fa-info-circle text-blue-500 mt-1 ml-2"></i>
                    <div class="text-sm text-blue-700 dark:text-blue-300">
                        <div class="font-medium mb-2">سيتم تصدير:</div>
                        <ul class="space-y-1 text-xs">
                            <li>• إحصائيات الوظائف الشاملة</li>
                            <li>• بيانات الطلبات حسب الحالة</li>
                            <li>• أداء الوظائف ومعدلات المشاهدة</li>
                            <li>• الرسوم البيانية والتحليلات</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="flex gap-3 p-6 border-t border-gray-200 dark:border-gray-700">
            <button onclick="hideExportModal()" class="flex-1 px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors font-medium">
                إلغاء
            </button>
            <button onclick="startExport()" class="flex-1 px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors flex items-center justify-center gap-2 font-medium">
                <i class="fas fa-download"></i>
                تصدير
            </button>
        </div>
    </div>
</div>
@endsection
