<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير تحليل المتقدمين</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #3b82f6;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #1f2937;
            margin: 0;
            font-size: 28px;
        }
        .header p {
            color: #6b7280;
            margin: 10px 0 0 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: #f3f4f6;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-card h3 {
            color: #374151;
            margin: 0 0 10px 0;
            font-size: 16px;
        }
        .stat-card .number {
            color: #3b82f6;
            font-size: 32px;
            font-weight: bold;
            margin: 0;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #1f2937;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .table th,
        .table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #e5e7eb;
        }
        .table th {
            background-color: #f9fafb;
            font-weight: bold;
            color: #374151;
        }
        .table tr:hover {
            background-color: #f9fafb;
        }
        .chart-placeholder {
            background: #f3f4f6;
            height: 200px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
            margin-bottom: 20px;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
            font-size: 14px;
        }
        @media print {
            body {
                background-color: white;
            }
            .container {
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>تقرير تحليل المتقدمين</h1>
            <p>{{ $employer->company_name ?? 'الشركة' }}</p>
            <p>تاريخ التقرير: {{ date('Y-m-d H:i:s') }}</p>
        </div>

        <!-- Statistics Grid -->
        <div class="stats-grid">
            <div class="stat-card">
                <h3>إجمالي المتقدمين</h3>
                <p class="number">{{ number_format($data['totalApplicants']) }}</p>
            </div>
            <div class="stat-card">
                <h3>متوسط الطلبات لكل وظيفة</h3>
                <p class="number">{{ $data['applicationsByJob']->count() > 0 ? round($data['applicationsByJob']->avg('applications_count'), 1) : 0 }}</p>
            </div>
        </div>

        <!-- Application Trend -->
        <div class="section">
            <h2>اتجاه الطلبات (آخر 30 يوم)</h2>
            <div class="chart-placeholder">
                <p>رسم بياني لاتجاه الطلبات خلال آخر 30 يوم</p>
            </div>
            <table class="table">
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>عدد الطلبات</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($data['applicationTrend']->take(10) as $trend)
                    <tr>
                        <td>{{ \Carbon\Carbon::parse($trend->date)->format('Y-m-d') }}</td>
                        <td>{{ number_format($trend->count) }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Top Jobs by Applications -->
        <div class="section">
            <h2>أكثر الوظائف طلباً</h2>
            <table class="table">
                <thead>
                    <tr>
                        <th>عنوان الوظيفة</th>
                        <th>عدد الطلبات</th>
                        <th>معدل التحويل</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($data['applicationsByJob'] as $job)
                    <tr>
                        <td>{{ $job->title }}</td>
                        <td>{{ number_format($job->applications_count ?? 0) }}</td>
                        <td>{{ $job->views > 0 ? round(($job->applications_count / $job->views) * 100, 1) : 0 }}%</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Top Applicant Locations -->
        <div class="section">
            <h2>أهم مواقع المتقدمين</h2>
            <table class="table">
                <thead>
                    <tr>
                        <th>الموقع</th>
                        <th>عدد المتقدمين</th>
                        <th>النسبة المئوية</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($data['topApplicantLocations'] as $location)
                    <tr>
                        <td>{{ $location->location ?? 'غير محدد' }}</td>
                        <td>{{ number_format($location->count) }}</td>
                        <td>{{ $data['totalApplicants'] > 0 ? round(($location->count / $data['totalApplicants']) * 100, 1) : 0 }}%</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>تم إنشاء هذا التقرير بواسطة نظام Hire Me</p>
            <p>{{ date('Y-m-d H:i:s') }}</p>
        </div>
    </div>
</body>
</html>
