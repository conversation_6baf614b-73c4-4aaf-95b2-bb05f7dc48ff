<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Notification;
use App\Services\NotificationService;

class NotificationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $notificationService = app(NotificationService::class);
        
        // الحصول على المستخدمين
        $admin = User::where('email', '<EMAIL>')->first();
        $employers = User::where('role', 'employer')->take(3)->get();
        $jobSeekers = User::where('role', 'job_seeker')->take(3)->get();
        
        if ($admin) {
            // إشعارات للمدير
            $notificationService->create(
                $admin,
                NotificationService::TYPE_SYSTEM_ANNOUNCEMENT,
                'مرحباً بك في نظام الإشعارات المحدث! يمكنك الآن متابعة جميع الأنشطة بسهولة.',
                route('admin.dashboard')
            );
            
            $notificationService->create(
                $admin,
                NotificationService::TYPE_JOB_APPLICATION,
                'تم استلام 5 طلبات توظيف جديدة اليوم',
                route('admin.applications.index')
            );
            
            $notificationService->create(
                $admin,
                NotificationService::TYPE_PAYMENT_SUCCESS,
                'تم استلام دفعة جديدة بقيمة 500 ريال من شركة التقنية المتطورة',
                route('admin.reports.financial')
            );
        }
        
        // إشعارات لأصحاب العمل
        foreach ($employers as $index => $employer) {
            $notificationService->create(
                $employer,
                NotificationService::TYPE_JOB_APPLICATION,
                'تم استلام طلب توظيف جديد للوظيفة: مطور PHP متقدم',
                route('employer.applications')
            );
            
            $notificationService->create(
                $employer,
                NotificationService::TYPE_PAYMENT_SUCCESS,
                'تم الدفع بنجاح! تم تفعيل حسابك لمدة شهر',
                route('employer.profile')
            );
            
            $notificationService->create(
                $employer,
                NotificationService::TYPE_COMPANY_RATING,
                'تم تقييم شركتك بـ 5 نجوم من قبل أحد المتقدمين',
                route('employer.profile'),
                ['rating' => 5, 'comment' => 'شركة ممتازة وبيئة عمل رائعة']
            );
            
            if ($index === 0) {
                $notificationService->create(
                    $employer,
                    NotificationService::TYPE_JOB_EXPIRED,
                    'انتهت صلاحية الوظيفة: مصمم جرافيك. يرجى تجديدها أو إنشاء وظيفة جديدة.',
                    route('employer.jobs')
                );
            }
        }
        
        // إشعارات للباحثين عن عمل
        foreach ($jobSeekers as $index => $jobSeeker) {
            $notificationService->create(
                $jobSeeker,
                NotificationService::TYPE_APPLICATION_UPDATE,
                'تم تحديث حالة طلبك للوظيفة "مطور واجهات أمامية" إلى: قيد المراجعة',
                route('job-seeker.applications')
            );
            
            $notificationService->create(
                $jobSeeker,
                NotificationService::TYPE_JOB_RECOMMENDATION,
                'وظيفة قد تهمك: محلل بيانات في شركة الابتكار التقني',
                route('jobs.show', 1)
            );
            
            $notificationService->create(
                $jobSeeker,
                NotificationService::TYPE_PROFILE_RATING,
                'تم تقييم ملفك الشخصي بـ 4 نجوم من قبل إحدى الشركات',
                route('job-seeker.profile'),
                ['rating' => 4, 'comment' => 'ملف شخصي متميز ومهارات قوية']
            );
            
            if ($index === 0) {
                $notificationService->create(
                    $jobSeeker,
                    NotificationService::TYPE_INTERVIEW_SCHEDULED,
                    'تم جدولة مقابلة للوظيفة "مطور PHP متقدم" في 2024-07-20 الساعة 10:00 صباحاً',
                    route('job-seeker.applications'),
                    [
                        'date' => '2024-07-20',
                        'time' => '10:00',
                        'location' => 'مكتب الشركة - الرياض',
                        'type' => 'حضوري'
                    ]
                );
                
                $notificationService->create(
                    $jobSeeker,
                    NotificationService::TYPE_APPLICATION_UPDATE,
                    'تهانينا! تم قبول طلبك للوظيفة "مصمم جرافيك"',
                    route('job-seeker.applications')
                );
            }
        }
        
        // إشعارات عامة للجميع
        $allUsers = User::all();
        foreach ($allUsers->take(5) as $user) {
            $notificationService->create(
                $user,
                NotificationService::TYPE_SYSTEM_ANNOUNCEMENT,
                'تحديث جديد: تم إضافة ميزة الإشعارات الفورية لتحسين تجربة المستخدم',
                null,
                ['version' => '2.1.0', 'features' => ['إشعارات فورية', 'تحسينات الأداء', 'واجهة محسنة']]
            );
        }
        
        // إنشاء بعض الإشعارات القديمة (مقروءة)
        foreach ($employers->take(2) as $employer) {
            $notification = $notificationService->create(
                $employer,
                NotificationService::TYPE_JOB_POSTED,
                'تم نشر وظيفتك "محاسب مالي" بنجاح',
                route('employer.jobs')
            );
            $notification->update(['is_read' => true, 'created_at' => now()->subDays(3)]);
        }
        
        foreach ($jobSeekers->take(2) as $jobSeeker) {
            $notification = $notificationService->create(
                $jobSeeker,
                NotificationService::TYPE_APPLICATION_UPDATE,
                'تم رفض طلبك للوظيفة "مطور تطبيقات الجوال"',
                route('job-seeker.applications')
            );
            $notification->update(['is_read' => true, 'created_at' => now()->subDays(5)]);
        }
    }
}
