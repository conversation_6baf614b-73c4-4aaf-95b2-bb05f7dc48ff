@extends('layouts.employer')

@section('title', 'التقييمات والمراجعات')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
            <div class="mb-4 sm:mb-0">
                <div class="flex items-center gap-3 mb-2">
                    <div class="w-10 h-10 rounded-xl bg-gradient-to-br from-yellow-400 to-orange-600 flex items-center justify-center text-white shadow-lg">
                        <i class="fas fa-star text-lg"></i>
                    </div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">التقييمات والمراجعات</h1>
                </div>
                <p class="text-gray-600 dark:text-gray-400">إدارة ومراجعة تقييمات الشركة والردود عليها</p>
            </div>
            <div class="flex flex-col sm:flex-row gap-3">
                <button class="w-full sm:w-auto btn btn-secondary" onclick="toggleFilters()">
                    <i class="fas fa-filter ml-2"></i>فلترة
                </button>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Reviews -->
            <div class="card group hover:shadow-lg transition-all duration-300">
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">إجمالي التقييمات</p>
                            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ $stats['total'] }}</p>
                        </div>
                        <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center text-white shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
                            <i class="fas fa-star text-lg"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Average Rating -->
            <div class="card group hover:shadow-lg transition-all duration-300">
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">متوسط التقييم</p>
                            <div class="flex items-center gap-2">
                                <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ $stats['average'] ?? 0 }}</p>
                                <span class="text-sm text-gray-500 dark:text-gray-400">/ 5</span>
                            </div>
                            <div class="flex items-center mt-1">
                                @for($i = 1; $i <= 5; $i++)
                                    <i class="fas fa-star text-sm {{ $i <= ($stats['average'] ?? 0) ? 'text-yellow-400' : 'text-gray-300' }}"></i>
                                @endfor
                            </div>
                        </div>
                        <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-yellow-400 to-orange-600 flex items-center justify-center text-white shadow-lg group-hover:shadow-yellow-500/25 transition-all duration-300">
                            <i class="fas fa-chart-line text-lg"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Approved Reviews -->
            <div class="card group hover:shadow-lg transition-all duration-300">
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">معتمدة</p>
                            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ $stats['approved'] }}</p>
                        </div>
                        <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center text-white shadow-lg group-hover:shadow-green-500/25 transition-all duration-300">
                            <i class="fas fa-check-circle text-lg"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pending Reviews -->
            <div class="card group hover:shadow-lg transition-all duration-300">
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">في الانتظار</p>
                            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ $stats['pending'] }}</p>
                        </div>
                        <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-orange-400 to-orange-600 flex items-center justify-center text-white shadow-lg group-hover:shadow-orange-500/25 transition-all duration-300">
                            <i class="fas fa-clock text-lg"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Panel -->
        <div id="filterPanel" class="hidden mb-8">
            <div class="card">
                <div class="p-6">
                    <form method="GET" action="{{ route('employer.reviews.index') }}" class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            <div>
                                <label for="search" class="form-label">البحث</label>
                                <input type="text" class="form-input" id="search" name="search"
                                       value="{{ request('search') }}" placeholder="البحث في اسم المقيم أو التعليق...">
                            </div>
                            <div>
                                <label for="rating" class="form-label">التقييم</label>
                                <select class="form-input" id="rating" name="rating">
                                    <option value="">جميع التقييمات</option>
                                    <option value="5" {{ request('rating') == '5' ? 'selected' : '' }}>5 نجوم</option>
                                    <option value="4" {{ request('rating') == '4' ? 'selected' : '' }}>4 نجوم</option>
                                    <option value="3" {{ request('rating') == '3' ? 'selected' : '' }}>3 نجوم</option>
                                    <option value="2" {{ request('rating') == '2' ? 'selected' : '' }}>2 نجوم</option>
                                    <option value="1" {{ request('rating') == '1' ? 'selected' : '' }}>1 نجمة</option>
                                </select>
                            </div>
                            <div>
                                <label for="status" class="form-label">الحالة</label>
                                <select class="form-input" id="status" name="status">
                                    <option value="">جميع الحالات</option>
                                    <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>معتمد</option>
                                    <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>في الانتظار</option>
                                    <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>مرفوض</option>
                                </select>
                            </div>
                            <div class="flex items-end gap-3">
                                <button type="submit" class="btn btn-primary flex-1">
                                    <i class="fas fa-search ml-2"></i>بحث
                                </button>
                                <a href="{{ route('employer.reviews.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times ml-2"></i>إعادة تعيين
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Reviews List -->
        <div class="card">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">جميع التقييمات</h3>
            </div>
            <div class="p-6">
                @if($reviews->count() > 0)
                    <div class="space-y-6">
                        @foreach($reviews as $review)
                            <div class="review-card p-6 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300 bg-white dark:bg-gray-800">
                                <div class="flex items-start gap-4">
                                    <!-- Avatar -->
                                    <div class="flex-shrink-0">
                                        <div class="w-12 h-12 rounded-full bg-gradient-to-br from-blue-400 to-purple-600 flex items-center justify-center text-white font-bold text-lg shadow-lg">
                                            {{ substr($review->reviewer->name, 0, 1) }}
                                        </div>
                                    </div>

                                    <!-- Content -->
                                    <div class="flex-1 min-w-0">
                                        <!-- Header -->
                                        <div class="flex items-start justify-between mb-3">
                                            <div>
                                                <h4 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $review->reviewer->name }}</h4>
                                                <p class="text-sm text-gray-500 dark:text-gray-400">{{ $review->reviewer->email }}</p>
                                            </div>
                                            <div class="flex items-center gap-3">
                                                <!-- Status Badge -->
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                                                    @if($review->status === 'approved')
                                                        bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400
                                                    @elseif($review->status === 'pending')
                                                        bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400
                                                    @else
                                                        bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400
                                                    @endif
                                                ">
                                                    @if($review->status === 'approved')
                                                        معتمد
                                                    @elseif($review->status === 'pending')
                                                        في الانتظار
                                                    @else
                                                        مرفوض
                                                    @endif
                                                </span>

                                                <!-- Actions -->
                                                <div class="flex items-center gap-2">
                                                    <a href="{{ route('employer.reviews.show', $review) }}"
                                                       class="inline-flex items-center justify-center w-8 h-8 rounded-lg bg-blue-100 hover:bg-blue-200 text-blue-600 transition-colors duration-200"
                                                       title="عرض التفاصيل">
                                                        <i class="fas fa-eye text-sm"></i>
                                                    </a>
                                                    @if($review->status === 'approved' && !$review->employer_response)
                                                        <button type="button"
                                                                class="inline-flex items-center justify-center w-8 h-8 rounded-lg bg-green-100 hover:bg-green-200 text-green-600 transition-colors duration-200"
                                                                onclick="openResponseModal({{ $review->id }})"
                                                                title="الرد على التقييم">
                                                            <i class="fas fa-reply text-sm"></i>
                                                        </button>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Rating -->
                                        <div class="flex items-center gap-3 mb-3">
                                            <div class="flex items-center">
                                                @for($i = 1; $i <= 5; $i++)
                                                    <i class="fas fa-star text-lg {{ $i <= $review->rating ? 'text-yellow-400' : 'text-gray-300' }}"></i>
                                                @endfor
                                            </div>
                                            <span class="text-lg font-semibold text-gray-900 dark:text-white">{{ $review->rating }}/5</span>
                                        </div>

                                        <!-- Comment -->
                                        @if($review->comment)
                                            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 mb-3">
                                                <p class="text-gray-700 dark:text-gray-300 leading-relaxed">{{ $review->comment }}</p>
                                            </div>
                                        @endif

                                        <!-- Response -->
                                        @if($review->employer_response)
                                            <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border-r-4 border-blue-400">
                                                <div class="flex items-center gap-2 mb-2">
                                                    <i class="fas fa-reply text-blue-600 dark:text-blue-400"></i>
                                                    <span class="text-sm font-medium text-blue-600 dark:text-blue-400">رد الشركة:</span>
                                                </div>
                                                <p class="text-gray-700 dark:text-gray-300">{{ $review->employer_response }}</p>
                                            </div>
                                        @endif

                                        <!-- Date -->
                                        <div class="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mt-3">
                                            <span class="flex items-center gap-1">
                                                <i class="fas fa-calendar text-xs"></i>
                                                {{ $review->created_at->format('Y-m-d') }}
                                            </span>
                                            <span class="flex items-center gap-1">
                                                <i class="fas fa-clock text-xs"></i>
                                                {{ $review->created_at->format('H:i') }}
                                            </span>
                                            <span class="text-gray-400">
                                                {{ $review->created_at->diffForHumans() }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    <div class="flex justify-center mt-8">
                        {{ $reviews->appends(request()->query())->links() }}
                    </div>
                @else
                    <div class="text-center py-16">
                        <div class="w-24 h-24 mx-auto mb-6 rounded-full bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 flex items-center justify-center">
                            <i class="fas fa-star text-3xl text-gray-400 dark:text-gray-500"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">لا توجد تقييمات</h3>
                        <p class="text-gray-600 dark:text-gray-400">ستظهر التقييمات هنا عند إضافتها</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Response Modal -->
<div id="responseModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">الرد على التقييم</h3>
                <button onclick="closeResponseModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <form id="responseForm" method="POST" class="p-6">
            @csrf
            <div class="space-y-6">
                <!-- Original Review -->
                <div>
                    <label class="form-label">التقييم الأصلي:</label>
                    <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <span id="reviewerName" class="font-semibold text-gray-900 dark:text-white"></span>
                            <div id="reviewStars" class="flex items-center"></div>
                        </div>
                        <p id="reviewComment" class="text-gray-700 dark:text-gray-300"></p>
                    </div>
                </div>

                <!-- Response -->
                <div>
                    <label for="response" class="form-label">ردك:</label>
                    <textarea name="response" id="response" class="form-input" rows="4"
                              placeholder="اكتب ردك على هذا التقييم..." required></textarea>
                </div>
            </div>

            <div class="flex items-center justify-end gap-3 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                <button type="button" onclick="closeResponseModal()" class="btn btn-secondary">إلغاء</button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-reply ml-2"></i>
                    إرسال الرد
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function toggleFilters() {
    const filterPanel = document.getElementById('filterPanel');
    const isHidden = filterPanel.classList.contains('hidden');

    if (isHidden) {
        filterPanel.classList.remove('hidden');
        filterPanel.style.opacity = '0';
        filterPanel.style.transform = 'translateY(-10px)';

        requestAnimationFrame(() => {
            filterPanel.style.transition = 'all 0.3s ease-out';
            filterPanel.style.opacity = '1';
            filterPanel.style.transform = 'translateY(0)';
        });
    } else {
        filterPanel.style.transition = 'all 0.3s ease-in';
        filterPanel.style.opacity = '0';
        filterPanel.style.transform = 'translateY(-10px)';

        setTimeout(() => {
            filterPanel.classList.add('hidden');
        }, 300);
    }
}

// Auto-hide filter panel when clicking outside
document.addEventListener('click', function(event) {
    const filterPanel = document.getElementById('filterPanel');
    const filterButton = event.target.closest('button[onclick="toggleFilters()"]');

    if (!filterPanel.contains(event.target) && !filterButton && !filterPanel.classList.contains('hidden')) {
        toggleFilters();
    }
});

function openResponseModal(reviewId) {
    const modal = document.getElementById('responseModal');
    const form = document.getElementById('responseForm');

    // Find the review data
    const reviewCard = document.querySelector(`[data-review-id="${reviewId}"]`) ||
                      document.querySelector('.review-card:nth-child(' + (reviewId) + ')');

    if (reviewCard) {
        const reviewerName = reviewCard.querySelector('h4').textContent;
        const reviewComment = reviewCard.querySelector('.bg-gray-50 p').textContent;
        const stars = reviewCard.querySelectorAll('.fas.fa-star.text-yellow-400').length;

        // Update modal content
        document.getElementById('reviewerName').textContent = reviewerName;
        document.getElementById('reviewComment').textContent = reviewComment;

        // Update stars
        const starsContainer = document.getElementById('reviewStars');
        starsContainer.innerHTML = '';
        for (let i = 1; i <= 5; i++) {
            const star = document.createElement('i');
            star.className = `fas fa-star ${i <= stars ? 'text-yellow-400' : 'text-gray-300'}`;
            starsContainer.appendChild(star);
        }

        // Update form action
        form.action = `/employer/reviews/${reviewId}/respond`;
    }

    modal.classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function closeResponseModal() {
    const modal = document.getElementById('responseModal');
    modal.classList.add('hidden');
    document.body.style.overflow = 'auto';

    // Clear form
    document.getElementById('response').value = '';
}

// Close modal when clicking outside
document.getElementById('responseModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeResponseModal();
    }
});

// Enhanced animations
document.addEventListener('DOMContentLoaded', function() {
    // Add stagger animation to review cards
    const reviewCards = document.querySelectorAll('.review-card');
    reviewCards.forEach((card, index) => {
        card.style.setProperty('--stagger', index);
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in-up');
    });
});
</script>

@push('styles')
<style>
.review-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.review-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
    transform: translateY(30px);
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .review-card:hover {
        transform: none;
        box-shadow: none;
    }

    .review-card {
        padding: 1rem;
    }
}

/* تحسين الألوان للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .review-card {
        background: linear-gradient(135deg, rgba(31, 41, 55, 0.8) 0%, rgba(17, 24, 39, 0.8) 100%);
    }
}

/* تحسين التركيز للوصولية */
.review-card:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* تحسين الأزرار */
.action-button {
    transition: all 0.2s ease-in-out;
}

.action-button:hover {
    transform: scale(1.1);
}

.action-button:active {
    transform: scale(0.95);
}
</style>
@endpush
@endsection
