/* تحسينات إضافية للوحة الإدارة */

/* تأثيرات الحركة المتقدمة */
@keyframes float {
    0%,
    100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes slideInFromRight {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInFromLeft {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes scaleIn {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

/* تأثيرات الخلفية المتحركة المحسنة */
.floating-shapes {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    z-index: -1;
}

.floating-shapes::before,
.floating-shapes::after {
    content: "";
    position: absolute;
    border-radius: 50%;
    animation: float 8s ease-in-out infinite;
    filter: blur(1px);
}

.floating-shapes::before {
    width: 120px;
    height: 120px;
    background: linear-gradient(
        45deg,
        rgba(99, 102, 241, 0.08),
        rgba(168, 85, 247, 0.08)
    );
    top: 15%;
    left: 8%;
    animation-delay: -3s;
}

.floating-shapes::after {
    width: 180px;
    height: 180px;
    background: linear-gradient(
        45deg,
        rgba(16, 185, 129, 0.06),
        rgba(245, 101, 101, 0.06)
    );
    top: 65%;
    right: 12%;
    animation-delay: -6s;
}

/* تحسينات البطاقات */
.enhanced-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.enhanced-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.dark .enhanced-card {
    background: rgba(30, 41, 59, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.dark .enhanced-card:hover {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* تأثيرات الأزرار المحسنة */
.btn-modern {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #0ea5e9 0%, #d946ef 100%);
    border: none;
    border-radius: 12px;
    padding: 12px 24px;
    color: white;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(14, 165, 233, 0.3);
}

.btn-modern::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: left 0.5s ease;
}

.btn-modern:hover::before {
    left: 100%;
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(14, 165, 233, 0.4);
}

.btn-modern:active {
    transform: translateY(0);
}

/* تحسينات الجدول */
.modern-table {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.modern-table th {
    background: linear-gradient(135deg, #0ea5e9 0%, #d946ef 100%);
    color: white;
    font-weight: 600;
    padding: 16px;
    text-align: right;
}

.modern-table td {
    padding: 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: background-color 0.2s ease;
}

.modern-table tr:hover td {
    background-color: rgba(14, 165, 233, 0.05);
}

.dark .modern-table {
    background: rgba(30, 41, 59, 0.95);
}

.dark .modern-table td {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .modern-table tr:hover td {
    background-color: rgba(14, 165, 233, 0.1);
}

/* تحسينات النماذج */
.modern-input {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(14, 165, 233, 0.2);
    border-radius: 12px;
    padding: 12px 16px;
    transition: all 0.3s ease;
    width: 100%;
}

.modern-input:focus {
    outline: none;
    border-color: #0ea5e9;
    box-shadow: 0 0 0 4px rgba(14, 165, 233, 0.1);
    background: rgba(255, 255, 255, 1);
}

.dark .modern-input {
    background: rgba(30, 41, 59, 0.9);
    border-color: rgba(14, 165, 233, 0.3);
    color: white;
}

.dark .modern-input:focus {
    background: rgba(30, 41, 59, 1);
    border-color: #38bdf8;
}

/* تأثيرات التحميل */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

.dark .loading-shimmer {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200px 100%;
}

/* تحسينات الإشعارات */
.notification-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-left: 4px solid #0ea5e9;
    border-radius: 12px;
    padding: 16px 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    animation: slideInFromRight 0.3s ease-out;
}

.notification-modern.success {
    border-left-color: #22c55e;
}

.notification-modern.warning {
    border-left-color: #f59e0b;
}

.notification-modern.error {
    border-left-color: #ef4444;
}

.dark .notification-modern {
    background: rgba(30, 41, 59, 0.95);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* تحسينات الشريط الجانبي */
.sidebar-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
}

.dark .sidebar-modern {
    background: rgba(15, 23, 42, 0.95);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.3);
}

/* تأثيرات الانتقال السلسة */
.smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تحسينات الرسوم البيانية */
.chart-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dark .chart-container {
    background: rgba(30, 41, 59, 0.95);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* تحسينات الأيقونات */
.icon-modern {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, #0ea5e9 0%, #d946ef 100%);
    color: white;
    font-size: 20px;
    box-shadow: 0 4px 15px rgba(14, 165, 233, 0.3);
    transition: all 0.3s ease;
}

.icon-modern:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(14, 165, 233, 0.4);
}

/* تحسينات الشاشات الصغيرة */
@media (max-width: 768px) {
    .enhanced-card {
        margin: 8px;
    }

    .btn-modern {
        padding: 10px 20px;
        font-size: 14px;
    }

    .modern-table {
        font-size: 14px;
    }

    .modern-table th,
    .modern-table td {
        padding: 12px 8px;
    }
}
