<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckPaymentStatus
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!$request->user() || !$request->user()->isEmployer()) {
            return redirect()->route('home');
        }
        
        // التحقق من وجود مدفوعات نشطة
        $hasActivePayment = $request->user()->employer->payments()
            ->where('status', 'completed')
            ->where('created_at', '>=', now()->subDays(30))
            ->exists();
            
        if (!$hasActivePayment) {
            return redirect()->route('payments.checkout', ['payment_type' => 'job_posting'])
                ->with('warning', 'يجب عليك دفع رسوم النشر قبل إضافة وظائف جديدة');
        }

        return $next($request);
    }
}