@extends('layouts.admin')

@section('title', 'تفاصيل المراجعة - Hire Me')

@section('content')
<!-- Review Details Content -->
<div class="p-6">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h2 class="text-2xl font-bold">تفاصيل المراجعة</h2>
            <p class="text-gray-600 dark:text-gray-400">معرف المراجعة: #{{ $id }}</p>
        </div>
        <div class="flex gap-3">
            <button class="btn-secondary">
                <i class="fas fa-arrow-right ml-2"></i>
                العودة للقائمة
            </button>
            <button class="btn-success">
                <i class="fas fa-check ml-2"></i>
                الموافقة
            </button>
            <button class="btn-danger">
                <i class="fas fa-times ml-2"></i>
                الرفض
            </button>
        </div>
    </div>

    <!-- Review Card -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden mb-6">
        <div class="p-6">
            <div class="flex justify-between items-start mb-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-12 w-12">
                        @if(isset($review))
                            @if($type == 'company')
                                <img class="h-12 w-12 rounded-full" src="{{ $review->jobSeeker->user->avatar ?? 'https://ui-avatars.com/api/?name=' . urlencode($review->jobSeeker->user->name) . '&background=random' }}" alt="">
                            @else
                                <img class="h-12 w-12 rounded-full" src="{{ $review->employer->user->avatar ?? 'https://ui-avatars.com/api/?name=' . urlencode($review->employer->user->name) . '&background=random' }}" alt="">
                            @endif
                        @else
                            <img class="h-12 w-12 rounded-full" src="https://ui-avatars.com/api/?name=User&background=random" alt="">
                        @endif
                    </div>
                    <div class="mr-4">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                            @if(isset($review))
                                @if($type == 'company')
                                    {{ $review->jobSeeker->user->name }}
                                @else
                                    {{ $review->employer->user->name }}
                                @endif
                            @else
                                مستخدم غير معروف
                            @endif
                        </h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">
                            @if(isset($review))
                                @if($type == 'company')
                                    {{ $review->jobSeeker->user->email }}
                                @else
                                    {{ $review->employer->user->email }}
                                @endif
                            @else
                                <EMAIL>
                            @endif
                        </p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">
                            تم النشر: {{ isset($review) ? $review->created_at->format('d M Y') : 'غير معروف' }}
                        </p>
                    </div>
                </div>
                <div class="text-left">
                    <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full {{ $type == 'company' ? 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100' : 'bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100' }}">
                        {{ $type == 'company' ? 'مراجعة شركة' : 'مراجعة مرشح' }}
                    </span>
                    <div class="flex text-yellow-400 mt-2">
                        @if(isset($review))
                            @for($i = 1; $i <= 5; $i++)
                                @if($i <= $review->rating)
                                    <i class="fas fa-star"></i>
                                @else
                                    <i class="far fa-star"></i>
                                @endif
                            @endfor
                            <span class="mr-2 text-gray-700 dark:text-gray-300">{{ $review->rating }}.0</span>
                        @else
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="far fa-star"></i>
                            <i class="far fa-star"></i>
                            <span class="mr-2 text-gray-700 dark:text-gray-300">3.0</span>
                        @endif
                    </div>
                </div>
            </div>

            <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    @if(isset($review))
                        @if($type == 'company')
                            {{ $review->employer->company_name ?? 'شركة' }}
                        @else
                            {{ $review->jobSeeker->job_title ?? 'مرشح' }}
                        @endif
                    @else
                        عنوان المراجعة
                    @endif
                </h4>
                <div class="prose dark:prose-invert max-w-none">
                    <p>{{ $review->comment ?? 'لا يوجد تعليق' }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Notes -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden mb-6">
        <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">ملاحظات المدير</h3>
            <form action="{{ route('admin.reviews.update-status', $id) }}" method="POST">
                @csrf
                <input type="hidden" name="type" value="{{ $type }}">
                <textarea name="admin_notes" class="form-textarea w-full" rows="4" placeholder="أضف ملاحظاتك هنا...">{{ $review->admin_notes ?? '' }}</textarea>
                <div class="mt-4 flex justify-between">
                    <div>
                        <button type="submit" name="status" value="approved" class="btn-success">
                            <i class="fas fa-check ml-2"></i>
                            الموافقة على المراجعة
                        </button>
                        <button type="submit" name="status" value="rejected" class="btn-danger mr-2">
                            <i class="fas fa-times ml-2"></i>
                            رفض المراجعة
                        </button>
                    </div>
                    <button type="submit" name="status" value="{{ $review->status ?? 'pending' }}" class="btn-primary">
                        <i class="fas fa-save ml-2"></i>
                        حفظ الملاحظات فقط
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Review History -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
        <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">سجل المراجعة</h3>
            <div class="space-y-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <div class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center">
                            <i class="fas fa-plus text-white"></i>
                        </div>
                    </div>
                    <div class="mr-4 flex-1">
                        <p class="text-sm text-gray-700 dark:text-gray-300">تم إنشاء المراجعة</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">{{ isset($review) ? $review->created_at->format('d M Y - h:i A') : 'غير معروف' }}</p>
                    </div>
                </div>

                @if(isset($review) && $review->updated_at && $review->updated_at->gt($review->created_at))
                <div class="flex">
                    <div class="flex-shrink-0">
                        <div class="h-8 w-8 rounded-full bg-yellow-500 flex items-center justify-center">
                            <i class="fas fa-edit text-white"></i>
                        </div>
                    </div>
                    <div class="mr-4 flex-1">
                        <p class="text-sm text-gray-700 dark:text-gray-300">تم تحديث المراجعة</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">{{ $review->updated_at->format('d M Y - h:i A') }}</p>
                    </div>
                </div>
                @endif

                @if(isset($review) && isset($review->status))
                <div class="flex">
                    <div class="flex-shrink-0">
                        <div class="h-8 w-8 rounded-full {{ $review->status == 'approved' ? 'bg-green-500' : ($review->status == 'rejected' ? 'bg-red-500' : 'bg-yellow-500') }} flex items-center justify-center">
                            <i class="fas {{ $review->status == 'approved' ? 'fa-check' : ($review->status == 'rejected' ? 'fa-times' : 'fa-clock') }} text-white"></i>
                        </div>
                    </div>
                    <div class="mr-4 flex-1">
                        <p class="text-sm text-gray-700 dark:text-gray-300">
                            @if($review->status == 'approved')
                                تمت الموافقة على المراجعة
                            @elseif($review->status == 'rejected')
                                تم رفض المراجعة
                            @else
                                المراجعة قيد الانتظار
                            @endif
                        </p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">{{ $review->updated_at->format('d M Y - h:i A') }}</p>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
