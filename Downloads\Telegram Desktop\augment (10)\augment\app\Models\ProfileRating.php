<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProfileRating extends Model
{
    use HasFactory;

    protected $fillable = [
        'employer_id',
        'job_seeker_id',
        'rating',
        'comment',
        'status',
        'admin_notes',
    ];

    /**
     * Get the employer that owns the rating.
     */
    public function employer(): BelongsTo
    {
        return $this->belongsTo(Employer::class);
    }

    /**
     * Get the job seeker that is rated.
     */
    public function jobSeeker(): BelongsTo
    {
        return $this->belongsTo(JobSeeker::class);
    }
}