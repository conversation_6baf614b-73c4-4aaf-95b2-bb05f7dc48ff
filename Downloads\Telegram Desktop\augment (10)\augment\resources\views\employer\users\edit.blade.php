@extends('layouts.employer')

@section('title', 'تعديل المستخدم - Hire Me')
@section('header_title', 'تعديل المستخدم')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-bold">تعديل المستخدم</h2>
                <p class="text-gray-600 dark:text-gray-400 mt-1">تعديل بيانات المستخدم {{ $user->name }}</p>
            </div>
            <div class="flex gap-3">
                <a href="{{ route('employer.users.show', $user) }}" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors flex items-center gap-2">
                    <i class="fas fa-eye"></i>
                    <span>عرض</span>
                </a>
                <a href="{{ route('employer.users.index') }}" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors flex items-center gap-2">
                    <i class="fas fa-arrow-right"></i>
                    <span>العودة</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg">
            <div class="flex items-center">
                <i class="fas fa-check-circle ml-2"></i>
                {{ session('success') }}
            </div>
        </div>
    @endif

    @if(session('error'))
        <div class="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
            <div class="flex items-center">
                <i class="fas fa-exclamation-circle ml-2"></i>
                {{ session('error') }}
            </div>
        </div>
    @endif

    <!-- Edit Form -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm overflow-hidden">
        <div class="p-6">
            <form action="{{ route('employer.users.update', $user) }}" method="POST">
                @csrf
                @method('PUT')

                <!-- User Avatar and Basic Info -->
                <div class="flex items-start gap-6 mb-8">
                    <div class="w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-2xl font-bold">
                        {{ substr($user->name, 0, 1) }}
                    </div>
                    <div class="flex-1">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">تعديل بيانات المستخدم</h3>
                        <p class="text-gray-600 dark:text-gray-400">قم بتعديل المعلومات أدناه وانقر على حفظ</p>
                    </div>
                </div>

                <!-- Form Fields -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Personal Information -->
                    <div class="space-y-4">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                            المعلومات الشخصية
                        </h4>
                        
                        <!-- Name -->
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                الاسم <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="name" name="name" value="{{ old('name', $user->name) }}" 
                                   class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white @error('name') border-red-500 @enderror">
                            @error('name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Email -->
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                البريد الإلكتروني <span class="text-red-500">*</span>
                            </label>
                            <input type="email" id="email" name="email" value="{{ old('email', $user->email) }}" 
                                   class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white @error('email') border-red-500 @enderror">
                            @error('email')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Phone -->
                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                رقم الهاتف
                            </label>
                            <input type="text" id="phone" name="phone" value="{{ old('phone', $user->phone) }}" 
                                   class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white @error('phone') border-red-500 @enderror">
                            @error('phone')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Location -->
                        <div>
                            <label for="location" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                الموقع
                            </label>
                            <input type="text" id="location" name="location" value="{{ old('location', $user->location) }}" 
                                   class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white @error('location') border-red-500 @enderror">
                            @error('location')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Account Information -->
                    <div class="space-y-4">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                            معلومات الحساب
                        </h4>
                        
                        <!-- Role -->
                        <div>
                            <label for="role" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                الدور <span class="text-red-500">*</span>
                            </label>
                            <select id="role" name="role"
                                    class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white @error('role') border-red-500 @enderror">
                                <option value="admin" {{ old('role', $user->role) === 'admin' ? 'selected' : '' }}>مدير</option>
                                <option value="employer" {{ old('role', $user->role) === 'employer' ? 'selected' : '' }}>صاحب عمل</option>
                                <option value="job_seeker" {{ old('role', $user->role) === 'job_seeker' ? 'selected' : '' }}>باحث عن عمل</option>
                            </select>
                            @error('role')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Account Info Display -->
                        <div class="space-y-3 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">تاريخ الإنضمام:</span>
                                <span class="font-medium text-gray-900 dark:text-white">{{ $user->created_at->format('Y/m/d H:i') }}</span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">آخر تحديث:</span>
                                <span class="font-medium text-gray-900 dark:text-white">{{ $user->updated_at->format('Y/m/d H:i') }}</span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">حالة التفعيل:</span>
                                <span class="font-medium text-gray-900 dark:text-white">
                                    @if($user->email_verified_at)
                                        <span class="text-green-600">مفعل</span>
                                    @else
                                        <span class="text-yellow-600">غير مفعل</span>
                                    @endif
                                </span>
                            </div>
                        </div>

                        <!-- Note -->
                        <div class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                            <div class="flex items-start gap-2">
                                <i class="fas fa-info-circle text-blue-500 mt-0.5"></i>
                                <div class="text-sm text-blue-700 dark:text-blue-300">
                                    <p class="font-medium mb-1">ملاحظة:</p>
                                    <ul class="text-xs space-y-1">
                                        <li>• تغيير الدور قد يؤثر على صلاحيات المستخدم</li>
                                        <li>• لا يمكن تغيير كلمة المرور من هنا</li>
                                        <li>• يمكن تفعيل/إلغاء تفعيل الحساب من صفحة العرض</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <div class="flex gap-3">
                        <button type="submit" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2">
                            <i class="fas fa-save"></i>
                            حفظ التغييرات
                        </button>
                        
                        <a href="{{ route('employer.users.show', $user) }}" class="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors flex items-center gap-2">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                        
                        <a href="{{ route('employer.users.index') }}" class="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors flex items-center gap-2">
                            <i class="fas fa-list"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const email = document.getElementById('email').value.trim();
    
    if (!name) {
        e.preventDefault();
        alert('يرجى إدخال الاسم');
        document.getElementById('name').focus();
        return;
    }
    
    if (!email) {
        e.preventDefault();
        alert('يرجى إدخال البريد الإلكتروني');
        document.getElementById('email').focus();
        return;
    }
    
    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        e.preventDefault();
        alert('يرجى إدخال بريد إلكتروني صحيح');
        document.getElementById('email').focus();
        return;
    }
});

// Auto-save draft (optional)
let saveTimeout;
function autoSave() {
    clearTimeout(saveTimeout);
    saveTimeout = setTimeout(() => {
        // Could implement auto-save functionality here
        console.log('Auto-saving draft...');
    }, 2000);
}

// Add auto-save listeners
document.querySelectorAll('input, select, textarea').forEach(element => {
    element.addEventListener('input', autoSave);
});
</script>
@endsection
