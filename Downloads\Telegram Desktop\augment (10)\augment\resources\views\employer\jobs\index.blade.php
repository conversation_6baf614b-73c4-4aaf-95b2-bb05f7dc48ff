@extends('layouts.employer')

@section('title', 'إدارة الوظائف - Hire Me')
@section('header_title', 'إدارة الوظائف')

@section('content')
<div class="p-8 space-y-8">
    <!-- Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">إدارة الوظائف</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">إدارة وتتبع جميع الوظائف المنشورة</p>
        </div>

        <div class="flex gap-3 mt-4 md:mt-0">
            <a href="{{ route('employer.jobs.drafts') }}" class="px-6 py-3 bg-yellow-100 text-yellow-700 rounded-xl hover:bg-yellow-200 transition-all duration-300 flex items-center gap-2">
                <i class="fas fa-file-alt"></i>
                المسودات ({{ $stats['draft'] ?? 0 }})
            </a>
            <a href="{{ route('employer.jobs.create') }}" class="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                <i class="fas fa-plus"></i>
                نشر وظيفة جديدة
            </a>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="stat-card p-6 rounded-xl">
            <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-briefcase text-white"></i>
                </div>
                <div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">إجمالي الوظائف</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $stats['total'] }}</p>
                </div>
            </div>
        </div>

        <div class="stat-card p-6 rounded-xl">
            <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-check-circle text-white"></i>
                </div>
                <div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">الوظائف النشطة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $stats['active'] }}</p>
                </div>
            </div>
        </div>

        <div class="stat-card p-6 rounded-xl">
            <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-clock text-white"></i>
                </div>
                <div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">منتهية الصلاحية</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $stats['expired'] }}</p>
                </div>
            </div>
        </div>

        <div class="stat-card p-6 rounded-xl">
            <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-gradient-to-br from-gray-500 to-gray-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-edit text-white"></i>
                </div>
                <div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">مسودات</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $stats['draft'] }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="glass-card rounded-xl p-6">
        <form method="GET" action="{{ route('employer.jobs.index') }}" class="flex flex-col md:flex-row gap-4">
            <div class="flex-1">
                <input type="text" name="search" value="{{ request('search') }}" placeholder="البحث في الوظائف..." class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
            </div>

            <div>
                <select name="status" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    <option value="">جميع الحالات</option>
                    <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>نشطة</option>
                    <option value="expired" {{ request('status') == 'expired' ? 'selected' : '' }}>منتهية الصلاحية</option>
                    <option value="draft" {{ request('status') == 'draft' ? 'selected' : '' }}>مسودات</option>
                </select>
            </div>

            <div class="flex gap-2">
                <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-search mr-2"></i>
                    بحث
                </button>
                <a href="{{ route('employer.jobs.index') }}" class="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                    <i class="fas fa-times mr-2"></i>
                    إعادة تعيين
                </a>
            </div>
        </form>
    </div>

    <!-- Jobs List -->
    <div class="glass-card rounded-xl overflow-hidden">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">قائمة الوظائف</h2>
        </div>

        @if($jobs->count() > 0)
        <div class="divide-y divide-gray-200 dark:divide-gray-700">
            @foreach($jobs as $job)
            <div class="job-card p-6 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-all duration-300 fade-in">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <div class="flex items-center gap-3 mb-2">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                <a href="{{ route('employer.jobs.show', $job) }}" class="hover:text-blue-600 dark:hover:text-blue-400">
                                    {{ $job->title }}
                                </a>
                            </h3>

                            @if($job->is_featured)
                                <span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400 rounded-full">
                                    <i class="fas fa-star mr-1"></i>
                                    مميزة
                                </span>
                            @endif

                            @if($job->is_active && $job->expires_at > now())
                                <span class="status-badge status-active">
                                    <i class="fas fa-check-circle"></i>
                                    نشطة
                                </span>
                            @elseif($job->expires_at <= now())
                                <span class="status-badge status-expired">
                                    <i class="fas fa-clock"></i>
                                    منتهية الصلاحية
                                </span>
                            @else
                                <span class="status-badge status-inactive">
                                    <i class="fas fa-pause-circle"></i>
                                    مسودة
                                </span>
                            @endif
                        </div>

                        <div class="flex items-center gap-6 text-sm text-gray-600 dark:text-gray-400 mb-3">
                            <span class="flex items-center gap-1">
                                <i class="fas fa-map-marker-alt"></i>
                                {{ $job->location }}
                            </span>
                            <span class="flex items-center gap-1">
                                <i class="fas fa-briefcase"></i>
                                {{ getJobTypeInArabic($job->job_type) }}
                            </span>
                            @if($job->salary_min || $job->salary_max)
                            <span class="flex items-center gap-1">
                                <i class="fas fa-money-bill-wave"></i>
                                {{ $job->salary_range }}
                            </span>
                            @endif
                            <span class="flex items-center gap-1">
                                <i class="fas fa-users"></i>
                                {{ $job->applications->count() }} طلب
                            </span>
                            <span class="flex items-center gap-1">
                                <i class="fas fa-eye"></i>
                                {{ $job->views_count }} مشاهدة
                            </span>
                        </div>

                        <div class="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-500">
                            <span>تم النشر: {{ $job->created_at->diffForHumans() }}</span>
                            <span>ينتهي في: {{ $job->expires_at->diffForHumans() }}</span>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="flex items-center gap-2 mr-4">
                        <!-- عرض التفاصيل -->
                        <a href="{{ route('employer.jobs.show', $job) }}"
                           class="px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                           title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                            عرض
                        </a>

                        <!-- تعديل -->
                        <a href="{{ route('employer.jobs.edit', $job) }}"
                           class="px-3 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
                           title="تعديل">
                            <i class="fas fa-edit"></i>
                            تعديل
                        </a>

                        <!-- تفعيل/إلغاء التفعيل -->
                        <form action="{{ route('employer.jobs.toggle-status', $job) }}" method="POST" class="inline-block">
                            @csrf
                            @method('PATCH')
                            <button type="submit"
                                    class="px-3 py-2 {{ $job->is_active ? 'bg-orange-500 hover:bg-orange-600' : 'bg-emerald-500 hover:bg-emerald-600' }} text-white rounded transition-colors"
                                    title="{{ $job->is_active ? 'إلغاء التفعيل' : 'تفعيل' }}">
                                <i class="fas fa-{{ $job->is_active ? 'pause' : 'play' }}"></i>
                                {{ $job->is_active ? 'إيقاف' : 'تفعيل' }}
                            </button>
                        </form>

                        <!-- نسخ الوظيفة -->
                        <form action="{{ route('employer.jobs.duplicate', $job) }}" method="POST" class="inline-block">
                            @csrf
                            <button type="submit"
                                    class="px-3 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors"
                                    title="نسخ الوظيفة">
                                <i class="fas fa-copy"></i>
                                نسخ
                            </button>
                        </form>

                        <!-- حذف -->
                        <form action="{{ route('employer.jobs.destroy', $job) }}" method="POST" class="inline-block">
                            @csrf
                            @method('DELETE')
                            <button type="submit"
                                    class="px-3 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                                    onclick="return confirm('هل أنت متأكد من حذف الوظيفة؟')"
                                    title="حذف الوظيفة">
                                <i class="fas fa-trash-alt"></i>
                                حذف
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <!-- Pagination -->
        @if($jobs->hasPages())
        <div class="p-6 border-t border-gray-200 dark:border-gray-700">
            {{ $jobs->links() }}
        </div>
        @endif

        @else
        <div class="p-12 text-center">
            <div class="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-full flex items-center justify-center">
                <i class="fas fa-briefcase text-4xl text-gray-400"></i>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">لا توجد وظائف منشورة</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-6">ابدأ بنشر وظيفتك الأولى لجذب أفضل المواهب</p>
            <a href="{{ route('employer.jobs.create') }}" class="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 shadow-lg">
                <i class="fas fa-plus"></i>
                نشر وظيفة جديدة
            </a>
        </div>
        @endif
    </div>
</div>



@endsection

@push('styles')
<style>
/* تحسينات للنوافذ المنبثقة */
[x-cloak] {
    display: none !important;
}

.job-item {
    transition: all 0.3s ease-in-out;
}

.job-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* تحسين الانتقالات */
.stat-card {
    transition: all 0.3s ease-in-out;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

/* تحسين الأداء */
.dropdown-menu {
    will-change: transform, opacity;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

/* تحسين أزرار الإجراءات */
.action-btn {
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.action-btn:hover::before {
    width: 100px;
    height: 100px;
}

.action-btn:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* تحسين التولتيب */
.tooltip {
    pointer-events: none;
    z-index: 1000;
}

/* تحسين الأزرار بنفس تنسيق الأدمن */
.admin-style-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 12px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.admin-style-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.admin-style-btn i {
    font-size: 16px;
    transition: transform 0.3s ease;
}

.admin-style-btn:hover i {
    transform: scale(1.1);
}

/* تأثيرات الألوان للأزرار */
.btn-view {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
}

.btn-view:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
}

.btn-edit {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.btn-edit:hover {
    background: linear-gradient(135deg, #059669, #047857);
}

.btn-toggle {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.btn-toggle:hover {
    background: linear-gradient(135deg, #d97706, #b45309);
}

.btn-copy {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
}

.btn-copy:hover {
    background: linear-gradient(135deg, #7c3aed, #6d28d9);
}

.btn-delete {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.btn-delete:hover {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {

    // Add ripple effect to buttons
    document.querySelectorAll('.admin-style-btn').forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // Add success animation for form submissions
    document.querySelectorAll('form[method="POST"]').forEach(form => {
        form.addEventListener('submit', function(e) {
            const button = this.querySelector('button[type="submit"]');
            if (button) {
                button.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    button.style.transform = 'scale(1)';
                }, 150);
            }
        });
    });
});
</script>

<style>
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}
</style>
@endpush
