@extends('layouts.app')

@section('title', 'اختبار رفع الملفات')

@section('content')
<div class="py-16 bg-gray-50 dark:bg-gray-900">
    <div class="p-6">
        <div class="max-w-4xl mx-auto">
            <div class="bg-white dark:bg-dark-card rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700 p-8">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">اختبار رفع الملفات</h1>
                
                @if(session('success'))
                    <div class="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg">
                        {{ session('success') }}
                    </div>
                @endif
                
                @if($errors->any())
                    <div class="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
                        <ul class="list-disc list-inside">
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif
                
                <form action="{{ route('test-upload.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    
                    <!-- Avatar Upload -->
                    <div class="mb-8">
                        <h2 class="text-xl font-semibold mb-4">رفع الصورة الشخصية</h2>
                        <div class="flex items-center gap-6">
                            <div class="w-24 h-24 rounded-full overflow-hidden border-2 border-gray-300">
                                @if(Auth::user()->avatar)
                                    <img src="{{ asset('storage/' . Auth::user()->avatar) }}" alt="Avatar" class="w-full h-full object-cover">
                                @else
                                    <div class="w-full h-full bg-gray-100 flex items-center justify-center">
                                        <i class="fas fa-user text-2xl text-gray-400"></i>
                                    </div>
                                @endif
                            </div>
                            <div>
                                <input type="file" name="avatar" id="avatar" accept="image/*" class="mb-2">
                                <p class="text-sm text-gray-500">PNG, JPG, GIF حتى 2MB</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Resume Upload -->
                    <div class="mb-8">
                        <h2 class="text-xl font-semibold mb-4">رفع السيرة الذاتية</h2>
                        <div>
                            <input type="file" name="resume" id="resume" accept=".pdf,.doc,.docx" class="mb-2">
                            <p class="text-sm text-gray-500">PDF, DOC, DOCX حتى 2MB</p>
                            @if(Auth::user()->jobSeeker && Auth::user()->jobSeeker->resume)
                                <div class="mt-2">
                                    <a href="{{ Storage::url(Auth::user()->jobSeeker->resume) }}" target="_blank" class="text-blue-600 hover:text-blue-800">
                                        عرض السيرة الذاتية الحالية
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                    
                    <!-- Test Info -->
                    <div class="mb-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <h3 class="font-semibold text-blue-800 mb-2">معلومات الاختبار:</h3>
                        <ul class="text-sm text-blue-700 space-y-1">
                            <li>• مجلد الصور: storage/app/public/avatars</li>
                            <li>• مجلد السير الذاتية: storage/app/public/resumes</li>
                            <li>• الحد الأقصى للحجم: 2MB</li>
                            <li>• أنواع الصور المدعومة: JPG, PNG, GIF</li>
                            <li>• أنواع المستندات المدعومة: PDF, DOC, DOCX</li>
                        </ul>
                    </div>
                    
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium">
                        رفع الملفات
                    </button>
                </form>
                
                <!-- Current Files Info -->
                <div class="mt-12 pt-8 border-t border-gray-200">
                    <h2 class="text-xl font-semibold mb-4">الملفات الحالية</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Avatar Info -->
                        <div class="p-4 bg-gray-50 rounded-lg">
                            <h3 class="font-medium mb-2">الصورة الشخصية</h3>
                            @if(Auth::user()->avatar)
                                <p class="text-sm text-green-600">✓ موجودة: {{ Auth::user()->avatar }}</p>
                                <p class="text-xs text-gray-500 mt-1">
                                    الرابط: {{ asset('storage/' . Auth::user()->avatar) }}
                                </p>
                            @else
                                <p class="text-sm text-red-600">✗ غير موجودة</p>
                            @endif
                        </div>
                        
                        <!-- Resume Info -->
                        <div class="p-4 bg-gray-50 rounded-lg">
                            <h3 class="font-medium mb-2">السيرة الذاتية</h3>
                            @if(Auth::user()->jobSeeker && Auth::user()->jobSeeker->resume)
                                <p class="text-sm text-green-600">✓ موجودة: {{ Auth::user()->jobSeeker->resume }}</p>
                                <p class="text-xs text-gray-500 mt-1">
                                    الرابط: {{ Storage::url(Auth::user()->jobSeeker->resume) }}
                                </p>
                            @else
                                <p class="text-sm text-red-600">✗ غير موجودة</p>
                            @endif
                        </div>
                    </div>
                </div>
                
                <!-- Storage Links Check -->
                <div class="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <h3 class="font-semibold text-yellow-800 mb-2">فحص الروابط:</h3>
                    <div class="text-sm text-yellow-700 space-y-1">
                        <p>• تأكد من تشغيل: <code>php artisan storage:link</code></p>
                        <p>• مجلد storage/app/public يجب أن يكون مربوط بـ public/storage</p>
                        <p>• تحقق من صلاحيات الكتابة في مجلد storage</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
