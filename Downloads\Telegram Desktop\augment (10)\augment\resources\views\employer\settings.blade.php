@extends('layouts.employer')

@section('title', 'الإعدادات - Hire Me')
@section('header_title', 'الإعدادات')

@section('content')
<div class="p-8 space-y-8">
    <!-- Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">الإعدادات</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">إدارة إعدادات الحساب والتفضيلات</p>
        </div>
    </div>

    <!-- Settings Form -->
    <form action="{{ route('employer.settings.update') }}" method="POST" class="space-y-8">
        @csrf
        @method('PUT')
        
        <!-- Notification Settings -->
        <div class="glass-card rounded-xl p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
                <i class="fas fa-bell text-blue-600"></i>
                إعدادات الإشعارات
            </h2>

            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">إشعارات البريد الإلكتروني</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">تلقي الإشعارات عبر البريد الإلكتروني</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" name="email_notifications" value="1" 
                               {{ old('email_notifications', $employer->settings['email_notifications'] ?? true) ? 'checked' : '' }}
                               class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                </div>

                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">إشعارات الرسائل النصية</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">تلقي الإشعارات عبر الرسائل النصية</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" name="sms_notifications" value="1" 
                               {{ old('sms_notifications', $employer->settings['sms_notifications'] ?? false) ? 'checked' : '' }}
                               class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                </div>

                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">إشعارات طلبات التوظيف</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">تلقي إشعار عند وصول طلب توظيف جديد</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" name="application_notifications" value="1" 
                               {{ old('application_notifications', $employer->settings['application_notifications'] ?? true) ? 'checked' : '' }}
                               class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                </div>

                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">رسائل التسويق</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">تلقي رسائل تسويقية حول المنتجات والخدمات</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" name="marketing_emails" value="1" 
                               {{ old('marketing_emails', $employer->settings['marketing_emails'] ?? false) ? 'checked' : '' }}
                               class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                </div>
            </div>
        </div>

        <!-- Language & Region -->
        <div class="glass-card rounded-xl p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
                <i class="fas fa-globe text-green-600"></i>
                اللغة والمنطقة
            </h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="language" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        اللغة
                    </label>
                    <select id="language" name="language"
                            class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                        <option value="ar" {{ old('language', $employer->settings['language'] ?? 'ar') == 'ar' ? 'selected' : '' }}>العربية</option>
                        <option value="en" {{ old('language', $employer->settings['language'] ?? 'ar') == 'en' ? 'selected' : '' }}>English</option>
                    </select>
                </div>

                <div>
                    <label for="timezone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        المنطقة الزمنية
                    </label>
                    <select id="timezone" name="timezone"
                            class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                        <option value="Africa/Tripoli" {{ old('timezone', $employer->settings['timezone'] ?? 'Africa/Tripoli') == 'Africa/Tripoli' ? 'selected' : '' }}>طرابلس (GMT+2)</option>
                        <option value="Africa/Cairo" {{ old('timezone', $employer->settings['timezone'] ?? 'Africa/Tripoli') == 'Africa/Cairo' ? 'selected' : '' }}>القاهرة (GMT+2)</option>
                        <option value="Asia/Kuwait" {{ old('timezone', $employer->settings['timezone'] ?? 'Asia/Riyadh') == 'Asia/Kuwait' ? 'selected' : '' }}>الكويت (GMT+3)</option>
                        <option value="UTC" {{ old('timezone', $employer->settings['timezone'] ?? 'Asia/Riyadh') == 'UTC' ? 'selected' : '' }}>UTC (GMT+0)</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Privacy Settings -->
        <div class="glass-card rounded-xl p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
                <i class="fas fa-shield-alt text-purple-600"></i>
                إعدادات الخصوصية
            </h2>

            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">إظهار الملف الشخصي للشركة</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">السماح للباحثين عن عمل برؤية ملف شركتك</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" name="public_profile" value="1" 
                               {{ old('public_profile', $employer->settings['public_profile'] ?? true) ? 'checked' : '' }}
                               class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                </div>

                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">إظهار معلومات الاتصال</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">عرض رقم الهاتف والبريد الإلكتروني في الملف العام</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" name="show_contact_info" value="1" 
                               {{ old('show_contact_info', $employer->settings['show_contact_info'] ?? false) ? 'checked' : '' }}
                               class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="flex justify-end">
            <button type="submit" class="px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 flex items-center gap-2 shadow-lg">
                <i class="fas fa-save"></i>
                حفظ الإعدادات
            </button>
        </div>
    </form>

    <!-- Danger Zone -->
    <div class="glass-card rounded-xl p-6 border-red-200 dark:border-red-800">
        <h2 class="text-xl font-semibold text-red-600 dark:text-red-400 mb-6 flex items-center gap-2">
            <i class="fas fa-exclamation-triangle"></i>
            منطقة الخطر
        </h2>

        <div class="space-y-4">
            <div class="flex items-center justify-between p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                <div>
                    <h3 class="text-sm font-medium text-red-900 dark:text-red-100">حذف الحساب</h3>
                    <p class="text-sm text-red-700 dark:text-red-300">حذف الحساب نهائياً مع جميع البيانات</p>
                </div>
                <button type="button" onclick="confirmDelete()" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                    حذف الحساب
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete() {
    if (confirm('هل أنت متأكد من حذف الحساب؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        if (confirm('تأكيد أخير: سيتم حذف جميع الوظائف والطلبات والبيانات نهائياً.')) {
            // هنا يمكن إضافة منطق حذف الحساب
            alert('تم إرسال طلب حذف الحساب. سيتم التواصل معك خلال 24 ساعة.');
        }
    }
}
</script>
@endsection
