/* تحسينات خاصة بصفحة طلبات التوظيف */

/* تأثيرات الحركة للبطاقات */
@keyframes slideInUp {
    0% {
        transform: translateY(30px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInFromRight {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutToRight {
    0% {
        transform: translateX(0);
        opacity: 1;
    }
    100% {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes fadeInScale {
    0% {
        transform: scale(0.95);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* تحسينات الجدول */
.applications-table {
    animation: slideInUp 0.6s ease-out;
}

.applications-table tbody tr {
    animation: fadeInScale 0.4s ease-out;
    animation-fill-mode: both;
}

.applications-table tbody tr:nth-child(1) { animation-delay: 0.1s; }
.applications-table tbody tr:nth-child(2) { animation-delay: 0.2s; }
.applications-table tbody tr:nth-child(3) { animation-delay: 0.3s; }
.applications-table tbody tr:nth-child(4) { animation-delay: 0.4s; }
.applications-table tbody tr:nth-child(5) { animation-delay: 0.5s; }

/* تحسينات الأزرار */
.action-button {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.action-button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.action-button:hover::before {
    width: 100px;
    height: 100px;
}

.action-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* تحسينات الحالات */
.status-badge {
    position: relative;
    overflow: hidden;
    animation: pulse 2s infinite;
}

.status-badge.pending {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    box-shadow: 0 4px 15px rgba(251, 191, 36, 0.3);
}

.status-badge.accepted {
    background: linear-gradient(135deg, #10b981, #059669);
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.status-badge.rejected {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.status-badge.reviewed {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

/* تحسينات الصور الشخصية */
.avatar-container {
    position: relative;
    transition: all 0.3s ease;
}

.avatar-container:hover {
    transform: scale(1.1);
}

.avatar-container::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4);
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.avatar-container:hover::after {
    opacity: 1;
}

/* تحسينات الفلاتر */
.filter-panel {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    animation: slideInUp 0.4s ease-out;
}

.dark .filter-panel {
    background: rgba(30, 41, 59, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* تحسينات الإحصائيات */
.stat-card {
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.stat-card:hover::before {
    left: 100%;
}

.stat-card:hover {
    transform: translateY(-8px) scale(1.02);
}

/* تحسينات الإشعارات */
.notification-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    min-width: 300px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 12px;
    padding: 16px 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #3b82f6;
    animation: slideInFromRight 0.4s ease-out;
    transition: all 0.3s ease;
}

.notification-toast.success {
    border-left-color: #10b981;
}

.notification-toast.error {
    border-left-color: #ef4444;
}

.notification-toast.warning {
    border-left-color: #f59e0b;
}

/* تحسينات الشاشات الصغيرة */
@media (max-width: 768px) {
    .applications-table {
        font-size: 14px;
        overflow-x: auto;
    }

    .applications-table table {
        min-width: 800px;
    }

    .stat-card {
        margin-bottom: 16px;
    }

    .filter-panel {
        margin: 16px;
        border-radius: 12px;
    }

    .action-button {
        padding: 6px;
        font-size: 12px;
        width: 32px;
        height: 32px;
    }

    .btn-modern {
        padding: 8px 16px;
        font-size: 14px;
    }

    .notification-toast {
        right: 10px;
        left: 10px;
        min-width: auto;
    }

    .floating-shapes {
        display: none;
    }
}

@media (max-width: 640px) {
    .applications-table th,
    .applications-table td {
        padding: 8px 4px;
        font-size: 12px;
    }

    .stat-card .text-3xl {
        font-size: 1.5rem;
    }

    .icon-modern {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }
}

/* تحسينات الوضع المظلم */
.dark .applications-table {
    background: rgba(30, 41, 59, 0.95);
}

.dark .stat-card {
    background: rgba(30, 41, 59, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .notification-toast {
    background: rgba(30, 41, 59, 0.95);
    color: white;
}

/* تأثيرات التحميل */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

.dark .loading-skeleton {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200px 100%;
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

/* تحسينات الانتقالات */
.smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تحسينات التركيز */
.focus-ring:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    border-color: #3b82f6;
}

/* تحسينات التمرير */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

.dark .custom-scrollbar::-webkit-scrollbar-track {
    background: #1e293b;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #475569;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #64748b;
}

/* تحسينات Tooltips */
[title] {
    position: relative;
}

[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    animation: fadeInScale 0.2s ease-out;
}

[title]:hover::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(100%);
    border: 5px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
    z-index: 1000;
}

/* تحسينات الحالة النشطة */
.btn-modern:active {
    transform: translateY(1px) scale(0.98);
}

.action-button:active {
    transform: translateY(1px) scale(0.95);
}

/* تحسينات التركيز للوصولية */
.btn-modern:focus-visible,
.action-button:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* تحسينات الحركة للجدول */
.applications-table tbody tr {
    transition: all 0.2s ease;
}

.applications-table tbody tr:hover {
    transform: translateX(-2px);
    box-shadow: 4px 0 12px rgba(0, 0, 0, 0.1);
}

.dark .applications-table tbody tr:hover {
    box-shadow: 4px 0 12px rgba(0, 0, 0, 0.3);
}

/* تحسينات الرسوم المتحركة للتحميل */
.loading-overlay {
    backdrop-filter: blur(8px);
    background: rgba(0, 0, 0, 0.3);
}

.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* تحسينات الطباعة */
@media print {
    .btn-modern,
    .action-button,
    .floating-shapes,
    .notification-toast,
    .filter-panel {
        display: none !important;
    }

    .applications-table {
        box-shadow: none;
        border: 1px solid #000;
        background: white !important;
    }

    .stat-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
        background: white !important;
    }

    body {
        background: white !important;
    }

    .status-badge {
        background: white !important;
        color: black !important;
        border: 1px solid #ccc !important;
    }
}

/* تحسينات إضافية للحركة */
.fade-in {
    animation: fadeInScale 0.6s ease-out;
}

.slide-up {
    animation: slideInUp 0.6s ease-out;
}

.bounce-in {
    animation: bounceIn 0.8s ease-out;
}

@keyframes bounceIn {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* تحسينات الوصولية */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* تحسينات التباين العالي */
@media (prefers-contrast: high) {
    .enhanced-card {
        border: 2px solid #000;
    }

    .btn-modern {
        border: 2px solid #000;
    }

    .status-badge {
        border: 1px solid #000;
    }
}

/* تحسينات تقليل الحركة */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
