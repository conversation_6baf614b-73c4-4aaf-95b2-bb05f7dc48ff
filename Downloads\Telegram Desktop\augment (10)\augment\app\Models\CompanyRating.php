<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CompanyRating extends Model
{
    use HasFactory;

    protected $fillable = [
        'job_seeker_id',
        'employer_id',
        'rating',
        'comment',
        'status',
        'admin_notes',
    ];

    /**
     * Get the job seeker that owns the rating.
     */
    public function jobSeeker(): BelongsTo
    {
        return $this->belongsTo(JobSeeker::class);
    }

    /**
     * Get the employer that is rated.
     */
    public function employer(): BelongsTo
    {
        return $this->belongsTo(Employer::class);
    }
}