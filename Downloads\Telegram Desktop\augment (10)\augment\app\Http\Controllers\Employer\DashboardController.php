<?php

namespace App\Http\Controllers\Employer;

use App\Http\Controllers\Controller;
use App\Models\Job;
use App\Models\Application;
use App\Models\User;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class DashboardController extends Controller
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->middleware('auth');
        $this->middleware('role:employer');
        $this->notificationService = $notificationService;
    }

    /**
     * عرض لوحة تحكم صاحب العمل
     */
    public function index()
    {
        $user = Auth::user();
        $employer = $user->employer;

        // إنشاء ملف صاحب عمل إذا لم يكن موجوداً
        if (!$employer) {
            if ($user->role === 'employer') {
                $employer = \App\Models\Employer::create([
                    'user_id' => $user->id,
                    'company_name' => $user->company_name ?? 'شركة ' . $user->name,
                    'company_description' => $user->company_description ?? 'وصف الشركة',
                    'industry' => $user->industry ?? 'تقنية المعلومات',
                    'location' => $user->location ?? 'طرابلس',
                    'company_size' => $user->company_size ?? '1-10',
                ]);
            } else {
                return redirect()->route('home')->with('error', 'ليس لديك صلاحية للوصول إلى هذه الصفحة');
            }
        }

        // الإحصائيات الأساسية - محسوبة بدقة
        $totalJobs = Job::where('employer_id', $employer->id)->where('is_active', true)->count();
        $activeJobs = Job::where('employer_id', $employer->id)
                        ->where('is_active', true)
                        ->where('expires_at', '>', now())
                        ->count();
        $expiredJobs = Job::where('employer_id', $employer->id)
                         ->where('is_active', true)
                         ->where('expires_at', '<=', now())
                         ->count();
        $draftJobs = Job::where('employer_id', $employer->id)->where('is_active', false)->count();

        $totalApplications = Application::whereHas('job', function($query) use ($employer) {
            $query->where('employer_id', $employer->id);
        })->count();

        $newApplications = Application::whereHas('job', function($query) use ($employer) {
            $query->where('employer_id', $employer->id);
        })->where('status', 'pending')
          ->where('created_at', '>=', Carbon::today())
          ->count();

        $totalViews = Job::where('employer_id', $employer->id)->sum('views') ?: 0;

        // الوظائف الحديثة
        $recentJobs = Job::where('employer_id', $employer->id)
                        ->latest()
                        ->take(5)
                        ->get();

        // الطلبات الحديثة
        $recentApplications = Application::whereHas('job', function($query) use ($employer) {
            $query->where('employer_id', $employer->id);
        })->with(['jobSeeker.user', 'job'])
          ->latest()
          ->take(5)
          ->get();

        return view('employer.dashboard', compact(
            'totalJobs',
            'totalApplications',
            'newApplications',
            'recentJobs',
            'recentApplications'
        ));
    }

    /**
     * عرض الإحصائيات المفصلة
     */
    public function statistics()
    {
        $user = Auth::user();
        $employer = $user->employer;

        // إنشاء ملف صاحب عمل إذا لم يكن موجوداً
        if (!$employer) {
            if ($user->role === 'employer') {
                $employer = \App\Models\Employer::create([
                    'user_id' => $user->id,
                    'company_name' => $user->company_name ?? 'شركة ' . $user->name,
                    'company_description' => $user->company_description ?? 'وصف الشركة',
                    'industry' => $user->industry ?? 'تقنية المعلومات',
                    'location' => $user->location ?? 'طرابلس',
                    'company_size' => $user->company_size ?? '1-10',
                ]);
            } else {
                return redirect()->route('home')->with('error', 'ليس لديك صلاحية للوصول إلى هذه الصفحة');
            }
        }

        // الإحصائيات السريعة (منقولة من الصفحة الرئيسية)
        $totalJobs = Job::where('employer_id', $employer->id)->where('is_active', true)->count();
        $activeJobs = Job::where('employer_id', $employer->id)
                        ->where('is_active', true)
                        ->where('expires_at', '>', now())
                        ->count();
        $expiredJobs = Job::where('employer_id', $employer->id)
                         ->where('is_active', true)
                         ->where('expires_at', '<=', now())
                         ->count();
        $draftJobs = Job::where('employer_id', $employer->id)->where('is_active', false)->count();

        $totalApplications = Application::whereHas('job', function($query) use ($employer) {
            $query->where('employer_id', $employer->id);
        })->count();

        $newApplications = Application::whereHas('job', function($query) use ($employer) {
            $query->where('employer_id', $employer->id);
        })->where('status', 'pending')
          ->where('created_at', '>=', Carbon::today())
          ->count();

        $totalViews = Job::where('employer_id', $employer->id)->sum('views') ?: 0;

        // إحصائيات الوظائف
        $jobStats = [
            'total' => Job::where('employer_id', $employer->id)->count(),
            'active' => Job::where('employer_id', $employer->id)->where('is_active', true)->count(),
            'expired' => Job::where('employer_id', $employer->id)->where('expires_at', '<', now())->count(),
            'draft' => Job::where('employer_id', $employer->id)->where('is_active', false)->count(),
        ];

        // إحصائيات الطلبات
        $applicationStats = [
            'total' => Application::whereHas('job', function($query) use ($employer) {
                $query->where('employer_id', $employer->id);
            })->count(),
            'pending' => Application::whereHas('job', function($query) use ($employer) {
                $query->where('employer_id', $employer->id);
            })->where('status', 'pending')->count(),
            'reviewed' => Application::whereHas('job', function($query) use ($employer) {
                $query->where('employer_id', $employer->id);
            })->where('status', 'reviewed')->count(),
            'accepted' => Application::whereHas('job', function($query) use ($employer) {
                $query->where('employer_id', $employer->id);
            })->where('status', 'accepted')->count(),
            'rejected' => Application::whereHas('job', function($query) use ($employer) {
                $query->where('employer_id', $employer->id);
            })->where('status', 'rejected')->count(),
        ];

        // إحصائيات شهرية للطلبات
        $monthlyApplications = [];
        for ($i = 11; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i);
            $count = Application::whereHas('job', function($query) use ($employer) {
                $query->where('employer_id', $employer->id);
            })->whereYear('created_at', $month->year)
              ->whereMonth('created_at', $month->month)
              ->count();

            $monthlyApplications[] = [
                'month' => $month->format('M Y'),
                'count' => $count
            ];
        }

        // أفضل الوظائف من حيث عدد الطلبات
        $topJobs = Job::where('employer_id', $employer->id)
                     ->withCount('applications')
                     ->orderBy('applications_count', 'desc')
                     ->take(10)
                     ->get();

        return view('employer.statistics', compact(
            'jobStats',
            'applicationStats',
            'monthlyApplications',
            'topJobs',
            'totalJobs',
            'activeJobs',
            'expiredJobs',
            'draftJobs',
            'totalApplications',
            'newApplications',
            'totalViews'
        ));
    }

    /**
     * عرض لوحة التقارير
     */
    public function reports()
    {
        $user = Auth::user();
        $employer = $user->employer;

        // إنشاء ملف صاحب عمل إذا لم يكن موجوداً
        if (!$employer) {
            if ($user->role === 'employer') {
                $employer = \App\Models\Employer::create([
                    'user_id' => $user->id,
                    'company_name' => $user->company_name ?? 'شركة ' . $user->name,
                    'company_description' => $user->company_description ?? 'وصف الشركة',
                    'industry' => $user->industry ?? 'تقنية المعلومات',
                    'location' => $user->location ?? 'طرابلس',
                    'company_size' => $user->company_size ?? '1-10',
                ]);
            } else {
                return redirect()->route('home')->with('error', 'ليس لديك صلاحية للوصول إلى هذه الصفحة');
            }
        }

        // الإحصائيات الأساسية
        $totalJobs = Job::where('employer_id', $employer->id)->where('is_active', true)->count();
        $activeJobs = Job::where('employer_id', $employer->id)
                        ->where('is_active', true)
                        ->where('expires_at', '>', now())
                        ->count();
        $totalApplications = Application::whereHas('job', function($query) use ($employer) {
            $query->where('employer_id', $employer->id);
        })->count();
        $totalViews = Job::where('employer_id', $employer->id)->sum('views') ?: 0;

        return view('employer.reports', compact(
            'totalJobs',
            'activeJobs',
            'totalApplications',
            'totalViews'
        ));
    }

    /**
     * عرض الملف الشخصي
     */
    public function profile()
    {
        $user = Auth::user();
        $employer = $user->employer;

        // إنشاء ملف صاحب عمل إذا لم يكن موجوداً
        if (!$employer) {
            if ($user->role === 'employer') {
                $employer = \App\Models\Employer::create([
                    'user_id' => $user->id,
                    'company_name' => $user->company_name ?? 'شركة ' . $user->name,
                    'company_description' => $user->company_description ?? 'وصف الشركة',
                    'industry' => $user->industry ?? 'تقنية المعلومات',
                    'location' => $user->location ?? 'طرابلس',
                    'company_size' => $user->company_size ?? '1-10',
                ]);
            } else {
                return redirect()->route('home')->with('error', 'ليس لديك صلاحية للوصول إلى هذه الصفحة');
            }
        }

        return view('employer.profile', compact('user', 'employer'));
    }

    public function updateProfile(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . Auth::id(),
            'company_name' => 'nullable|string|max:255',
            'company_description' => 'nullable|string|max:1000',
            'company_website' => 'nullable|url|max:255',
            'company_size' => 'nullable|string|max:50',
            'industry' => 'nullable|string|max:100',
            'location' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
        ]);

        $user = Auth::user();
        $employer = $user->employer;

        // تحديث بيانات المستخدم
        $user->update($request->only([
            'name', 'email', 'phone', 'location'
        ]));

        // تحديث بيانات الشركة
        if ($employer) {
            $employer->update($request->only([
                'company_name', 'company_description', 'company_website',
                'company_size', 'industry', 'location'
            ]));
        }

        return redirect()->back()->with('success', 'تم تحديث الملف الشخصي بنجاح');
    }

    /**
     * عرض الإعدادات
     */
    public function settings()
    {
        $employer = Auth::user();
        return view('employer.settings', compact('employer'));
    }

    /**
     * تحديث الإعدادات
     */
    public function updateSettings(Request $request)
    {
        $request->validate([
            'email_notifications' => 'boolean',
            'sms_notifications' => 'boolean',
            'application_notifications' => 'boolean',
            'marketing_emails' => 'boolean',
            'language' => 'string|in:ar,en',
            'timezone' => 'string|max:50',
        ]);

        $employer = Auth::user();

        // تحديث إعدادات المستخدم
        $settings = $employer->settings ?? [];
        $settings = array_merge($settings, $request->only([
            'email_notifications',
            'sms_notifications',
            'application_notifications',
            'marketing_emails',
            'language',
            'timezone'
        ]));

        $employer->update(['settings' => $settings]);

        return redirect()->back()->with('success', 'تم تحديث الإعدادات بنجاح');
    }

    /**
     * عرض صفحة الباقات والدفع
     */
    public function packages()
    {
        $employer = Auth::user()->employer;

        // إنشاء اشتراك افتراضي إذا لم يكن موجوداً
        if (!$employer) {
            return redirect()->route('employer.profile')->with('error', 'يجب إكمال ملف الشركة أولاً');
        }

        $currentSubscription = $employer->currentSubscription();
        if (!$currentSubscription) {
            $currentSubscription = \App\Models\Subscription::createDefault($employer);
        }

        // الباقات المتاحة
        $packages = [
            [
                'id' => 'basic',
                'name' => 'الباقة المجانية',
                'price' => 0,
                'duration' => 30,
                'job_limit' => 3,
                'features' => [
                    'نشر 3 وظائف شهرياً',
                    'عرض الطلبات الأساسي',
                    'دعم فني محدود'
                ],
                'is_current' => $currentSubscription->package_type === 'basic'
            ],
            [
                'id' => 'premium',
                'name' => 'الباقة المميزة',
                'price' => 299,
                'duration' => 30,
                'job_limit' => 15,
                'features' => [
                    'نشر 15 وظيفة شهرياً',
                    'إبراز الوظائف في النتائج',
                    'تحليلات مفصلة',
                    'دعم فني أولوية',
                    'تصدير البيانات'
                ],
                'is_current' => $currentSubscription->package_type === 'premium'
            ],
            [
                'id' => 'enterprise',
                'name' => 'باقة الشركات',
                'price' => 599,
                'duration' => 30,
                'job_limit' => -1, // غير محدود
                'features' => [
                    'وظائف غير محدودة',
                    'صفحة شركة مخصصة',
                    'مدير حساب مخصص',
                    'تكامل API',
                    'تقارير مخصصة',
                    'دعم فني 24/7'
                ],
                'is_current' => $currentSubscription->package_type === 'enterprise'
            ]
        ];

        // حساب الوظائف المتبقية والمنشورة بدقة
        $jobsLeft = $currentSubscription->getRemainingJobs();

        // حساب الوظائف المنشورة فعلياً هذا الشهر من قاعدة البيانات
        $actualJobsPostedThisMonth = \App\Models\Job::where('employer_id', $employer->id)
            ->where('is_active', true)
            ->whereYear('posted_at', now()->year)
            ->whereMonth('posted_at', now()->month)
            ->count();

        // تحديث العداد في الاشتراك إذا كان مختلفاً
        if ($actualJobsPostedThisMonth !== $currentSubscription->jobs_posted_this_month) {
            $currentSubscription->update(['jobs_posted_this_month' => $actualJobsPostedThisMonth]);
            $currentSubscription->refresh();
        }

        $jobsPostedThisMonth = $actualJobsPostedThisMonth;

        // حساب الأيام المتبقية بدقة
        $daysLeft = max(0, $currentSubscription->ends_at->diffInDays(now(), false));
        if ($currentSubscription->ends_at < now()) {
            $daysLeft = 0;
        }

        return view('employer.packages', compact('packages', 'currentSubscription', 'jobsLeft', 'jobsPostedThisMonth', 'daysLeft'));
    }

    /**
     * عرض صفحة تصدير البيانات
     */
    public function exports()
    {
        $user = Auth::user();
        $employer = $user->employer;

        if (!$employer) {
            return redirect()->route('employer.profile')->with('error', 'يجب إكمال ملف الشركة أولاً');
        }

        // إحصائيات سريعة للتصدير
        $exportStats = [
            'total_applications' => Application::whereHas('job', function($query) use ($employer) {
                $query->where('employer_id', $employer->id);
            })->count(),
            'total_jobs' => Job::where('employer_id', $employer->id)->count(),
            'pending_applications' => Application::whereHas('job', function($query) use ($employer) {
                $query->where('employer_id', $employer->id);
            })->where('status', 'pending')->count(),
        ];

        return view('employer.exports', compact('exportStats'));
    }

    /**
     * إنشاء بيانات تجريبية للاختبار
     */
    public function createTestData()
    {
        $user = Auth::user();
        $employer = $user->employer;

        if (!$employer) {
            return redirect()->route('employer.profile')->with('error', 'يجب إكمال ملف الشركة أولاً');
        }

        // إنشاء وظائف تجريبية
        $jobTitles = [
            'مطور ويب متقدم',
            'مصمم جرافيك إبداعي',
            'مسوق رقمي محترف',
            'محاسب مالي',
            'مدير مبيعات'
        ];

        $categories = \App\Models\Category::all();
        if ($categories->isEmpty()) {
            \App\Models\Category::create(['name' => 'تقنية المعلومات']);
            $categories = \App\Models\Category::all();
        }

        foreach ($jobTitles as $index => $title) {
            \App\Models\Job::create([
                'title' => $title,
                'description' => 'وصف تفصيلي للوظيفة: ' . $title . '. نبحث عن مرشح مؤهل للانضمام إلى فريقنا.',
                'requirements' => 'خبرة لا تقل عن 3 سنوات، إجادة اللغة الإنجليزية، مهارات تواصل ممتازة.',
                'category_id' => $categories->first()->id,
                'employer_id' => $employer->id,
                'location' => 'طرابلس، ليبيا',
                'job_type' => ['full_time', 'part_time', 'remote'][array_rand(['full_time', 'part_time', 'remote'])],
                'expires_at' => now()->addDays(30),
                'is_active' => $index < 4, // 4 وظائف نشطة، 1 مسودة
                'views' => rand(25, 150),
                'posted_at' => now()->subDays(rand(1, 15)),
            ]);
        }

        return redirect()->route('employer.profile')->with('success', 'تم إنشاء البيانات التجريبية بنجاح! تم إضافة 5 وظائف (4 نشطة + 1 مسودة)');
    }

    /**
     * إصلاح تاريخ التسجيل
     */
    public function fixRegistrationDate()
    {
        $user = Auth::user();
        $employer = $user->employer;

        if (!$employer) {
            return redirect()->route('employer.profile')->with('error', 'يجب إكمال ملف الشركة أولاً');
        }

        // استخدام تاريخ إنشاء المستخدم كتاريخ التسجيل الصحيح
        $userCreatedAt = $user->created_at;

        if ($userCreatedAt) {
            // تحديث تاريخ إنشاء صاحب العمل ليكون نفس تاريخ المستخدم
            $employer->update([
                'created_at' => $userCreatedAt,
                'updated_at' => now()
            ]);

            $daysSince = (int) $userCreatedAt->diffInDays(now());
            return redirect()->route('employer.profile')
                           ->with('success', "تم إصلاح تاريخ التسجيل بنجاح! الآن: {$daysSince} يوم منذ التسجيل");
        }

        // إذا لم يكن هناك تاريخ للمستخدم، استخدم تاريخ اليوم
        $today = now();
        $employer->update([
            'created_at' => $today,
            'updated_at' => $today
        ]);

        return redirect()->route('employer.profile')
                       ->with('success', "تم تعيين تاريخ التسجيل إلى اليوم: 0 يوم منذ التسجيل");
    }

    /**
     * إعادة تعيين تاريخ التسجيل إلى اليوم
     */
    public function resetRegistrationDate()
    {
        $user = Auth::user();
        $employer = $user->employer;

        if (!$employer) {
            return redirect()->route('employer.profile')->with('error', 'يجب إكمال ملف الشركة أولاً');
        }

        // إعادة تعيين التاريخ إلى اليوم
        $today = now();
        $employer->update([
            'created_at' => $today,
            'updated_at' => $today
        ]);

        return redirect()->route('employer.profile')
                       ->with('success', "تم إعادة تعيين تاريخ التسجيل إلى اليوم بنجاح! الآن: 0 يوم منذ التسجيل");
    }

    /**
     * تصدير تقرير الإحصائيات
     */
    public function exportStatistics(Request $request)
    {
        $user = Auth::user();
        $employer = $user->employer;

        if (!$employer) {
            return redirect()->route('employer.profile')->with('error', 'يجب إكمال ملف الشركة أولاً');
        }

        $format = $request->input('format', 'csv');

        // جمع جميع الإحصائيات
        $jobStats = [
            'total' => Job::where('employer_id', $employer->id)->count(),
            'active' => Job::where('employer_id', $employer->id)->where('is_active', true)->count(),
            'expired' => Job::where('employer_id', $employer->id)->where('expires_at', '<', now())->count(),
            'draft' => Job::where('employer_id', $employer->id)->where('is_active', false)->count(),
        ];

        $applicationStats = [
            'total' => Application::whereHas('job', function($query) use ($employer) {
                $query->where('employer_id', $employer->id);
            })->count(),
            'pending' => Application::whereHas('job', function($query) use ($employer) {
                $query->where('employer_id', $employer->id);
            })->where('status', 'pending')->count(),
            'accepted' => Application::whereHas('job', function($query) use ($employer) {
                $query->where('employer_id', $employer->id);
            })->where('status', 'accepted')->count(),
            'rejected' => Application::whereHas('job', function($query) use ($employer) {
                $query->where('employer_id', $employer->id);
            })->where('status', 'rejected')->count(),
        ];

        // أفضل الوظائف
        $topJobs = Job::where('employer_id', $employer->id)
                     ->withCount('applications')
                     ->orderBy('applications_count', 'desc')
                     ->take(10)
                     ->get();

        // حساب معدل القبول
        $acceptanceRate = $applicationStats['total'] > 0
            ? round(($applicationStats['accepted'] / $applicationStats['total']) * 100, 1)
            : 0;

        // إحصائيات شهرية للطلبات (آخر 6 أشهر)
        $monthlyApplications = [];
        for ($i = 5; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i);
            $count = Application::whereHas('job', function($query) use ($employer) {
                $query->where('employer_id', $employer->id);
            })->whereYear('created_at', $month->year)
              ->whereMonth('created_at', $month->month)
              ->count();

            $monthlyApplications[] = [
                'month' => $month->format('Y-m'),
                'month_name' => $month->format('F Y'),
                'count' => $count
            ];
        }

        if ($format === 'pdf') {
            return $this->exportStatisticsPDF($jobStats, $applicationStats, $topJobs, $acceptanceRate, $monthlyApplications, $employer);
        }

        return $this->exportStatisticsCSV($jobStats, $applicationStats, $topJobs, $monthlyApplications);
    }

    /**
     * تصدير الإحصائيات بصيغة CSV
     */
    private function exportStatisticsCSV($jobStats, $applicationStats, $topJobs, $monthlyApplications)
    {
        $filename = 'statistics_report_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($jobStats, $applicationStats, $topJobs, $monthlyApplications) {
            $file = fopen('php://output', 'w');

            // إضافة BOM للدعم العربي
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            // تقرير الإحصائيات العامة
            fputcsv($file, ['تقرير الإحصائيات العامة']);
            fputcsv($file, ['تاريخ التقرير', date('Y-m-d H:i:s')]);
            fputcsv($file, []);

            // إحصائيات الوظائف
            fputcsv($file, ['إحصائيات الوظائف']);
            fputcsv($file, ['إجمالي الوظائف', $jobStats['total']]);
            fputcsv($file, ['الوظائف النشطة', $jobStats['active']]);
            fputcsv($file, ['الوظائف المنتهية', $jobStats['expired']]);
            fputcsv($file, ['المسودات', $jobStats['draft']]);
            fputcsv($file, []);

            // إحصائيات الطلبات
            fputcsv($file, ['إحصائيات الطلبات']);
            fputcsv($file, ['إجمالي الطلبات', $applicationStats['total']]);
            fputcsv($file, ['الطلبات قيد المراجعة', $applicationStats['pending']]);
            fputcsv($file, ['الطلبات المقبولة', $applicationStats['accepted']]);
            fputcsv($file, ['الطلبات المرفوضة', $applicationStats['rejected']]);
            fputcsv($file, []);

            // الإحصائيات الشهرية
            fputcsv($file, ['الإحصائيات الشهرية للطلبات']);
            fputcsv($file, ['الشهر', 'عدد الطلبات']);
            foreach ($monthlyApplications as $monthData) {
                fputcsv($file, [
                    $monthData['month_name'],
                    $monthData['count']
                ]);
            }
            fputcsv($file, []);

            // أفضل الوظائف
            fputcsv($file, ['أفضل الوظائف من حيث عدد الطلبات']);
            fputcsv($file, ['عنوان الوظيفة', 'عدد الطلبات', 'عدد المشاهدات', 'الحالة']);
            foreach ($topJobs as $job) {
                fputcsv($file, [
                    $job->title,
                    $job->applications_count,
                    $job->views ?? 0,
                    $job->is_active ? 'نشطة' : 'غير نشطة'
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * تصدير الإحصائيات بصيغة PDF
     */
    private function exportStatisticsPDF($jobStats, $applicationStats, $topJobs, $acceptanceRate, $monthlyApplications, $employer)
    {
        $data = [
            'employer' => $employer,
            'jobStats' => $jobStats,
            'applicationStats' => $applicationStats,
            'topJobs' => $topJobs,
            'acceptanceRate' => $acceptanceRate,
            'monthlyApplications' => $monthlyApplications,
            'reportDate' => now()->format('Y-m-d H:i:s')
        ];

        // إنشاء HTML للتقرير
        $html = view('employer.reports.statistics-pdf', $data)->render();

        // إنشاء PDF باستخدام DomPDF (يحتاج تثبيت المكتبة)
        // للآن سنعيد HTML كاستجابة
        return response($html, 200, [
            'Content-Type' => 'text/html; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="statistics_report_' . date('Y-m-d_H-i-s') . '.html"'
        ]);
    }
}
