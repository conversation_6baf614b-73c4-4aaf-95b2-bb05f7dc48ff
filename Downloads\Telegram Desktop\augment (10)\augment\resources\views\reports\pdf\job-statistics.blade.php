<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير إحصائيات الوظائف</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #3b82f6;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #1f2937;
            margin: 0;
            font-size: 28px;
        }
        .header p {
            color: #6b7280;
            margin: 10px 0 0 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: #f3f4f6;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-card h3 {
            color: #374151;
            margin: 0 0 10px 0;
            font-size: 16px;
        }
        .stat-card .number {
            color: #3b82f6;
            font-size: 32px;
            font-weight: bold;
            margin: 0;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #1f2937;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .table th,
        .table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #e5e7eb;
        }
        .table th {
            background-color: #f9fafb;
            font-weight: bold;
            color: #374151;
        }
        .table tr:hover {
            background-color: #f9fafb;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
            font-size: 14px;
        }
        @media print {
            body {
                background-color: white;
            }
            .container {
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>تقرير إحصائيات الوظائف</h1>
            <p>{{ $employer->company_name ?? 'الشركة' }}</p>
            <p>تاريخ التقرير: {{ date('Y-m-d H:i:s') }}</p>
        </div>

        <!-- Statistics Grid -->
        <div class="stats-grid">
            <div class="stat-card">
                <h3>إجمالي الوظائف</h3>
                <p class="number">{{ number_format($data['totalJobs']) }}</p>
            </div>
            <div class="stat-card">
                <h3>الوظائف النشطة</h3>
                <p class="number">{{ number_format($data['activeJobs']) }}</p>
            </div>
            <div class="stat-card">
                <h3>إجمالي الطلبات</h3>
                <p class="number">{{ number_format($data['totalApplications']) }}</p>
            </div>
            <div class="stat-card">
                <h3>مشاهدات الوظائف</h3>
                <p class="number">{{ number_format($data['jobViews']) }}</p>
            </div>
        </div>

        <!-- Applications by Status -->
        <div class="section">
            <h2>الطلبات حسب الحالة</h2>
            <table class="table">
                <thead>
                    <tr>
                        <th>الحالة</th>
                        <th>العدد</th>
                        <th>النسبة المئوية</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($data['applicationsByStatus'] as $status)
                    <tr>
                        <td>{{ \App\Models\Application::getStatusOptions()[$status->status] ?? $status->status }}</td>
                        <td>{{ number_format($status->count) }}</td>
                        <td>{{ $data['totalApplications'] > 0 ? round(($status->count / $data['totalApplications']) * 100, 1) : 0 }}%</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Recent Jobs -->
        <div class="section">
            <h2>الوظائف الحديثة</h2>
            <table class="table">
                <thead>
                    <tr>
                        <th>عنوان الوظيفة</th>
                        <th>عدد الطلبات</th>
                        <th>تاريخ النشر</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($data['recentJobs'] as $job)
                    <tr>
                        <td>{{ $job->title }}</td>
                        <td>{{ number_format($job->applications_count ?? 0) }}</td>
                        <td>{{ $job->created_at->format('Y-m-d') }}</td>
                        <td>{{ $job->is_active ? 'نشطة' : 'غير نشطة' }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>تم إنشاء هذا التقرير بواسطة نظام Hire Me</p>
            <p>{{ date('Y-m-d H:i:s') }}</p>
        </div>
    </div>
</body>
</html>
