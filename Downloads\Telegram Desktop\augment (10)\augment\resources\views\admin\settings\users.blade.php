@extends('layouts.admin')

@section('title', 'إدارة المستخدمين - Hire Me')
@section('header_title', 'إدارة المستخدمين')

@section('content')
<div class="p-6">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
            <h2 class="text-2xl font-bold">إدارة المستخدمين</h2>
            <p class="text-gray-600 dark:text-gray-400 mt-1">إدارة حسابات المستخدمين وصلاحياتهم</p>
        </div>
        <div class="mt-4 md:mt-0 flex gap-3">
            <button class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors flex items-center gap-2" id="exportUsersBtn">
                <i class="fas fa-download"></i>
                <span>تصدير</span>
            </button>
            <button class="px-4 py-2 gradient-bg text-white rounded-lg hover:opacity-90 transition-colors flex items-center gap-2" id="addUserBtn">
                <i class="fas fa-plus"></i>
                <span>إضافة مستخدم</span>
            </button>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6 mb-6">
        <div class="flex flex-col md:flex-row gap-4">
            <div class="flex-1">
                <div class="relative">
                    <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" name="search" id="search" class="block w-full pr-10 p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" placeholder="البحث عن اسم أو بريد إلكتروني...">
                </div>
            </div>
            <div class="w-full md:w-48">
                <select id="roleFilter" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                    <option value="">الدور</option>
                    <option value="admin">مدير النظام</option>
                    <option value="manager">مدير</option>
                    <option value="recruiter">مسؤول توظيف</option>
                    <option value="interviewer">مقابل</option>
                    <option value="viewer">مشاهد</option>
                </select>
            </div>
            <div class="w-full md:w-48">
                <select id="statusFilter" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                    <option value="">الحالة</option>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                    <option value="pending">قيد التفعيل</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm overflow-hidden">
        <div class="overflow-x-auto">
            <table class="w-full text-right text-sm">
                <thead class="bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-300">
                    <tr>
                        <th class="px-6 py-4">المستخدم</th>
                        <th class="px-6 py-4">البريد الإلكتروني</th>
                        <th class="px-6 py-4">الدور</th>
                        <th class="px-6 py-4">تاريخ الإنضمام</th>
                        <th class="px-6 py-4">آخر تسجيل دخول</th>
                        <th class="px-6 py-4">الحالة</th>
                        <th class="px-6 py-4">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse($users ?? [] as $user)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">
                                <div class="flex items-center gap-3">
                                    <div class="w-10 h-10 bg-{{ $user->color ?? 'blue' }}-100 dark:bg-{{ $user->color ?? 'blue' }}-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                                        @if($user->avatar)
                                            <img src="{{ $user->avatar }}" alt="{{ $user->name }}" class="w-full h-full rounded-full object-cover">
                                        @else
                                            <i class="fas fa-user text-{{ $user->color ?? 'blue' }}-600 dark:text-{{ $user->color ?? 'blue' }}-400"></i>
                                        @endif
                                    </div>
                                    <div>
                                        <p class="font-medium">{{ $user->name }}</p>
                                        <p class="text-gray-500 dark:text-gray-400 text-xs">{{ $user->position }}</p>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">{{ $user->email }}</td>
                            <td class="px-6 py-4">
                                @if($user->role == 'admin')
                                    <span class="px-2.5 py-0.5 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 rounded-full text-xs">مدير النظام</span>
                                @elseif($user->role == 'manager')
                                    <span class="px-2.5 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 rounded-full text-xs">مدير</span>
                                @elseif($user->role == 'recruiter')
                                    <span class="px-2.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">مسؤول توظيف</span>
                                @elseif($user->role == 'interviewer')
                                    <span class="px-2.5 py-0.5 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400 rounded-full text-xs">مقابل</span>
                                @else
                                    <span class="px-2.5 py-0.5 bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400 rounded-full text-xs">مشاهد</span>
                                @endif
                            </td>
                            <td class="px-6 py-4">{{ $user->created_at }}</td>
                            <td class="px-6 py-4">{{ $user->last_login }}</td>
                            <td class="px-6 py-4">
                                @if($user->status == 'active')
                                    <span class="px-2.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">نشط</span>
                                @elseif($user->status == 'inactive')
                                    <span class="px-2.5 py-0.5 bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400 rounded-full text-xs">غير نشط</span>
                                @else
                                    <span class="px-2.5 py-0.5 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400 rounded-full text-xs">قيد التفعيل</span>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                                    <button type="button" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 edit-user" data-user-id="{{ $user->id }}">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 delete-user" data-user-id="{{ $user->id }}">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <!-- Sample Users -->
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">
                                <div class="flex items-center gap-3">
                                    <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-user text-purple-600 dark:text-purple-400"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium">عبدالله محمد</p>
                                        <p class="text-gray-500 dark:text-gray-400 text-xs">المدير التنفيذي</p>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4"><EMAIL></td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 rounded-full text-xs">مدير النظام</span>
                            </td>
                            <td class="px-6 py-4">15 يناير، 2023</td>
                            <td class="px-6 py-4">منذ ساعة</td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">نشط</span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                                    <button type="button" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 edit-user" data-user-id="1">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 delete-user" data-user-id="1">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">
                                <div class="flex items-center gap-3">
                                    <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-user text-blue-600 dark:text-blue-400"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium">سارة عبدالله</p>
                                        <p class="text-gray-500 dark:text-gray-400 text-xs">مديرة الموارد البشرية</p>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4"><EMAIL></td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 rounded-full text-xs">مدير</span>
                            </td>
                            <td class="px-6 py-4">20 يناير، 2023</td>
                            <td class="px-6 py-4">منذ يومين</td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">نشط</span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                                    <button type="button" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 edit-user" data-user-id="2">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 delete-user" data-user-id="2">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">
                                <div class="flex items-center gap-3">
                                    <div class="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-user text-green-600 dark:text-green-400"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium">محمد أحمد</p>
                                        <p class="text-gray-500 dark:text-gray-400 text-xs">مسؤول توظيف</p>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4"><EMAIL></td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">مسؤول توظيف</span>
                            </td>
                            <td class="px-6 py-4">5 فبراير، 2023</td>
                            <td class="px-6 py-4">منذ 3 أيام</td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">نشط</span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                                    <button type="button" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 edit-user" data-user-id="3">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 delete-user" data-user-id="3">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">
                                <div class="flex items-center gap-3">
                                    <div class="w-10 h-10 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-user text-yellow-600 dark:text-yellow-400"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium">فاطمة علي</p>
                                        <p class="text-gray-500 dark:text-gray-400 text-xs">مقابل تقني</p>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4"><EMAIL></td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400 rounded-full text-xs">مقابل</span>
                            </td>
                            <td class="px-6 py-4">10 فبراير، 2023</td>
                            <td class="px-6 py-4">منذ أسبوع</td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">نشط</span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                                    <button type="button" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 edit-user" data-user-id="4">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 delete-user" data-user-id="4">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">
                                <div class="flex items-center gap-3">
                                    <div class="w-10 h-10 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-user text-gray-600 dark:text-gray-400"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium">خالد عمر</p>
                                        <p class="text-gray-500 dark:text-gray-400 text-xs">محلل بيانات</p>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4"><EMAIL></td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5 bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400 rounded-full text-xs">مشاهد</span>
                            </td>
                            <td class="px-6 py-4">15 فبراير، 2023</td>
                            <td class="px-6 py-4">منذ شهر</td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5 bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400 rounded-full text-xs">غير نشط</span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                                    <button type="button" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 edit-user" data-user-id="5">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 delete-user" data-user-id="5">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <div class="px-6 py-4 flex items-center justify-between border-t border-gray-200 dark:border-gray-700">
            <div class="text-sm text-gray-700 dark:text-gray-300">
                عرض <span class="font-medium">1</span> إلى <span class="font-medium">5</span> من <span class="font-medium">{{ $totalUsers ?? '12' }}</span> النتائج
            </div>
            <div class="flex items-center space-x-2 rtl:space-x-reverse">
                <a href="#" class="px-3 py-1 text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">السابق</a>
                <a href="#" class="px-3 py-1 text-white bg-primary border border-primary rounded-md">1</a>
                <a href="#" class="px-3 py-1 text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">2</a>
                <a href="#" class="px-3 py-1 text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">3</a>
                <a href="#" class="px-3 py-1 text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">التالي</a>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit User Modal -->
<div id="userModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
        </div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white dark:bg-dark-card rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white dark:bg-dark-card px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold" id="modalTitle">إضافة مستخدم جديد</h3>
                    <button type="button" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300" id="closeModal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form id="userForm" class="space-y-4">
                    <input type="hidden" id="userId" name="userId" value="">
                    
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الاسم الكامل <span class="text-red-600">*</span></label>
                        <input type="text" name="name" id="name" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" required>
                    </div>
                    
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">البريد الإلكتروني <span class="text-red-600">*</span></label>
                        <input type="email" name="email" id="email" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" required>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="role" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الدور <span class="text-red-600">*</span></label>
                            <select name="role" id="role" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" required>
                                <option value="">اختر الدور</option>
                                <option value="admin">مدير النظام</option>
                                <option value="manager">مدير</option>
                                <option value="recruiter">مسؤول توظيف</option>
                                <option value="interviewer">مقابل</option>
                                <option value="viewer">مشاهد</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="position" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">المنصب</label>
                            <input type="text" name="position" id="position" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                        </div>
                    </div>
                    
                    <div id="passwordFields">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">كلمة المرور <span class="text-red-600">*</span></label>
                                <input type="password" name="password" id="password" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" required>
                            </div>
                            
                            <div>
                                <label for="password_confirmation" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">تأكيد كلمة المرور <span class="text-red-600">*</span></label>
                                <input type="password" name="password_confirmation" id="password_confirmation" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" required>
                            </div>
                        </div>
                        
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">يجب أن تحتوي كلمة المرور على 8 أحرف على الأقل، وتشمل حرفًا كبيرًا وحرفًا صغيرًا ورقمًا ورمزًا خاصًا.</p>
                    </div>
                    
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الحالة</label>
                        <select name="status" id="status" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                            <option value="pending">قيد التفعيل</option>
                        </select>
                    </div>
                    
                    <div class="flex items-center pt-2">
                        <input id="send_welcome_email" name="send_welcome_email" type="checkbox" class="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary">
                        <label for="send_welcome_email" class="mr-2 text-sm font-medium text-gray-700 dark:text-gray-300">إرسال بريد ترحيبي</label>
                    </div>
                </form>
            </div>
            <div class="bg-gray-50 dark:bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="saveUser" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 gradient-bg text-base font-medium text-white hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm">
                    حفظ
                </button>
                <button type="button" id="cancelModal" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    إلغاء
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
        </div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white dark:bg-dark-card rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white dark:bg-dark-card px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/30 sm:mx-0 sm:h-10 sm:w-10">
                        <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400"></i>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:mr-4 sm:text-right">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                            حذف المستخدم
                        </h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                هل أنت متأكد من رغبتك في حذف هذا المستخدم؟ لا يمكن التراجع عن هذا الإجراء.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="confirmDelete" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                    حذف
                </button>
                <button type="button" id="cancelDelete" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    إلغاء
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Export Modal -->
<div id="exportModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
    <div class="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-md w-full mx-4">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">تصدير بيانات المستخدمين</h3>
            <button onclick="hideExportModal()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    اختر صيغة التصدير
                </label>
                <div class="space-y-2">
                    <label class="flex items-center">
                        <input type="radio" name="export_format" value="csv" checked class="text-blue-600 focus:ring-blue-500">
                        <span class="mr-2 text-gray-700 dark:text-gray-300">CSV (Excel) - جميع البيانات</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="export_format" value="pdf" class="text-blue-600 focus:ring-blue-500">
                        <span class="mr-2 text-gray-700 dark:text-gray-300">PDF - تقرير مفصل</span>
                    </label>
                </div>
            </div>

            <div class="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                <div class="flex items-start gap-2">
                    <i class="fas fa-info-circle text-blue-500 mt-0.5"></i>
                    <div class="text-sm text-blue-700 dark:text-blue-300">
                        <p class="font-medium mb-1">سيتم تصدير:</p>
                        <ul class="text-xs space-y-1">
                            <li>• أسماء المستخدمين وبياناتهم</li>
                            <li>• الأدوار والصلاحيات</li>
                            <li>• حالات التفعيل</li>
                            <li>• تواريخ الإنضمام</li>
                            <li>• إحصائيات شاملة</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="flex gap-3 pt-4">
                <button onclick="exportUsers()" class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-download mr-2"></i>
                    تصدير
                </button>
                <button onclick="hideExportModal()" class="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    إلغاء
                </button>
            </div>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
    // Add User Modal
    const addUserBtn = document.getElementById('addUserBtn');
    const userModal = document.getElementById('userModal');
    const closeModal = document.getElementById('closeModal');
    const cancelModal = document.getElementById('cancelModal');
    const modalTitle = document.getElementById('modalTitle');
    const userForm = document.getElementById('userForm');
    const passwordFields = document.getElementById('passwordFields');
    const saveUser = document.getElementById('saveUser');
    const userId = document.getElementById('userId');
    
    // Show add user modal
    addUserBtn.addEventListener('click', () => {
        modalTitle.textContent = 'إضافة مستخدم جديد';
        userForm.reset();
        userId.value = '';
        passwordFields.classList.remove('hidden');
        document.getElementById('password').required = true;
        document.getElementById('password_confirmation').required = true;
        userModal.classList.remove('hidden');
    });
    
    // Edit User
    const editButtons = document.querySelectorAll('.edit-user');
    
    editButtons.forEach(button => {
        button.addEventListener('click', () => {
            const id = button.getAttribute('data-user-id');
            modalTitle.textContent = 'تعديل المستخدم';
            userId.value = id;
            
            // In a real app, you would fetch user data by ID and populate the form
            // For demonstration, we'll use dummy data
            
            // Simulating user data retrieval
            let user;
            if (id === '1') {
                user = {
                    name: 'عبدالله محمد',
                    email: '<EMAIL>',
                    role: 'admin',
                    position: 'المدير التنفيذي',
                    status: 'active'
                };
            } else if (id === '2') {
                user = {
                    name: 'سارة عبدالله',
                    email: '<EMAIL>',
                    role: 'manager',
                    position: 'مديرة الموارد البشرية',
                    status: 'active'
                };
            } else if (id === '3') {
                user = {
                    name: 'محمد أحمد',
                    email: '<EMAIL>',
                    role: 'recruiter',
                    position: 'مسؤول توظيف',
                    status: 'active'
                };
            } else if (id === '4') {
                user = {
                    name: 'فاطمة علي',
                    email: '<EMAIL>',
                    role: 'interviewer',
                    position: 'مقابل تقني',
                    status: 'active'
                };
            } else {
                user = {
                    name: 'خالد عمر',
                    email: '<EMAIL>',
                    role: 'viewer',
                    position: 'محلل بيانات',
                    status: 'inactive'
                };
            }
            
            // Populate form with user data
            document.getElementById('name').value = user.name;
            document.getElementById('email').value = user.email;
            document.getElementById('role').value = user.role;
            document.getElementById('position').value = user.position;
            document.getElementById('status').value = user.status;
            
            // Hide password fields for editing
            passwordFields.classList.add('hidden');
            document.getElementById('password').required = false;
            document.getElementById('password_confirmation').required = false;
            
            // Show modal
            userModal.classList.remove('hidden');
        });
    });
    
    // Hide modal
    closeModal.addEventListener('click', () => {
        userModal.classList.add('hidden');
    });
    
    cancelModal.addEventListener('click', () => {
        userModal.classList.add('hidden');
    });
    
    // Save user
    saveUser.addEventListener('click', () => {
        // In a real app, you would submit the form data via AJAX
        // For demonstration, we'll just hide the modal
        
        // Validate form
        if (userForm.checkValidity()) {
            // Here you would submit the form
            console.log('Saving user...');
            
            // Hide modal
            userModal.classList.add('hidden');
            
            // Show success notification (in a real app)
            alert('تم حفظ المستخدم بنجاح');
        } else {
            // Trigger HTML5 validation
            userForm.reportValidity();
        }
    });
    
    // Delete User Confirmation
    const deleteButtons = document.querySelectorAll('.delete-user');
    const deleteModal = document.getElementById('deleteModal');
    const confirmDelete = document.getElementById('confirmDelete');
    const cancelDelete = document.getElementById('cancelDelete');
    let userIdToDelete = null;
    
    // Show delete modal
    deleteButtons.forEach(button => {
        button.addEventListener('click', () => {
            userIdToDelete = button.getAttribute('data-user-id');
            deleteModal.classList.remove('hidden');
        });
    });
    
    // Hide delete modal
    cancelDelete.addEventListener('click', () => {
        deleteModal.classList.add('hidden');
        userIdToDelete = null;
    });
    
    // Confirm delete
    confirmDelete.addEventListener('click', () => {
        if (userIdToDelete) {
            // In a real app, you would send a DELETE request to the server
            console.log(`Deleting user with ID: ${userIdToDelete}`);
            
            // Hide modal
            deleteModal.classList.add('hidden');
            userIdToDelete = null;
            
            // Show success notification (in a real app)
            alert('تم حذف المستخدم بنجاح');
        }
    });
    
    // Close modals when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target === userModal || e.target === deleteModal) {
            userModal.classList.add('hidden');
            deleteModal.classList.add('hidden');
            userIdToDelete = null;
        }
    });
    
    // Filter users
    const searchInput = document.getElementById('search');
    const roleFilter = document.getElementById('roleFilter');
    const statusFilter = document.getElementById('statusFilter');
    
    // Add event listeners for filters (in a real app, these would trigger AJAX requests or form submissions)
    [searchInput, roleFilter, statusFilter].forEach(element => {
        element.addEventListener('change', () => {
            console.log('Filtering users with:', {
                search: searchInput.value,
                role: roleFilter.value,
                status: statusFilter.value
            });
        });
    });
    
    // Search as you type
    searchInput.addEventListener('input', () => {
        console.log('Searching for:', searchInput.value);
    });
    
    // Export users
    const exportUsersBtn = document.getElementById('exportUsersBtn');

    exportUsersBtn.addEventListener('click', () => {
        showExportModal();
    });

    // Show export modal
    function showExportModal() {
        document.getElementById('exportModal').classList.remove('hidden');
    }

    // Hide export modal
    function hideExportModal() {
        document.getElementById('exportModal').classList.add('hidden');
    }

    // Export users
    function exportUsers() {
        const selectedFormat = document.querySelector('input[name="export_format"]:checked').value;
        const button = event.target.closest('button');
        const originalText = button.innerHTML;

        // إظهار رسالة تحميل
        button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>جاري التصدير...';
        button.disabled = true;

        // بناء URL مع المعاملات
        let exportUrl = '{{ route("admin.users.export") }}';
        const urlParams = new URLSearchParams();

        // إضافة نوع التصدير
        if (selectedFormat === 'pdf') {
            urlParams.append('format', 'pdf');
        }

        // إضافة الفلاتر الحالية
        const searchInput = document.querySelector('input[name="search"]');
        if (searchInput && searchInput.value) {
            urlParams.append('search', searchInput.value);
        }

        const roleFilter = document.querySelector('select[name="role"]');
        if (roleFilter && roleFilter.value) {
            urlParams.append('role', roleFilter.value);
        }

        const statusFilter = document.querySelector('select[name="status"]');
        if (statusFilter && statusFilter.value) {
            urlParams.append('status', statusFilter.value);
        }

        if (urlParams.toString()) {
            exportUrl += '?' + urlParams.toString();
        }

        // تحميل الملف
        window.location.href = exportUrl;

        // إعادة تعيين الزر وإخفاء modal
        setTimeout(() => {
            button.innerHTML = originalText;
            button.disabled = false;
            hideExportModal();
            showSuccessMessage('تم تصدير الملف بنجاح!');
        }, 2000);
    }

    // وظيفة إظهار رسالة النجاح
    function showSuccessMessage(message) {
        const successDiv = document.createElement('div');
        successDiv.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 flex items-center gap-2';
        successDiv.innerHTML = `
            <i class="fas fa-check-circle"></i>
            <span>${message}</span>
        `;

        document.body.appendChild(successDiv);

        setTimeout(() => {
            if (document.body.contains(successDiv)) {
                document.body.removeChild(successDiv);
            }
        }, 3000);
    }

    // إغلاق modal عند النقر خارجه
    document.getElementById('exportModal').addEventListener('click', function(e) {
        if (e.target === this) {
            hideExportModal();
        }
    });
</script>

<style>
/* تحسينات modal التصدير */
#exportModal {
    backdrop-filter: blur(4px);
}

#exportModal .bg-white {
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسينات أزرار الراديو */
input[type="radio"] {
    width: 18px;
    height: 18px;
}
</style>

@endsection