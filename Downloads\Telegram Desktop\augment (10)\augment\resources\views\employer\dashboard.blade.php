@extends('layouts.employer')

@section('title', 'لوحة تحكم صاحب العمل - Hire Me')
@section('header_title', 'لوحة التحكم')

@section('content')
<div class="p-8 space-y-8">
    <!-- Welcome Section -->
    <div class="glass-card rounded-2xl p-8 animate-fade-in">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-3xl font-bold text-gradient mb-3">مرحباً بك، {{ Auth::user()->name }}! 🏢</h2>
                <p class="text-gray-600 dark:text-gray-400 text-lg">إدارة شاملة لوظائفك وطلبات التوظيف</p>
                <div class="flex items-center gap-6 mt-4">
                    <div class="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span>النظام متاح ويعمل بكفاءة</span>
                    </div>
                    <div class="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                        <i class="fas fa-clock text-blue-500"></i>
                        <span>آخر تحديث: {{ now()->format('H:i') }}</span>
                    </div>
                </div>
            </div>
            <div class="hidden md:block">
                <div class="w-20 h-20 gradient-primary rounded-2xl flex items-center justify-center shadow-2xl glow-effect animate-float">
                    <i class="fas fa-building text-white text-3xl"></i>
                </div>
            </div>
        </div>
    </div>


    <!-- ملخص سريع -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="glass-card p-6 rounded-xl text-center">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mx-auto mb-3">
                <i class="fas fa-briefcase text-white"></i>
            </div>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ number_format($totalJobs ?? 0) }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-400">وظيفة نشطة</p>
        </div>

        <div class="glass-card p-6 rounded-xl text-center">
            <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mx-auto mb-3">
                <i class="fas fa-users text-white"></i>
            </div>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ number_format($totalApplications ?? 0) }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-400">طلب توظيف</p>
        </div>

        <div class="glass-card p-6 rounded-xl text-center">
            <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mx-auto mb-3">
                <i class="fas fa-bell text-white"></i>
            </div>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ number_format($newApplications ?? 0) }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-400">طلب جديد اليوم</p>
        </div>

        <div class="glass-card p-6 rounded-xl text-center border-2 border-dashed border-blue-300 dark:border-blue-600 hover:border-blue-500 transition-colors">
            <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mx-auto mb-3">
                <i class="fas fa-chart-line text-white"></i>
            </div>
            <a href="{{ route('employer.reports') }}" class="text-lg font-bold text-blue-600 hover:text-blue-800 dark:text-blue-400 block mb-2">
                عرض لوحة التقارير
            </a>
            <p class="text-sm text-gray-600 dark:text-gray-400">تقارير شاملة ومفصلة</p>
            <div class="mt-2">
                <i class="fas fa-arrow-right text-blue-500 text-sm"></i>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="glass-card rounded-xl p-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">إجراءات سريعة</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <a href="{{ route('employer.jobs.create') }}" class="action-card p-4 rounded-lg text-center hover:scale-105 transition-transform">
                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-plus text-white"></i>
                </div>
                <h3 class="font-medium text-gray-900 dark:text-white">نشر وظيفة جديدة</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">أضف وظيفة جديدة لشركتك</p>
            </a>

            <a href="{{ route('employer.jobs.drafts') }}" class="action-card p-4 rounded-lg text-center hover:scale-105 transition-transform">
                <div class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-file-alt text-white"></i>
                </div>
                <h3 class="font-medium text-gray-900 dark:text-white">المسودات</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">إدارة مسودات الوظائف</p>
            </a>

            <a href="{{ route('employer.applications.index') }}" class="action-card p-4 rounded-lg text-center hover:scale-105 transition-transform">
                <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-users text-white"></i>
                </div>
                <h3 class="font-medium text-gray-900 dark:text-white">الطلبات</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">مراجعة طلبات التوظيف</p>
            </a>

            <a href="{{ route('employer.reports') }}" class="action-card p-4 rounded-lg text-center hover:scale-105 transition-transform">
                <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-chart-bar text-white"></i>
                </div>
                <h3 class="font-medium text-gray-900 dark:text-white">لوحة التقارير</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">تقارير مفصلة</p>
            </a>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Jobs -->
        <div class="glass-card rounded-xl overflow-hidden">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between">
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">الوظائف الحديثة</h2>
                    <a href="{{ route('employer.jobs.index') }}" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm">
                        عرض الكل
                    </a>
                </div>
            </div>
            <div class="divide-y divide-gray-200 dark:divide-gray-700">
                @forelse($recentJobs ?? [] as $job)
                <div class="p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="font-medium text-gray-900 dark:text-white">{{ $job->title }}</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">{{ $job->created_at->diffForHumans() }}</p>
                        </div>
                        <span class="px-2 py-1 text-xs font-medium rounded-full {{ $job->is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                            {{ $job->is_active ? 'نشطة' : 'غير نشطة' }}
                        </span>
                    </div>
                </div>
                @empty
                <div class="p-8 text-center">
                    <i class="fas fa-briefcase text-gray-400 text-3xl mb-3"></i>
                    <p class="text-gray-600 dark:text-gray-400">لا توجد وظائف منشورة بعد</p>
                    <a href="{{ route('employer.jobs.create') }}" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm">
                        انشر وظيفتك الأولى
                    </a>
                </div>
                @endforelse
            </div>
        </div>

        <!-- Recent Applications -->
        <div class="glass-card rounded-xl overflow-hidden">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between">
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">الطلبات الحديثة</h2>
                    <a href="{{ route('employer.applications.index') }}" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm">
                        عرض الكل
                    </a>
                </div>
            </div>
            <div class="divide-y divide-gray-200 dark:divide-gray-700">
                @forelse($recentApplications ?? [] as $application)
                <div class="p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="font-medium text-gray-900 dark:text-white">
                                {{ $application->jobSeeker && $application->jobSeeker->user ? $application->jobSeeker->user->name : 'مستخدم غير معروف' }}
                            </h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">{{ $application->job->title }}</p>
                            <p class="text-xs text-gray-500 dark:text-gray-500">{{ $application->created_at->diffForHumans() }}</p>
                        </div>
                        <span class="px-2 py-1 text-xs font-medium rounded-full {{ getApplicationStatusColor($application->status) }}">
                            {{ getApplicationStatusInArabic($application->status) }}
                        </span>
                    </div>
                </div>
                @empty
                <div class="p-8 text-center">
                    <i class="fas fa-users text-gray-400 text-3xl mb-3"></i>
                    <p class="text-gray-600 dark:text-gray-400">لا توجد طلبات جديدة</p>
                </div>
                @endforelse
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glow-effect {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.animate-fade-in {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
    transform: translateY(20px);
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}
</style>
@endpush


