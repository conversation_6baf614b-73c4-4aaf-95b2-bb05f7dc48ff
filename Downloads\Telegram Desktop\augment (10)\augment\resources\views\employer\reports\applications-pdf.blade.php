<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير طلبات التوظيف - {{ $employer->name }}</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #3b82f6;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #1f2937;
            margin: 0;
            font-size: 28px;
        }
        .header p {
            color: #6b7280;
            margin: 10px 0 0 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #3b82f6;
        }
        .stat-card h3 {
            color: #1f2937;
            margin: 0 0 5px 0;
            font-size: 24px;
        }
        .stat-card p {
            color: #6b7280;
            margin: 0;
            font-size: 14px;
        }
        .applications-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .applications-table th,
        .applications-table td {
            padding: 10px;
            text-align: right;
            border-bottom: 1px solid #e5e7eb;
            font-size: 12px;
        }
        .applications-table th {
            background-color: #f3f4f6;
            color: #374151;
            font-weight: bold;
        }
        .applications-table tr:nth-child(even) {
            background-color: #f9fafb;
        }
        .status-pending {
            color: #d97706;
            font-weight: bold;
        }
        .status-accepted {
            color: #059669;
            font-weight: bold;
        }
        .status-rejected {
            color: #dc2626;
            font-weight: bold;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            text-align: center;
            color: #6b7280;
            font-size: 14px;
        }
        .summary-box {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .summary-box h3 {
            margin: 0 0 10px 0;
            font-size: 18px;
        }
        .summary-box p {
            margin: 5px 0;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>تقرير طلبات التوظيف</h1>
            <p>{{ $employer->name }}</p>
            <p>تاريخ التقرير: {{ $reportDate }}</p>
        </div>

        <!-- Summary Box -->
        <div class="summary-box">
            <h3>ملخص التقرير</h3>
            <p>إجمالي الطلبات: {{ $totalApplications }}</p>
            <p>تم إنشاء هذا التقرير في {{ $reportDate }}</p>
        </div>

        <!-- Statistics Grid -->
        <div class="stats-grid">
            <div class="stat-card">
                <h3>{{ $totalApplications }}</h3>
                <p>إجمالي الطلبات</p>
            </div>
            <div class="stat-card">
                <h3>{{ $applications->where('status', 'pending')->count() }}</h3>
                <p>قيد المراجعة</p>
            </div>
            <div class="stat-card">
                <h3>{{ $applications->where('status', 'accepted')->count() }}</h3>
                <p>مقبولة</p>
            </div>
            <div class="stat-card">
                <h3>{{ $applications->where('status', 'rejected')->count() }}</h3>
                <p>مرفوضة</p>
            </div>
        </div>

        <!-- Applications Table -->
        <div>
            <h3 style="color: #1f2937; margin-bottom: 15px;">تفاصيل طلبات التوظيف</h3>
            <table class="applications-table">
                <thead>
                    <tr>
                        <th>المتقدم</th>
                        <th>البريد الإلكتروني</th>
                        <th>الوظيفة</th>
                        <th>الموقع</th>
                        <th>الحالة</th>
                        <th>تاريخ التقديم</th>
                        <th>التقييم</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($applications as $application)
                    <tr>
                        <td>{{ $application->jobSeeker && $application->jobSeeker->user ? $application->jobSeeker->user->name : 'غير محدد' }}</td>
                        <td>{{ $application->jobSeeker && $application->jobSeeker->user ? $application->jobSeeker->user->email : 'غير محدد' }}</td>
                        <td>{{ $application->job->title }}</td>
                        <td>{{ $application->job->location ?? 'غير محدد' }}</td>
                        <td>
                            <span class="status-{{ $application->status }}">
                                {{ getApplicationStatusInArabic($application->status) }}
                            </span>
                        </td>
                        <td>{{ $application->created_at->format('Y-m-d') }}</td>
                        <td>{{ $application->employer_rating ?? 'غير مقيم' }}</td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="7" style="text-align: center; color: #6b7280;">لا توجد طلبات</td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        @if($applications->count() > 0)
        <!-- Additional Statistics -->
        <div style="margin-top: 30px;">
            <h3 style="color: #1f2937; margin-bottom: 15px;">إحصائيات إضافية</h3>
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px;">
                <div style="background: #f8fafc; padding: 15px; border-radius: 8px;">
                    <h4 style="margin: 0 0 10px 0; color: #374151;">معدل القبول</h4>
                    <p style="margin: 0; font-size: 18px; color: #059669; font-weight: bold;">
                        @php
                            $acceptedCount = $applications->where('status', 'accepted')->count();
                        @endphp
                        {{ $totalApplications > 0 ? round(($acceptedCount / $totalApplications) * 100, 1) : 0 }}%
                    </p>
                </div>
                <div style="background: #f8fafc; padding: 15px; border-radius: 8px;">
                    <h4 style="margin: 0 0 10px 0; color: #374151;">الطلبات المعلقة</h4>
                    <p style="margin: 0; font-size: 18px; color: #d97706; font-weight: bold;">
                        {{ $applications->where('status', 'pending')->count() }}
                    </p>
                </div>
            </div>
        </div>
        @endif

        <!-- Footer -->
        <div class="footer">
            <p>تم إنشاء هذا التقرير بواسطة نظام Hire Me</p>
            <p>{{ config('app.url') }}</p>
        </div>
    </div>
</body>
</html>
