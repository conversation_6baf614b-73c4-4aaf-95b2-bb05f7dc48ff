

<?php $__env->startSection('title', 'تفاصيل الطلب - Hire Me'); ?>
<?php $__env->startSection('header_title', 'تفاصيل الطلب'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Application Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
            <h2 class="text-2xl font-bold">طلب تقديم: <?php echo e($application->jobSeeker->user->name); ?></h2>
            <p class="text-gray-600 dark:text-gray-400 mt-1">
                <span><?php echo e($application->job->title); ?></span> •
                <span>تاريخ التقديم: <?php echo e($application->created_at->format('Y/m/d')); ?></span>
            </p>
        </div>
        <div class="mt-4 md:mt-0 flex gap-3">
            <a href="<?php echo e(route('admin.applications.index')); ?>" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors flex items-center gap-2">
                <i class="fas fa-arrow-right"></i>
                <span>العودة للقائمة</span>
            </a>
            <button id="actionButton" class="px-5 py-2.5 gradient-bg text-white rounded-lg hover:opacity-90 transition-colors flex items-center gap-2">
                <i class="fas fa-user-check"></i>
                <span>تغيير الحالة</span>
                <i class="fas fa-chevron-down ml-1"></i>
            </button>
        </div>

        <!-- Status Dropdown Menu -->
        <div id="statusDropdown" class="absolute hidden mt-12 left-6 md:right-6 md:left-auto z-10 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg">
            <div class="py-1">
                <form action="<?php echo e(route('admin.applications.update-status', $application)); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>
                    <button type="submit" name="status" value="pending" class="w-full text-right px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-clock ml-2 text-yellow-500"></i>
                        قيد المراجعة
                    </button>
                    <button type="submit" name="status" value="reviewed" class="w-full text-right px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-search ml-2 text-blue-500"></i>
                        تمت المراجعة
                    </button>
                    <button type="submit" name="status" value="shortlisted" class="w-full text-right px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-list-alt ml-2 text-indigo-500"></i>
                        القائمة المختصرة
                    </button>
                    <button type="submit" name="status" value="hired" class="w-full text-right px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-check-circle ml-2 text-green-500"></i>
                        تم التعيين
                    </button>
                    <button type="submit" name="status" value="rejected" class="w-full text-right px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-times-circle ml-2 text-red-500"></i>
                        مرفوض
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Application Details -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Application Status -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-bold">حالة الطلب</h3>
                    <div>
                        <?php if($application->status == 'pending'): ?>
                            <span class="px-3 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400 rounded-full text-sm">قيد المراجعة</span>
                        <?php elseif($application->status == 'reviewed'): ?>
                            <span class="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 rounded-full text-sm">تمت المراجعة</span>
                        <?php elseif($application->status == 'shortlisted'): ?>
                            <span class="px-3 py-1 bg-indigo-100 dark:bg-indigo-900/30 text-indigo-700 dark:text-indigo-400 rounded-full text-sm">القائمة المختصرة</span>
                        <?php elseif($application->status == 'hired'): ?>
                            <span class="px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-sm">تم التعيين</span>
                        <?php elseif($application->status == 'rejected'): ?>
                            <span class="px-3 py-1 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 rounded-full text-sm">مرفوض</span>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Status Timeline -->
                <div class="relative">
                    <!-- Status Line -->
                    <div class="absolute top-5 left-6 right-6 h-0.5 bg-gray-200 dark:bg-gray-700"></div>

                    <!-- Status Steps -->
                    <div class="relative flex justify-between">
                        <div class="text-center">
                            <div class="w-10 h-10 mx-auto rounded-full <?php echo e($application->status != '' ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400' : 'bg-gray-100 dark:bg-gray-800 text-gray-400'); ?> flex items-center justify-center">
                                <i class="fas fa-clock"></i>
                            </div>
                            <p class="mt-2 text-xs font-medium <?php echo e($application->status != '' ? 'text-yellow-600 dark:text-yellow-400' : 'text-gray-500 dark:text-gray-400'); ?>">قيد المراجعة</p>
                            <p class="text-xs text-gray-500 dark:text-gray-400"><?php echo e($application->created_at->format('Y/m/d')); ?></p>
                        </div>

                        <div class="text-center">
                            <div class="w-10 h-10 mx-auto rounded-full <?php echo e($application->status == 'reviewed' || $application->status == 'shortlisted' || $application->status == 'hired' || $application->status == 'rejected' ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400' : 'bg-gray-100 dark:bg-gray-800 text-gray-400'); ?> flex items-center justify-center">
                                <i class="fas fa-search"></i>
                            </div>
                            <p class="mt-2 text-xs font-medium <?php echo e($application->status == 'reviewed' || $application->status == 'shortlisted' || $application->status == 'hired' || $application->status == 'rejected' ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400'); ?>">تمت المراجعة</p>
                            <?php if($application->status == 'reviewed' || $application->status == 'shortlisted' || $application->status == 'hired' || $application->status == 'rejected'): ?>
                                <p class="text-xs text-gray-500 dark:text-gray-400"><?php echo e($application->updated_at->format('Y/m/d')); ?></p>
                            <?php endif; ?>
                        </div>

                        <div class="text-center">
                            <div class="w-10 h-10 mx-auto rounded-full <?php echo e($application->status == 'shortlisted' || $application->status == 'hired' || $application->status == 'rejected' ? 'bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400' : 'bg-gray-100 dark:bg-gray-800 text-gray-400'); ?> flex items-center justify-center">
                                <i class="fas fa-list-alt"></i>
                            </div>
                            <p class="mt-2 text-xs font-medium <?php echo e($application->status == 'shortlisted' || $application->status == 'hired' || $application->status == 'rejected' ? 'text-indigo-600 dark:text-indigo-400' : 'text-gray-500 dark:text-gray-400'); ?>">القائمة المختصرة</p>
                            <?php if($application->status == 'shortlisted' || $application->status == 'hired' || $application->status == 'rejected'): ?>
                                <p class="text-xs text-gray-500 dark:text-gray-400"><?php echo e($application->updated_at->format('Y/m/d')); ?></p>
                            <?php endif; ?>
                        </div>

                        <div class="text-center">
                            <div class="w-10 h-10 mx-auto rounded-full <?php echo e($application->status == 'hired' ? 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400' : ($application->status == 'rejected' ? 'bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400' : 'bg-gray-100 dark:bg-gray-800 text-gray-400')); ?> flex items-center justify-center">
                                <i class="<?php echo e($application->status == 'hired' ? 'fas fa-check-circle' : ($application->status == 'rejected' ? 'fas fa-times-circle' : 'fas fa-flag-checkered')); ?>"></i>
                            </div>
                            <p class="mt-2 text-xs font-medium <?php echo e($application->status == 'hired' ? 'text-green-600 dark:text-green-400' : ($application->status == 'rejected' ? 'text-red-600 dark:text-red-400' : 'text-gray-500 dark:text-gray-400')); ?>"><?php echo e($application->status == 'hired' ? 'تم التعيين' : ($application->status == 'rejected' ? 'مرفوض' : 'القرار النهائي')); ?></p>
                            <?php if($application->status == 'hired' || $application->status == 'rejected'): ?>
                                <p class="text-xs text-gray-500 dark:text-gray-400"><?php echo e($application->updated_at->format('Y/m/d')); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cover Letter / Personal Statement -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">خطاب التقديم</h3>

                <div class="text-gray-600 dark:text-gray-400 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <p class="whitespace-pre-line"><?php echo e($application->cover_letter); ?></p>
                </div>
            </div>

            <!-- Applicant Resume -->
            <?php if($application->resume): ?>
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-bold">السيرة الذاتية</h3>
                    <div>
                        <a href="<?php echo e(asset('storage/' . $application->resume)); ?>" target="_blank" class="text-primary hover:underline text-sm flex items-center gap-1">
                            <i class="fas fa-download"></i>
                            <span>تحميل السيرة الذاتية</span>
                        </a>
                    </div>
                </div>

                <!-- Resume Preview -->
                <div class="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden h-96 bg-gray-50 dark:bg-gray-800">
                    <div class="flex items-center justify-center h-full">
                        <div class="text-center">
                            <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                                <i class="fas fa-file-pdf text-2xl text-red-500"></i>
                            </div>
                            <h4 class="font-medium mb-2"><?php echo e(basename($application->resume)); ?></h4>
                            <p class="text-gray-500 dark:text-gray-400 text-sm mb-4">معاينة السيرة الذاتية غير متوفرة</p>
                            <a href="<?php echo e(asset('storage/' . $application->resume)); ?>" target="_blank" class="px-4 py-2 gradient-bg text-white rounded-lg hover:opacity-90 transition-colors inline-flex items-center gap-2">
                                <i class="fas fa-eye"></i>
                                <span>معاينة</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Answers to Screening Questions (if any) -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">أسئلة الفرز</h3>

                <?php if(isset($application->screening_questions) && count($application->screening_questions) > 0): ?>
                    <div class="space-y-4">
                        <?php $__currentLoopData = $application->screening_questions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $question): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div>
                                <p class="font-medium"><?php echo e($question->question); ?></p>
                                <p class="text-gray-600 dark:text-gray-400 mt-1"><?php echo e($question->answer); ?></p>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="space-y-4">
                        <div>
                            <p class="font-medium">ما هي خبرتك في استخدام React.js؟</p>
                            <p class="text-gray-600 dark:text-gray-400 mt-1">لدي 3 سنوات من الخبرة في استخدام React.js في مشاريع متعددة. عملت على تطوير تطبيقات ويب تفاعلية كاملة باستخدام React مع Redux لإدارة الحالة. قمت أيضاً بتنفيذ معالجة الأخطاء وتحسين الأداء للتطبيقات القائمة على React.</p>
                        </div>

                        <div>
                            <p class="font-medium">هل لديك خبرة في استخدام واجهات برمجة التطبيقات RESTful؟</p>
                            <p class="text-gray-600 dark:text-gray-400 mt-1">نعم، لدي خبرة واسعة في العمل مع واجهات برمجة التطبيقات RESTful. قمت بتطوير وتكامل العديد من واجهات برمجة التطبيقات باستخدام تقنيات مثل Fetch API و Axios. كما أنني على دراية جيدة بمعالجة البيانات وإدارة حالات الأخطاء في طلبات واجهة برمجة التطبيقات.</p>
                        </div>

                        <div>
                            <p class="font-medium">هل يمكنك العمل تحت ضغط وإنجاز المهام خلال المواعيد النهائية الضيقة؟</p>
                            <p class="text-gray-600 dark:text-gray-400 mt-1">نعم، أنا معتاد على العمل في بيئات سريعة الخطى وتحت ضغط المواعيد النهائية. أستخدم مهارات إدارة الوقت والتنظيم للحفاظ على الإنتاجية وضمان تسليم المشاريع في الوقت المحدد. في وظيفتي السابقة، نجحت في إكمال مشروع كبير قبل الموعد النهائي بأسبوع رغم الجدول الزمني الضيق.</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Notes & Evaluation -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold">الملاحظات والتقييم</h3>
                </div>

                <!-- Rating -->
                <div class="flex items-center gap-2 mb-4">
                    <p class="text-gray-600 dark:text-gray-400">التقييم:</p>
                    <div class="flex items-center gap-1">
                        <?php for($i = 1; $i <= 5; $i++): ?>
                            <span class="<?php echo e($i <= ($application->rating ?? 0) ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600'); ?>">
                                <i class="fas fa-star"></i>
                            </span>
                        <?php endfor; ?>
                    </div>
                </div>

                <!-- Notes Form -->
                <form action="<?php echo e(route('admin.applications.update-status', $application)); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>
                    <div class="mb-4">
                        <label for="admin_notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">ملاحظات المدير</label>
                        <textarea name="admin_notes" id="admin_notes" rows="4" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base"><?php echo e($application->admin_notes); ?></textarea>
                    </div>

                    <div class="mb-4">
                        <label for="rating" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">تقييم المرشح (من 1 إلى 5)</label>
                        <select name="rating" id="rating" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                            <option value="">بدون تقييم</option>
                            <option value="1" <?php echo e($application->rating == 1 ? 'selected' : ''); ?>>1 - ضعيف</option>
                            <option value="2" <?php echo e($application->rating == 2 ? 'selected' : ''); ?>>2 - مقبول</option>
                            <option value="3" <?php echo e($application->rating == 3 ? 'selected' : ''); ?>>3 - جيد</option>
                            <option value="4" <?php echo e($application->rating == 4 ? 'selected' : ''); ?>>4 - جيد جداً</option>
                            <option value="5" <?php echo e($application->rating == 5 ? 'selected' : ''); ?>>5 - ممتاز</option>
                        </select>
                    </div>

                    <div class="flex justify-end">
                        <button type="submit" class="px-4 py-2 gradient-bg text-white rounded-lg hover:opacity-90 transition-colors">
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>

            <!-- Interview Schedule (if status is Interview) -->
            <?php if(($application->status ?? '') == 'interview'): ?>
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">جدولة المقابلة</h3>

                <?php if(isset($application->interview) && $application->interview->scheduled): ?>
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 rounded-full bg-indigo-100 dark:bg-indigo-900/30 flex items-center justify-center">
                                <i class="fas fa-calendar-check text-indigo-600 dark:text-indigo-400"></i>
                            </div>
                            <div>
                                <p class="font-medium"><?php echo e($application->interview->date); ?> <?php echo e($application->interview->time); ?></p>
                                <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo e($application->interview->type); ?></p>
                            </div>
                        </div>
                        <button class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary transition-colors">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>

                    <div class="space-y-2 text-gray-600 dark:text-gray-400">
                        <p><span class="font-medium">نوع المقابلة:</span> <?php echo e($application->interview->type); ?></p>
                        <p><span class="font-medium">المقابلين:</span> <?php echo e($application->interview->interviewers); ?></p>
                        <?php if($application->interview->location): ?>
                            <p><span class="font-medium">الموقع:</span> <?php echo e($application->interview->location); ?></p>
                        <?php endif; ?>
                        <?php if($application->interview->meeting_link): ?>
                            <p><span class="font-medium">رابط الاجتماع:</span> <a href="<?php echo e($application->interview->meeting_link); ?>" target="_blank" class="text-primary hover:underline"><?php echo e($application->interview->meeting_link); ?></a></p>
                        <?php endif; ?>
                        <?php if($application->interview->notes): ?>
                            <p><span class="font-medium">ملاحظات:</span> <?php echo e($application->interview->notes); ?></p>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div>
                        <button class="px-4 py-2.5 gradient-bg text-white rounded-lg hover:opacity-90 transition-colors flex items-center gap-2">
                            <i class="fas fa-calendar-plus"></i>
                            <span>جدولة مقابلة</span>
                        </button>
                    </div>
                <?php endif; ?>
            </div>
            <?php endif; ?>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Applicant Information -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <div class="flex items-center gap-4 mb-6">
                    <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                        <?php if($application->jobSeeker->user->avatar): ?>
                            <img src="<?php echo e(asset('storage/' . $application->jobSeeker->user->avatar)); ?>" alt="<?php echo e($application->jobSeeker->user->name); ?>" class="w-14 h-14 rounded-full object-cover">
                        <?php else: ?>
                            <i class="fas fa-user text-primary text-xl"></i>
                        <?php endif; ?>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold"><?php echo e($application->jobSeeker->user->name); ?></h3>
                        <p class="text-gray-600 dark:text-gray-400 text-sm"><?php echo e($application->jobSeeker->job_title ?: 'باحث عن عمل'); ?></p>
                    </div>
                </div>

                <div class="space-y-4">
                    <div class="flex items-start gap-3">
                        <div class="w-8 text-center text-gray-500 dark:text-gray-400 pt-0.5">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div>
                            <p class="text-gray-500 dark:text-gray-400 text-xs">البريد الإلكتروني</p>
                            <p class="font-medium"><?php echo e($application->jobSeeker->user->email); ?></p>
                        </div>
                    </div>

                    <?php if($application->jobSeeker->phone): ?>
                    <div class="flex items-start gap-3">
                        <div class="w-8 text-center text-gray-500 dark:text-gray-400 pt-0.5">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div>
                            <p class="text-gray-500 dark:text-gray-400 text-xs">رقم الهاتف</p>
                            <p class="font-medium"><?php echo e($application->jobSeeker->phone); ?></p>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if($application->jobSeeker->location): ?>
                    <div class="flex items-start gap-3">
                        <div class="w-8 text-center text-gray-500 dark:text-gray-400 pt-0.5">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div>
                            <p class="text-gray-500 dark:text-gray-400 text-xs">الموقع</p>
                            <p class="font-medium"><?php echo e($application->jobSeeker->location); ?></p>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if($application->jobSeeker->website): ?>
                    <div class="flex items-start gap-3">
                        <div class="w-8 text-center text-gray-500 dark:text-gray-400 pt-0.5">
                            <i class="fas fa-globe"></i>
                        </div>
                        <div>
                            <p class="text-gray-500 dark:text-gray-400 text-xs">الموقع الإلكتروني</p>
                            <a href="<?php echo e($application->jobSeeker->website); ?>" target="_blank" class="font-medium text-primary hover:underline"><?php echo e($application->jobSeeker->website); ?></a>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if($application->jobSeeker->linkedin): ?>
                    <div class="flex items-start gap-3">
                        <div class="w-8 text-center text-gray-500 dark:text-gray-400 pt-0.5">
                            <i class="fab fa-linkedin"></i>
                        </div>
                        <div>
                            <p class="text-gray-500 dark:text-gray-400 text-xs">لينكد إن</p>
                            <a href="<?php echo e($application->jobSeeker->linkedin); ?>" target="_blank" class="font-medium text-primary hover:underline"><?php echo e($application->jobSeeker->linkedin); ?></a>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if($application->jobSeeker->skills): ?>
                    <div class="flex items-start gap-3">
                        <div class="w-8 text-center text-gray-500 dark:text-gray-400 pt-0.5">
                            <i class="fas fa-code"></i>
                        </div>
                        <div>
                            <p class="text-gray-500 dark:text-gray-400 text-xs">المهارات</p>
                            <div class="flex flex-wrap gap-2 mt-1">
                                <?php $__currentLoopData = explode(',', $application->jobSeeker->skills); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $skill): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs"><?php echo e(trim($skill)); ?></span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Job Information -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">معلومات الوظيفة</h3>

                <div class="flex items-center gap-3 mb-4">
                    <div class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center text-primary flex-shrink-0">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <div>
                        <a href="<?php echo e(route('admin.jobs.show', $application->job->id)); ?>" class="font-medium hover:text-primary"><?php echo e($application->job->title); ?></a>
                        <p class="text-gray-500 dark:text-gray-400 text-xs"><?php echo e($application->job->employer->company_name); ?></p>
                    </div>
                </div>

                <div class="space-y-3">
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-500 dark:text-gray-400">الموقع:</span>
                        <span><?php echo e($application->job->location); ?></span>
                    </div>
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-500 dark:text-gray-400">نوع الوظيفة:</span>
                        <span>
                            <?php if($application->job->job_type == 'full_time'): ?>
                                دوام كامل
                            <?php elseif($application->job->job_type == 'part_time'): ?>
                                دوام جزئي
                            <?php elseif($application->job->job_type == 'remote'): ?>
                                عن بعد
                            <?php elseif($application->job->job_type == 'freelance'): ?>
                                عمل حر
                            <?php else: ?>
                                <?php echo e($application->job->job_type); ?>

                            <?php endif; ?>
                        </span>
                    </div>
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-500 dark:text-gray-400">تاريخ النشر:</span>
                        <span><?php echo e($application->job->created_at->format('Y/m/d')); ?></span>
                    </div>
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-500 dark:text-gray-400">عدد الطلبات:</span>
                        <span><?php echo e($application->job->applications->count()); ?></span>
                    </div>
                    <?php if($application->job->salary_range): ?>
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-500 dark:text-gray-400">نطاق الراتب:</span>
                        <span><?php echo e($application->job->salary_range); ?></span>
                    </div>
                    <?php endif; ?>
                </div>

                <div class="mt-4">
                    <a href="<?php echo e(route('admin.jobs.show', $application->job->id)); ?>" class="text-primary hover:underline text-sm flex items-center gap-1 justify-center">
                        <i class="fas fa-eye"></i>
                        <span>عرض تفاصيل الوظيفة</span>
                    </a>
                </div>
            </div>

            <!-- Application Actions -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">الإجراءات المتاحة</h3>

                <div class="space-y-3">
                    <a href="mailto:<?php echo e($application->jobSeeker->user->email); ?>" class="w-full px-4 py-2.5 gradient-bg text-white rounded-lg hover:opacity-90 transition-colors flex items-center gap-2 justify-center">
                        <i class="fas fa-envelope"></i>
                        <span>إرسال بريد إلكتروني</span>
                    </a>

                    <form action="<?php echo e(route('admin.applications.update-status', $application)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>
                        <input type="hidden" name="status" value="shortlisted">
                        <button type="submit" class="w-full px-4 py-2.5 border border-primary text-primary bg-white dark:bg-gray-800 rounded-lg hover:bg-primary/10 transition-colors flex items-center gap-2 justify-center">
                            <i class="fas fa-list-alt"></i>
                            <span>إضافة للقائمة المختصرة</span>
                        </button>
                    </form>

                    <form action="<?php echo e(route('admin.applications.update-status', $application)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>
                        <input type="hidden" name="status" value="rejected">
                        <button type="submit" class="w-full px-4 py-2.5 border border-red-500 text-red-500 bg-white dark:bg-gray-800 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/10 transition-colors flex items-center gap-2 justify-center">
                            <i class="fas fa-times-circle"></i>
                            <span>رفض الطلب</span>
                        </button>
                    </form>

                    <form action="<?php echo e(route('admin.applications.destroy', $application)); ?>" method="POST" onsubmit="return confirm('هل أنت متأكد من حذف هذا الطلب؟');">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="w-full px-4 py-2.5 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center gap-2 justify-center">
                            <i class="fas fa-trash-alt"></i>
                            <span>حذف الطلب</span>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    // Status Dropdown toggle
    const actionButton = document.getElementById('actionButton');
    const statusDropdown = document.getElementById('statusDropdown');

    if (actionButton && statusDropdown) {
        actionButton.addEventListener('click', (e) => {
            e.stopPropagation();
            statusDropdown.classList.toggle('hidden');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!actionButton.contains(e.target) && !statusDropdown.contains(e.target)) {
                statusDropdown.classList.add('hidden');
            }
        });
    }
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\Telegram Desktop\augment (10)\augment\resources\views/admin/applications/show.blade.php ENDPATH**/ ?>