@extends('layouts.admin')

@section('title', 'تفاصيل الوظيفة - Hire Me')
@section('header_title', 'تفاصيل الوظيفة')

@section('content')
<div class="p-6">
    <!-- Job Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
            <h2 class="text-2xl font-bold">{{ $job->title ?? 'مطور واجهات أمامية' }}</h2>
            <p class="text-gray-600 dark:text-gray-400 mt-1">
                <span>{{ $job->department ?? 'قسم تطوير البرمجيات' }}</span> •
                <span>{{ $job->location ?? 'طرابلس' }}</span> •
                <span>
                    @switch($job->job_type)
                        @case('full_time')
                            دوام كامل
                            @break
                        @case('part_time')
                            دوام جزئي
                            @break
                        @case('remote')
                            عن بعد
                            @break
                        @case('freelance')
                            عمل حر
                            @break
                        @default
                            {{ $job->job_type ?? 'دوام كامل' }}
                    @endswitch
                </span>
            </p>
        </div>
        <div class="mt-4 md:mt-0 flex gap-3">
            <a href="{{ route('admin.jobs.edit', $job->id ?? 1) }}" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors flex items-center gap-2">
                <i class="fas fa-edit"></i>
                <span>تعديل</span>
            </a>
            <button id="deleteJobBtn" class="px-4 py-2 border border-red-300 dark:border-red-800 rounded-lg text-red-700 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30 transition-colors flex items-center gap-2">
                <i class="fas fa-trash-alt"></i>
                <span>حذف</span>
            </button>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Job Details -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Job Information -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <div class="mb-6 flex items-center gap-4">
                    <div class="w-14 h-14 {{ $job->color_class ?? 'gradient-bg' }} rounded-lg flex items-center justify-center text-white flex-shrink-0">
                        <i class="fas fa-{{ $job->icon ?? 'code' }} text-2xl"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold">{{ $job->title ?? 'مطور واجهات أمامية' }}</h3>
                        <div class="flex flex-wrap items-center gap-3 mt-1 text-gray-600 dark:text-gray-400 text-sm">
                            <span class="flex items-center gap-1">
                                <i class="fas fa-map-marker-alt"></i>
                                {{ $job->location ?? 'الرياض' }}
                            </span>
                            <span class="flex items-center gap-1">
                                <i class="fas fa-clock"></i>
                                @switch($job->job_type)
                                    @case('full_time')
                                        دوام كامل
                                        @break
                                    @case('part_time')
                                        دوام جزئي
                                        @break
                                    @case('remote')
                                        عن بعد
                                        @break
                                    @case('freelance')
                                        عمل حر
                                        @break
                                    @default
                                        {{ $job->job_type ?? 'دوام كامل' }}
                                @endswitch
                            </span>
                            <span class="flex items-center gap-1">
                                <i class="fas fa-calendar-alt"></i>
                                تاريخ النشر: {{ $job->posted_at ? $job->posted_at->format('d M, Y') : 'غير محدد' }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Status & Tags -->
                <div class="flex flex-wrap gap-2 mb-6">
                    @if(($job->status ?? 'active') == 'active')
                        <span class="px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-sm">نشط</span>
                    @elseif(($job->status ?? '') == 'inactive')
                        <span class="px-3 py-1 bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400 rounded-full text-sm">غير نشط</span>
                    @elseif(($job->status ?? '') == 'draft')
                        <span class="px-3 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400 rounded-full text-sm">مسودة</span>
                    @elseif(($job->status ?? '') == 'expired')
                        <span class="px-3 py-1 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 rounded-full text-sm">منتهي</span>
                    @endif

                    @if($job->featured ?? false)
                        <span class="px-3 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 rounded-full text-sm">مميز</span>
                    @endif

                    @if($job->urgent ?? false)
                        <span class="px-3 py-1 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 rounded-full text-sm">عاجل</span>
                    @endif
                </div>

                <!-- Job Description -->
                <div class="border-b border-gray-200 dark:border-gray-700 pb-5 mb-5">
                    <h4 class="text-lg font-semibold mb-3">وصف الوظيفة</h4>
                    <div class="text-gray-600 dark:text-gray-400 space-y-2">
                        <p>{{ $job->description }}</p>
                    </div>
                </div>

                <!-- Job Requirements -->
                <div class="border-b border-gray-200 dark:border-gray-700 pb-5 mb-5">
                    <h4 class="text-lg font-semibold mb-3">المتطلبات</h4>
                    <ul class="list-disc list-inside space-y-1 text-gray-600 dark:text-gray-400">
                        @foreach(explode("\n", $job->requirements) as $requirement)
                            <li>{{ ltrim($requirement, "- ") }}</li>
                        @endforeach
                    </ul>
                </div>

                <!-- Benefits -->
                <div class="border-b border-gray-200 dark:border-gray-700 pb-5 mb-5">
                    <h4 class="text-lg font-semibold mb-3">المميزات والفوائد</h4>
                    <ul class="list-disc list-inside space-y-1 text-gray-600 dark:text-gray-400">
                        @foreach(explode("\n", $job->benefits) as $benefit)
                            <li>{{ ltrim($benefit, "- ") }}</li>
                        @endforeach
                    </ul>
                </div>

                <!-- Skills & Qualifications -->
                <div>
                    <h4 class="text-lg font-semibold mb-3">المهارات المطلوبة</h4>
                    <div class="flex flex-wrap gap-2">
                        @foreach(explode(",", $job->skills) as $skill)
                            <span class="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm">{{ trim($skill) }}</span>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Applications -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold">طلبات التقديم</h3>
                    <a href="{{ route('admin.applications.index', ['job_id' => $job->id ?? 1]) }}" class="text-primary text-sm hover:underline">عرض الكل</a>
                </div>

                <div class="overflow-x-auto">
                    <table class="w-full text-right text-sm">
                        <thead class="bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-300">
                            <tr>
                                <th class="px-6 py-3">المرشح</th>
                                <th class="px-6 py-3">تاريخ التقديم</th>
                                <th class="px-6 py-3">الحالة</th>
                                <th class="px-6 py-3">التقييم</th>
                                <th class="px-6 py-3">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                            @forelse($job->applications ?? [] as $application)
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                                    <td class="px-6 py-3">
                                        <div class="flex items-center gap-3">
                                            <div class="w-8 h-8 bg-{{ $application->color ?? 'blue' }}-100 dark:bg-{{ $application->color ?? 'blue' }}-900/30 rounded-full flex items-center justify-center">
                                                <i class="fas fa-user text-{{ $application->color ?? 'blue' }}-600 dark:text-{{ $application->color ?? 'blue' }}-400 text-sm"></i>
                                            </div>
                                            <span class="font-medium">{{ $application->applicant_name }}</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-3 text-gray-600 dark:text-gray-400">{{ $application->created_at }}</td>
                                    <td class="px-6 py-3">
                                        @if($application->status == 'new')
                                            <span class="px-2.5 py-1 text-xs font-medium text-blue-700 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30 rounded-full">جديد</span>
                                        @elseif($application->status == 'reviewing')
                                            <span class="px-2.5 py-1 text-xs font-medium text-yellow-700 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/30 rounded-full">قيد المراجعة</span>
                                        @elseif($application->status == 'shortlisted')
                                            <span class="px-2.5 py-1 text-xs font-medium text-purple-700 dark:text-purple-400 bg-purple-100 dark:bg-purple-900/30 rounded-full">بالقائمة المختصرة</span>
                                        @elseif($application->status == 'interview')
                                            <span class="px-2.5 py-1 text-xs font-medium text-indigo-700 dark:text-indigo-400 bg-indigo-100 dark:bg-indigo-900/30 rounded-full">مقابلة</span>
                                        @elseif($application->status == 'hired')
                                            <span class="px-2.5 py-1 text-xs font-medium text-green-700 dark:text-green-400 bg-green-100 dark:bg-green-900/30 rounded-full">تم التعيين</span>
                                        @elseif($application->status == 'rejected')
                                            <span class="px-2.5 py-1 text-xs font-medium text-red-700 dark:text-red-400 bg-red-100 dark:bg-red-900/30 rounded-full">مرفوض</span>
                                        @else
                                            <span class="px-2.5 py-1 text-xs font-medium text-gray-700 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/30 rounded-full">{{ $application->status }}</span>
                                        @endif
                                    </td>
                                    <td class="px-6 py-3">
                                        <div class="flex items-center gap-1">
                                            @for($i = 1; $i <= 5; $i++)
                                                @if($i <= $application->rating)
                                                    <i class="fas fa-star text-yellow-400 text-xs"></i>
                                                @else
                                                    <i class="far fa-star text-gray-300 dark:text-gray-600 text-xs"></i>
                                                @endif
                                            @endfor
                                        </div>
                                    </td>
                                    <td class="px-6 py-3">
                                        <a href="{{ route('admin.applications.show', $application->id) }}" class="text-primary hover:text-primary/80">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            @empty
                                <!-- Sample application data for display -->
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                                    <td class="px-6 py-3">
                                        <div class="flex items-center gap-3">
                                            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                                                <i class="fas fa-user text-blue-600 dark:text-blue-400 text-sm"></i>
                                            </div>
                                            <span class="font-medium">أحمد محمد</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-3 text-gray-600 dark:text-gray-400">منذ ساعتين</td>
                                    <td class="px-6 py-3">
                                        <span class="px-2.5 py-1 text-xs font-medium text-blue-700 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30 rounded-full">جديد</span>
                                    </td>
                                    <td class="px-6 py-3">
                                        <div class="flex items-center gap-1">
                                            <i class="fas fa-star text-yellow-400 text-xs"></i>
                                            <i class="fas fa-star text-yellow-400 text-xs"></i>
                                            <i class="fas fa-star text-yellow-400 text-xs"></i>
                                            <i class="fas fa-star text-yellow-400 text-xs"></i>
                                            <i class="far fa-star text-gray-300 dark:text-gray-600 text-xs"></i>
                                        </div>
                                    </td>
                                    <td class="px-6 py-3">
                                        <a href="{{ route('admin.applications.show', 1) }}" class="text-primary hover:text-primary/80">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                                    <td class="px-6 py-3">
                                        <div class="flex items-center gap-3">
                                            <div class="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                                                <i class="fas fa-user text-green-600 dark:text-green-400 text-sm"></i>
                                            </div>
                                            <span class="font-medium">سارة عبدالله</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-3 text-gray-600 dark:text-gray-400">منذ 5 ساعات</td>
                                    <td class="px-6 py-3">
                                        <span class="px-2.5 py-1 text-xs font-medium text-yellow-700 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/30 rounded-full">قيد المراجعة</span>
                                    </td>
                                    <td class="px-6 py-3">
                                        <div class="flex items-center gap-1">
                                            <i class="fas fa-star text-yellow-400 text-xs"></i>
                                            <i class="fas fa-star text-yellow-400 text-xs"></i>
                                            <i class="fas fa-star text-yellow-400 text-xs"></i>
                                            <i class="fas fa-star text-yellow-400 text-xs"></i>
                                            <i class="fas fa-star text-yellow-400 text-xs"></i>
                                        </div>
                                    </td>
                                    <td class="px-6 py-3">
                                        <a href="{{ route('admin.applications.show', 2) }}" class="text-primary hover:text-primary/80">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                @if($job->applications->count() > 0)
                    <div class="mt-4 text-center">
                        <a href="{{ route('admin.applications.index', ['job_id' => $job->id]) }}" class="text-primary hover:underline text-sm">عرض جميع الطلبات ({{ $job->applications->count() }})</a>
                    </div>
                @else
                    <div class="mt-4 text-center text-gray-500 dark:text-gray-400">
                        لا توجد طلبات لهذه الوظيفة بعد.
                    </div>
                @endif
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Job Stats -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">إحصائيات الوظيفة</h3>

                <div class="space-y-4">
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                                <i class="fas fa-eye text-blue-600 dark:text-blue-400"></i>
                            </div>
                            <div>
                                <p class="text-gray-500 dark:text-gray-400 text-xs">المشاهدات</p>
                                <p class="font-bold">{{ $job->views ?? 0 }}</p>
                            </div>
                        </div>
                        <div class="text-green-600 dark:text-green-400 text-sm">
                            <i class="fas fa-arrow-up"></i>
                            <span>{{ $job->views > 0 ? '15%' : '0%' }}</span>
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                                <i class="fas fa-file-alt text-green-600 dark:text-green-400"></i>
                            </div>
                            <div>
                                <p class="text-gray-500 dark:text-gray-400 text-xs">الطلبات</p>
                                <p class="font-bold">{{ $job->applications->count() ?? 0 }}</p>
                            </div>
                        </div>
                        <div class="text-green-600 dark:text-green-400 text-sm">
                            <i class="fas fa-arrow-up"></i>
                            <span>{{ $job->applications->count() > 0 ? '10%' : '0%' }}</span>
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
                                <i class="fas fa-user-check text-purple-600 dark:text-purple-400"></i>
                            </div>
                            <div>
                                <p class="text-gray-500 dark:text-gray-400 text-xs">المرشحون</p>
                                <p class="font-bold">{{ $job->applications->where('status', 'shortlisted')->count() ?? 0 }}</p>
                            </div>
                        </div>
                        <div class="text-green-600 dark:text-green-400 text-sm">
                            <i class="fas fa-arrow-up"></i>
                            <span>{{ $job->applications->where('status', 'shortlisted')->count() > 0 ? '20%' : '0%' }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Job Key Details -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">تفاصيل رئيسية</h3>

                <div class="space-y-4">
                    <div class="flex items-start gap-3">
                        <div class="w-8 text-center text-gray-500 dark:text-gray-400 pt-0.5">
                            <i class="fas fa-briefcase"></i>
                        </div>
                        <div>
                            <p class="text-gray-500 dark:text-gray-400 text-sm">نوع الوظيفة</p>
                            <p class="font-medium">
                                @switch($job->job_type)
                                    @case('full_time')
                                        دوام كامل
                                        @break
                                    @case('part_time')
                                        دوام جزئي
                                        @break
                                    @case('remote')
                                        عن بعد
                                        @break
                                    @case('freelance')
                                        عمل حر
                                        @break
                                    @default
                                        {{ $job->job_type ?? 'دوام كامل' }}
                                @endswitch
                            </p>
                        </div>
                    </div>

                    <div class="flex items-start gap-3">
                        <div class="w-8 text-center text-gray-500 dark:text-gray-400 pt-0.5">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div>
                            <p class="text-gray-500 dark:text-gray-400 text-sm">الموقع</p>
                            <p class="font-medium">{{ $job->location ?? 'الرياض' }}</p>
                        </div>
                    </div>

                    <div class="flex items-start gap-3">
                        <div class="w-8 text-center text-gray-500 dark:text-gray-400 pt-0.5">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div>
                            <p class="text-gray-500 dark:text-gray-400 text-sm">نطاق الراتب</p>
                            <p class="font-medium">{{ ($job->salary_min || $job->salary_max) ? $job->salary_range : 'غير محدد' }}</p>
                        </div>
                    </div>

                    <div class="flex items-start gap-3">
                        <div class="w-8 text-center text-gray-500 dark:text-gray-400 pt-0.5">
                            <i class="fas fa-user-graduate"></i>
                        </div>
                        <div>
                            <p class="text-gray-500 dark:text-gray-400 text-sm">المؤهل العلمي</p>
                            <p class="font-medium">
                                @if(($job->education ?? 'bachelor') == 'high_school')
                                    ثانوية عامة
                                @elseif(($job->education ?? 'bachelor') == 'diploma')
                                    دبلوم
                                @elseif(($job->education ?? 'bachelor') == 'bachelor')
                                    بكالوريوس
                                @elseif(($job->education ?? 'bachelor') == 'master')
                                    ماجستير
                                @elseif(($job->education ?? 'bachelor') == 'phd')
                                    دكتوراه
                                @else
                                    {{ $job->education ?? 'بكالوريوس' }}
                                @endif
                            </p>
                        </div>
                    </div>

                    <div class="flex items-start gap-3">
                        <div class="w-8 text-center text-gray-500 dark:text-gray-400 pt-0.5">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div>
                            <p class="text-gray-500 dark:text-gray-400 text-sm">تاريخ النشر</p>
                            <p class="font-medium">{{ $job->posted_at ? $job->posted_at->format('d M, Y') : 'غير محدد' }}</p>
                        </div>
                    </div>

                    <div class="flex items-start gap-3">
                        <div class="w-8 text-center text-gray-500 dark:text-gray-400 pt-0.5">
                            <i class="fas fa-hourglass-end"></i>
                        </div>
                        <div>
                            <p class="text-gray-500 dark:text-gray-400 text-sm">تاريخ انتهاء التقديم</p>
                            <p class="font-medium">{{ $job->expires_at ? $job->expires_at->format('d M, Y') : 'غير محدد' }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Share Job -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">مشاركة الوظيفة</h3>

                <div class="space-y-3">
                    <p class="text-gray-600 dark:text-gray-400 text-sm">شارك هذه الوظيفة على وسائل التواصل الاجتماعي</p>

                    <div class="flex gap-3">
                        <button class="w-10 h-10 flex items-center justify-center rounded-full bg-blue-600 text-white hover:bg-blue-700 transition-colors">
                            <i class="fab fa-facebook-f"></i>
                        </button>
                        <button class="w-10 h-10 flex items-center justify-center rounded-full bg-sky-500 text-white hover:bg-sky-600 transition-colors">
                            <i class="fab fa-twitter"></i>
                        </button>
                        <button class="w-10 h-10 flex items-center justify-center rounded-full bg-blue-500 text-white hover:bg-blue-600 transition-colors">
                            <i class="fab fa-linkedin-in"></i>
                        </button>
                        <button class="w-10 h-10 flex items-center justify-center rounded-full bg-green-600 text-white hover:bg-green-700 transition-colors">
                            <i class="fab fa-whatsapp"></i>
                        </button>
                        <button class="w-10 h-10 flex items-center justify-center rounded-full bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>

                    <div class="relative">
                        <input type="text" value="{{ url('/jobs/' . $job->id) }}" class="block w-full pr-10 p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-sm" readonly>
                        <button class="absolute left-2.5 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400 hover:text-primary transition-colors" id="copyLink">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
        </div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white dark:bg-dark-card rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white dark:bg-dark-card px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/30 sm:mx-0 sm:h-10 sm:w-10">
                        <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400"></i>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:mr-4 sm:text-right">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                            حذف الوظيفة
                        </h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                هل أنت متأكد من رغبتك في حذف هذه الوظيفة؟ جميع البيانات المرتبطة بها ستفقد ولا يمكن التراجع عن هذا الإجراء.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="confirmDelete" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                    حذف
                </button>
                <button type="button" id="cancelDelete" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    إلغاء
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Delete job confirmation
    const deleteJobBtn = document.getElementById('deleteJobBtn');
    const deleteModal = document.getElementById('deleteModal');
    const confirmDelete = document.getElementById('confirmDelete');
    const cancelDelete = document.getElementById('cancelDelete');

    // Show delete modal
    deleteJobBtn.addEventListener('click', () => {
        deleteModal.classList.remove('hidden');
    });

    // Cancel delete
    cancelDelete.addEventListener('click', () => {
        deleteModal.classList.add('hidden');
    });

    // Confirm delete
    confirmDelete.addEventListener('click', () => {
        // Create a form to submit the delete request
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = "{{ route('admin.jobs.destroy', $job->id) }}";
        form.style.display = 'none';

        // Add CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = "{{ csrf_token() }}";
        form.appendChild(csrfToken);

        // Add method field
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';
        form.appendChild(methodField);

        // Append form to body and submit
        document.body.appendChild(form);
        form.submit();
    });

    // Close modal when clicking outside
    deleteModal.addEventListener('click', (e) => {
        if (e.target === deleteModal) {
            deleteModal.classList.add('hidden');
        }
    });

    // Copy link functionality
    const copyLink = document.getElementById('copyLink');

    copyLink.addEventListener('click', () => {
        const input = copyLink.previousElementSibling;
        input.select();
        document.execCommand('copy');

        // Show copied tooltip (in a real app)
        copyLink.setAttribute('data-original-text', copyLink.innerHTML);
        copyLink.innerHTML = '<i class="fas fa-check"></i>';

        setTimeout(() => {
            copyLink.innerHTML = copyLink.getAttribute('data-original-text');
        }, 2000);
    });
</script>
@endsection