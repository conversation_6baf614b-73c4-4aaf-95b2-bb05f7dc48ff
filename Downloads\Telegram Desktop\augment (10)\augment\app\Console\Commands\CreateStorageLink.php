<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class CreateStorageLink extends Command
{
    protected $signature = 'storage:link-fix';
    protected $description = 'Create storage link for Windows and fix image paths';

    public function handle()
    {
        $this->info('Creating storage link...');
        
        $target = storage_path('app/public');
        $link = public_path('storage');
        
        // حذف الرابط إذا كان موجوداً
        if (File::exists($link)) {
            if (is_link($link)) {
                unlink($link);
                $this->info('Removed existing symbolic link.');
            } elseif (is_dir($link)) {
                File::deleteDirectory($link);
                $this->info('Removed existing directory.');
            }
        }
        
        // إنشاء مجلد storage في public إذا لم يكن موجوداً
        if (!File::exists($link)) {
            File::makeDirectory($link, 0755, true);
            $this->info('Created storage directory in public.');
        }
        
        // نسخ الملفات من storage/app/public إلى public/storage
        if (File::exists($target)) {
            File::copyDirectory($target, $link);
            $this->info('Copied files from storage/app/public to public/storage.');
        }
        
        $this->info('Storage link created successfully!');
        $this->info('You can now access files at: ' . url('/storage/filename'));
        
        return 0;
    }
}
