<?php

namespace App\Console\Commands;

use App\Models\Application;
use Illuminate\Console\Command;

class CleanupOrphanedApplications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'applications:cleanup {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'تنظيف طلبات التوظيف المرتبطة بوظائف أو مستخدمين محذوفين';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧹 بدء تنظيف طلبات التوظيف...');

        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->warn('⚠️  وضع المعاينة - لن يتم حذف أي بيانات فعلياً');
        }

        // تنظيف الطلبات المرتبطة بوظائف محذوفة
        $orphanedJobApplications = Application::whereDoesntHave('job')->get();
        $this->info("📋 طلبات مرتبطة بوظائف محذوفة: {$orphanedJobApplications->count()}");

        if (!$dryRun && $orphanedJobApplications->count() > 0) {
            foreach ($orphanedJobApplications as $app) {
                $app->delete();
            }
            $this->info("✅ تم حذف {$orphanedJobApplications->count()} طلب مرتبط بوظائف محذوفة");
        }

        // تنظيف الطلبات المرتبطة بباحثين عن عمل محذوفين
        $orphanedJobSeekerApplications = Application::whereDoesntHave('jobSeeker')->get();
        $this->info("👤 طلبات مرتبطة بباحثين محذوفين: {$orphanedJobSeekerApplications->count()}");

        if (!$dryRun && $orphanedJobSeekerApplications->count() > 0) {
            foreach ($orphanedJobSeekerApplications as $app) {
                $app->delete();
            }
            $this->info("✅ تم حذف {$orphanedJobSeekerApplications->count()} طلب مرتبط بباحثين محذوفين");
        }

        // تنظيف الطلبات المرتبطة بمستخدمين محذوفين
        $orphanedUserApplications = Application::whereDoesntHave('jobSeeker.user')->get();
        $this->info("🔗 طلبات مرتبطة بمستخدمين محذوفين: {$orphanedUserApplications->count()}");

        if (!$dryRun && $orphanedUserApplications->count() > 0) {
            foreach ($orphanedUserApplications as $app) {
                $app->delete();
            }
            $this->info("✅ تم حذف {$orphanedUserApplications->count()} طلب مرتبط بمستخدمين محذوفين");
        }

        $totalCleaned = $orphanedJobApplications->count() + $orphanedJobSeekerApplications->count() + $orphanedUserApplications->count();

        if ($dryRun) {
            $this->warn("📊 المعاينة: سيتم حذف {$totalCleaned} طلب إجمالي");
            $this->info("💡 لتنفيذ الحذف الفعلي، قم بتشغيل الأمر بدون --dry-run");
        } else {
            $this->info("🎉 تم الانتهاء! تم تنظيف {$totalCleaned} طلب إجمالي");
        }

        // إحصائيات نهائية
        $validApplications = Application::whereHas('job')->whereHas('jobSeeker.user')->count();
        $this->info("📈 الطلبات الصحيحة المتبقية: {$validApplications}");

        return Command::SUCCESS;
    }
}
