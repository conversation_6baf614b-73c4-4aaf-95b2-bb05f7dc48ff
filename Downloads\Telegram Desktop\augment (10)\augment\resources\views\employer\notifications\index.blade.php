@extends('layouts.employer')

@section('title', 'الإشعارات')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">الإشعارات</h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">
                    @if($unreadCount > 0)
                        لديك {{ $unreadCount }} إشعار غير مقروء
                    @else
                        جميع الإشعارات مقروءة
                    @endif
                </p>
            </div>

            <div class="flex gap-3 mt-4 md:mt-0">
                @if($unreadCount > 0)
                <form action="{{ route('employer.notifications.mark-all-read') }}" method="POST" class="inline">
                    @csrf
                    <button type="submit" class="px-4 py-2 bg-gradient-to-r from-yellow-600 to-orange-600 text-white rounded-lg hover:from-yellow-700 hover:to-orange-700 transition-all duration-300 flex items-center gap-2 shadow-lg">
                        <i class="fas fa-check-double"></i>
                        تحديد الكل كمقروء
                    </button>
                </form>
                @endif

                <button onclick="toggleFilters()" class="px-4 py-2 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-lg hover:from-gray-700 hover:to-gray-800 transition-all duration-300 flex items-center gap-2 shadow-lg filter-button relative">
                    <i class="fas fa-filter"></i>
                    فلترة
                    @if(request('search') || request('type') || request('is_read'))
                        <span class="absolute -top-1 -right-1 w-3 h-3 bg-orange-500 rounded-full animate-pulse"></span>
                    @endif
                </button>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="stat-card p-6 rounded-xl fade-in stagger-item">
                <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center icon-bounce">
                        <i class="fas fa-bell text-white"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 dark:text-gray-400">إجمالي الإشعارات</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $notifications->total() }}</p>
                    </div>
                </div>
            </div>

            <div class="stat-card p-6 rounded-xl fade-in stagger-item {{ $unreadCount > 0 ? 'pulse-new' : '' }}">
                <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-exclamation-circle text-white"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 dark:text-gray-400">غير مقروءة</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $unreadCount }}</p>
                    </div>
                </div>
            </div>

            <div class="stat-card p-6 rounded-xl fade-in stagger-item">
                <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check-circle text-white"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 dark:text-gray-400">مقروءة</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $notifications->total() - $unreadCount }}</p>
                    </div>
                </div>
            </div>

            <div class="stat-card p-6 rounded-xl fade-in stagger-item">
                <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-calendar-day text-white"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 dark:text-gray-400">اليوم</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $notifications->where('created_at', '>=', today())->count() }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Filters Display -->
        @if(request('search') || request('type') || request('is_read'))
        <div class="mb-6">
            <div class="flex flex-wrap items-center gap-2">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">الفلاتر النشطة:</span>

                @if(request('search'))
                <span class="inline-flex items-center gap-1 px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-sm rounded-full">
                    <i class="fas fa-search text-xs"></i>
                    "{{ request('search') }}"
                    <a href="{{ request()->fullUrlWithQuery(['search' => null]) }}" class="ml-1 text-blue-600 hover:text-blue-800">
                        <i class="fas fa-times text-xs"></i>
                    </a>
                </span>
                @endif

                @if(request('type'))
                <span class="inline-flex items-center gap-1 px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 text-sm rounded-full">
                    <i class="fas fa-tag text-xs"></i>
                    {{ getNotificationTypeInArabic(request('type')) }}
                    <a href="{{ request()->fullUrlWithQuery(['type' => null]) }}" class="ml-1 text-green-600 hover:text-green-800">
                        <i class="fas fa-times text-xs"></i>
                    </a>
                </span>
                @endif

                @if(request('is_read'))
                <span class="inline-flex items-center gap-1 px-3 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 text-sm rounded-full">
                    <i class="fas fa-eye{{ request('is_read') == '1' ? '' : '-slash' }} text-xs"></i>
                    {{ request('is_read') == '1' ? 'مقروءة' : 'غير مقروءة' }}
                    <a href="{{ request()->fullUrlWithQuery(['is_read' => null]) }}" class="ml-1 text-purple-600 hover:text-purple-800">
                        <i class="fas fa-times text-xs"></i>
                    </a>
                </span>
                @endif

                <a href="{{ route('employer.notifications.index') }}" class="inline-flex items-center gap-1 px-3 py-1 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 text-sm rounded-full hover:bg-red-200 dark:hover:bg-red-900/50 transition-colors">
                    <i class="fas fa-times text-xs"></i>
                    إزالة جميع الفلاتر
                </a>
            </div>
        </div>
        @endif

        <!-- Search and Filters -->
        <div id="filterPanel" class="hidden mb-8">
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <form method="GET" action="{{ route('employer.notifications.index') }}" class="flex flex-col md:flex-row gap-4">
                    <div class="flex-1">
                        <div class="relative">
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                            <input type="text" name="search" id="search" value="{{ request('search') }}"
                                   class="block w-full pr-10 p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base"
                                   placeholder="البحث في الإشعارات...">
                        </div>
                    </div>
                    <div class="w-full md:w-48">
                        <select name="type" id="type"
                                class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                            <option value="">جميع الأنواع</option>
                            <option value="job_application_received" {{ request('type') == 'job_application_received' ? 'selected' : '' }}>طلب توظيف جديد</option>
                            <option value="application_status_update" {{ request('type') == 'application_status_update' ? 'selected' : '' }}>تحديث حالة طلب</option>
                            <option value="profile_rated" {{ request('type') == 'profile_rated' ? 'selected' : '' }}>تقييم ملف شخصي</option>
                            <option value="company_rated" {{ request('type') == 'company_rated' ? 'selected' : '' }}>تقييم شركة</option>
                            <option value="system_announcement" {{ request('type') == 'system_announcement' ? 'selected' : '' }}>إعلان النظام</option>
                            <option value="job_recommendation" {{ request('type') == 'job_recommendation' ? 'selected' : '' }}>توصية وظيفة</option>
                        </select>
                    </div>
                    <div class="w-full md:w-48">
                        <select name="is_read" id="is_read"
                                class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                            <option value="">جميع الحالات</option>
                            <option value="0" {{ request('is_read') === '0' ? 'selected' : '' }}>غير مقروءة</option>
                            <option value="1" {{ request('is_read') === '1' ? 'selected' : '' }}>مقروءة</option>
                        </select>
                    </div>
                    <div class="flex gap-2">
                        <button type="submit"
                                class="px-4 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 flex items-center gap-2 shadow-lg">
                            <i class="fas fa-search"></i>
                            بحث
                        </button>
                        <a href="{{ route('employer.notifications.index') }}"
                           class="px-4 py-2.5 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-lg hover:from-gray-700 hover:to-gray-800 transition-all duration-300 flex items-center gap-2 shadow-lg">
                            <i class="fas fa-times"></i>
                            إعادة تعيين
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Notifications List -->
        <div class="glass-card rounded-xl overflow-hidden">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between">
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">قائمة الإشعارات</h2>
                    <div class="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                        @if(request('search') || request('type') || request('is_read'))
                            <div class="flex items-center gap-2">
                                <i class="fas fa-filter text-blue-500"></i>
                                <span>مفلترة: {{ $notifications->total() }} من أصل {{ \App\Models\Notification::where('user_id', Auth::id())->count() }}</span>
                                @if(request('search'))
                                    <span class="text-xs bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 px-2 py-1 rounded">
                                        البحث: "{{ request('search') }}"
                                    </span>
                                @endif
                                <a href="{{ route('employer.notifications.index') }}" class="text-blue-500 hover:text-blue-700 underline">
                                    إزالة الفلاتر
                                </a>
                            </div>
                        @else
                            <div class="flex items-center gap-4">
                                <span>{{ $notifications->total() }} إشعار</span>
                                @if($notifications->total() > 0)
                                    <span class="text-xs text-gray-400">
                                        آخر تحديث: {{ $notifications->first()->created_at->diffForHumans() }}
                                    </span>
                                @endif
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <div class="divide-y divide-gray-200 dark:divide-gray-700 notification-list">
                @forelse($notifications as $notification)
                <div class="p-6 notification-item fade-in stagger-item {{ $notification->is_read ? '' : 'bg-blue-50 dark:bg-blue-900/20 unread' }}">
                    <div class="flex items-start gap-4">
                        <!-- Icon -->
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 rounded-full {{ $notification->is_read ? 'bg-gray-100 dark:bg-gray-700' : 'bg-blue-100 dark:bg-blue-900/30' }} flex items-center justify-center">
                                <i class="fas fa-{{ getNotificationIcon($notification->type) }} {{ $notification->is_read ? 'text-gray-500' : 'text-blue-600 dark:text-blue-400' }}"></i>
                            </div>
                        </div>

                        <!-- Content -->
                        <div class="flex-1 min-w-0">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <p class="text-gray-900 dark:text-white {{ $notification->is_read ? '' : 'font-semibold' }}">
                                        {{ $notification->message }}
                                    </p>
                                    <div class="flex items-center gap-4 mt-2 text-sm text-gray-500 dark:text-gray-400">
                                        <span>{{ $notification->created_at->diffForHumans() }}</span>
                                        <span class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">{{ getNotificationTypeInArabic($notification->type) }}</span>
                                        @if(!$notification->is_read)
                                        <span class="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded text-xs">جديد</span>
                                        @endif
                                    </div>
                                </div>

                                <!-- Actions -->
                                <div class="flex items-center gap-2 ml-4">
                                    @if(!$notification->is_read)
                                    <span class="w-2 h-2 bg-blue-600 rounded-full"></span>
                                    @endif

                                    <div class="relative" x-data="{ open: false }">
                                        <button @click="open = !open"
                                                @keydown.enter="open = !open"
                                                @keydown.space.prevent="open = !open"
                                                :aria-expanded="open"
                                                aria-haspopup="true"
                                                aria-label="خيارات الإشعار"
                                                class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>

                                        <div x-show="open"
                                             x-cloak
                                             @click.away="open = false"
                                             x-transition:enter="transition ease-out duration-200"
                                             x-transition:enter-start="opacity-0 scale-95"
                                             x-transition:enter-end="opacity-100 scale-100"
                                             x-transition:leave="transition ease-in duration-150"
                                             x-transition:leave-start="opacity-100 scale-100"
                                             x-transition:leave-end="opacity-0 scale-95"
                                             role="menu"
                                             aria-label="خيارات الإشعار"
                                             class="absolute left-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-[9999] dropdown-menu">
                                            @if($notification->link)
                                            <a href="{{ route('employer.notifications.mark-read', $notification) }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-t-lg">
                                                <i class="fas fa-external-link-alt ml-2"></i>
                                                عرض التفاصيل
                                            </a>
                                            @endif

                                            @if(!$notification->is_read)
                                            <form action="{{ route('employer.notifications.mark-read', $notification) }}" method="POST" class="block">
                                                @csrf
                                                <button type="submit" class="w-full text-right px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                                    <i class="fas fa-check ml-2"></i>
                                                    تحديد كمقروء
                                                </button>
                                            </form>
                                            @endif

                                            <form action="{{ route('employer.notifications.destroy', $notification) }}" method="POST" class="block">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="w-full text-right px-4 py-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-b-lg" onclick="return confirm('هل أنت متأكد من حذف هذا الإشعار؟')">
                                                    <i class="fas fa-trash ml-2"></i>
                                                    حذف
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @empty
                <div class="text-center py-12">
                    <div class="w-24 h-24 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                        <i class="fas fa-bell-slash text-3xl text-gray-400"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">لا توجد إشعارات</h3>
                    <p class="text-gray-500 dark:text-gray-400">ستظهر الإشعارات هنا عند وصولها</p>
                </div>
                @endforelse
            </div>
        </div>

        <!-- Pagination -->
        @if($notifications->hasPages())
        <div class="mt-8">
            <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
                <div class="text-sm text-gray-700 dark:text-gray-300">
                    عرض {{ $notifications->firstItem() }} إلى {{ $notifications->lastItem() }} من أصل {{ $notifications->total() }} إشعار
                </div>
                <div class="flex justify-center">
                    {{ $notifications->appends(request()->query())->links() }}
                </div>
            </div>
        </div>
        @endif
    </div>
</div>

<script>
function toggleFilters() {
    const filterPanel = document.getElementById('filterPanel');
    const isHidden = filterPanel.classList.contains('hidden');

    if (isHidden) {
        filterPanel.classList.remove('hidden');
        filterPanel.style.opacity = '0';
        filterPanel.style.transform = 'translateY(-10px)';

        requestAnimationFrame(() => {
            filterPanel.style.transition = 'all 0.3s ease-out';
            filterPanel.style.opacity = '1';
            filterPanel.style.transform = 'translateY(0)';
        });
    } else {
        filterPanel.style.transition = 'all 0.3s ease-in';
        filterPanel.style.opacity = '0';
        filterPanel.style.transform = 'translateY(-10px)';

        setTimeout(() => {
            filterPanel.classList.add('hidden');
        }, 300);
    }
}

// Auto-hide filter panel when clicking outside
document.addEventListener('click', function(event) {
    const filterPanel = document.getElementById('filterPanel');
    const filterButton = event.target.closest('button[onclick="toggleFilters()"]');

    if (!filterPanel.contains(event.target) && !filterButton && !filterPanel.classList.contains('hidden')) {
        toggleFilters();
    }
});

// Enhanced filter functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search');
    const typeFilter = document.getElementById('type');
    const readFilter = document.getElementById('is_read');
    const filterForm = document.querySelector('#filterPanel form');

    // Load saved filter preferences
    loadFilterPreferences();

    // Auto-submit form on filter change
    if (typeFilter && readFilter) {
        [typeFilter, readFilter].forEach(element => {
            element.addEventListener('change', () => {
                saveFilterPreferences();
                filterForm.submit();
            });
        });
    }

    // Search as you type (with debounce)
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);

            // Add visual feedback
            this.classList.add('search-loading');

            searchTimeout = setTimeout(() => {
                this.classList.remove('search-loading');
                if (this.value.length >= 3 || this.value.length === 0) {
                    saveFilterPreferences();
                    filterForm.submit();
                }
            }, 500); // Wait 500ms after user stops typing
        });
    }

    // Add loading state to form
    filterForm.addEventListener('submit', function() {
        const submitButton = this.querySelector('button[type="submit"]');
        if (submitButton) {
            const originalText = submitButton.innerHTML;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري البحث...';
            submitButton.disabled = true;

            // Re-enable after 3 seconds (fallback)
            setTimeout(() => {
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
            }, 3000);
        }
    });

    // Highlight search terms in results
    const searchTerm = searchInput?.value;
    if (searchTerm && searchTerm.length > 0) {
        highlightSearchTerms(searchTerm);
    }

    // Add filter panel animation classes
    const filterPanel = document.getElementById('filterPanel');
    if (filterPanel) {
        filterPanel.addEventListener('transitionstart', function() {
            if (!this.classList.contains('hidden')) {
                this.classList.add('slide-down');
            }
        });
    }
});

// Save filter preferences to localStorage
function saveFilterPreferences() {
    const preferences = {
        search: document.getElementById('search')?.value || '',
        type: document.getElementById('type')?.value || '',
        is_read: document.getElementById('is_read')?.value || ''
    };
    localStorage.setItem('notifications_filter_preferences', JSON.stringify(preferences));
}

// Load filter preferences from localStorage
function loadFilterPreferences() {
    try {
        const saved = localStorage.getItem('notifications_filter_preferences');
        if (saved) {
            const preferences = JSON.parse(saved);

            // Only load if current URL doesn't have parameters (to avoid conflicts)
            const urlParams = new URLSearchParams(window.location.search);
            if (!urlParams.has('search') && !urlParams.has('type') && !urlParams.has('is_read')) {
                if (preferences.search && document.getElementById('search')) {
                    document.getElementById('search').value = preferences.search;
                }
                if (preferences.type && document.getElementById('type')) {
                    document.getElementById('type').value = preferences.type;
                }
                if (preferences.is_read && document.getElementById('is_read')) {
                    document.getElementById('is_read').value = preferences.is_read;
                }
            }
        }
    } catch (e) {
        console.log('Could not load filter preferences:', e);
    }
}

function highlightSearchTerms(term) {
    const notificationItems = document.querySelectorAll('.notification-item p');
    notificationItems.forEach(item => {
        const text = item.textContent;
        const highlightedText = text.replace(
            new RegExp(`(${term})`, 'gi'),
            '<mark class="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">$1</mark>'
        );
        if (highlightedText !== text) {
            item.innerHTML = highlightedText;
        }
    });
}

// Auto-refresh notifications every 30 seconds
setInterval(function() {
    fetch(window.location.href, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.text())
    .then(html => {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const newUnreadCount = doc.querySelector('.text-2xl')?.textContent?.trim();

        const currentCount = document.querySelector('.text-2xl')?.textContent?.trim();
        if (currentCount && newUnreadCount && currentCount !== newUnreadCount) {
            showNewNotificationIndicator();
        }
    })
    .catch(error => console.log('Auto-refresh failed:', error));
}, 30000);

function showNewNotificationIndicator() {
    const indicator = document.createElement('div');
    indicator.className = 'fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
    indicator.innerHTML = '<i class="fas fa-bell mr-2"></i>إشعارات جديدة متاحة';

    document.body.appendChild(indicator);

    setTimeout(() => {
        indicator.classList.remove('translate-x-full');
    }, 100);

    setTimeout(() => {
        indicator.classList.add('translate-x-full');
        setTimeout(() => {
            indicator.remove();
        }, 300);
    }, 3000);

    indicator.addEventListener('click', () => {
        window.location.reload();
    });
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + F to focus search
    if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
        e.preventDefault();
        const searchInput = document.getElementById('search');
        if (searchInput) {
            const filterPanel = document.getElementById('filterPanel');
            if (filterPanel.classList.contains('hidden')) {
                toggleFilters();
            }
            setTimeout(() => {
                searchInput.focus();
                searchInput.select();
            }, 300);
        }
    }

    // Escape to close filter panel
    if (e.key === 'Escape') {
        const filterPanel = document.getElementById('filterPanel');
        if (!filterPanel.classList.contains('hidden')) {
            toggleFilters();
        }
    }
});
</script>

@push('styles')
<style>
/* Additional page-specific styles */
.notification-list {
    max-height: 70vh;
    overflow-y: auto;
}

/* Filter panel animation */
#filterPanel {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Notification item click effect */
.notification-item {
    cursor: pointer;
}

.notification-item:active {
    transform: scale(0.98);
}

/* Enhanced form styling */
.form-input {
    transition: all 0.3s ease;
    border: 1px solid #d1d5db;
    background: #f9fafb;
}

.form-input:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: #ffffff;
}

.dark .form-input {
    border-color: #374151;
    background: #1f2937;
    color: #ffffff;
}

.dark .form-input:focus {
    border-color: #60a5fa;
    background: #111827;
}

/* Search input enhancements */
.search-input-container {
    position: relative;
}

.search-input-container::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6);
    transition: width 0.3s ease;
}

.search-input-container:focus-within::after {
    width: 100%;
}

/* Filter button states */
.filter-button {
    position: relative;
    overflow: hidden;
}

.filter-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.filter-button:hover::before {
    left: 100%;
}

/* Loading state for search */
.search-loading {
    position: relative;
}

.search-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}

/* Highlight search results */
mark {
    background: linear-gradient(120deg, #fbbf24 0%, #f59e0b 100%);
    color: #1f2937;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: 600;
}

.dark mark {
    background: linear-gradient(120deg, #d97706 0%, #b45309 100%);
    color: #ffffff;
}

/* Filter panel slide animation */
#filterPanel.slide-down {
    animation: slideDown 0.3s ease-out;
}

#filterPanel.slide-up {
    animation: slideUp 0.3s ease-in;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

/* Mobile filter improvements */
@media (max-width: 768px) {
    #filterPanel form {
        flex-direction: column;
    }

    #filterPanel .flex {
        flex-direction: column;
        width: 100%;
    }

    #filterPanel button {
        width: 100%;
        justify-content: center;
    }
}
</style>
@endpush
@endsection
