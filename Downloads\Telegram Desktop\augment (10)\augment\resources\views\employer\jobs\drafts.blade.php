@extends('layouts.employer')

@section('title', 'المسودات')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="glass-card rounded-xl p-6">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
                    <i class="fas fa-file-alt text-yellow-600"></i>
                    المسودات
                </h1>
                <p class="text-gray-600 dark:text-gray-400">إدارة مسودات الوظائف الخاصة بك</p>
            </div>

            <div class="flex flex-col sm:flex-row gap-3">
                <a href="{{ route('employer.jobs.index') }}"
                   class="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-300 flex items-center gap-2">
                    <i class="fas fa-list"></i>
                    الوظائف المنشورة
                </a>
                <a href="{{ route('employer.jobs.create') }}"
                   class="px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-300 flex items-center gap-2">
                    <i class="fas fa-plus"></i>
                    إنشاء وظيفة جديدة
                </a>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="glass-card rounded-xl p-6">
        <form method="GET" action="{{ route('employer.jobs.drafts') }}" class="flex flex-col md:flex-row gap-4">
            <div class="flex-1">
                <div class="relative">
                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    <input type="text"
                           name="search"
                           value="{{ request('search') }}"
                           placeholder="البحث في المسودات..."
                           class="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                </div>
            </div>
            <button type="submit"
                    class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-300 flex items-center gap-2">
                <i class="fas fa-search"></i>
                بحث
            </button>
        </form>
    </div>

    <!-- Jobs List -->
    @if($jobs->count() > 0)
        <div class="space-y-4">
            @foreach($jobs as $job)
                <div class="glass-card rounded-xl p-6 hover:shadow-lg transition-all duration-300 border-l-4 border-yellow-500">
                    <div class="flex flex-col lg:flex-row justify-between items-start gap-4">
                        <div class="flex-1">
                            <div class="flex items-start gap-4">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center">
                                        <i class="fas fa-file-alt text-white text-lg"></i>
                                    </div>
                                </div>

                                <div class="flex-1">
                                    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">
                                        {{ $job->title ?: 'مسودة بدون عنوان' }}
                                    </h3>

                                    <div class="flex flex-wrap items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mb-3">
                                        @if($job->category)
                                            <span class="flex items-center gap-1">
                                                <i class="fas fa-tag"></i>
                                                {{ $job->category->name }}
                                            </span>
                                        @endif

                                        @if($job->location)
                                            <span class="flex items-center gap-1">
                                                <i class="fas fa-map-marker-alt"></i>
                                                {{ $job->location }}
                                            </span>
                                        @endif

                                        <span class="flex items-center gap-1">
                                            <i class="fas fa-clock"></i>
                                            {{ $job->created_at->diffForHumans() }}
                                        </span>
                                    </div>

                                    @if($job->description)
                                        <p class="text-gray-700 dark:text-gray-300 line-clamp-2">
                                            {{ Str::limit(strip_tags($job->description), 150) }}
                                        </p>
                                    @else
                                        <p class="text-gray-500 dark:text-gray-400 italic">لم يتم إضافة وصف للوظيفة</p>
                                    @endif
                                </div>
                                </div>
                            </div>

                        <div class="flex flex-col sm:flex-row gap-3">
                            <a href="{{ route('employer.jobs.edit', $job) }}"
                               class="px-4 py-2 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-all duration-300 flex items-center gap-2">
                                <i class="fas fa-edit"></i>
                                تحرير
                            </a>

                            @if($job->title && $job->description && $job->category_id)
                                <form action="{{ route('employer.jobs.publish', $job) }}" method="POST" class="inline">
                                    @csrf
                                    @method('PATCH')
                                    <button type="submit"
                                            class="px-4 py-2 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/50 transition-all duration-300 flex items-center gap-2"
                                            onclick="return confirm('هل أنت متأكد من نشر هذه الوظيفة؟')">
                                        <i class="fas fa-paper-plane"></i>
                                        نشر
                                    </button>
                                </form>
                            @else
                                <span class="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 rounded-lg flex items-center gap-2"
                                      title="يجب إكمال البيانات المطلوبة قبل النشر">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    غير مكتمل
                                </span>
                            @endif

                            <form action="{{ route('employer.jobs.destroy', $job) }}" method="POST" class="inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit"
                                        class="px-4 py-2 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 rounded-lg hover:bg-red-200 dark:hover:bg-red-900/50 transition-all duration-300 flex items-center gap-2"
                                        onclick="return confirm('هل أنت متأكد من حذف هذه المسودة؟')">
                                    <i class="fas fa-trash"></i>
                                    حذف
                                </button>
                            </form>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            @if($jobs->hasPages())
                <div class="mt-8">
                    {{ $jobs->appends(request()->query())->links() }}
                </div>
            @endif
    @else
        <!-- Empty State -->
        <div class="glass-card rounded-xl p-12 text-center">
            <div class="w-24 h-24 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-file-alt text-white text-3xl"></i>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">لا توجد مسودات</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-8">لم تقم بحفظ أي مسودات بعد. ابدأ بإنشاء وظيفة جديدة واحفظها كمسودة.</p>
            <a href="{{ route('employer.jobs.create') }}"
               class="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-300">
                <i class="fas fa-plus"></i>
                إنشاء وظيفة جديدة
            </a>
        </div>
    @endif

    <!-- Pagination -->
    @if($jobs->hasPages())
        <div class="mt-6">
            {{ $jobs->appends(request()->query())->links() }}
        </div>
    @endif
</div>
@endsection
