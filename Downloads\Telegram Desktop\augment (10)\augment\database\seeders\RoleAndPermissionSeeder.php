<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RoleAndPermissionSeeder extends Seeder
{
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // Job permissions
            'create jobs',
            'edit jobs',
            'delete jobs',
            'view jobs',
            
            // Application permissions
            'create applications',
            'view applications',
            'manage applications',
            
            // Profile permissions
            'edit profile',
            'view profiles',
            'rate profiles',
            
            // Company permissions
            'edit company',
            'view companies',
            'rate companies',
            
            // Report permissions
            'view reports',
            'export data',
            
            // Payment permissions
            'make payments',
            'view payment history',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles and assign permissions
        $jobSeekerRole = Role::firstOrCreate(['name' => 'job_seeker']);
        $jobSeekerRole->syncPermissions([
            'view jobs',
            'create applications',
            'view applications',
            'edit profile',
            'view profiles',
            'view companies',
            'rate companies',
        ]);

        $employerRole = Role::firstOrCreate(['name' => 'employer']);
        $employerRole->syncPermissions([
            'create jobs',
            'edit jobs',
            'delete jobs',
            'view jobs',
            'view applications',
            'manage applications',
            'edit company',
            'view profiles',
            'rate profiles',
            'view reports',
            'export data',
            'make payments',
            'view payment history',
        ]);

        // Create admin role with all permissions
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $adminRole->syncPermissions(Permission::all());
    }
}