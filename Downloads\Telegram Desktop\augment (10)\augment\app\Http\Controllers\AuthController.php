<?php

namespace App\Http\Controllers;

use App\Models\Employer;
use App\Models\JobSeeker;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;
use Lara<PERSON>\Socialite\Facades\Socialite;
use Exception;

class AuthController extends Controller
{
    public function showRegisterForm()
    {
        return view('auth.register');
    }

    public function register(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => ['required', 'confirmed', Password::defaults()],
            'role' => 'required|in:job_seeker,employer',
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->role,
        ]);

        if ($request->role === 'job_seeker') {
            JobSeeker::create(['user_id' => $user->id]);
        } else {
            Employer::create([
                'user_id' => $user->id,
                'company_name' => $request->name . ' - شركة'
            ]);
        }

        Auth::login($user);

        // توجيه المستخدم بناءً على دوره
        if ($user->role === 'job_seeker') {
            return redirect()->route('job-seeker.profile');
        } else if ($user->role === 'employer') {
            return redirect()->route('employer.dashboard');
        } else {
            return redirect()->route('home');
        }
    }

    public function showLoginForm()
    {
        return view('auth.login');
    }

    public function login(Request $request)
    {
        $credentials = $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        if (Auth::attempt($credentials, $request->remember)) {
            $request->session()->regenerate();

            // توجيه المستخدم بناءً على دوره
            $user = Auth::user();
            if ($user->role === 'job_seeker') {
                return redirect()->intended(route('job-seeker.profile'));
            } else if ($user->role === 'employer') {
                return redirect()->intended(route('employer.dashboard'));
            } else if ($user->role === 'admin') {
                return redirect()->intended(route('admin.dashboard'));
            } else {
                return redirect()->intended(route('home'));
            }
        }

        return back()->withErrors([
            'email' => 'The provided credentials do not match our records.',
        ])->onlyInput('email');
    }

    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        return redirect('/');
    }

    /**
     * توجيه المستخدم إلى صفحة المصادقة الخاصة بالمزود
     */
    public function redirectToProvider($provider)
    {
        try {
            // التحقق من أن المزود مدعوم
            if (!in_array($provider, ['google', 'facebook', 'linkedin'])) {
                return redirect()->route('login')
                    ->withErrors(['error' => 'مزود المصادقة غير مدعوم.']);
            }

            // إعدادات خاصة لـ LinkedIn
            if ($provider === 'linkedin') {
                return Socialite::driver($provider)
                    ->scopes(['r_liteprofile', 'r_emailaddress'])
                    ->redirect();
            }

            return Socialite::driver($provider)->redirect();
        } catch (Exception $e) {
            return redirect()->route('login')
                ->withErrors(['error' => 'حدث خطأ أثناء توجيهك إلى ' . $provider . '. الرجاء المحاولة مرة أخرى.']);
        }
    }

    /**
     * الحصول على معلومات المستخدم من المزود
     */
    public function handleProviderCallback($provider)
    {
        try {
            // التحقق من أن المزود مدعوم
            if (!in_array($provider, ['google', 'facebook', 'linkedin'])) {
                return redirect()->route('login')
                    ->withErrors(['error' => 'مزود المصادقة غير مدعوم.']);
            }

            // الحصول على بيانات المستخدم من المزود
            $socialUser = Socialite::driver($provider)->user();

            // التحقق من وجود البريد الإلكتروني
            if (!$socialUser->getEmail()) {
                return redirect()->route('login')
                    ->withErrors(['error' => 'لم نتمكن من الحصول على بريدك الإلكتروني من ' . $provider . '. الرجاء المحاولة مرة أخرى.']);
            }

            // البحث عن المستخدم في قاعدة البيانات بناءً على معرف المزود
            $user = User::where('provider', $provider)
                        ->where('provider_id', $socialUser->getId())
                        ->first();

            // إذا لم يتم العثور على المستخدم، قم بإنشاء مستخدم جديد
            if (!$user) {
                // التحقق مما إذا كان البريد الإلكتروني موجودًا بالفعل
                $existingUser = User::where('email', $socialUser->getEmail())->first();

                if ($existingUser) {
                    // تحديث معلومات المزود للمستخدم الموجود
                    $existingUser->update([
                        'provider' => $provider,
                        'provider_id' => $socialUser->getId(),
                        'avatar' => $socialUser->getAvatar(),
                    ]);

                    $user = $existingUser;
                } else {
                    // إنشاء مستخدم جديد
                    $user = User::create([
                        'name' => $socialUser->getName() ?: 'مستخدم جديد',
                        'email' => $socialUser->getEmail(),
                        'password' => Hash::make(uniqid()), // كلمة مرور عشوائية
                        'role' => 'job_seeker', // الدور الافتراضي
                        'provider' => $provider,
                        'provider_id' => $socialUser->getId(),
                        'avatar' => $socialUser->getAvatar(),
                        'email_verified_at' => now(), // تأكيد البريد الإلكتروني تلقائياً للمصادقة الاجتماعية
                    ]);

                    // إنشاء ملف باحث عن عمل
                    JobSeeker::create(['user_id' => $user->id]);
                }
            }

            // تسجيل دخول المستخدم
            Auth::login($user, true); // تذكر المستخدم

            // رسالة ترحيب
            session()->flash('success', 'مرحباً بك! تم تسجيل دخولك بنجاح باستخدام ' . ucfirst($provider));

            // توجيه المستخدم بناءً على دوره
            if ($user->role === 'job_seeker') {
                return redirect()->route('job-seeker.profile');
            } else if ($user->role === 'employer') {
                return redirect()->route('employer.dashboard');
            } else if ($user->role === 'admin') {
                return redirect()->route('admin.dashboard');
            } else {
                return redirect()->route('home');
            }

        } catch (\Laravel\Socialite\Two\InvalidStateException $e) {
            return redirect()->route('login')
                ->withErrors(['error' => 'انتهت صلاحية جلسة المصادقة. الرجاء المحاولة مرة أخرى.']);
        } catch (\GuzzleHttp\Exception\ClientException $e) {
            return redirect()->route('login')
                ->withErrors(['error' => 'فشل في الاتصال مع ' . $provider . '. الرجاء التحقق من إعدادات التطبيق.']);
        } catch (Exception $e) {
            \Log::error('Social login error: ' . $e->getMessage(), [
                'provider' => $provider,
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('login')
                ->withErrors(['error' => 'حدث خطأ أثناء تسجيل الدخول باستخدام ' . $provider . '. الرجاء المحاولة مرة أخرى.']);
        }
    }
}
