<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الصور - Hire Me</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">اختبار عرض الصور</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- Default Avatars -->
            <div class="bg-white rounded-lg p-6 shadow-lg">
                <h2 class="text-xl font-semibold mb-4">الصور الافتراضية</h2>
                
                <div class="space-y-4">
                    <div class="flex items-center gap-4">
                        <img src="{{ defaultAvatar('أحمد محمد') }}" alt="أحمد محمد" class="w-16 h-16 rounded-full">
                        <div>
                            <p class="font-medium">أحمد محمد</p>
                            <p class="text-sm text-gray-600">صورة افتراضية بالأحرف الأولى</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center gap-4">
                        <img src="{{ defaultAvatar('فاطمة علي') }}" alt="فاطمة علي" class="w-16 h-16 rounded-full">
                        <div>
                            <p class="font-medium">فاطمة علي</p>
                            <p class="text-sm text-gray-600">صورة افتراضية بالأحرف الأولى</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center gap-4">
                        <img src="{{ defaultAvatar('') }}" alt="مستخدم" class="w-16 h-16 rounded-full">
                        <div>
                            <p class="font-medium">مستخدم بدون اسم</p>
                            <p class="text-sm text-gray-600">صورة افتراضية عامة</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Storage URLs -->
            <div class="bg-white rounded-lg p-6 shadow-lg">
                <h2 class="text-xl font-semibold mb-4">مسارات Storage</h2>
                
                <div class="space-y-4">
                    <div>
                        <p class="font-medium">مسار عادي:</p>
                        <code class="text-sm bg-gray-100 p-2 rounded block">{{ storageUrl('avatars/test.jpg') }}</code>
                    </div>
                    
                    <div>
                        <p class="font-medium">مسار بـ public/:</p>
                        <code class="text-sm bg-gray-100 p-2 rounded block">{{ storageUrl('public/avatars/test.jpg') }}</code>
                    </div>
                    
                    <div>
                        <p class="font-medium">مسار HTTP كامل:</p>
                        <code class="text-sm bg-gray-100 p-2 rounded block">{{ storageUrl('https://example.com/image.jpg') }}</code>
                    </div>
                    
                    <div>
                        <p class="font-medium">مسار فارغ:</p>
                        <code class="text-sm bg-gray-100 p-2 rounded block">{{ storageUrl('') ?? 'null' }}</code>
                    </div>
                </div>
            </div>
            
            <!-- Test Images -->
            <div class="bg-white rounded-lg p-6 shadow-lg">
                <h2 class="text-xl font-semibold mb-4">اختبار الصور</h2>
                
                <div class="space-y-4">
                    <div>
                        <p class="font-medium mb-2">صورة من storage (إذا كانت موجودة):</p>
                        <img src="{{ storageUrl('test-image.jpg') }}" alt="صورة اختبار" class="w-32 h-32 object-cover rounded-lg border" 
                             onerror="this.src='{{ defaultAvatar('Test') }}'; this.classList.add('rounded-full');">
                    </div>
                    
                    <div>
                        <p class="font-medium mb-2">صورة شعار (إذا كانت موجودة):</p>
                        <img src="{{ storageUrl('logos/company-logo.png') }}" alt="شعار الشركة" class="w-32 h-32 object-cover rounded-lg border"
                             onerror="this.src='{{ defaultAvatar('Company') }}'; this.classList.add('rounded-full');">
                    </div>
                </div>
            </div>
            
            <!-- Helper Functions -->
            <div class="bg-white rounded-lg p-6 shadow-lg">
                <h2 class="text-xl font-semibold mb-4">دوال المساعدة</h2>
                
                <div class="space-y-4">
                    <div>
                        <p class="font-medium">تنسيق الراتب:</p>
                        <p class="text-sm">{{ formatSalary(1000, 2000, 'LYD') }}</p>
                        <p class="text-sm">{{ formatSalary(500, null, 'USD') }}</p>
                        <p class="text-sm">{{ formatSalary(null, 1500, 'EUR') }}</p>
                    </div>
                    
                    <div>
                        <p class="font-medium">رموز العملات:</p>
                        <p class="text-sm">LYD: {{ getCurrencySymbol('LYD') }}</p>
                        <p class="text-sm">USD: {{ getCurrencySymbol('USD') }}</p>
                        <p class="text-sm">EUR: {{ getCurrencySymbol('EUR') }}</p>
                    </div>
                    
                    <div>
                        <p class="font-medium">أنواع الوظائف:</p>
                        <p class="text-sm">full_time: {{ getJobTypeInArabic('full_time') }}</p>
                        <p class="text-sm">remote: {{ getJobTypeInArabic('remote') }}</p>
                        <p class="text-sm">freelance: {{ getJobTypeInArabic('freelance') }}</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 class="font-semibold text-blue-900 mb-2">ملاحظات:</h3>
            <ul class="text-sm text-blue-800 space-y-1">
                <li>• إذا لم تظهر الصور من storage، تأكد من تشغيل: <code>php artisan storage:link-fix</code></li>
                <li>• الصور الافتراضية تستخدم خدمة UI Avatars الخارجية</li>
                <li>• يمكن الوصول للصور عبر: <code>/storage/filename</code></li>
                <li>• الصور المفقودة ستظهر كصور افتراضية تلقائياً</li>
            </ul>
        </div>
    </div>
</body>
</html>
