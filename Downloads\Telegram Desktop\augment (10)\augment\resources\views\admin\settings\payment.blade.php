@extends('layouts.admin')

@section('title', 'إعدادات الدفع والاشتراكات - Hire Me')
@section('header_title', 'إعدادات الدفع والاشتراكات')

@section('content')
<div class="p-6">
    <div class="mb-6">
        <h2 class="text-2xl font-bold">إعدادات الدفع والاشتراكات</h2>
        <p class="text-gray-600 dark:text-gray-400 mt-1">إدارة باقات الاشتراك وطرق الدفع والمعاملات المالية</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Current Subscription -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">الاشتراك الحالي</h3>
                
                <div class="mb-6">
                    <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800">
                        <div class="flex items-center gap-4">
                            <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                                <i class="fas fa-crown text-purple-600 dark:text-purple-400 text-xl"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-lg">{{ $subscription->plan_name ?? 'الباقة المتميزة' }}</h4>
                                <p class="text-gray-500 dark:text-gray-400 text-sm">تجدد في {{ $subscription->renewal_date ?? '1 فبراير، 2024' }}</p>
                            </div>
                        </div>
                        <div>
                            <span class="px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-sm">{{ $subscription->status ?? 'نشط' }}</span>
                        </div>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <p class="text-sm text-gray-500 dark:text-gray-400 mb-1">الفترة</p>
                        <p class="font-medium">{{ $subscription->billing_cycle ?? 'سنوي' }}</p>
                    </div>
                    
                    <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <p class="text-sm text-gray-500 dark:text-gray-400 mb-1">المبلغ</p>
                        <p class="font-medium">{{ $subscription->amount ?? '5,999' }} ريال / {{ $subscription->billing_cycle ?? 'سنة' }}</p>
                    </div>
                    
                    <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <p class="text-sm text-gray-500 dark:text-gray-400 mb-1">طريقة الدفع</p>
                        <div class="flex items-center gap-2">
                            <i class="fas fa-credit-card text-blue-500"></i>
                            <p class="font-medium">{{ $subscription->payment_method ?? 'بطاقة فيزا **** 4582' }}</p>
                        </div>
                    </div>
                </div>
                
                <div class="space-y-3">
                    <h4 class="font-medium">مميزات الباقة:</h4>
                    <ul class="space-y-2 text-gray-600 dark:text-gray-400">
                        <li class="flex items-center gap-2">
                            <i class="fas fa-check-circle text-green-500"></i>
                            <span>{{ $subscription->features->jobs_limit ?? 'عدد غير محدود من الوظائف' }}</span>
                        </li>
                        <li class="flex items-center gap-2">
                            <i class="fas fa-check-circle text-green-500"></i>
                            <span>{{ $subscription->features->featured_jobs ?? '5 وظائف مميزة شهريًا' }}</span>
                        </li>
                        <li class="flex items-center gap-2">
                            <i class="fas fa-check-circle text-green-500"></i>
                            <span>{{ $subscription->features->candidates_database ?? 'الوصول الكامل لقاعدة بيانات المرشحين' }}</span>
                        </li>
                        <li class="flex items-center gap-2">
                            <i class="fas fa-check-circle text-green-500"></i>
                            <span>{{ $subscription->features->analytics ?? 'تقارير وتحليلات متقدمة' }}</span>
                        </li>
                        <li class="flex items-center gap-2">
                            <i class="fas fa-check-circle text-green-500"></i>
                            <span>{{ $subscription->features->support ?? 'دعم فني على مدار الساعة' }}</span>
                        </li>
                    </ul>
                </div>
                
                <div class="flex flex-wrap gap-3 mt-6">
                    <button type="button" class="px-4 py-2 gradient-bg text-white rounded-lg hover:opacity-90 transition-colors">
                        ترقية الاشتراك
                    </button>
                    <button type="button" class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                        تغيير طريقة الدفع
                    </button>
                    <button type="button" class="px-4 py-2 border border-red-300 dark:border-red-800 text-red-600 dark:text-red-400 bg-white dark:bg-gray-800 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/10 transition-colors">
                        إلغاء الاشتراك
                    </button>
                </div>
            </div>
            
            <!-- Available Plans -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">باقات الاشتراك المتاحة</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Basic Plan -->
                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 {{ ($subscription->plan_id ?? '') == 'basic' ? 'ring-2 ring-primary' : '' }}">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <h4 class="font-bold text-lg">الأساسية</h4>
                                <p class="text-gray-500 dark:text-gray-400 text-sm">للشركات الصغيرة</p>
                            </div>
                            <div class="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                                <i class="fas fa-leaf text-green-600 dark:text-green-400"></i>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <p class="text-3xl font-bold">1,999<span class="text-lg font-normal text-gray-500 dark:text-gray-400"> ريال</span></p>
                            <p class="text-gray-500 dark:text-gray-400 text-sm">لكل سنة</p>
                        </div>
                        
                        <div class="mb-4">
                            <ul class="space-y-2 text-gray-600 dark:text-gray-400 text-sm">
                                <li class="flex items-center gap-2">
                                    <i class="fas fa-check text-green-500"></i>
                                    <span>10 وظائف شهريًا</span>
                                </li>
                                <li class="flex items-center gap-2">
                                    <i class="fas fa-check text-green-500"></i>
                                    <span>1 وظيفة مميزة شهريًا</span>
                                </li>
                                <li class="flex items-center gap-2">
                                    <i class="fas fa-check text-green-500"></i>
                                    <span>البحث الأساسي في قاعدة المرشحين</span>
                                </li>
                                <li class="flex items-center gap-2">
                                    <i class="fas fa-check text-green-500"></i>
                                    <span>تقارير أساسية</span>
                                </li>
                                <li class="flex items-center gap-2">
                                    <i class="fas fa-check text-green-500"></i>
                                    <span>دعم فني عبر البريد الإلكتروني</span>
                                </li>
                            </ul>
                        </div>
                        
                        <button type="button" class="w-full px-4 py-2 {{ ($subscription->plan_id ?? '') == 'basic' ? 'bg-gray-300 dark:bg-gray-700 cursor-not-allowed' : 'border border-primary text-primary bg-white dark:bg-gray-800 hover:bg-primary/10 transition-colors' }} rounded-lg text-center">
                            {{ ($subscription->plan_id ?? '') == 'basic' ? 'الباقة الحالية' : 'اختيار الباقة' }}
                        </button>
                    </div>
                    
                    <!-- Standard Plan -->
                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 {{ ($subscription->plan_id ?? '') == 'standard' ? 'ring-2 ring-primary' : '' }}">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <h4 class="font-bold text-lg">القياسية</h4>
                                <p class="text-gray-500 dark:text-gray-400 text-sm">للشركات المتوسطة</p>
                            </div>
                            <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                                <i class="fas fa-star text-blue-600 dark:text-blue-400"></i>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <p class="text-3xl font-bold">600<span class="text-lg font-normal text-gray-500 dark:text-gray-400"> دينار</span></p>
                            <p class="text-gray-500 dark:text-gray-400 text-sm">لكل سنة</p>
                        </div>
                        
                        <div class="mb-4">
                            <ul class="space-y-2 text-gray-600 dark:text-gray-400 text-sm">
                                <li class="flex items-center gap-2">
                                    <i class="fas fa-check text-green-500"></i>
                                    <span>30 وظيفة شهريًا</span>
                                </li>
                                <li class="flex items-center gap-2">
                                    <i class="fas fa-check text-green-500"></i>
                                    <span>3 وظائف مميزة شهريًا</span>
                                </li>
                                <li class="flex items-center gap-2">
                                    <i class="fas fa-check text-green-500"></i>
                                    <span>البحث المتقدم في قاعدة المرشحين</span>
                                </li>
                                <li class="flex items-center gap-2">
                                    <i class="fas fa-check text-green-500"></i>
                                    <span>تقارير وتحليلات متقدمة</span>
                                </li>
                                <li class="flex items-center gap-2">
                                    <i class="fas fa-check text-green-500"></i>
                                    <span>دعم فني عبر الهاتف والبريد</span>
                                </li>
                            </ul>
                        </div>
                        
                        <button type="button" class="w-full px-4 py-2 {{ ($subscription->plan_id ?? '') == 'standard' ? 'bg-gray-300 dark:bg-gray-700 cursor-not-allowed' : 'border border-primary text-primary bg-white dark:bg-gray-800 hover:bg-primary/10 transition-colors' }} rounded-lg text-center">
                            {{ ($subscription->plan_id ?? '') == 'standard' ? 'الباقة الحالية' : 'اختيار الباقة' }}
                        </button>
                    </div>
                    
                    <!-- Premium Plan -->
                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 {{ ($subscription->plan_id ?? 'premium') == 'premium' ? 'ring-2 ring-primary' : '' }}">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <h4 class="font-bold text-lg">المتميزة</h4>
                                <p class="text-gray-500 dark:text-gray-400 text-sm">للشركات الكبيرة</p>
                            </div>
                            <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                                <i class="fas fa-crown text-purple-600 dark:text-purple-400"></i>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <p class="text-3xl font-bold">5,999<span class="text-lg font-normal text-gray-500 dark:text-gray-400"> ريال</span></p>
                            <p class="text-gray-500 dark:text-gray-400 text-sm">لكل سنة</p>
                        </div>
                        
                        <div class="mb-4">
                            <ul class="space-y-2 text-gray-600 dark:text-gray-400 text-sm">
                                <li class="flex items-center gap-2">
                                    <i class="fas fa-check text-green-500"></i>
                                    <span>عدد غير محدود من الوظائف</span>
                                </li>
                                <li class="flex items-center gap-2">
                                    <i class="fas fa-check text-green-500"></i>
                                    <span>5 وظائف مميزة شهريًا</span>
                                </li>
                                <li class="flex items-center gap-2">
                                    <i class="fas fa-check text-green-500"></i>
                                    <span>الوصول الكامل لقاعدة المرشحين</span>
                                </li>
                                <li class="flex items-center gap-2">
                                    <i class="fas fa-check text-green-500"></i>
                                    <span>تقارير وتحليلات متقدمة</span>
                                </li>
                                <li class="flex items-center gap-2">
                                    <i class="fas fa-check text-green-500"></i>
                                    <span>دعم فني على مدار الساعة</span>
                                </li>
                            </ul>
                        </div>
                        
                        <button type="button" class="w-full px-4 py-2 {{ ($subscription->plan_id ?? 'premium') == 'premium' ? 'bg-gray-300 dark:bg-gray-700 cursor-not-allowed' : 'border border-primary text-primary bg-white dark:bg-gray-800 hover:bg-primary/10 transition-colors' }} rounded-lg text-center">
                            {{ ($subscription->plan_id ?? 'premium') == 'premium' ? 'الباقة الحالية' : 'اختيار الباقة' }}
                        </button>
                    </div>
                </div>
                
                <div class="mt-4 text-center">
                    <p class="text-gray-500 dark:text-gray-400 text-sm">تحتاج إلى باقة مخصصة؟ <a href="#" class="text-primary hover:underline">اتصل بنا</a> للحصول على عرض خاص.</p>
                </div>
            </div>
            
            <!-- Payment Methods -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold">طرق الدفع</h3>
                    <button type="button" class="text-primary hover:text-primary/80 flex items-center gap-1">
                        <i class="fas fa-plus"></i>
                        <span>إضافة طريقة دفع</span>
                    </button>
                </div>
                
                <div class="space-y-4">
                    @forelse($paymentMethods ?? [] as $method)
                        <div class="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                                    @if($method->type == 'visa')
                                        <i class="fab fa-cc-visa text-blue-600"></i>
                                    @elseif($method->type == 'mastercard')
                                        <i class="fab fa-cc-mastercard text-red-600"></i>
                                    @elseif($method->type == 'amex')
                                        <i class="fab fa-cc-amex text-purple-600"></i>
                                    @else
                                        <i class="fas fa-credit-card text-gray-600"></i>
                                    @endif
                                </div>
                                <div>
                                    <p class="font-medium">{{ $method->name }}</p>
                                    <p class="text-gray-500 dark:text-gray-400 text-sm">ينتهي في {{ $method->expires }}</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-2">
                                @if($method->is_default)
                                    <span class="px-2.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">الافتراضية</span>
                                @else
                                    <button type="button" class="text-primary hover:text-primary/80 text-sm">تعيين كافتراضية</button>
                                @endif
                                <button type="button" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                    @empty
                        <!-- Sample Payment Methods -->
                        <div class="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                                    <i class="fab fa-cc-visa text-blue-600"></i>
                                </div>
                                <div>
                                    <p class="font-medium">بطاقة فيزا</p>
                                    <p class="text-gray-500 dark:text-gray-400 text-sm">**** **** **** 4582</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-2">
                                <span class="px-2.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">الافتراضية</span>
                                <button type="button" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                                    <i class="fab fa-cc-mastercard text-red-600"></i>
                                </div>
                                <div>
                                    <p class="font-medium">ماستركارد</p>
                                    <p class="text-gray-500 dark:text-gray-400 text-sm">**** **** **** 8745</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-2">
                                <button type="button" class="text-primary hover:text-primary/80 text-sm">تعيين كافتراضية</button>
                                <button type="button" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                    @endforelse
                </div>
            </div>
            
            <!-- Billing History -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">سجل الفواتير</h3>
                
                <div class="overflow-x-auto">
                    <table class="w-full text-right text-sm">
                        <thead class="bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-300">
                            <tr>
                                <th class="px-6 py-3">رقم الفاتورة</th>
                                <th class="px-6 py-3">التاريخ</th>
                                <th class="px-6 py-3">المبلغ</th>
                                <th class="px-6 py-3">طريقة الدفع</th>
                                <th class="px-6 py-3">الحالة</th>
                                <th class="px-6 py-3"></th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                            @forelse($billingHistory ?? [] as $invoice)
                                <tr class="bg-white dark:bg-gray-900">
                                    <td class="px-6 py-4 font-medium">{{ $invoice->number }}</td>
                                    <td class="px-6 py-4">{{ $invoice->date }}</td>
                                    <td class="px-6 py-4">{{ $invoice->amount }} ريال</td>
                                    <td class="px-6 py-4">{{ $invoice->payment_method }}</td>
                                    <td class="px-6 py-4">
                                        <span class="px-2.5 py-0.5 
                                            @if($invoice->status == 'paid') bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400
                                            @elseif($invoice->status == 'pending') bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400
                                            @elseif($invoice->status == 'failed') bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400
                                            @endif rounded-full text-xs">
                                            {{ $invoice->status_label }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 text-left">
                                        <a href="{{ $invoice->download_url }}" class="text-primary hover:text-primary/80">
                                            <i class="fas fa-download"></i>
                                        </a>
                                    </td>
                                </tr>
                            @empty
                                <!-- Sample Invoices -->
                                <tr class="bg-white dark:bg-gray-900">
                                    <td class="px-6 py-4 font-medium">INV-2023-001</td>
                                    <td class="px-6 py-4">1 فبراير، 2023</td>
                                    <td class="px-6 py-4">5,999 ريال</td>
                                    <td class="px-6 py-4">بطاقة فيزا **** 4582</td>
                                    <td class="px-6 py-4">
                                        <span class="px-2.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">مدفوعة</span>
                                    </td>
                                    <td class="px-6 py-4 text-left">
                                        <a href="#" class="text-primary hover:text-primary/80">
                                            <i class="fas fa-download"></i>
                                        </a>
                                    </td>
                                </tr>
                                <tr class="bg-white dark:bg-gray-900">
                                    <td class="px-6 py-4 font-medium">INV-2022-012</td>
                                    <td class="px-6 py-4">1 فبراير، 2022</td>
                                    <td class="px-6 py-4">5,999 ريال</td>
                                    <td class="px-6 py-4">بطاقة فيزا **** 4582</td>
                                    <td class="px-6 py-4">
                                        <span class="px-2.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">مدفوعة</span>
                                    </td>
                                    <td class="px-6 py-4 text-left">
                                        <a href="#" class="text-primary hover:text-primary/80">
                                            <i class="fas fa-download"></i>
                                        </a>
                                    </td>
                                </tr>
                                <tr class="bg-white dark:bg-gray-900">
                                    <td class="px-6 py-4 font-medium">INV-2021-012</td>
                                    <td class="px-6 py-4">1 فبراير، 2021</td>
                                    <td class="px-6 py-4">3,999 ريال</td>
                                    <td class="px-6 py-4">بطاقة فيزا **** 4582</td>
                                    <td class="px-6 py-4">
                                        <span class="px-2.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">مدفوعة</span>
                                    </td>
                                    <td class="px-6 py-4 text-left">
                                        <a href="#" class="text-primary hover:text-primary/80">
                                            <i class="fas fa-download"></i>
                                        </a>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Subscription Summary -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">ملخص الاشتراك</h3>
                
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600 dark:text-gray-400">الباقة</span>
                        <span class="font-medium">{{ $subscription->plan_name ?? 'الباقة المتميزة' }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600 dark:text-gray-400">تاريخ البدء</span>
                        <span class="font-medium">{{ $subscription->start_date ?? '1 فبراير، 2023' }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600 dark:text-gray-400">تاريخ التجديد</span>
                        <span class="font-medium">{{ $subscription->renewal_date ?? '1 فبراير، 2024' }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600 dark:text-gray-400">دورة الفوترة</span>
                        <span class="font-medium">{{ $subscription->billing_cycle ?? 'سنوية' }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center border-t border-gray-200 dark:border-gray-700 pt-4">
                        <span class="text-gray-600 dark:text-gray-400">القيمة السنوية</span>
                        <span class="font-medium">{{ $subscription->yearly_amount ?? '5,999' }} ريال</span>
                    </div>
                </div>
                
                <div class="mt-6">
                    <button type="button" class="w-full px-4 py-2 border border-primary text-primary bg-white dark:bg-gray-800 rounded-lg hover:bg-primary/10 transition-colors flex items-center gap-2 justify-center">
                        <i class="fas fa-file-invoice"></i>
                        <span>عرض الفاتورة الحالية</span>
                    </button>
                </div>
            </div>
            
            <!-- Billing Information -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold">معلومات الفوترة</h3>
                    <button type="button" class="text-primary hover:text-primary/80">
                        <i class="fas fa-edit"></i>
                    </button>
                </div>
                
                <div class="space-y-3">
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">الاسم</p>
                        <p class="font-medium">{{ $billing->name ?? 'شركة التقنية المتطورة' }}</p>
                    </div>
                    
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">الرقم الضريبي</p>
                        <p class="font-medium">{{ $billing->tax_number ?? '300000000000003' }}</p>
                    </div>
                    
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">البريد الإلكتروني للفواتير</p>
                        <p class="font-medium">{{ $billing->email ?? '<EMAIL>' }}</p>
                    </div>
                    
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">العنوان</p>
                        <p class="font-medium">{{ $billing->address ?? 'شارع العليا، حي الورود، الرياض، المملكة العربية السعودية' }}</p>
                    </div>
                </div>
            </div>
            
            <!-- Help & Support -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">المساعدة والدعم</h3>
                
                <div class="space-y-4">
                    <a href="#" class="flex items-center gap-3 text-primary hover:text-primary/80 transition-colors">
                        <i class="fas fa-question-circle"></i>
                        <span>الأسئلة المتكررة</span>
                    </a>
                    
                    <a href="#" class="flex items-center gap-3 text-primary hover:text-primary/80 transition-colors">
                        <i class="fas fa-credit-card"></i>
                        <span>طرق الدفع المدعومة</span>
                    </a>
                    
                    <a href="#" class="flex items-center gap-3 text-primary hover:text-primary/80 transition-colors">
                        <i class="fas fa-file-contract"></i>
                        <span>شروط الاشتراك</span>
                    </a>
                </div>
                
                <div class="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <p class="text-sm text-gray-500 dark:text-gray-400">تحتاج إلى مساعدة بخصوص الفواتير والاشتراكات؟</p>
                    <button class="w-full mt-2 px-4 py-2 border border-primary text-primary bg-white dark:bg-gray-800 rounded-lg hover:bg-primary/10 transition-colors flex items-center gap-2 justify-center">
                        <i class="fas fa-headset"></i>
                        <span>التواصل مع الدعم</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Payment Method Modal (Hidden by default) -->
<div id="addPaymentModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
        </div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white dark:bg-dark-card rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white dark:bg-dark-card px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold">إضافة طريقة دفع جديدة</h3>
                    <button type="button" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300" id="closeModal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form class="space-y-4">
                    <div>
                        <label for="card_number" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">رقم البطاقة <span class="text-red-600">*</span></label>
                        <input type="text" name="card_number" id="card_number" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" placeholder="0000 0000 0000 0000" required>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="expiry_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">تاريخ الانتهاء <span class="text-red-600">*</span></label>
                            <input type="text" name="expiry_date" id="expiry_date" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" placeholder="MM/YY" required>
                        </div>
                        
                        <div>
                            <label for="cvv" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">رمز الأمان <span class="text-red-600">*</span></label>
                            <input type="text" name="cvv" id="cvv" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" placeholder="123" required>
                        </div>
                    </div>
                    
                    <div>
                        <label for="cardholder_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">اسم حامل البطاقة <span class="text-red-600">*</span></label>
                        <input type="text" name="cardholder_name" id="cardholder_name" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" placeholder="الاسم كما يظهر على البطاقة" required>
                    </div>
                    
                    <div class="flex items-center">
                        <input id="default_payment" name="default_payment" type="checkbox" class="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary">
                        <label for="default_payment" class="mr-2 text-sm font-medium text-gray-700 dark:text-gray-300">تعيين كطريقة الدفع الافتراضية</label>
                    </div>
                </form>
            </div>
            <div class="bg-gray-50 dark:bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 gradient-bg text-base font-medium text-white hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm">
                    إضافة طريقة الدفع
                </button>
                <button type="button" id="cancelAddPayment" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    إلغاء
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Add Payment Method Modal
    const addPaymentBtn = document.querySelector('button:has(.fa-plus)');
    const addPaymentModal = document.getElementById('addPaymentModal');
    const closeModal = document.getElementById('closeModal');
    const cancelAddPayment = document.getElementById('cancelAddPayment');
    
    // Show modal
    addPaymentBtn.addEventListener('click', () => {
        addPaymentModal.classList.remove('hidden');
    });
    
    // Hide modal
    closeModal.addEventListener('click', () => {
        addPaymentModal.classList.add('hidden');
    });
    
    cancelAddPayment.addEventListener('click', () => {
        addPaymentModal.classList.add('hidden');
    });
    
    // Close modal when clicking outside
    addPaymentModal.addEventListener('click', (e) => {
        if (e.target === addPaymentModal) {
            addPaymentModal.classList.add('hidden');
        }
    });
    
    // Handle plan selection
    const planButtons = document.querySelectorAll('button:not([disabled])');
    
    planButtons.forEach(button => {
        button.addEventListener('click', function() {
            if (this.textContent.trim() === 'اختيار الباقة') {
                // In a real app, this would navigate to a checkout page or show a confirmation modal
                alert('سيتم توجيهك إلى صفحة تأكيد الاشتراك');
            }
        });
    });
</script>
@endsection