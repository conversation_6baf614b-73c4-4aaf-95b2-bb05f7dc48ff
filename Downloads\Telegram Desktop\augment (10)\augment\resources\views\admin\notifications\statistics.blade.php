@extends('layouts.admin')

@section('title', 'إحصائيات الإشعارات - Hire Me')
@section('header_title', 'إحصائيات الإشعارات')

@section('styles')
<style>
    .stat-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    .glass-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .chart-container {
        min-height: 300px;
    }
</style>
@endsection

@section('content')
<div class="p-8 space-y-8">
    <!-- Header -->
    <div class="flex items-center gap-4 mb-8">
        <a href="{{ route('admin.notifications.index') }}" class="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors">
            <i class="fas fa-arrow-right text-gray-600 dark:text-gray-400"></i>
        </a>
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">إحصائيات الإشعارات</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">تحليل شامل لنشاط الإشعارات في النظام</p>
        </div>
    </div>

    <!-- Overview Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="stat-card p-6 rounded-xl">
            <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-bell text-white"></i>
                </div>
                <div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">إجمالي الإشعارات</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ number_format($totalNotifications) }}</p>
                </div>
            </div>
        </div>

        <div class="stat-card p-6 rounded-xl">
            <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-exclamation-circle text-white"></i>
                </div>
                <div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">غير مقروءة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ number_format($unreadNotifications) }}</p>
                </div>
            </div>
        </div>

        <div class="stat-card p-6 rounded-xl">
            <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-paper-plane text-white"></i>
                </div>
                <div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">أرسلتها الإدارة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ number_format($adminSentNotifications) }}</p>
                </div>
            </div>
        </div>

        <div class="stat-card p-6 rounded-xl">
            <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-percentage text-white"></i>
                </div>
                <div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">معدل القراءة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">
                        {{ $totalNotifications > 0 ? number_format((($totalNotifications - $unreadNotifications) / $totalNotifications) * 100, 1) : 0 }}%
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Notifications by Type Chart -->
        <div class="glass-card rounded-xl p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">الإشعارات حسب النوع</h3>
            <div id="typeChart" class="h-64"></div>
        </div>

        <!-- Monthly Notifications Chart -->
        <div class="glass-card rounded-xl p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">الإشعارات الشهرية (آخر 6 أشهر)</h3>
            <div id="monthlyChart" class="h-64"></div>
        </div>
    </div>

    <!-- Detailed Breakdown -->
    <div class="glass-card rounded-xl p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">تفصيل الإشعارات حسب النوع</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            @foreach($notificationsByType as $type => $count)
            <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                        <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-{{ getNotificationIcon($type) }} text-white text-xs"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-900 dark:text-white">{{ getNotificationTypeInArabic($type) }}</p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">{{ $type }}</p>
                        </div>
                    </div>
                    <span class="text-lg font-bold text-gray-900 dark:text-white">{{ number_format($count) }}</span>
                </div>
            </div>
            @endforeach
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="glass-card rounded-xl p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">النشاط الأخير</h3>

        @php
            $recentNotifications = \App\Models\Notification::with('user')
                ->latest()
                ->take(10)
                ->get();
        @endphp

        <div class="space-y-4">
            @foreach($recentNotifications as $notification)
            <div class="flex items-center gap-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-{{ getNotificationIcon($notification->type) }} text-white text-sm"></i>
                </div>
                <div class="flex-1">
                    <p class="font-medium text-gray-900 dark:text-white">{{ $notification->message }}</p>
                    <div class="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mt-1">
                        <span>{{ $notification->user->name }}</span>
                        <span>{{ getNotificationTypeInArabic($notification->type) }}</span>
                        <span>{{ $notification->created_at->diffForHumans() }}</span>
                    </div>
                </div>
                <div class="text-right">
                    @if($notification->is_read)
                    <span class="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded text-xs">مقروء</span>
                    @else
                    <span class="px-2 py-1 bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded text-xs">غير مقروء</span>
                    @endif
                </div>
            </div>
            @endforeach
        </div>
    </div>
</div>


@endsection

@section('scripts')
<script>
// Notifications by Type Chart
const typeChartOptions = {
    series: @json(array_values($notificationsByType->toArray())),
    chart: {
        type: 'donut',
        height: 250,
        fontFamily: 'inherit',
    },
    labels: [
        @foreach($notificationsByType as $type => $count)
            '{{ getNotificationTypeInArabic($type) }}',
        @endforeach
    ],
    colors: ['#3B82F6', '#8B5CF6', '#10B981', '#F59E0B', '#EF4444', '#6366F1', '#EC4899', '#14B8A6'],
    legend: {
        position: 'bottom',
    },
    responsive: [{
        breakpoint: 480,
        options: {
            chart: {
                width: 200
            },
            legend: {
                position: 'bottom'
            }
        }
    }]
};

const typeChart = new ApexCharts(document.querySelector("#typeChart"), typeChartOptions);
typeChart.render();

// Monthly Notifications Chart
const monthlyChartOptions = {
    series: [{
        name: 'الإشعارات',
        data: @json(array_values($monthlyStats))
    }],
    chart: {
        type: 'area',
        height: 250,
        fontFamily: 'inherit',
        toolbar: {
            show: false
        }
    },
    xaxis: {
        categories: @json(array_keys($monthlyStats)),
        labels: {
            style: {
                colors: '#6B7280'
            }
        }
    },
    yaxis: {
        labels: {
            style: {
                colors: '#6B7280'
            }
        }
    },
    colors: ['#3B82F6'],
    fill: {
        type: 'gradient',
        gradient: {
            shadeIntensity: 1,
            opacityFrom: 0.7,
            opacityTo: 0.3,
        }
    },
    stroke: {
        curve: 'smooth',
        width: 2
    },
    grid: {
        borderColor: '#E5E7EB'
    }
};

const monthlyChart = new ApexCharts(document.querySelector("#monthlyChart"), monthlyChartOptions);
monthlyChart.render();
</script>
@endsection
