<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notification_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('type'); // نوع الإشعار
            $table->boolean('web_enabled')->default(true); // إشعارات الويب
            $table->boolean('email_enabled')->default(true); // إشعارات البريد الإلكتروني
            $table->boolean('push_enabled')->default(false); // الإشعارات المدفوعة (مستقبلاً)
            $table->timestamps();

            $table->unique(['user_id', 'type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notification_settings');
    }
};
