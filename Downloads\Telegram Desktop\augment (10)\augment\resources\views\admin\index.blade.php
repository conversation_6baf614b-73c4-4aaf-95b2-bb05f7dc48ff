@extends('layouts.admin')

@section('title', 'لوحة تحكم المدير - Hire Me')

@section('content')
<!-- Dashboard Content -->
<div class="p-8 space-y-8">
    <!-- Welcome Section -->
    <div class="glass-card rounded-2xl p-8 animate-fade-in">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-3xl font-bold text-gradient mb-3">مرحباً بك في لوحة تحكم الأدمن! 👨‍💼</h2>
                <p class="text-gray-600 dark:text-gray-400 text-lg">إدارة شاملة لنظام التوظيف والمستخدمين</p>
                <div class="flex items-center gap-6 mt-4">
                    <div class="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span>النظام متاح ويعمل بكفاءة</span>
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                        آخر تحديث: {{ now()->format('Y/m/d H:i') }}
                    </div>
                    <div class="text-sm bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 px-3 py-1 rounded-full">
                        <i class="fas fa-shield-alt text-xs ml-1"></i>
                        لوحة تحكم الأدمن
                    </div>
                </div>
            </div>
            <div class="hidden lg:block">
                <div class="w-32 h-32 gradient-primary rounded-full flex items-center justify-center shadow-2xl glow-effect">
                    <i class="fas fa-tachometer-alt text-4xl text-white"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Stat Card 1 -->
        <div class="stat-card rounded-2xl p-6 group">
            <div class="flex justify-between items-start mb-6">
                <div>
                    <p class="text-gray-500 dark:text-gray-400 text-sm font-medium mb-2">الوظائف النشطة</p>
                    <h3 class="text-4xl font-bold text-gradient">{{ $activeJobsCount ?? 31 }}</h3>
                </div>
                <div class="w-16 h-16 rounded-2xl gradient-primary flex items-center justify-center text-white shadow-lg group-hover:shadow-primary-500/25 transition-all duration-300 group-hover:scale-110">
                    <i class="fas fa-briefcase text-2xl"></i>
                </div>
            </div>
            <div class="flex items-center {{ ($jobsPercentage ?? 12) >= 0 ? 'text-success-600 dark:text-success-400' : 'text-danger-600 dark:text-danger-400' }}">
                <div class="w-8 h-8 rounded-full bg-success-100 dark:bg-success-900/30 flex items-center justify-center ml-3">
                    <i class="fas fa-arrow-{{ ($jobsPercentage ?? 12) >= 0 ? 'up' : 'down' }} text-sm"></i>
                </div>
                <span class="text-sm font-medium">{{ abs($jobsPercentage ?? 12) }}% {{ ($jobsPercentage ?? 12) >= 0 ? 'أعلى' : 'أقل' }} من الشهر الماضي</span>
            </div>
        </div>

        <!-- Stat Card 2 -->
        <div class="stat-card rounded-2xl p-6 group">
            <div class="flex justify-between items-start mb-6">
                <div>
                    <p class="text-gray-500 dark:text-gray-400 text-sm font-medium mb-2">إجمالي الطلبات</p>
                    <h3 class="text-4xl font-bold text-gradient">{{ $totalApplications ?? 23 }}</h3>
                </div>
                <div class="w-16 h-16 rounded-2xl gradient-accent flex items-center justify-center text-white shadow-lg group-hover:shadow-accent-500/25 transition-all duration-300 group-hover:scale-110">
                    <i class="fas fa-file-alt text-2xl"></i>
                </div>
            </div>
            <div class="flex items-center {{ ($applicationsPercentage ?? 8) >= 0 ? 'text-success-600 dark:text-success-400' : 'text-danger-600 dark:text-danger-400' }}">
                <div class="w-8 h-8 rounded-full bg-success-100 dark:bg-success-900/30 flex items-center justify-center ml-3">
                    <i class="fas fa-arrow-{{ ($applicationsPercentage ?? 8) >= 0 ? 'up' : 'down' }} text-sm"></i>
                </div>
                <span class="text-sm font-medium">{{ abs($applicationsPercentage ?? 8) }}% {{ ($applicationsPercentage ?? 8) >= 0 ? 'أعلى' : 'أقل' }} من الشهر الماضي</span>
            </div>
        </div>

        <!-- Stat Card 3 -->
        <div class="stat-card rounded-2xl p-6 group">
            <div class="flex justify-between items-start mb-6">
                <div>
                    <p class="text-gray-500 dark:text-gray-400 text-sm font-medium mb-2">المستخدمين النشطين</p>
                    <h3 class="text-4xl font-bold text-gradient">{{ $activeUsers ?? 18 }}</h3>
                </div>
                <div class="w-16 h-16 rounded-2xl gradient-warning flex items-center justify-center text-white shadow-lg group-hover:shadow-warning-500/25 transition-all duration-300 group-hover:scale-110">
                    <i class="fas fa-users text-2xl"></i>
                </div>
            </div>
            <div class="flex items-center {{ ($usersPercentage ?? 15) >= 0 ? 'text-success-600 dark:text-success-400' : 'text-danger-600 dark:text-danger-400' }}">
                <div class="w-8 h-8 rounded-full bg-success-100 dark:bg-success-900/30 flex items-center justify-center ml-3">
                    <i class="fas fa-arrow-{{ ($usersPercentage ?? 15) >= 0 ? 'up' : 'down' }} text-sm"></i>
                </div>
                <span class="text-sm font-medium">{{ abs($usersPercentage ?? 15) }}% {{ ($usersPercentage ?? 15) >= 0 ? 'أعلى' : 'أقل' }} من الشهر الماضي</span>
            </div>
        </div>

        <!-- Stat Card 4 -->
        <div class="stat-card rounded-2xl p-6 group">
            <div class="flex justify-between items-start mb-6">
                <div>
                    <p class="text-gray-500 dark:text-gray-400 text-sm font-medium mb-2">التعيينات الجديدة</p>
                    <h3 class="text-4xl font-bold text-gradient">{{ $newHires ?? 6 }}</h3>
                </div>
                <div class="w-16 h-16 rounded-2xl gradient-success flex items-center justify-center text-white shadow-lg group-hover:shadow-success-500/25 transition-all duration-300 group-hover:scale-110">
                    <i class="fas fa-user-check text-2xl"></i>
                </div>
            </div>
            <div class="flex items-center {{ ($hiresPercentage ?? 25) >= 0 ? 'text-success-600 dark:text-success-400' : 'text-danger-600 dark:text-danger-400' }}">
                <div class="w-8 h-8 rounded-full bg-success-100 dark:bg-success-900/30 flex items-center justify-center ml-3">
                    <i class="fas fa-arrow-{{ ($hiresPercentage ?? 25) >= 0 ? 'up' : 'down' }} text-sm"></i>
                </div>
                <span class="text-sm font-medium">{{ abs($hiresPercentage ?? 25) }}% {{ ($hiresPercentage ?? 25) >= 0 ? 'أعلى' : 'أقل' }} من الشهر الماضي</span>
            </div>
        </div>
    </div>

    <!-- Recent Applications & Job Posts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Recent Applications -->
        <div class="glass-card rounded-2xl p-8 animate-fade-in">
            <div class="flex justify-between items-center mb-8">
                <div class="flex items-center gap-3">
                    <div class="w-10 h-10 gradient-accent rounded-xl flex items-center justify-center">
                        <i class="fas fa-file-alt text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gradient">أحدث الطلبات</h3>
                </div>
                <a href="{{ route('admin.applications.index') }}" class="btn-gradient text-white px-4 py-2 rounded-xl text-sm font-medium hover:shadow-lg transition-all duration-300">
                    عرض الكل
                </a>
            </div>

            <div class="space-y-4">
                @forelse($recentApplications ?? [
                    ['id' => 1, 'applicant_name' => 'سارة محمد الأحمد', 'job_title' => 'مطور ويب متقدم', 'time_ago' => 'منذ ساعتين', 'status' => 'pending'],
                    ['id' => 2, 'applicant_name' => 'أحمد خالد العلي', 'job_title' => 'مصمم جرافيك', 'time_ago' => 'منذ 4 ساعات', 'status' => 'reviewed'],
                    ['id' => 3, 'applicant_name' => 'فاطمة عبدالله', 'job_title' => 'محاسب مالي', 'time_ago' => 'منذ يوم', 'status' => 'accepted'],
                    ['id' => 4, 'applicant_name' => 'محمد سعد القحطاني', 'job_title' => 'أخصائي تسويق رقمي', 'time_ago' => 'منذ يومين', 'status' => 'pending']
                ] as $application)
                    <div class="group flex items-center gap-4 p-4 rounded-xl hover:bg-gradient-to-r hover:from-primary-50 hover:to-accent-50 dark:hover:from-primary-900/20 dark:hover:to-accent-900/20 transition-all duration-300 border border-transparent hover:border-primary-200 dark:hover:border-primary-700">
                        <div class="w-14 h-14 gradient-primary rounded-2xl flex items-center justify-center flex-shrink-0 shadow-lg group-hover:shadow-primary-500/25 transition-all duration-300 group-hover:scale-110">
                            <i class="fas fa-user text-white text-lg"></i>
                        </div>
                        <div class="flex-1 min-w-0">
                            <h4 class="font-bold text-lg truncate text-gray-800 dark:text-gray-200 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">{{ $application['applicant_name'] }}</h4>
                            <p class="text-gray-600 dark:text-gray-400 truncate text-sm">{{ $application['job_title'] }}</p>
                            <div class="flex items-center gap-2 mt-1">
                                @php
                                    $statusColors = [
                                        'pending' => 'bg-warning-100 text-warning-800 dark:bg-warning-900/30 dark:text-warning-300',
                                        'reviewed' => 'bg-primary-100 text-primary-800 dark:bg-primary-900/30 dark:text-primary-300',
                                        'accepted' => 'bg-success-100 text-success-800 dark:bg-success-900/30 dark:text-success-300',
                                        'rejected' => 'bg-danger-100 text-danger-800 dark:bg-danger-900/30 dark:text-danger-300'
                                    ];
                                    $statusText = [
                                        'pending' => 'قيد المراجعة',
                                        'reviewed' => 'تمت المراجعة',
                                        'accepted' => 'مقبول',
                                        'rejected' => 'مرفوض'
                                    ];
                                @endphp
                                <span class="px-2 py-1 rounded-full text-xs font-medium {{ $statusColors[$application['status']] ?? $statusColors['pending'] }}">
                                    {{ $statusText[$application['status']] ?? $statusText['pending'] }}
                                </span>
                            </div>
                        </div>
                        <div class="text-sm whitespace-nowrap text-gray-500 dark:text-gray-400 font-medium">
                            {{ $application['time_ago'] }}
                        </div>
                        <div>
                            <a href="{{ route('admin.applications.show', $application['id']) }}" class="w-10 h-10 rounded-xl bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-800 dark:to-primary-900 flex items-center justify-center text-primary-600 dark:text-primary-400 hover:shadow-lg transition-all duration-300 group-hover:scale-110">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                    </div>
                @empty
                    <div class="text-center py-12 text-gray-500 dark:text-gray-400">
                        <div class="w-20 h-20 mx-auto mb-4 gradient-primary rounded-full flex items-center justify-center opacity-50">
                            <i class="fas fa-inbox text-3xl text-white"></i>
                        </div>
                        <p class="text-lg font-medium">لا توجد طلبات حتى الآن</p>
                        <p class="text-sm mt-1">ستظهر الطلبات الجديدة هنا</p>
                    </div>
                @endforelse
            </div>
        </div>

        <!-- Recent Job Posts -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-bold">أحدث الوظائف المنشورة</h3>
                <a href="{{ route('admin.jobs.index') }}" class="text-primary text-sm hover:underline">عرض الكل</a>
            </div>

            <div class="space-y-4">
                @forelse($recentJobs ?? [] as $job)
                    <div class="flex items-center gap-4 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                        <div class="w-12 h-12 {{ $job['color_class'] ?? 'gradient-bg' }} rounded-lg flex items-center justify-center flex-shrink-0 text-white">
                            <i class="fas fa-{{ $job['icon'] ?? 'briefcase' }}"></i>
                        </div>
                        <div class="flex-1 min-w-0">
                            <h4 class="font-bold text-lg truncate">{{ $job['title'] ?? 'وظيفة غير معروفة' }}</h4>
                            <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                                <span class="truncate ml-2">
                                    <i class="fas fa-map-marker-alt ml-1"></i>
                                    {{ $job['location'] ?? 'غير محدد' }}
                                </span>
                                <span class="truncate">
                                    <i class="fas fa-clock ml-1"></i>
                                    {{ $job['type'] ?? 'غير محدد' }}
                                </span>
                            </div>
                        </div>
                        <div class="text-primary font-medium whitespace-nowrap">
                            {{ $job['applications_count'] ?? 0 }} طلب
                        </div>
                        <div>
                            <a href="{{ route('admin.jobs.show', $job['id'] ?? 1) }}" class="text-primary hover:text-primary/80">
                                <i class="fas fa-ellipsis-v"></i>
                            </a>
                        </div>
                    </div>
                @empty
                    <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                        <i class="fas fa-briefcase text-4xl mb-3"></i>
                        <p>لا توجد وظائف منشورة حتى الآن</p>
                    </div>
                @endforelse
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Applications Chart -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-bold">إحصائيات الطلبات</h3>
                <select class="bg-gray-100 dark:bg-gray-800 border-0 rounded-lg px-3 py-2 text-sm">
                    <option>آخر 7 أيام</option>
                    <option>آخر 30 يوم</option>
                    <option>آخر 90 يوم</option>
                </select>
            </div>

            <div class="h-64 flex items-end justify-between">
                @foreach($weeklyApplicationsData ?? [
                    ['day' => 'السبت', 'height' => 40, 'count' => 0],
                    ['day' => 'الأحد', 'height' => 28, 'count' => 0],
                    ['day' => 'الإثنين', 'height' => 48, 'count' => 0],
                    ['day' => 'الثلاثاء', 'height' => 32, 'count' => 0],
                    ['day' => 'الأربعاء', 'height' => 56, 'count' => 0],
                    ['day' => 'الخميس', 'height' => 36, 'count' => 0],
                    ['day' => 'الجمعة', 'height' => 20, 'count' => 0]
                ] as $data)
                    <div class="flex flex-col items-center">
                        <div class="gradient-bg w-8 rounded-t-lg" style="height: {{ $data['height'] }}px;"></div>
                        <span class="mt-2 text-xs text-gray-500 dark:text-gray-400">{{ $data['day'] }}</span>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Job Views Chart -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-bold">أكثر الوظائف مشاهدة</h3>
                <select class="bg-gray-100 dark:bg-gray-800 border-0 rounded-lg px-3 py-2 text-sm">
                    <option>هذا الشهر</option>
                    <option>الشهر الماضي</option>
                    <option>آخر 3 أشهر</option>
                </select>
            </div>

            <div class="space-y-4">
                @forelse($topViewedJobs ?? [] as $job)
                    <div>
                        <div class="flex justify-between mb-1">
                            <span class="font-medium">{{ $job['title'] ?? 'وظيفة غير معروفة' }}</span>
                            <span class="font-medium">{{ $job['views'] ?? 0 }}</span>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                            <div class="gradient-bg h-2.5 rounded-full" style="width: {{ $job['percentage'] ?? 0 }}%;"></div>
                        </div>
                    </div>
                @empty
                    <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                        <i class="fas fa-eye-slash text-4xl mb-3"></i>
                        <p>لا توجد بيانات مشاهدات متاحة</p>
                    </div>
                @endforelse
            </div>
        </div>
    </div>
</div>
@endsection