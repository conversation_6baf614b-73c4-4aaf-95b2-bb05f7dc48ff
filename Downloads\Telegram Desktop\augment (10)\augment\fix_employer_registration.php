<?php

/**
 * Script لإصلاح مشكلة تسجيل أصحاب العمل
 * يقوم بتعديل جدول employers لجعل company_name nullable
 */

require_once 'vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as Capsule;
use Illuminate\Database\Schema\Blueprint;

// إعداد الاتصال بقاعدة البيانات
$capsule = new Capsule;

// قراءة إعدادات قاعدة البيانات من .env
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

$capsule->addConnection([
    'driver' => 'mysql',
    'host' => $_ENV['DB_HOST'] ?? 'localhost',
    'database' => $_ENV['DB_DATABASE'] ?? 'augment',
    'username' => $_ENV['DB_USERNAME'] ?? 'root',
    'password' => $_ENV['DB_PASSWORD'] ?? '',
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'prefix' => '',
]);

$capsule->setAsGlobal();
$capsule->bootEloquent();

try {
    echo "بدء إصلاح جدول employers...\n";
    
    // التحقق من وجود الجدول
    if (!Capsule::schema()->hasTable('employers')) {
        echo "جدول employers غير موجود!\n";
        exit(1);
    }
    
    // التحقق من حالة العمود company_name
    $columns = Capsule::schema()->getColumnListing('employers');
    if (!in_array('company_name', $columns)) {
        echo "العمود company_name غير موجود في جدول employers!\n";
        exit(1);
    }
    
    // تعديل العمود ليصبح nullable
    Capsule::schema()->table('employers', function (Blueprint $table) {
        $table->string('company_name')->nullable()->change();
    });
    
    echo "تم إصلاح جدول employers بنجاح! العمود company_name أصبح nullable.\n";
    
    // التحقق من وجود سجلات employers بدون company_name وإصلاحها
    $employersWithoutCompanyName = Capsule::table('employers')
        ->whereNull('company_name')
        ->orWhere('company_name', '')
        ->get();
    
    if ($employersWithoutCompanyName->count() > 0) {
        echo "تم العثور على " . $employersWithoutCompanyName->count() . " سجل employer بدون company_name. جاري الإصلاح...\n";
        
        foreach ($employersWithoutCompanyName as $employer) {
            $user = Capsule::table('users')->where('id', $employer->user_id)->first();
            if ($user) {
                $companyName = $user->name . ' - شركة';
                Capsule::table('employers')
                    ->where('id', $employer->id)
                    ->update(['company_name' => $companyName]);
                echo "تم إصلاح employer ID: {$employer->id} - Company Name: {$companyName}\n";
            }
        }
    }
    
    echo "تم الانتهاء من إصلاح جميع المشاكل!\n";
    
} catch (Exception $e) {
    echo "حدث خطأ: " . $e->getMessage() . "\n";
    exit(1);
}
