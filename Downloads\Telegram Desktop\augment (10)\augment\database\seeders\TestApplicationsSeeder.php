<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\JobSeeker;
use App\Models\Employer;
use App\Models\Job;
use App\Models\Application;
use App\Models\Category;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class TestApplicationsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء مستخدم باحث عن عمل للاختبار
        $jobSeekerUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'shahoda22',
                'password' => Hash::make('Pp123456'),
                'role' => 'job_seeker',
            ]
        );

        // إنشاء ملف باحث عن عمل
        $jobSeeker = JobSeeker::firstOrCreate(
            ['user_id' => $jobSeekerUser->id],
            [
                'skills' => 'PHP, Laravel, JavaScript, Vue.js, MySQL',
                'experience' => 'مطور ويب بخبرة 3 سنوات في تطوير تطبيقات الويب باستخدام Laravel و Vue.js',
                'education' => 'بكالوريوس في علوم الحاسب',
                'job_title' => 'مطور ويب',
                'location' => 'طرابلس, ليبيا',
                'is_available' => true,
            ]
        );

        // إنشاء مستخدم صاحب عمل للاختبار
        $employerUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'شركة التقنية المتقدمة',
                'password' => Hash::make('password'),
                'role' => 'employer',
            ]
        );

        // إنشاء ملف صاحب عمل
        $employer = Employer::firstOrCreate(
            ['user_id' => $employerUser->id],
            [
                'company_name' => 'شركة التقنية المتقدمة',
                'company_description' => 'شركة رائدة في مجال تطوير البرمجيات والحلول التقنية',
                'industry' => 'تقنية المعلومات',
                'website' => 'https://www.techcompany.ly',
                'location' => 'طرابلس, ليبيا',
                'company_size' => '51-200',
                'founded_year' => 2015,
            ]
        );

        // إنشاء تصنيف
        $category = Category::firstOrCreate(
            ['name' => 'تقنية المعلومات'],
            [
                'slug' => 'information-technology',
                'icon' => 'laptop-code',
            ]
        );

        // إنشاء وظائف تجريبية
        $jobs = [
            [
                'title' => 'مطور PHP متقدم',
                'description' => 'نبحث عن مطور PHP متقدم للانضمام إلى فريقنا المتميز',
                'requirements' => 'خبرة 3+ سنوات في PHP و Laravel',
                'salary_range' => '3000 - 5000 دينار ليبي',
                'job_type' => 'full_time',
            ],
            [
                'title' => 'مطور Frontend',
                'description' => 'مطور واجهات أمامية متخصص في Vue.js و React',
                'requirements' => 'خبرة في JavaScript و Vue.js',
                'salary_range' => '2500 - 4000 دينار ليبي',
                'job_type' => 'full_time',
            ],
            [
                'title' => 'مصمم UI/UX',
                'description' => 'مصمم واجهات مستخدم وتجربة مستخدم',
                'requirements' => 'خبرة في Figma و Adobe XD',
                'salary_range' => '2000 - 3500 دينار ليبي',
                'job_type' => 'part_time',
            ],
        ];

        $createdJobs = [];
        foreach ($jobs as $jobData) {
            $job = Job::create([
                'employer_id' => $employer->id,
                'category_id' => $category->id,
                'title' => $jobData['title'],
                'description' => $jobData['description'],
                'requirements' => $jobData['requirements'],
                'salary_range' => $jobData['salary_range'],
                'location' => 'طرابلس, ليبيا',
                'job_type' => $jobData['job_type'],
                'posted_at' => Carbon::now()->subDays(rand(1, 30)),
                'expires_at' => Carbon::now()->addDays(rand(30, 90)),
                'is_active' => true,
                'is_featured' => rand(0, 1) == 1,
                'views' => rand(10, 500),
            ]);
            $createdJobs[] = $job;
        }

        // إنشاء طلبات توظيف
        $statuses = ['pending', 'accepted', 'rejected'];
        $coverLetters = [
            'أتقدم بطلب للحصول على هذه الوظيفة لأنني أعتقد أن مهاراتي وخبرتي تتناسب مع متطلبات المنصب. لدي خبرة واسعة في تطوير تطبيقات الويب باستخدام PHP و Laravel.',
            'أنا مهتم جداً بهذه الوظيفة وأعتقد أنني المرشح المناسب. لدي شغف كبير بالتطوير والتعلم المستمر.',
            'بناءً على خبرتي في مجال التطوير، أعتقد أنني أستطيع المساهمة بشكل إيجابي في نجاح شركتكم وتحقيق أهدافها.',
        ];

        foreach ($createdJobs as $index => $job) {
            Application::create([
                'job_id' => $job->id,
                'job_seeker_id' => $jobSeeker->id,
                'cover_letter' => $coverLetters[$index],
                'status' => $statuses[array_rand($statuses)],
                'applied_at' => Carbon::now()->subDays(rand(1, 20)),
            ]);
        }

        $this->command->info('تم إنشاء البيانات التجريبية للطلبات بنجاح!');
    }
}
