<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير المستخدمين - Hire Me</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #3b82f6;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #1f2937;
            margin: 0;
            font-size: 28px;
        }
        .header p {
            color: #6b7280;
            margin: 10px 0 0 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #3b82f6;
        }
        .stat-card h3 {
            color: #1f2937;
            margin: 0 0 5px 0;
            font-size: 24px;
        }
        .stat-card p {
            color: #6b7280;
            margin: 0;
            font-size: 14px;
        }
        .role-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin: 20px 0;
        }
        .role-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .role-card h4 {
            margin: 0 0 5px 0;
            font-size: 20px;
        }
        .role-card p {
            margin: 0;
            opacity: 0.9;
        }
        .users-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .users-table th,
        .users-table td {
            padding: 8px;
            text-align: right;
            border-bottom: 1px solid #e5e7eb;
            font-size: 11px;
        }
        .users-table th {
            background-color: #f3f4f6;
            color: #374151;
            font-weight: bold;
        }
        .users-table tr:nth-child(even) {
            background-color: #f9fafb;
        }
        .role-admin {
            color: #dc2626;
            font-weight: bold;
        }
        .role-employer {
            color: #059669;
            font-weight: bold;
        }
        .role-candidate {
            color: #3b82f6;
            font-weight: bold;
        }
        .status-verified {
            color: #059669;
            font-weight: bold;
        }
        .status-unverified {
            color: #d97706;
            font-weight: bold;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            text-align: center;
            color: #6b7280;
            font-size: 14px;
        }
        .summary-box {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .summary-box h3 {
            margin: 0 0 10px 0;
            font-size: 18px;
        }
        .summary-box p {
            margin: 5px 0;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>تقرير إدارة المستخدمين</h1>
            <p>نظام Hire Me</p>
            <p>تاريخ التقرير: {{ $reportDate }}</p>
        </div>

        <!-- Summary Box -->
        <div class="summary-box">
            <h3>ملخص التقرير</h3>
            <p>إجمالي المستخدمين: {{ $totalUsers }}</p>
            <p>المستخدمين المفعلين: {{ $verificationStats['verified'] }}</p>
            <p>المستخدمين غير المفعلين: {{ $verificationStats['unverified'] }}</p>
        </div>

        <!-- Statistics Grid -->
        <div class="stats-grid">
            <div class="stat-card">
                <h3>{{ $totalUsers }}</h3>
                <p>إجمالي المستخدمين</p>
            </div>
            <div class="stat-card">
                <h3>{{ $verificationStats['verified'] }}</h3>
                <p>مفعلين</p>
            </div>
            <div class="stat-card">
                <h3>{{ $verificationStats['unverified'] }}</h3>
                <p>غير مفعلين</p>
            </div>
            <div class="stat-card">
                <h3>{{ $stats['recent_users'] }}</h3>
                <p>جدد (آخر 7 أيام)</p>
            </div>
        </div>

        <!-- Role Statistics -->
        <div>
            <h3 style="color: #1f2937; margin-bottom: 15px;">توزيع المستخدمين حسب الدور</h3>
            <div class="role-stats">
                <div class="role-card">
                    <h4>{{ $roleStats['admins'] }}</h4>
                    <p>المديرين</p>
                </div>
                <div class="role-card">
                    <h4>{{ $roleStats['employers'] }}</h4>
                    <p>أصحاب العمل</p>
                </div>
                <div class="role-card">
                    <h4>{{ $roleStats['candidates'] }}</h4>
                    <p>الباحثين عن عمل</p>
                </div>
            </div>
        </div>

        <!-- Users Table -->
        <div>
            <h3 style="color: #1f2937; margin-bottom: 15px;">قائمة المستخدمين</h3>
            <table class="users-table">
                <thead>
                    <tr>
                        <th>الاسم</th>
                        <th>البريد الإلكتروني</th>
                        <th>الدور</th>
                        <th>الموقع</th>
                        <th>الحالة</th>
                        <th>تاريخ الإنضمام</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($users as $user)
                    <tr>
                        <td>{{ $user->name }}</td>
                        <td>{{ $user->email }}</td>
                        <td>
                            <span class="role-{{ $user->role }}">
                                @if($user->role === 'admin')
                                    مدير
                                @elseif($user->role === 'employer')
                                    صاحب عمل
                                @else
                                    باحث عن عمل
                                @endif
                            </span>
                        </td>
                        <td>{{ $user->location ?? 'غير محدد' }}</td>
                        <td>
                            <span class="status-{{ $user->email_verified_at ? 'verified' : 'unverified' }}">
                                {{ $user->email_verified_at ? 'مفعل' : 'غير مفعل' }}
                            </span>
                        </td>
                        <td>{{ $user->created_at->format('Y-m-d') }}</td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="6" style="text-align: center; color: #6b7280;">لا توجد مستخدمين</td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        @if($users->count() > 0)
        <!-- Additional Statistics -->
        <div style="margin-top: 30px;">
            <h3 style="color: #1f2937; margin-bottom: 15px;">إحصائيات إضافية</h3>
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px;">
                <div style="background: #f8fafc; padding: 15px; border-radius: 8px;">
                    <h4 style="margin: 0 0 10px 0; color: #374151;">معدل التفعيل</h4>
                    <p style="margin: 0; font-size: 18px; color: #059669; font-weight: bold;">
                        {{ $totalUsers > 0 ? round(($verificationStats['verified'] / $totalUsers) * 100, 1) : 0 }}%
                    </p>
                </div>
                <div style="background: #f8fafc; padding: 15px; border-radius: 8px;">
                    <h4 style="margin: 0 0 10px 0; color: #374151;">المستخدمين الجدد</h4>
                    <p style="margin: 0; font-size: 18px; color: #3b82f6; font-weight: bold;">
                        {{ $stats['recent_users'] }}
                    </p>
                </div>
            </div>
        </div>
        @endif

        <!-- Footer -->
        <div class="footer">
            <p>تم إنشاء هذا التقرير بواسطة نظام Hire Me</p>
            <p>{{ config('app.url') }}</p>
        </div>
    </div>
</body>
</html>
