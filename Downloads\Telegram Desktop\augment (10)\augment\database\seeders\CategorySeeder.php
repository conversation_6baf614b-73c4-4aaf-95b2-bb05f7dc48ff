<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'تقنية المعلومات',
                'icon' => 'laptop-code',
                'description' => 'وظائف في مجال تقنية المعلومات وتطوير البرمجيات',
            ],
            [
                'name' => 'التمويل والمحاسبة',
                'icon' => 'hand-holding-dollar',
                'description' => 'وظائف في مجال المالية والمحاسبة والتمويل',
            ],
            [
                'name' => 'التسويق والمبيعات',
                'icon' => 'bullhorn',
                'description' => 'وظائف في مجال التسويق والمبيعات والعلاقات العامة',
            ],
            [
                'name' => 'الرعاية الصحية',
                'icon' => 'user-doctor',
                'description' => 'وظائف في مجال الرعاية الصحية والطب',
            ],
            [
                'name' => 'التعليم والتدريب',
                'icon' => 'graduation-cap',
                'description' => 'وظائف في مجال التعليم والتدريب والتطوير',
            ],
            [
                'name' => 'الإدارة والأعمال',
                'icon' => 'building',
                'description' => 'وظائف في مجال الإدارة وريادة الأعمال',
            ],
            [
                'name' => 'التصميم والإبداع',
                'icon' => 'palette',
                'description' => 'وظائف في مجال التصميم والفنون والإبداع',
            ],
            [
                'name' => 'القانون والاستشارات',
                'icon' => 'gavel',
                'description' => 'وظائف في مجال القانون والاستشارات القانونية',
            ],
        ];

        foreach ($categories as $category) {
            Category::firstOrCreate(
                ['slug' => Str::slug($category['name'])],
                [
                    'name' => $category['name'],
                    'icon' => $category['icon'],
                    'description' => $category['description'],
                ]
            );
        }
    }
}
