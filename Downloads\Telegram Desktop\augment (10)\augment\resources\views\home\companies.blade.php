<!-- Top Companies Section -->
<section class="py-16 bg-white dark:bg-gray-900">
    <div class="p-6">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-building text-primary mr-3"></i>
                    الشركات المميزة
                </h2>
                <p class="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                    اكتشف أفضل الشركات والمؤسسات التي تبحث عن المواهب المتميزة
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                @forelse($featuredEmployers as $employer)
                <!-- Company Card -->
                <div class="bg-white dark:bg-dark-card rounded-2xl shadow-sm hover:shadow-xl hover:scale-105 transition-all duration-300 p-6 border border-gray-100 dark:border-gray-700 group">
                    <!-- Company Logo -->
                    <div class="relative mb-6">
                        <div class="w-24 h-24 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-2xl flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300">
                            @if($employer->company_logo)
                                <img src="{{ asset('storage/' . $employer->company_logo) }}"
                                     alt="{{ $employer->company_name }}"
                                     class="w-20 h-20 object-contain rounded-xl">
                            @else
                                <i class="fas fa-building text-4xl text-primary"></i>
                            @endif
                        </div>
                        <div class="absolute -top-2 -right-2 w-8 h-8 gradient-bg rounded-full flex items-center justify-center text-white text-xs font-bold shadow-lg">
                            {{ $employer->jobs->count() }}
                        </div>
                    </div>

                    <!-- Company Name -->
                    <h3 class="text-xl font-bold mb-3 text-center group-hover:text-primary transition-colors duration-300">
                        <a href="{{ route('employers.show', $employer) }}" class="hover:text-primary transition">
                            {{ $employer->company_name }}
                        </a>
                    </h3>

                    <!-- Rating -->
                    <div class="flex justify-center items-center gap-1 mb-4">
                        @php
                            $rating = $employer->getAverageRatingAttribute();
                            $fullStars = floor($rating);
                            $halfStar = $rating - $fullStars >= 0.5;
                        @endphp

                        @for($i = 1; $i <= 5; $i++)
                            @if($i <= $fullStars)
                                <i class="fas fa-star text-yellow-500"></i>
                            @elseif($i == $fullStars + 1 && $halfStar)
                                <i class="fas fa-star-half-alt text-yellow-500"></i>
                            @else
                                <i class="far fa-star text-yellow-500"></i>
                            @endif
                        @endfor

                        <span class="text-sm text-gray-600 dark:text-gray-400 mr-2">({{ number_format($rating, 1) }})</span>
                    </div>

                    <!-- Company Info -->
                    <div class="text-center space-y-2 mb-4">
                        <div class="flex items-center justify-center text-gray-600 dark:text-gray-400 text-sm">
                            <i class="fas fa-map-marker-alt mr-2 text-primary"></i>
                            {{ $employer->location ?: 'غير محدد' }}
                        </div>
                        <div class="flex items-center justify-center text-gray-600 dark:text-gray-400 text-sm">
                            <i class="fas fa-briefcase mr-2 text-primary"></i>
                            {{ $employer->jobs->where('expires_at', '>', now())->count() }} وظيفة متاحة
                        </div>
                    </div>

                    <!-- Action Button -->
                    <div class="text-center">
                        <a href="{{ route('employers.show', $employer) }}" class="gradient-bg text-white px-6 py-2 rounded-lg hover:opacity-90 hover:scale-105 transition-all duration-300 text-sm font-medium shadow-lg">
                            <i class="fas fa-eye mr-1"></i>
                            عرض الشركة
                        </a>
                    </div>
                </div>
                @empty
                <div class="col-span-full">
                    <div class="text-center py-16">
                        <div class="w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-building text-3xl text-gray-400"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">لا توجد شركات مميزة</h3>
                        <p class="text-gray-600 dark:text-gray-400 mb-6">لم يتم العثور على شركات مميزة في الوقت الحالي</p>
                        <a href="{{ route('employers.index') }}" class="gradient-bg text-white px-6 py-3 rounded-lg hover:opacity-90 transition-opacity">
                            تصفح جميع الشركات
                        </a>
                    </div>
                </div>
                @endforelse
            </div>

            <!-- View All Companies Button -->
            <div class="text-center mt-12">
                <a href="{{ route('employers.index') }}" class="inline-flex items-center px-8 py-4 bg-white dark:bg-dark-card text-primary border-2 border-primary rounded-xl hover:bg-primary hover:text-white transition-all duration-300 font-semibold shadow-lg hover:shadow-xl">
                    <i class="fas fa-building mr-2"></i>
                    عرض جميع الشركات
                    <i class="fas fa-arrow-left mr-2"></i>
                </a>
            </div>
        </div>
    </div>
</section>