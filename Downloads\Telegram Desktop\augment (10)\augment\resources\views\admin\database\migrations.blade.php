@extends('layouts.admin')

@section('title', 'إدارة الترحيلات - Hire Me')
@section('header_title', 'إدارة الترحيلات')

@section('content')
<div class="p-6">
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center gap-4">
                <a href="{{ route('admin.database.index') }}" class="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white">
                    <i class="fas fa-arrow-right text-xl"></i>
                </a>
                <div>
                    <h2 class="text-2xl font-bold">إدارة الترحيلات</h2>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">إدارة ترحيلات قاعدة البيانات والتحديثات</p>
                </div>
            </div>
            
            <div class="flex gap-3">
                <form action="{{ route('admin.database.migrations.rollback') }}" method="POST" class="inline">
                    @csrf
                    <button type="submit" class="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors flex items-center gap-2" onclick="return confirm('هل تريد التراجع عن آخر دفعة من الترحيلات؟')">
                        <i class="fas fa-undo"></i>
                        <span>تراجع</span>
                    </button>
                </form>
                
                <form action="{{ route('admin.database.migrations.run') }}" method="POST" class="inline">
                    @csrf
                    <button type="submit" class="btn-gradient text-white px-4 py-2 rounded-lg hover:opacity-90 transition-colors flex items-center gap-2" onclick="return confirm('هل تريد تشغيل الترحيلات المعلقة؟')">
                        <i class="fas fa-play"></i>
                        <span>تشغيل الترحيلات</span>
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg">
            <div class="flex items-center">
                <i class="fas fa-check-circle ml-2"></i>
                {{ session('success') }}
            </div>
        </div>
    @endif

    @if(session('error'))
        <div class="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
            <div class="flex items-center">
                <i class="fas fa-exclamation-circle ml-2"></i>
                {{ session('error') }}
            </div>
        </div>
    @endif

    @if(session('output'))
        <div class="mb-6 p-4 bg-gray-100 border border-gray-400 text-gray-700 rounded-lg">
            <div class="flex items-start">
                <i class="fas fa-terminal ml-2 mt-1"></i>
                <div>
                    <p class="font-medium mb-2">مخرجات الأمر:</p>
                    <pre class="text-sm bg-gray-800 text-green-400 p-3 rounded overflow-x-auto">{{ session('output') }}</pre>
                </div>
            </div>
        </div>
    @endif

    <!-- Migration Statistics -->
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                    <i class="fas fa-code-branch text-blue-600 dark:text-blue-400 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm text-gray-500 dark:text-gray-400">إجمالي الترحيلات</p>
                    <p class="text-2xl font-bold">{{ $migrations->count() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                    <i class="fas fa-check text-green-600 dark:text-green-400 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm text-gray-500 dark:text-gray-400">المُنفذة</p>
                    <p class="text-2xl font-bold">{{ $migrations->where('status', 'مُنفذ')->count() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center">
                    <i class="fas fa-clock text-yellow-600 dark:text-yellow-400 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm text-gray-500 dark:text-gray-400">المعلقة</p>
                    <p class="text-2xl font-bold">{{ $migrations->where('status', 'معلق')->count() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
                    <i class="fas fa-layer-group text-purple-600 dark:text-purple-400 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm text-gray-500 dark:text-gray-400">آخر دفعة</p>
                    <p class="text-2xl font-bold">{{ $migrations->where('status', 'مُنفذ')->max('batch') ?? 0 }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Migration Guidelines -->
    <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-xl p-6 mb-8">
        <div class="flex items-start gap-4">
            <i class="fas fa-exclamation-triangle text-yellow-600 dark:text-yellow-400 text-xl mt-1"></i>
            <div>
                <h3 class="text-lg font-bold text-yellow-900 dark:text-yellow-100 mb-2">تحذيرات مهمة</h3>
                <div class="text-yellow-800 dark:text-yellow-200 space-y-2">
                    <p>• <strong>انتبه:</strong> تشغيل الترحيلات قد يغير هيكل قاعدة البيانات بشكل دائم</p>
                    <p>• <strong>نصيحة:</strong> أنشئ نسخة احتياطية قبل تشغيل أي ترحيلات جديدة</p>
                    <p>• <strong>تحذير:</strong> التراجع عن الترحيلات قد يؤدي إلى فقدان البيانات</p>
                    <p>• <strong>مهم:</strong> اختبر الترحيلات في بيئة التطوير أولاً</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Migrations List -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-bold">قائمة الترحيلات</h3>
        </div>
        
        @if($migrations->isNotEmpty())
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-800">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">اسم الترحيل</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الحالة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الدفعة</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-dark-card divide-y divide-gray-200 dark:divide-gray-700">
                        @foreach($migrations as $migration)
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <i class="fas fa-file-code text-gray-400 ml-2"></i>
                                        <span class="text-sm font-medium text-gray-900 dark:text-white font-mono">{{ $migration['file'] }}</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @if($migration['status'] === 'مُنفذ')
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                                            <i class="fas fa-check ml-1"></i>
                                            مُنفذ
                                        </span>
                                    @else
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400">
                                            <i class="fas fa-clock ml-1"></i>
                                            معلق
                                        </span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                    {{ $migration['batch'] ?? '-' }}
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="px-6 py-12 text-center text-gray-500 dark:text-gray-400">
                <i class="fas fa-code-branch text-4xl mb-4"></i>
                <p class="text-lg font-medium mb-2">لا توجد ترحيلات</p>
                <p>لم يتم العثور على أي ملفات ترحيل في المشروع</p>
            </div>
        @endif
    </div>
</div>
@endsection
