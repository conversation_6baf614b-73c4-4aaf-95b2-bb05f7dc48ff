<?php

namespace App\Http\Controllers;

use App\Models\Job;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use <PERSON><PERSON>\Cashier\Cashier;

class PaymentController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:employer']);
    }
    
    public function checkout(Request $request)
    {
        $request->validate([
            'payment_type' => 'required|in:job_posting,featured_listing',
            'job_id' => 'nullable|exists:jobs,id',
        ]);
        
        $user = Auth::user();
        $amount = 0;
        
        if ($request->payment_type === 'job_posting') {
            $amount = 50; // $50 for regular job posting
        } else if ($request->payment_type === 'featured_listing') {
            $amount = 100; // $100 for featured listing
        }
        
        return view('payments.checkout', [
            'amount' => $amount,
            'payment_type' => $request->payment_type,
            'job_id' => $request->job_id,
            'intent' => $user->createSetupIntent(),
        ]);
    }
    
    public function process(Request $request)
    {
        $request->validate([
            'payment_method' => 'required',
            'payment_type' => 'required|in:job_posting,featured_listing',
            'job_id' => 'nullable|exists:jobs,id',
        ]);
        
        $user = Auth::user();
        $amount = 0;
        
        if ($request->payment_type === 'job_posting') {
            $amount = 50; // $50 for regular job posting
        } else if ($request->payment_type === 'featured_listing') {
            $amount = 100; // $100 for featured listing
        }
        
        try {
            // Process payment with Stripe
            $payment = $user->charge($amount * 100, $request->payment_method);
            
            // Record payment in database
            $paymentRecord = new Payment();
            $paymentRecord->employer_id = $user->employer->id;
            $paymentRecord->payment_id = $payment->id;
            $paymentRecord->amount = $amount;
            $paymentRecord->payment_method = 'credit_card';
            $paymentRecord->status = 'completed';
            $paymentRecord->type = $request->payment_type;
            $paymentRecord->save();
            
            // If payment is for featuring a job
            if ($request->payment_type === 'featured_listing' && $request->job_id) {
                $job = Job::findOrFail($request->job_id);
                $this->authorize('update', $job);
                $job->is_featured = true;
                $job->save();
            }
            
            return redirect()->route('employer.jobs')->with('success', 'Payment processed successfully');
            
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Payment failed: ' . $e->getMessage());
        }
    }
    
    public function history()
    {
        $payments = Auth::user()->employer->payments()->latest()->paginate(10);
        return view('payments.history', compact('payments'));
    }
}