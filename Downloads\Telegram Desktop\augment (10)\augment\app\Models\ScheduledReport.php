<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ScheduledReport extends Model
{
    use HasFactory;
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'user_id',
        'email',
        'report_type',
        'frequency',
        'day_of_week',
        'day_of_month',
        'filters',
        'include_attachment',
        'last_sent_at',
    ];
    
    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'filters' => 'array',
        'include_attachment' => 'boolean',
        'last_sent_at' => 'datetime',
    ];
    
    /**
     * Get the user that owns the scheduled report.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
