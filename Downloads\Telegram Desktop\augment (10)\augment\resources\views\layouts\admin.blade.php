<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'لوحة تحكم الأدمن - Hire Me')</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <link rel="stylesheet" href="{{ asset('css/color-themes.css') }}">
    <link rel="stylesheet" href="{{ asset('css/admin-enhancements.css') }}">
    <link rel="stylesheet" href="{{ asset('css/theme-transitions.css') }}">
    <!-- نظام الثيم المحسن -->
    <script src="{{ asset('js/theme-system.js') }}"></script>
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        // نظام ألوان متدرج وجميل
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                        accent: {
                            50: '#fdf4ff',
                            100: '#fae8ff',
                            200: '#f5d0fe',
                            300: '#f0abfc',
                            400: '#e879f9',
                            500: '#d946ef',
                            600: '#c026d3',
                            700: '#a21caf',
                            800: '#86198f',
                            900: '#701a75',
                        },
                        success: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                        },
                        warning: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            200: '#fde68a',
                            300: '#fcd34d',
                            400: '#fbbf24',
                            500: '#f59e0b',
                            600: '#d97706',
                            700: '#b45309',
                            800: '#92400e',
                            900: '#78350f',
                        },
                        danger: {
                            50: '#fef2f2',
                            100: '#fee2e2',
                            200: '#fecaca',
                            300: '#fca5a5',
                            400: '#f87171',
                            500: '#ef4444',
                            600: '#dc2626',
                            700: '#b91c1c',
                            800: '#991b1b',
                            900: '#7f1d1d',
                        },
                        // ألوان الوضع المظلم المحسنة
                        'dark-bg': '#0f172a',
                        'dark-card': '#1e293b',
                        'dark-sidebar': '#0f172a',
                        'dark-surface': '#334155',
                    },
                    fontFamily: {
                        'tajawal': ['Tajawal', 'sans-serif']
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-in': 'slideIn 0.3s ease-out',
                        'bounce-in': 'bounceIn 0.6s ease-out',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideIn: {
                            '0%': { transform: 'translateX(-100%)' },
                            '100%': { transform: 'translateX(0)' },
                        },
                        bounceIn: {
                            '0%': { transform: 'scale(0.3)', opacity: '0' },
                            '50%': { transform: 'scale(1.05)' },
                            '70%': { transform: 'scale(0.9)' },
                            '100%': { transform: 'scale(1)', opacity: '1' },
                        },
                        glow: {
                            '0%': { boxShadow: '0 0 5px rgba(14, 165, 233, 0.5)' },
                            '100%': { boxShadow: '0 0 20px rgba(14, 165, 233, 0.8)' },
                        },
                    },
                    backdropBlur: {
                        xs: '2px',
                    }
                },
            }
        }

        // نظام الثيم يتم تحميله من ملف منفصل
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;600;700;800&display=swap');

        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #fefefe 0%, #f8fafc 50%, #f1f5f9 100%);
        }

        .dark body {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
        }

        /* تدرجات لونية جميلة */
        .gradient-primary {
            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 50%, #0369a1 100%);
        }

        .gradient-accent {
            background: linear-gradient(135deg, #d946ef 0%, #c026d3 50%, #a21caf 100%);
        }

        .gradient-success {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 50%, #15803d 100%);
        }

        .gradient-warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #b45309 100%);
        }

        .gradient-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%);
        }

        /* خلفية الشريط الجانبي مع تأثير زجاجي */
        .glass-sidebar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            height: 100vh;
            max-height: 100vh;
        }

        .dark .glass-sidebar {
            background: rgba(15, 23, 42, 0.95);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* إصلاح مشكلة التمرير في الشريط الجانبي */
        .sidebar-content {
            height: calc(100vh - 140px);
            overflow-y: auto !important;
            overflow-x: hidden;
            max-height: calc(100vh - 140px);
        }

        /* شريط التمرير المخصص */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(148, 163, 184, 0.1);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #0ea5e9, #d946ef);
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #0284c7, #c026d3);
        }

        .dark ::-webkit-scrollbar-track {
            background: rgba(51, 65, 85, 0.3);
        }

        /* عنصر التنقل النشط */
        .active-nav-item {
            background: linear-gradient(135deg, rgba(14, 165, 233, 0.1) 0%, rgba(217, 70, 239, 0.1) 100%);
            color: #0ea5e9;
            border-right: 4px solid;
            border-image: linear-gradient(135deg, #0ea5e9, #d946ef) 1;
            position: relative;
            overflow: hidden;
        }

        .active-nav-item::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(135deg, #0ea5e9, #d946ef);
            animation: glow 2s ease-in-out infinite alternate;
        }

        .dark .active-nav-item {
            background: linear-gradient(135deg, rgba(14, 165, 233, 0.2) 0%, rgba(217, 70, 239, 0.2) 100%);
            color: #38bdf8;
        }

        /* تأثيرات الهوفر المحسنة */
        .nav-item-hover {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .nav-item-hover::before {
            content: '';
            position: absolute;
            top: 0;
            right: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(14, 165, 233, 0.1), transparent);
            transition: right 0.5s ease;
        }

        .nav-item-hover:hover::before {
            right: 100%;
        }

        .nav-item-hover:hover {
            background: linear-gradient(135deg, rgba(14, 165, 233, 0.05) 0%, rgba(217, 70, 239, 0.05) 100%);
            transform: translateX(8px);
            color: #0ea5e9;
        }

        .dark .nav-item-hover:hover {
            color: #38bdf8;
        }

        /* بطاقات بتأثير زجاجي */
        .glass-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .dark .glass-card {
            background: rgba(30, 41, 59, 0.9);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        /* تأثيرات الأزرار */
        .btn-gradient {
            background: linear-gradient(135deg, #0ea5e9 0%, #d946ef 100%);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-gradient::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn-gradient:hover::before {
            left: 100%;
        }

        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(14, 165, 233, 0.3);
        }

        /* تأثيرات الإحصائيات */
        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .stat-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .dark .stat-card {
            background: rgba(30, 41, 59, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .dark .stat-card:hover {
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        /* تأثير النبضة للإشعارات */
        .notification-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: .5;
            }
        }

        /* تأثير التوهج */
        .glow-effect {
            box-shadow: 0 0 20px rgba(14, 165, 233, 0.3);
            animation: glow 2s ease-in-out infinite alternate;
        }

        /* تحسين الخطوط */
        .text-gradient {
            background: linear-gradient(135deg, #0ea5e9, #d946ef);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* تأثيرات إضافية للحركة */
        .animate-blob {
            border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
            animation: blob 7s ease-in-out infinite;
        }

        .animation-delay-2000 {
            animation-delay: 2s;
        }

        .animation-delay-4000 {
            animation-delay: 4s;
        }

        @keyframes blob {
            0% {
                border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
                transform: translate(0px, 0px) scale(1);
            }
            33% {
                border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%;
                transform: translate(30px, -50px) scale(1.1);
            }
            66% {
                border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%;
                transform: translate(-20px, 20px) scale(0.9);
            }
            100% {
                border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
                transform: translate(0px, 0px) scale(1);
            }
        }

        /* Custom Scrollbar Styles */
        .scrollbar-thin {
            scrollbar-width: thin;
        }

        .scrollbar-thin::-webkit-scrollbar {
            width: 6px;
        }

        .scrollbar-thin::-webkit-scrollbar-track {
            background: transparent;
        }

        .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
            background-color: #d1d5db;
            border-radius: 3px;
        }

        .dark .scrollbar-thumb-gray-600::-webkit-scrollbar-thumb {
            background-color: #4b5563;
            border-radius: 3px;
        }

        .scrollbar-thin::-webkit-scrollbar-thumb:hover {
            background-color: #9ca3af;
        }

        .dark .scrollbar-thin::-webkit-scrollbar-thumb:hover {
            background-color: #6b7280;
        }

        /* إجبار التمرير على العمل */
        .sidebar-content {
            min-height: 0;
            flex: 1;
        }

        /* تأكد من أن الشريط الجانبي يأخذ الارتفاع الكامل */
        aside.glass-sidebar {
            display: flex !important;
            flex-direction: column !important;
        }

        /* إجبار المحتوى على التمرير */
        .sidebar-content nav {
            padding-bottom: 2rem;
        }

        /* تأكد من أن جميع العناصر مرئية */
        .sidebar-content > * {
            flex-shrink: 0;
        }

        /* إضافة تمرير قوي للشريط الجانبي */
        .glass-sidebar .sidebar-content {
            overflow-y: scroll !important;
            overflow-x: hidden !important;
            -webkit-overflow-scrolling: touch;
        }

        /* إخفاء scrollbar في Firefox */
        .sidebar-content {
            scrollbar-width: thin;
            scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
        }

        /* تأكد من أن المحتوى يأخذ المساحة الكاملة */
        .sidebar-content nav:last-child {
            padding-bottom: 3rem;
        }

        /* حل نهائي لمشكلة التمرير */
        @media (min-width: 1024px) {
            .glass-sidebar {
                height: 100vh !important;
                display: flex !important;
                flex-direction: column !important;
            }

            .glass-sidebar .sidebar-content {
                flex: 1 !important;
                overflow-y: auto !important;
                overflow-x: hidden !important;
                height: auto !important;
                max-height: none !important;
                min-height: 0 !important;
            }
        }
    </style>
    @yield('styles')
</head>
<body class="bg-gradient-to-br from-slate-50 to-slate-200 dark:from-dark-bg dark:to-slate-900 text-gray-800 dark:text-gray-200 transition-all duration-500 min-h-screen">
    <div class="flex min-h-screen relative">
        <!-- خلفية متحركة بألوان هادئة وجميلة -->
        <div class="fixed inset-0 overflow-hidden pointer-events-none">
            <div class="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-100 to-indigo-200 dark:from-blue-900/30 dark:to-indigo-900/30 rounded-full filter blur-2xl opacity-30 animate-blob"></div>
            <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-purple-100 to-pink-200 dark:from-purple-900/30 dark:to-pink-900/30 rounded-full filter blur-2xl opacity-30 animate-blob animation-delay-2000"></div>
            <div class="absolute top-40 left-40 w-80 h-80 bg-gradient-to-br from-emerald-100 to-teal-200 dark:from-emerald-900/30 dark:to-teal-900/30 rounded-full filter blur-2xl opacity-30 animate-blob animation-delay-4000"></div>
        </div>

        <!-- Sidebar -->
        <aside class="w-64 glass-sidebar shadow-2xl fixed h-full z-20 hidden lg:block animate-slide-in" style="display: flex; flex-direction: column; height: 100vh;">
            <div class="p-6 border-b border-white/20 dark:border-gray-700/50 flex-shrink-0">
                <div class="flex items-center justify-center">
                    <div class="text-center">
                        <div class="w-16 h-16 mx-auto mb-3 gradient-primary rounded-2xl flex items-center justify-center shadow-lg glow-effect">
                            <i class="fas fa-briefcase text-2xl text-white"></i>
                        </div>
                        <h1 class="text-gradient text-xl font-bold tracking-wide">Hire Me</h1>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">لوحة التحكم المتقدمة</p>
                    </div>
                </div>
            </div>

            <div class="py-6 sidebar-content scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent" style="flex: 1 1 auto; overflow-y: auto !important; overflow-x: hidden; min-height: 0; height: auto;">
                <div class="px-6 mb-4">
                    <h3 class="text-xs text-gray-500 dark:text-gray-400 uppercase font-semibold tracking-wider flex items-center gap-2">
                        <div class="w-2 h-2 bg-primary-500 rounded-full"></div>
                        لوحة التحكم
                    </h3>
                </div>
                <nav class="space-y-1 px-3">
                    <a href="{{ route('admin.index') }}" class="group flex items-center gap-3 px-3 py-3 rounded-xl {{ request()->routeIs('admin.index') ? 'active-nav-item' : 'nav-item-hover text-gray-700 dark:text-gray-300' }}">
                        <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center text-white shadow-lg group-hover:shadow-primary-500/25 transition-all duration-300">
                            <i class="fas fa-tachometer-alt text-sm"></i>
                        </div>
                        <span class="font-medium">الرئيسية</span>
                    </a>
                    <a href="{{ route('admin.jobs.index') }}" class="group flex items-center gap-3 px-3 py-3 rounded-xl {{ request()->routeIs('admin.jobs.*') ? 'active-nav-item' : 'nav-item-hover text-gray-700 dark:text-gray-300' }}">
                        <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-accent-400 to-accent-600 flex items-center justify-center text-white shadow-lg group-hover:shadow-accent-500/25 transition-all duration-300">
                            <i class="fas fa-briefcase text-sm"></i>
                        </div>
                        <span class="font-medium">إدارة الوظائف</span>
                    </a>
                    <a href="{{ route('admin.applications.index') }}" class="group flex items-center gap-3 px-3 py-3 rounded-xl {{ request()->routeIs('admin.applications.*') ? 'active-nav-item' : 'nav-item-hover text-gray-700 dark:text-gray-300' }}">
                        <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-warning-400 to-warning-600 flex items-center justify-center text-white shadow-lg group-hover:shadow-warning-500/25 transition-all duration-300">
                            <i class="fas fa-file-alt text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <span class="font-medium">طلبات التوظيف</span>
                        </div>
                        @php
                            $adminApplicationCount = \App\Models\Application::where('status', 'pending')->count();
                        @endphp
                        @if($adminApplicationCount > 0)
                            <span class="bg-gradient-to-r from-danger-400 to-danger-600 text-white text-xs px-2 py-1 rounded-full shadow-lg notification-pulse">{{ $adminApplicationCount > 99 ? '99+' : $adminApplicationCount }}</span>
                        @endif
                    </a>
                    <a href="{{ route('admin.candidates.index') }}" class="group flex items-center gap-3 px-3 py-3 rounded-xl {{ request()->routeIs('admin.candidates.*') ? 'active-nav-item' : 'nav-item-hover text-gray-700 dark:text-gray-300' }}">
                        <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-success-400 to-success-600 flex items-center justify-center text-white shadow-lg group-hover:shadow-success-500/25 transition-all duration-300">
                            <i class="fas fa-users text-sm"></i>
                        </div>
                        <span class="font-medium">المرشحون المحفوظون</span>
                    </a>
                    <a href="{{ route('admin.reviews.index') }}" class="group flex items-center gap-3 px-3 py-3 rounded-xl {{ request()->routeIs('admin.reviews.*') ? 'active-nav-item' : 'nav-item-hover text-gray-700 dark:text-gray-300' }}">
                        <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-yellow-400 to-orange-500 flex items-center justify-center text-white shadow-lg group-hover:shadow-yellow-500/25 transition-all duration-300">
                            <i class="fas fa-star text-sm"></i>
                        </div>
                        <span class="font-medium">المراجعات والتقييمات</span>
                    </a>
                    <a href="{{ route('admin.reports.index') }}" class="group flex items-center gap-3 px-3 py-3 rounded-xl {{ request()->routeIs('admin.reports.*') ? 'active-nav-item' : 'nav-item-hover text-gray-700 dark:text-gray-300' }}">
                        <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-indigo-400 to-purple-600 flex items-center justify-center text-white shadow-lg group-hover:shadow-indigo-500/25 transition-all duration-300">
                            <i class="fas fa-chart-bar text-sm"></i>
                        </div>
                        <span class="font-medium">التقارير والإحصائيات</span>
                    </a>
                    <a href="{{ route('admin.notifications.index') }}" class="group flex items-center gap-3 px-3 py-3 rounded-xl {{ request()->routeIs('admin.notifications.*') ? 'active-nav-item' : 'nav-item-hover text-gray-700 dark:text-gray-300' }}">
                        <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-yellow-400 to-orange-500 flex items-center justify-center text-white shadow-lg group-hover:shadow-yellow-500/25 transition-all duration-300">
                            <i class="fas fa-bell text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <span class="font-medium">الإشعارات</span>
                        </div>
                        @php
                            $unreadCount = auth()->check() ? app(\App\Services\NotificationService::class)->getUnreadCount(auth()->user()) : 0;
                        @endphp
                        @if($unreadCount > 0)
                        <span class="bg-gradient-to-r from-danger-400 to-danger-600 text-white text-xs px-2 py-1 rounded-full shadow-lg notification-pulse">{{ $unreadCount > 99 ? '99+' : $unreadCount }}</span>
                        @endif
                    </a>
                </nav>

                <div class="px-6 mb-4 mt-8">
                    <h3 class="text-xs text-gray-500 dark:text-gray-400 uppercase font-semibold tracking-wider flex items-center gap-2">
                        <div class="w-2 h-2 bg-accent-500 rounded-full"></div>
                        إعدادات الأدمن
                    </h3>
                </div>
                <nav class="space-y-1 px-3">
                    <a href="{{ route('admin.settings.general') }}" class="group flex items-center gap-3 px-3 py-3 rounded-xl {{ request()->routeIs('admin.settings.general') ? 'active-nav-item' : 'nav-item-hover text-gray-700 dark:text-gray-300' }}">
                        <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-slate-400 to-slate-600 flex items-center justify-center text-white shadow-lg group-hover:shadow-slate-500/25 transition-all duration-300">
                            <i class="fas fa-cog text-sm"></i>
                        </div>
                        <span class="font-medium">الإعدادات العامة</span>
                    </a>
                    <a href="{{ route('admin.settings.system') }}" class="group flex items-center gap-3 px-3 py-3 rounded-xl {{ request()->routeIs('admin.settings.system') ? 'active-nav-item' : 'nav-item-hover text-gray-700 dark:text-gray-300' }}">
                        <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-indigo-400 to-indigo-600 flex items-center justify-center text-white shadow-lg group-hover:shadow-indigo-500/25 transition-all duration-300">
                            <i class="fas fa-cogs text-sm"></i>
                        </div>
                        <span class="font-medium">إعدادات النظام</span>
                    </a>
                    <a href="{{ route('notifications.settings') }}" class="group flex items-center gap-3 px-3 py-3 rounded-xl {{ request()->routeIs('notifications.settings') ? 'active-nav-item' : 'nav-item-hover text-gray-700 dark:text-gray-300' }}">
                        <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-amber-400 to-orange-600 flex items-center justify-center text-white shadow-lg group-hover:shadow-amber-500/25 transition-all duration-300">
                            <i class="fas fa-bell-slash text-sm"></i>
                        </div>
                        <span class="font-medium">إعدادات الإشعارات</span>
                    </a>
                    <a href="{{ route('admin.users.index') }}" class="group flex items-center gap-3 px-3 py-3 rounded-xl {{ request()->routeIs('admin.users.*') ? 'active-nav-item' : 'nav-item-hover text-gray-700 dark:text-gray-300' }}">
                        <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-purple-400 to-purple-600 flex items-center justify-center text-white shadow-lg group-hover:shadow-purple-500/25 transition-all duration-300">
                            <i class="fas fa-users-cog text-sm"></i>
                        </div>
                        <span class="font-medium">إدارة المستخدمين</span>
                    </a>
                    <a href="{{ route('admin.database.index') }}" class="group flex items-center gap-3 px-3 py-3 rounded-xl {{ request()->routeIs('admin.database.*') ? 'active-nav-item' : 'nav-item-hover text-gray-700 dark:text-gray-300' }}">
                        <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-orange-400 to-orange-600 flex items-center justify-center text-white shadow-lg group-hover:shadow-orange-500/25 transition-all duration-300">
                            <i class="fas fa-database text-sm"></i>
                        </div>
                        <span class="font-medium">إدارة قاعدة البيانات</span>
                    </a>
                </nav>

                <!-- زر تسجيل الخروج -->
                <div class="px-3 mt-8 pb-6">
                    <form method="POST" action="{{ route('logout') }}">
                        @csrf
                        <button type="submit" class="group w-full flex items-center gap-3 px-3 py-3 rounded-xl nav-item-hover text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-all duration-300">
                            <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-red-400 to-red-600 flex items-center justify-center text-white shadow-lg group-hover:shadow-red-500/25 transition-all duration-300">
                                <i class="fas fa-sign-out-alt text-sm"></i>
                            </div>
                            <span class="font-medium">تسجيل الخروج</span>
                        </button>
                    </form>
                </div>
            </div>
        </aside>

        <!-- Mobile Menu Button -->
        <button id="mobileMenuBtn" class="lg:hidden fixed top-4 right-4 z-30 p-3 bg-primary-600 text-white rounded-lg shadow-lg">
            <i class="fas fa-bars"></i>
        </button>

        <!-- Main Content -->
        <main class="flex-1 lg:mr-64 transition-all duration-500 relative z-10">
            <!-- Header -->
            <header class="glass-card sticky top-0 z-10 shadow-xl border-0 animate-fade-in">
                <div class="flex items-center justify-between px-8 py-6">
                    <div class="flex items-center gap-6">
                        <button id="darkModeToggle" class="group relative p-3 rounded-xl bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-700 dark:to-slate-800 hover:from-primary-100 hover:to-primary-200 dark:hover:from-primary-800 dark:hover:to-primary-900 transition-all duration-300 shadow-lg hover:shadow-xl">
                            <i class="fas fa-moon dark:hidden text-slate-600 group-hover:text-primary-600 transition-colors"></i>
                            <i class="fas fa-sun hidden dark:block text-yellow-400 group-hover:text-yellow-300 transition-colors"></i>
                        </button>
                        <div>
                            <h1 class="text-2xl font-bold text-gradient">@yield('header_title', 'لوحة التحكم')</h1>
                            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">مرحباً بك في نظام إدارة التوظيف</p>
                        </div>
                    </div>

                    <div class="flex items-center gap-3">
                        @auth
                        @include('components.notification-dropdown')
                        @endauth
                        <button class="group relative p-3 rounded-xl bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-800 dark:to-primary-900 hover:shadow-lg transition-all duration-300">
                            <i class="fas fa-envelope text-primary-600 dark:text-primary-400 group-hover:scale-110 transition-transform"></i>
                            <span class="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-br from-success-400 to-success-600 text-white text-xs rounded-full flex items-center justify-center notification-pulse shadow-lg">7</span>
                        </button>
                        <a href="/" class="group p-3 rounded-xl bg-gradient-to-br from-success-100 to-success-200 dark:from-success-800 dark:to-success-900 hover:shadow-lg transition-all duration-300">
                            <i class="fas fa-home text-success-600 dark:text-success-400 group-hover:scale-110 transition-transform"></i>
                        </a>
                        <div class="w-px h-8 bg-gray-300 dark:bg-gray-600"></div>
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit" class="group p-3 rounded-xl bg-gradient-to-br from-red-100 to-red-200 dark:from-red-800 dark:to-red-900 hover:shadow-lg transition-all duration-300">
                                <i class="fas fa-sign-out-alt text-red-600 dark:text-red-400 group-hover:scale-110 transition-transform"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </header>

            @yield('content')
        </main>
    </div>

    <script>


        // Mobile Menu Toggle
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const sidebar = document.querySelector('aside');

        if (mobileMenuBtn && sidebar) {
            mobileMenuBtn.addEventListener('click', () => {
                sidebar.classList.toggle('hidden');
                sidebar.classList.toggle('lg:block');
            });
        }

        // نظام الثيم يتم إدارته من theme-system.js

        // Add smooth scroll behavior
        document.documentElement.style.scrollBehavior = 'smooth';

        // Add entrance animations to nav items
        document.addEventListener('DOMContentLoaded', () => {
            const navItems = document.querySelectorAll('nav a');
            navItems.forEach((item, index) => {
                item.style.animationDelay = `${index * 0.1}s`;
                item.classList.add('animate-fade-in');
            });
        });

        // Add hover sound effect (optional)
        const navItems = document.querySelectorAll('.nav-item-hover');
        navItems.forEach(item => {
            item.addEventListener('mouseenter', () => {
                // You can add a subtle sound effect here if desired
                item.style.transform = 'translateX(8px) scale(1.02)';
            });

            item.addEventListener('mouseleave', () => {
                item.style.transform = 'translateX(0) scale(1)';
            });
        });

        // إصلاح مشكلة التمرير في الشريط الجانبي
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarContent = document.querySelector('.sidebar-content');
            if (sidebarContent) {
                // تأكد من أن التمرير يعمل
                sidebarContent.style.overflowY = 'auto';
                sidebarContent.style.height = 'calc(100vh - 140px)';
                sidebarContent.style.maxHeight = 'calc(100vh - 140px)';

                // إضافة تمرير سلس
                sidebarContent.style.scrollBehavior = 'smooth';

                console.log('Sidebar scroll fixed');
            }
        });
    </script>

    <!-- ملف التحسينات الإضافية -->
    <script src="{{ asset('js/admin-enhancements.js') }}"></script>
    <script src="{{ asset('js/notifications.js') }}"></script>

    @if(session('realtime_notification'))
    <div data-flash-notification
         data-message="{{ session('realtime_notification.message') }}"
         data-type="info"
         style="display: none;"></div>
    @endif

    @yield('scripts')
</body>
</html>
