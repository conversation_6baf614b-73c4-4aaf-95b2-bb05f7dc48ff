<?php

namespace App\Http\Controllers;

use App\Models\Job;
use App\Models\Employer;
use App\Models\Category;
use App\Models\Testimonial;

class HomeController extends Controller
{
    /**
     * عرض الصفحة الرئيسية
     */
    public function index()
    {
        // الحصول على الوظائف المميزة (أحدث 6 وظائف نشطة ومميزة)
        $featuredJobs = Job::with(['employer', 'category'])
            ->active() // استخدام scope active()
            ->where('is_featured', true)
            ->latest('posted_at')
            ->take(6)
            ->get();

        // إذا لم توجد وظائف مميزة كافية، احصل على أحدث الوظائف
        if ($featuredJobs->count() < 6) {
            $additionalJobs = Job::with(['employer', 'category'])
                ->active()
                ->whereNotIn('id', $featuredJobs->pluck('id'))
                ->latest('posted_at')
                ->take(6 - $featuredJobs->count())
                ->get();

            $featuredJobs = $featuredJobs->merge($additionalJobs);
        }

        // الحصول على الشركات المميزة (الشركات التي لديها أكثر وظائف نشطة)
        $featuredEmployers = Employer::with(['user', 'jobs' => function($query) {
                $query->active();
            }])
            ->withCount(['jobs' => function($query) {
                $query->active();
            }])
            ->having('jobs_count', '>', 0)
            ->orderByDesc('jobs_count')
            ->take(4)
            ->get();

        // الحصول على تصنيفات الوظائف مع عدد الوظائف النشطة
        $categories = Category::withCount(['jobs' => function($query) {
                $query->active();
            }])
            ->having('jobs_count', '>', 0)
            ->orderByDesc('jobs_count')
            ->take(8)
            ->get();

        // الحصول على آراء العملاء المميزة
        $testimonials = Testimonial::with('user')
            ->where('is_featured', true)
            ->latest()
            ->take(3)
            ->get();

        // إحصائيات عامة للصفحة الرئيسية
        $stats = [
            'total_jobs' => Job::active()->count(),
            'total_companies' => Employer::count(),
            'total_candidates' => \App\Models\JobSeeker::where('is_available', true)->count(),
            'total_applications' => \App\Models\Application::count(),
        ];

        return view('home', compact('featuredJobs', 'featuredEmployers', 'categories', 'testimonials', 'stats'));
    }
}
