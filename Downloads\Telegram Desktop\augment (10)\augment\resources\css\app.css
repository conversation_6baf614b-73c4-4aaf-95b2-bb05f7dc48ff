@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Tajawal font for Arabic support */
@import url("https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap");

/* RTL support */
[dir="rtl"] * {
    direction: rtl;
    text-align: right;
}

@layer base {
    body {
        @apply font-sans text-gray-800 bg-gray-50 dark:bg-gray-900 dark:text-gray-200;
    }
}

/* Custom Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

.animate-fade-in {
    animation: fadeIn 0.8s ease-out forwards;
}

.animate-slide-in-right {
    animation: slideInRight 0.8s ease-out forwards;
}

.animate-slide-in-left {
    animation: slideInLeft 0.8s ease-out forwards;
}

.animate-bounce-custom {
    animation: bounce 2s infinite;
}

/* Line clamp utility */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

@layer components {
    .btn {
        @apply px-4 py-2 rounded-xl font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-300 inline-flex items-center justify-center gap-2;
    }

    .btn-primary {
        @apply bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 focus:ring-blue-500 shadow-lg hover:shadow-xl transform hover:scale-105;
    }

    .btn-secondary {
        @apply bg-gray-200 text-gray-800 hover:bg-gray-300 focus:ring-gray-500 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 shadow-md hover:shadow-lg;
    }

    .card {
        @apply bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden;
    }

    .form-input {
        @apply w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-300;
    }

    .form-label {
        @apply block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2;
    }

    /* Notification specific styles */
    .notification-badge {
        @apply inline-flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-red-500 rounded-full;
    }

    .notification-item {
        @apply transition-all duration-300 hover:shadow-md;
    }

    .notification-item.unread {
        @apply border-l-4 border-orange-500;
    }

    .notification-icon {
        @apply w-10 h-10 rounded-lg flex items-center justify-center text-white shadow-lg;
    }

    /* Animation classes */
    .fade-in {
        animation: fadeIn 0.3s ease-in-out;
    }

    .slide-up {
        animation: slideUp 0.3s ease-out;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}
