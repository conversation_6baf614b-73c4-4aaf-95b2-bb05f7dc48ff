<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإحصائيات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 font-sans">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-center mb-8">اختبار الإحصائيات</h1>
        
        <!-- الإحصائيات العامة -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6 text-center">
                <i class="fas fa-users text-blue-500 text-3xl mb-2"></i>
                <h3 class="text-lg font-semibold">المستخدمين</h3>
                <p class="text-2xl font-bold">{{ \App\Models\User::count() }}</p>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6 text-center">
                <i class="fas fa-building text-green-500 text-3xl mb-2"></i>
                <h3 class="text-lg font-semibold">أصحاب العمل</h3>
                <p class="text-2xl font-bold">{{ \App\Models\Employer::count() }}</p>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6 text-center">
                <i class="fas fa-briefcase text-purple-500 text-3xl mb-2"></i>
                <h3 class="text-lg font-semibold">الوظائف</h3>
                <p class="text-2xl font-bold">{{ \App\Models\Job::count() }}</p>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6 text-center">
                <i class="fas fa-file-alt text-orange-500 text-3xl mb-2"></i>
                <h3 class="text-lg font-semibold">الطلبات</h3>
                <p class="text-2xl font-bold">{{ \App\Models\Application::count() }}</p>
            </div>
        </div>
        
        <!-- إحصائيات الوظائف -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">إحصائيات الوظائف</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="text-center">
                    <h4 class="font-semibold">الوظائف النشطة</h4>
                    <p class="text-xl font-bold text-green-600">{{ \App\Models\Job::where('is_active', true)->count() }}</p>
                </div>
                <div class="text-center">
                    <h4 class="font-semibold">الوظائف غير النشطة</h4>
                    <p class="text-xl font-bold text-red-600">{{ \App\Models\Job::where('is_active', false)->count() }}</p>
                </div>
                <div class="text-center">
                    <h4 class="font-semibold">الوظائف المنتهية</h4>
                    <p class="text-xl font-bold text-gray-600">{{ \App\Models\Job::where('expires_at', '<', now())->count() }}</p>
                </div>
            </div>
        </div>
        
        <!-- إحصائيات الطلبات -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">إحصائيات الطلبات</h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="text-center">
                    <h4 class="font-semibold">قيد المراجعة</h4>
                    <p class="text-xl font-bold text-yellow-600">{{ \App\Models\Application::where('status', 'pending')->count() }}</p>
                </div>
                <div class="text-center">
                    <h4 class="font-semibold">تمت المراجعة</h4>
                    <p class="text-xl font-bold text-blue-600">{{ \App\Models\Application::where('status', 'reviewed')->count() }}</p>
                </div>
                <div class="text-center">
                    <h4 class="font-semibold">مقبولة</h4>
                    <p class="text-xl font-bold text-green-600">{{ \App\Models\Application::where('status', 'accepted')->count() }}</p>
                </div>
                <div class="text-center">
                    <h4 class="font-semibold">مرفوضة</h4>
                    <p class="text-xl font-bold text-red-600">{{ \App\Models\Application::where('status', 'rejected')->count() }}</p>
                </div>
            </div>
        </div>
        
        <!-- قائمة أصحاب العمل -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">أصحاب العمل</h2>
            @if(\App\Models\Employer::count() > 0)
                <div class="overflow-x-auto">
                    <table class="w-full table-auto">
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="px-4 py-2 text-right">ID</th>
                                <th class="px-4 py-2 text-right">اسم الشركة</th>
                                <th class="px-4 py-2 text-right">المستخدم</th>
                                <th class="px-4 py-2 text-right">الوظائف</th>
                                <th class="px-4 py-2 text-right">الطلبات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach(\App\Models\Employer::with('user')->get() as $employer)
                            <tr class="border-b">
                                <td class="px-4 py-2">{{ $employer->id }}</td>
                                <td class="px-4 py-2">{{ $employer->company_name }}</td>
                                <td class="px-4 py-2">{{ $employer->user->name ?? 'غير محدد' }}</td>
                                <td class="px-4 py-2">{{ $employer->jobs()->count() }}</td>
                                <td class="px-4 py-2">
                                    {{ \App\Models\Application::whereHas('job', function($q) use ($employer) { 
                                        $q->where('employer_id', $employer->id); 
                                    })->count() }}
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <p class="text-gray-500 text-center">لا يوجد أصحاب عمل</p>
            @endif
        </div>
        
        <!-- أزرار الإجراءات -->
        <div class="text-center">
            <a href="{{ route('employer.statistics') }}" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg mr-4">
                صفحة الإحصائيات الرسمية
            </a>
            <a href="{{ route('employer.dashboard') }}" class="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg">
                لوحة التحكم
            </a>
        </div>
    </div>
</body>
</html>
