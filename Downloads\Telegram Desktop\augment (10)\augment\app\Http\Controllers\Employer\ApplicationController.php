<?php

namespace App\Http\Controllers\Employer;

use App\Http\Controllers\Controller;
use App\Models\Application;
use App\Models\Employer;
use App\Models\Job;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

class ApplicationController extends Controller
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->middleware('auth');
        $this->middleware('role:employer');
        $this->notificationService = $notificationService;
    }

    /**
     * عرض قائمة الطلبات
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        if (!$user) {
            return redirect()->route('login')->with('error', 'يجب تسجيل الدخول أولاً');
        }

        $employer = $user->employer;

        if (!$employer) {
            // محاولة إنشاء ملف صاحب عمل تلقائياً إذا كان المستخدم من نوع employer
            if ($user->role === 'employer') {
                $employer = \App\Models\Employer::create([
                    'user_id' => $user->id,
                    'company_name' => $user->company_name ?? 'شركة ' . $user->name,
                    'company_description' => $user->company_description ?? 'وصف الشركة',
                    'industry' => $user->industry ?? 'تقنية المعلومات',
                    'location' => $user->location ?? 'الرياض',
                    'company_size' => $user->company_size ?? '1-10',
                ]);
            } else {
                return redirect()->route('home')->with('error', 'ليس لديك صلاحية للوصول إلى هذه الصفحة');
            }
        }

        $query = Application::whereHas('job', function($q) use ($employer) {
            $q->where('employer_id', $employer->id);
        })->with(['jobSeeker.user', 'job']);

        // فلترة حسب الحالة
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // فلترة حسب الوظيفة
        if ($request->filled('job_id')) {
            $query->where('job_id', $request->job_id);
        }

        // البحث
        if ($request->filled('search')) {
            $query->whereHas('jobSeeker.user', function($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%');
            });
        }

        $applications = $query->latest()->paginate(15);

        // إحصائيات سريعة
        $stats = [
            'total' => Application::whereHas('job', function($q) use ($employer) {
                $q->where('employer_id', $employer->id);
            })->count(),
            'pending' => Application::whereHas('job', function($q) use ($employer) {
                $q->where('employer_id', $employer->id);
            })->where('status', 'pending')->count(),
            'reviewed' => Application::whereHas('job', function($q) use ($employer) {
                $q->where('employer_id', $employer->id);
            })->where('status', 'reviewed')->count(),
            'accepted' => Application::whereHas('job', function($q) use ($employer) {
                $q->where('employer_id', $employer->id);
            })->where('status', 'accepted')->count(),
            'rejected' => Application::whereHas('job', function($q) use ($employer) {
                $q->where('employer_id', $employer->id);
            })->where('status', 'rejected')->count(),
        ];

        // قائمة الوظائف للفلترة
        $jobs = Job::where('employer_id', $employer->id)->get();

        return view('employer.applications.index', compact('applications', 'stats', 'jobs'));
    }

    /**
     * عرض تفاصيل طلب التوظيف
     */
    public function show(Application $application)
    {
        // التحقق من أن الطلب يخص صاحب العمل
        $user = Auth::user();

        if (!$user) {
            return redirect()->route('login')->with('error', 'يجب تسجيل الدخول أولاً');
        }

        $employer = $user->employer;

        if (!$employer) {
            // محاولة إنشاء ملف صاحب عمل تلقائياً إذا كان المستخدم من نوع employer
            if ($user->role === 'employer') {
                $employer = \App\Models\Employer::create([
                    'user_id' => $user->id,
                    'company_name' => $user->company_name ?? 'شركة ' . $user->name,
                    'company_description' => $user->company_description ?? 'وصف الشركة',
                    'industry' => $user->industry ?? 'تقنية المعلومات',
                    'location' => $user->location ?? 'الرياض',
                    'company_size' => $user->company_size ?? '1-10',
                ]);
            } else {
                return redirect()->route('home')->with('error', 'ليس لديك صلاحية للوصول إلى هذه الصفحة');
            }
        }

        if ($application->job->employer_id !== $employer->id) {
            abort(403, 'غير مصرح لك بعرض هذا الطلب');
        }

        $application->load(['jobSeeker.user', 'job']);

        return view('employers.application-details', compact('application'));
    }

    /**
     * تحديث حالة الطلب
     */
    public function updateStatus(Request $request, Application $application)
    {
        // تحميل العلاقات المطلوبة
        $application->load(['job', 'jobSeeker.user']);

        // التحقق من أن الطلب يخص صاحب العمل
        $user = Auth::user();

        if (!$user) {
            return redirect()->route('login')->with('error', 'يجب تسجيل الدخول أولاً');
        }

        $employer = $user->employer;

        if (!$employer) {
            // محاولة إنشاء ملف صاحب عمل تلقائياً إذا كان المستخدم من نوع employer
            if ($user->role === 'employer') {
                $employer = \App\Models\Employer::create([
                    'user_id' => $user->id,
                    'company_name' => $user->company_name ?? 'شركة ' . $user->name,
                    'company_description' => $user->company_description ?? 'وصف الشركة',
                    'industry' => $user->industry ?? 'تقنية المعلومات',
                    'location' => $user->location ?? 'الرياض',
                    'company_size' => $user->company_size ?? '1-10',
                ]);
            } else {
                return redirect()->route('home')->with('error', 'ليس لديك صلاحية للوصول إلى هذه الصفحة');
            }
        }

        // إضافة debugging
        Log::info('Update status check', [
            'application_id' => $application->id,
            'job_id' => $application->job_id,
            'job_employer_id' => $application->job ? $application->job->employer_id : 'null',
            'current_employer_id' => $employer->id,
            'user_id' => $user->id,
            'user_role' => $user->role
        ]);

        if (!$application->job) {
            abort(404, 'الوظيفة غير موجودة');
        }

        if ($application->job->employer_id !== $employer->id) {
            abort(403, 'غير مصرح لك بتعديل هذا الطلب');
        }

        $request->validate([
            'status' => 'required|in:pending,reviewed,accepted,rejected,interview,hired',
            'notes' => 'nullable|string|max:1000',
        ]);

        $oldStatus = $application->status;

        // تحديث الحالة فقط (الحقول الأخرى ستُضاف لاحقاً)
        $application->update([
            'status' => $request->status,
        ]);

        // إضافة الملاحظات إذا كان الحقل موجود
        if (Schema::hasColumn('applications', 'employer_notes')) {
            $application->update(['employer_notes' => $request->notes]);
        }

        // إضافة تاريخ المراجعة إذا كان الحقل موجود
        if (Schema::hasColumn('applications', 'reviewed_at')) {
            $application->update(['reviewed_at' => now()]);
        }

        // إرسال إشعار للمتقدم عن تحديث حالة الطلب
        if ($oldStatus !== $request->status && $application->jobSeeker && $application->jobSeeker->user_id) {
            try {
                // إنشاء إشعار للمتقدم
                \App\Models\Notification::create([
                    'user_id' => $application->jobSeeker->user_id,
                    'type' => 'application_status_update',
                    'message' => 'تم تحديث حالة طلبك للوظيفة "' . $application->job->title . '" إلى ' . $this->getStatusInArabic($request->status),
                    'link' => route('employer.applications.show', $application),
                    'is_read' => false
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to create notification', [
                    'error' => $e->getMessage(),
                    'application_id' => $application->id
                ]);
            }
        }

        if ($request->expectsJson()) {
            return response()->json(['success' => true, 'message' => 'تم تحديث حالة الطلب بنجاح']);
        }

        return redirect()->back()->with('success', 'تم تحديث حالة الطلب بنجاح');
    }

    /**
     * عرض نموذج تغيير حالة الطلب
     */
    public function showStatusForm(Application $application)
    {
        // تحميل العلاقات المطلوبة
        $application->load(['job', 'jobSeeker.user']);

        // التحقق من أن الطلب يخص صاحب العمل
        $user = Auth::user();
        if (!$user) {
            return redirect()->route('login')->with('error', 'يجب تسجيل الدخول أولاً');
        }

        $employer = null;
        if ($user->role === 'employer') {
            $employer = Employer::where('user_id', $user->id)->first();
        } elseif ($user->role === 'admin') {
            // السماح للمدير بالوصول
            return view('employer.applications.status-form', compact('application'));
        }

        if (!$employer) {
            abort(403, 'غير مصرح لك بالوصول');
        }

        if (!$application->job) {
            abort(404, 'الوظيفة غير موجودة');
        }

        if ($application->job->employer_id !== $employer->id) {
            abort(403, 'غير مصرح لك بتعديل هذا الطلب');
        }

        return view('employer.applications.status-form', compact('application'));
    }

    /**
     * إضافة مراجعة للطلب
     */
    public function addReview(Request $request, Application $application)
    {
        // التحقق من أن الطلب يخص صاحب العمل
        if ($application->job->employer_id !== Auth::id()) {
            abort(403, 'غير مصرح لك بمراجعة هذا الطلب');
        }

        $request->validate([
            'rating' => 'required|integer|min:1|max:5',
            'review' => 'nullable|string|max:1000',
        ]);

        // إضافة المراجعة
        $application->update([
            'employer_rating' => $request->rating,
            'employer_review' => $request->review,
            'reviewed_at' => now(),
        ]);

        // إرسال إشعار تقييم الملف الشخصي
        if ($application->jobSeeker && $application->jobSeeker->user) {
            $this->notificationService->profileRated(
                $application->jobSeeker->user,
                $request->rating,
                $request->review
            );
        }

        return redirect()->back()->with('success', 'تم إضافة التقييم بنجاح');
    }

    /**
     * إحصائيات الطلبات للتحديث المباشر
     */
    public function stats()
    {
        $user = Auth::user();
        $employer = $user->employer;

        if (!$employer) {
            return response()->json(['error' => 'Employer not found'], 404);
        }

        $stats = [
            'total' => Application::whereHas('job', function($q) use ($employer) {
                $q->where('employer_id', $employer->id);
            })->count(),
            'pending' => Application::whereHas('job', function($q) use ($employer) {
                $q->where('employer_id', $employer->id);
            })->where('status', 'pending')->count(),
            'accepted' => Application::whereHas('job', function($q) use ($employer) {
                $q->where('employer_id', $employer->id);
            })->where('status', 'accepted')->count(),
            'rejected' => Application::whereHas('job', function($q) use ($employer) {
                $q->where('employer_id', $employer->id);
            })->where('status', 'rejected')->count(),
        ];

        return response()->json($stats);
    }

    /**
     * تصدير قائمة الطلبات
     */
    public function export(Request $request)
    {
        $employer = Auth::user();
        $format = $request->input('format', 'csv');

        $query = Application::whereHas('job', function($q) use ($employer) {
            $q->where('employer_id', $employer->id);
        })->with(['jobSeeker.user', 'job']);

        // تطبيق نفس الفلاتر
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('job_id')) {
            $query->where('job_id', $request->job_id);
        }

        // البحث
        if ($request->filled('search')) {
            $query->whereHas('jobSeeker.user', function($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%');
            });
        }

        $applications = $query->get();

        if ($format === 'pdf') {
            return $this->exportApplicationsPDF($applications, $employer);
        }

        return $this->exportApplicationsCSV($applications);
    }

    /**
     * تصدير الطلبات بصيغة CSV
     */
    private function exportApplicationsCSV($applications)
    {
        $filename = 'applications_' . date('Y-m-d_H-i-s') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($applications) {
            $file = fopen('php://output', 'w');

            // إضافة BOM للدعم العربي
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            // معلومات التقرير
            fputcsv($file, ['تقرير طلبات التوظيف']);
            fputcsv($file, ['تاريخ التصدير', date('Y-m-d H:i:s')]);
            fputcsv($file, ['عدد الطلبات', count($applications)]);
            fputcsv($file, []);

            // العناوين
            fputcsv($file, [
                'اسم المتقدم',
                'البريد الإلكتروني',
                'رقم الهاتف',
                'الوظيفة',
                'موقع الوظيفة',
                'الحالة',
                'تاريخ التقديم',
                'التقييم',
                'الملاحظات'
            ]);

            // البيانات
            foreach ($applications as $application) {
                $user = $application->jobSeeker && $application->jobSeeker->user ? $application->jobSeeker->user : null;
                fputcsv($file, [
                    $user ? $user->name : 'غير محدد',
                    $user ? $user->email : 'غير محدد',
                    $user ? ($user->phone ?? 'غير محدد') : 'غير محدد',
                    $application->job->title,
                    $application->job->location ?? 'غير محدد',
                    getApplicationStatusInArabic($application->status),
                    $application->created_at->format('Y-m-d H:i'),
                    $application->employer_rating ?? 'غير مقيم',
                    $application->employer_notes ?? ''
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * تصدير الطلبات بصيغة PDF
     */
    private function exportApplicationsPDF($applications, $employer)
    {
        $data = [
            'applications' => $applications,
            'employer' => $employer,
            'totalApplications' => $applications->count(),
            'reportDate' => now()->format('Y-m-d H:i:s'),
            'stats' => [
                'pending' => $applications->where('status', 'pending')->count(),
                'accepted' => $applications->where('status', 'accepted')->count(),
                'rejected' => $applications->where('status', 'rejected')->count(),
            ]
        ];

        // إنشاء HTML للتقرير
        $html = view('employer.reports.applications-pdf', $data)->render();

        // إنشاء PDF باستخدام DomPDF (يحتاج تثبيت المكتبة)
        // للآن سنعيد HTML كاستجابة
        return response($html, 200, [
            'Content-Type' => 'text/html; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="applications_report_' . date('Y-m-d_H-i-s') . '.html"'
        ]);
    }

    /**
     * ترجمة حالة الطلب إلى العربية
     */
    private function getStatusInArabic($status)
    {
        $statuses = [
            'pending' => 'قيد المراجعة',
            'reviewed' => 'تمت المراجعة',
            'accepted' => 'مقبول',
            'rejected' => 'مرفوض',
            'interview' => 'مقابلة شخصية',
            'hired' => 'تم التوظيف'
        ];

        return $statuses[$status] ?? $status;
    }

    /**
     * تصدير طلب واحد
     */
    public function exportSingle(Request $request, Application $application)
    {
        // تحميل العلاقات المطلوبة
        $application->load(['jobSeeker.user', 'job']);

        Log::info('Export single application called', [
            'application_id' => $application->id,
            'format' => $request->input('format', 'csv')
        ]);

        $user = Auth::user();

        if (!$user) {
            return redirect()->route('login')->with('error', 'يجب تسجيل الدخول أولاً');
        }

        $employer = null;
        if ($user->role === 'employer') {
            $employer = Employer::where('user_id', $user->id)->first();
        } elseif ($user->role === 'admin') {
            // السماح للمدير بالوصول
            $format = $request->input('format', 'csv');
            if ($format === 'pdf') {
                return $this->exportSingleApplicationPDF($application, null);
            }
            return $this->exportSingleApplicationCSV($application);
        }

        if (!$employer) {
            abort(403, 'غير مصرح لك بالوصول');
        }

        // التحقق من أن الطلب ينتمي لصاحب العمل
        if ($application->job->employer_id !== $employer->id) {
            abort(403, 'غير مصرح لك بتصدير هذا الطلب');
        }

        $format = $request->input('format', 'csv');

        // إذا كان الطلب JSON، نحصل على البيانات من JSON body
        if ($request->isJson()) {
            $data = $request->json()->all();
            $format = $data['format'] ?? 'csv';
        }

        if ($format === 'pdf') {
            return $this->exportSingleApplicationPDF($application, $employer);
        }

        return $this->exportSingleApplicationCSV($application);
    }

    /**
     * تصدير طلب واحد بصيغة CSV
     */
    private function exportSingleApplicationCSV($application)
    {
        $filename = 'application_' . $application->id . '_' . date('Y-m-d_H-i-s') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($application) {
            $file = fopen('php://output', 'w');

            // إضافة BOM للدعم العربي
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            // معلومات التقرير
            fputcsv($file, ['تفاصيل طلب التوظيف']);
            fputcsv($file, ['رقم الطلب', $application->id]);
            fputcsv($file, ['تاريخ التصدير', date('Y-m-d H:i:s')]);
            fputcsv($file, []);

            // معلومات المتقدم
            fputcsv($file, ['معلومات المتقدم']);
            fputcsv($file, ['الاسم', $application->jobSeeker && $application->jobSeeker->user ? $application->jobSeeker->user->name : 'غير محدد']);
            fputcsv($file, ['البريد الإلكتروني', $application->jobSeeker && $application->jobSeeker->user ? $application->jobSeeker->user->email : 'غير محدد']);
            fputcsv($file, ['رقم الهاتف', $application->jobSeeker && $application->jobSeeker->user ? ($application->jobSeeker->user->phone ?? 'غير محدد') : 'غير محدد']);
            fputcsv($file, []);

            // معلومات الوظيفة
            fputcsv($file, ['معلومات الوظيفة']);
            fputcsv($file, ['عنوان الوظيفة', $application->job->title]);
            fputcsv($file, ['موقع الوظيفة', $application->job->location ?? 'غير محدد']);
            fputcsv($file, ['نوع الوظيفة', getJobTypeInArabic($application->job->job_type)]);
            fputcsv($file, []);

            // معلومات الطلب
            fputcsv($file, ['معلومات الطلب']);
            fputcsv($file, ['الحالة', getApplicationStatusInArabic($application->status)]);
            fputcsv($file, ['تاريخ التقديم', $application->created_at->format('Y-m-d H:i')]);
            fputcsv($file, ['تاريخ آخر تحديث', $application->updated_at->format('Y-m-d H:i')]);
            fputcsv($file, ['التقييم', $application->employer_rating ?? 'غير مقيم']);
            fputcsv($file, []);

            // رسالة التقديم
            if ($application->cover_letter) {
                fputcsv($file, ['رسالة التقديم']);
                fputcsv($file, [strip_tags($application->cover_letter)]);
                fputcsv($file, []);
            }

            // ملاحظات صاحب العمل
            if ($application->employer_notes) {
                fputcsv($file, ['ملاحظات صاحب العمل']);
                fputcsv($file, [strip_tags($application->employer_notes)]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * تصدير طلب واحد بصيغة PDF
     */
    private function exportSingleApplicationPDF($application, $employer)
    {
        $data = [
            'application' => $application,
            'employer' => $employer,
            'reportDate' => now()->format('Y-m-d H:i:s'),
        ];

        // إنشاء HTML للتقرير
        $html = view('employer.reports.single-application-pdf', $data)->render();

        // إنشاء PDF باستخدام DomPDF (يحتاج تثبيت المكتبة)
        // للآن سنعيد HTML كاستجابة
        return response($html, 200, [
            'Content-Type' => 'text/html; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="application_' . $application->id . '_' . date('Y-m-d_H-i-s') . '.html"'
        ]);
    }

    /**
     * تحديث حالة عدة طلبات مرة واحدة
     */
    public function bulkUpdate(Request $request)
    {
        $request->validate([
            'application_ids' => 'required|array',
            'application_ids.*' => 'exists:applications,id',
            'status' => 'required|in:pending,reviewed,accepted,rejected,hired'
        ]);

        $user = Auth::user();
        $employer = $user->employer;

        if (!$employer) {
            return response()->json([
                'success' => false,
                'message' => 'غير مصرح لك بهذا الإجراء'
            ], 403);
        }

        try {
            // التحقق من أن جميع الطلبات تخص هذا الموظف
            $applications = Application::whereIn('id', $request->application_ids)
                ->whereHas('job', function ($query) use ($employer) {
                    $query->where('employer_id', $employer->id);
                })
                ->get();

            if ($applications->count() !== count($request->application_ids)) {
                return response()->json([
                    'success' => false,
                    'message' => 'بعض الطلبات غير موجودة أو غير مصرح لك بالوصول إليها'
                ], 403);
            }

            $updatedCount = 0;
            foreach ($applications as $application) {
                $oldStatus = $application->status;
                $application->status = $request->status;

                if ($application->save()) {
                    $updatedCount++;

                    // إرسال إشعار للمتقدم
                    if ($application->jobSeeker && $application->jobSeeker->user) {
                        $statusMessages = [
                            'pending' => 'طلبك قيد المراجعة',
                            'reviewed' => 'تمت مراجعة طلبك',
                            'accepted' => 'تم قبول طلبك! تهانينا',
                            'rejected' => 'نأسف، لم يتم قبول طلبك هذه المرة',
                            'hired' => 'مبروك! تم اختيارك للوظيفة'
                        ];

                        $this->notificationService->create(
                            $application->jobSeeker->user,
                            'application_status_updated',
                            $statusMessages[$request->status] ?? 'تم تحديث حالة طلبك',
                            [
                                'application_id' => $application->id,
                                'job_title' => $application->job->title,
                                'old_status' => $oldStatus,
                                'new_status' => $request->status,
                                'company_name' => $employer->company_name
                            ]
                        );
                    }
                }
            }

            return response()->json([
                'success' => true,
                'message' => "تم تحديث {$updatedCount} طلب بنجاح",
                'updated_count' => $updatedCount
            ]);

        } catch (\Exception $e) {
            Log::error('Bulk update error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث الطلبات'
            ], 500);
        }
    }
}
