<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Employer;
use App\Models\Job;
use App\Models\Application;
use App\Models\Subscription;

class FixStatistics extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'stats:fix';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix and display correct statistics for employers';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Fixing employer statistics...');
        
        // الحصول على جميع أصحاب العمل
        $employers = Employer::with('user')->get();
        
        if ($employers->isEmpty()) {
            $this->error('No employers found!');
            return 1;
        }
        
        foreach ($employers as $employer) {
            $this->info("Processing employer: {$employer->company_name}");
            
            // إنشاء اشتراك افتراضي إذا لم يكن موجوداً
            if (!$employer->currentSubscription()) {
                Subscription::createDefault($employer);
                $this->info("Created default subscription for {$employer->company_name}");
            }
            
            // حساب الإحصائيات الصحيحة
            $totalJobs = Job::where('employer_id', $employer->id)->count();
            $activeJobs = Job::where('employer_id', $employer->id)->where('is_active', true)->count();
            $draftJobs = Job::where('employer_id', $employer->id)->where('is_active', false)->count();
            $totalApplications = Application::whereHas('job', function($query) use ($employer) {
                $query->where('employer_id', $employer->id);
            })->count();
            $totalViews = Job::where('employer_id', $employer->id)->sum('views') ?: 0;
            
            $this->table(
                ['Metric', 'Value'],
                [
                    ['Total Jobs', $totalJobs],
                    ['Active Jobs', $activeJobs],
                    ['Draft Jobs', $draftJobs],
                    ['Total Applications', $totalApplications],
                    ['Total Views', number_format($totalViews)],
                ]
            );
            
            // تحديث عداد الوظائف في الاشتراك
            $subscription = $employer->currentSubscription();
            if ($subscription) {
                $actualJobsThisMonth = Job::where('employer_id', $employer->id)
                    ->where('is_active', true)
                    ->whereYear('posted_at', now()->year)
                    ->whereMonth('posted_at', now()->month)
                    ->count();
                    
                $subscription->update(['jobs_posted_this_month' => $actualJobsThisMonth]);
                $this->info("Updated jobs counter: {$actualJobsThisMonth} jobs this month");
            }
        }
        
        $this->info('Statistics fixed successfully!');
        return 0;
    }
}
