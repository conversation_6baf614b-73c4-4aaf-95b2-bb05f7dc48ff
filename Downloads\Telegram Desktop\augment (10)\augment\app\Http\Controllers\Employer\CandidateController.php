<?php

namespace App\Http\Controllers\Employer;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\JobSeeker;
use App\Models\Application;
use App\Models\SavedCandidate;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

class CandidateController extends Controller
{
    /**
     * عرض قائمة المرشحين المحفوظين
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $employer = $user->employer;

        // إنشاء ملف صاحب عمل إذا لم يكن موجوداً
        if (!$employer) {
            if ($user->role === 'employer') {
                $employer = \App\Models\Employer::create([
                    'user_id' => $user->id,
                    'company_name' => $user->company_name ?? 'شركة ' . $user->name,
                    'company_description' => $user->company_description ?? 'وصف الشركة',
                    'industry' => $user->industry ?? 'تقنية المعلومات',
                    'location' => $user->location ?? 'طرابلس',
                    'company_size' => $user->company_size ?? '1-10',
                ]);
            } else {
                return redirect()->route('home')->with('error', 'ليس لديك صلاحية للوصول إلى هذه الصفحة');
            }
        }

        // إنشاء الجدول إذا لم يكن موجوداً
        $this->ensureTableExists();

        // جلب المرشحين المحفوظين
        try {
            $query = SavedCandidate::where('employer_id', $employer->id)
                ->with(['jobSeeker.user', 'jobSeeker.applications' => function($q) use ($employer) {
                    $q->whereHas('job', function($jobQuery) use ($employer) {
                        $jobQuery->where('employer_id', $employer->id);
                    });
                }]);
        } catch (\Exception $e) {
            // إذا فشل الاستعلام، استخدم بيانات فارغة
            $savedCandidates = collect();
            $stats = ['total' => 0, 'recent' => 0, 'applied' => 0];
            return view('employer.candidates.index', compact('savedCandidates', 'stats'));
        }

        // البحث
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('jobSeeker.user', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            })->orWhereHas('jobSeeker', function($q) use ($search) {
                $q->where('job_title', 'like', "%{$search}%")
                  ->orWhere('skills', 'like', "%{$search}%");
            });
        }

        // فلترة بالتخصص
        if ($request->filled('specialization')) {
            $query->whereHas('jobSeeker', function($q) use ($request) {
                $q->where('job_title', 'like', "%{$request->specialization}%")
                  ->orWhere('skills', 'like', "%{$request->specialization}%");
            });
        }

        // فلترة بمستوى الخبرة
        if ($request->filled('experience_level')) {
            $query->whereHas('jobSeeker', function($q) use ($request) {
                $q->where('experience', 'like', "%{$request->experience_level}%");
            });
        }

        $savedCandidates = $query->latest()->paginate(12);

        // إحصائيات سريعة
        try {
            $stats = [
                'total' => SavedCandidate::where('employer_id', $employer->id)->count(),
                'recent' => SavedCandidate::where('employer_id', $employer->id)
                    ->where('created_at', '>=', now()->subDays(7))->count(),
                'applied' => SavedCandidate::where('employer_id', $employer->id)
                    ->whereHas('jobSeeker.applications', function($q) use ($employer) {
                        $q->whereHas('job', function($jobQuery) use ($employer) {
                            $jobQuery->where('employer_id', $employer->id);
                        });
                    })->count(),
            ];
        } catch (\Exception $e) {
            $stats = ['total' => 0, 'recent' => 0, 'applied' => 0];
        }

        // معرف آخر مرشح محفوظ (إذا جاء من صفحة البحث)
        $recentlySavedId = $request->get('recently_saved');

        return view('employer.candidates.index', compact('savedCandidates', 'stats', 'recentlySavedId'));
    }

    /**
     * عرض ملف مرشح محدد
     */
    public function show($id)
    {
        $user = Auth::user();
        $employer = $user->employer;

        if (!$employer) {
            return redirect()->route('home')->with('error', 'ليس لديك صلاحية للوصول إلى هذه الصفحة');
        }

        $jobSeeker = JobSeeker::with(['user', 'applications' => function($q) use ($employer) {
            $q->whereHas('job', function($jobQuery) use ($employer) {
                $jobQuery->where('employer_id', $employer->id);
            })->with('job');
        }])->findOrFail($id);

        // التحقق من أن المرشح محفوظ
        $isSaved = SavedCandidate::where('employer_id', $employer->id)
            ->where('job_seeker_id', $jobSeeker->id)
            ->exists();

        return view('employer.candidates.show', compact('jobSeeker', 'isSaved'));
    }

    /**
     * حفظ مرشح
     */
    public function save(Request $request, $jobSeekerId)
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json(['success' => false, 'message' => 'يجب تسجيل الدخول أولاً']);
            }

            $employer = $user->employer;

            // إنشاء ملف صاحب عمل إذا لم يكن موجوداً
            if (!$employer) {
                if ($user->role === 'employer') {
                    $employer = \App\Models\Employer::create([
                        'user_id' => $user->id,
                        'company_name' => $user->company_name ?? 'شركة ' . $user->name,
                        'company_description' => $user->company_description ?? 'وصف الشركة',
                        'industry' => $user->industry ?? 'تقنية المعلومات',
                        'location' => $user->location ?? 'طرابلس',
                        'company_size' => $user->company_size ?? '1-10',
                    ]);
                } else {
                    return response()->json(['success' => false, 'message' => 'يجب أن تكون صاحب عمل للوصول لهذه الميزة']);
                }
            }

            // إنشاء الجدول إذا لم يكن موجوداً
            $this->ensureTableExists();

            $jobSeeker = JobSeeker::findOrFail($jobSeekerId);

            // التحقق من عدم وجود المرشح مسبقاً
            $exists = SavedCandidate::where('employer_id', $employer->id)
                ->where('job_seeker_id', $jobSeeker->id)
                ->exists();

            if ($exists) {
                return response()->json(['success' => false, 'message' => 'المرشح محفوظ مسبقاً']);
            }

            SavedCandidate::create([
                'employer_id' => $employer->id,
                'job_seeker_id' => $jobSeeker->id,
                'notes' => $request->notes
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم حفظ المرشح بنجاح',
                'candidate_id' => $jobSeeker->id,
                'candidate_name' => $jobSeeker->user->name,
                'employer_id' => $employer->id
            ]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(['success' => false, 'message' => 'المرشح غير موجود']);
        } catch (\Exception $e) {
            \Log::error('Error saving candidate: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'حدث خطأ: ' . $e->getMessage()]);
        }
    }

    /**
     * إلغاء حفظ مرشح
     */
    public function unsave($jobSeekerId)
    {
        $user = Auth::user();
        $employer = $user->employer;

        if (!$employer) {
            return response()->json(['success' => false, 'message' => 'غير مصرح لك']);
        }

        SavedCandidate::where('employer_id', $employer->id)
            ->where('job_seeker_id', $jobSeekerId)
            ->delete();

        return response()->json(['success' => true, 'message' => 'تم إلغاء حفظ المرشح']);
    }

    /**
     * تحديث ملاحظات المرشح
     */
    public function updateNotes(Request $request, $jobSeekerId)
    {
        $user = Auth::user();
        $employer = $user->employer;

        if (!$employer) {
            return response()->json(['success' => false, 'message' => 'غير مصرح لك']);
        }

        $savedCandidate = SavedCandidate::where('employer_id', $employer->id)
            ->where('job_seeker_id', $jobSeekerId)
            ->first();

        if (!$savedCandidate) {
            return response()->json(['success' => false, 'message' => 'المرشح غير محفوظ']);
        }

        $savedCandidate->update(['notes' => $request->notes]);

        return response()->json(['success' => true, 'message' => 'تم تحديث الملاحظات']);
    }

    /**
     * البحث عن مرشحين جدد
     */
    public function search(Request $request)
    {
        $user = Auth::user();
        $employer = $user->employer;

        // إنشاء ملف صاحب عمل إذا لم يكن موجوداً
        if (!$employer) {
            if ($user->role === 'employer') {
                $employer = \App\Models\Employer::create([
                    'user_id' => $user->id,
                    'company_name' => $user->company_name ?? 'شركة ' . $user->name,
                    'company_description' => $user->company_description ?? 'وصف الشركة',
                    'industry' => $user->industry ?? 'تقنية المعلومات',
                    'location' => $user->location ?? 'طرابلس',
                    'company_size' => $user->company_size ?? '1-10',
                ]);
            } else {
                return redirect()->route('home')->with('error', 'ليس لديك صلاحية للوصول إلى هذه الصفحة');
            }
        }

        $query = JobSeeker::with('user')
            ->whereHas('user', function($q) {
                $q->where('role', 'job_seeker');
            })
            ->where('is_available', true);

        // البحث
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->whereHas('user', function($userQuery) use ($search) {
                    $userQuery->where('name', 'like', "%{$search}%")
                             ->orWhere('email', 'like', "%{$search}%");
                })->orWhere('job_title', 'like', "%{$search}%")
                  ->orWhere('skills', 'like', "%{$search}%");
            });
        }

        // فلترة بالمهارات
        if ($request->filled('skills')) {
            $query->where('skills', 'like', "%{$request->skills}%");
        }

        // فلترة بالموقع
        if ($request->filled('location')) {
            $query->where('location', 'like', "%{$request->location}%");
        }

        $candidates = $query->latest()->paginate(12);

        // إضافة معلومات الحفظ
        $savedCandidateIds = SavedCandidate::where('employer_id', $employer->id)
            ->pluck('job_seeker_id')->toArray();

        return view('employer.candidates.search', compact('candidates', 'savedCandidateIds'));
    }

    /**
     * التأكد من وجود جدول saved_candidates
     */
    private function ensureTableExists()
    {
        if (!Schema::hasTable('saved_candidates')) {
            Schema::create('saved_candidates', function (Blueprint $table) {
                $table->id();
                $table->foreignId('employer_id')->constrained('employers')->onDelete('cascade');
                $table->foreignId('job_seeker_id')->constrained('job_seekers')->onDelete('cascade');
                $table->text('notes')->nullable();
                $table->timestamps();
                $table->unique(['employer_id', 'job_seeker_id']);
            });
        }
    }
}
