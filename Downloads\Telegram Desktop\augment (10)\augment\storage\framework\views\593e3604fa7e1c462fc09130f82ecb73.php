<?php $__env->startSection('title', 'إدارة المراجعات والتقييمات - Hire Me'); ?>

<?php $__env->startSection('content'); ?>
<!-- Reviews Content -->
<div class="p-6">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">إدارة المراجعات والتقييمات</h2>
        <div class="flex gap-3">
            <button class="btn-primary">
                <i class="fas fa-filter ml-2"></i>
                تصفية
            </button>
            <button class="btn-secondary">
                <i class="fas fa-download ml-2"></i>
                تصدير
            </button>
        </div>
    </div>

    <!-- Filters -->
    <form action="<?php echo e(route('admin.reviews.index')); ?>" method="GET" class="mb-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">نوع المراجعة</label>
                    <select name="type" class="form-select">
                        <option value="">جميع الأنواع</option>
                        <option value="company" <?php echo e(request('type') == 'company' ? 'selected' : ''); ?>>مراجعات الشركات</option>
                        <option value="candidate" <?php echo e(request('type') == 'candidate' ? 'selected' : ''); ?>>مراجعات المرشحين</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">التقييم</label>
                    <select name="rating" class="form-select">
                        <option value="">جميع التقييمات</option>
                        <option value="5" <?php echo e(request('rating') == '5' ? 'selected' : ''); ?>>5 نجوم</option>
                        <option value="4" <?php echo e(request('rating') == '4' ? 'selected' : ''); ?>>4 نجوم</option>
                        <option value="3" <?php echo e(request('rating') == '3' ? 'selected' : ''); ?>>3 نجوم</option>
                        <option value="2" <?php echo e(request('rating') == '2' ? 'selected' : ''); ?>>2 نجوم</option>
                        <option value="1" <?php echo e(request('rating') == '1' ? 'selected' : ''); ?>>1 نجمة</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        <option value="approved" <?php echo e(request('status') == 'approved' ? 'selected' : ''); ?>>موافق عليها</option>
                        <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>قيد المراجعة</option>
                        <option value="rejected" <?php echo e(request('status') == 'rejected' ? 'selected' : ''); ?>>مرفوضة</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">تاريخ النشر</label>
                    <select name="date" class="form-select">
                        <option value="">جميع الفترات</option>
                        <option value="today" <?php echo e(request('date') == 'today' ? 'selected' : ''); ?>>اليوم</option>
                        <option value="week" <?php echo e(request('date') == 'week' ? 'selected' : ''); ?>>هذا الأسبوع</option>
                        <option value="month" <?php echo e(request('date') == 'month' ? 'selected' : ''); ?>>هذا الشهر</option>
                        <option value="year" <?php echo e(request('date') == 'year' ? 'selected' : ''); ?>>هذا العام</option>
                    </select>
                </div>
            </div>
            <div class="mt-4 flex justify-end">
                <button type="submit" class="btn-primary">
                    <i class="fas fa-filter ml-2"></i>
                    تطبيق الفلتر
                </button>
                <a href="<?php echo e(route('admin.reviews.index')); ?>" class="btn-secondary mr-2">
                    <i class="fas fa-times ml-2"></i>
                    إعادة ضبط
                </a>
            </div>
        </div>
    </form>

    <!-- Reviews Table -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        المراجع
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        النوع
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        التقييم
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        المحتوى
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        الحالة
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        تاريخ النشر
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        الإجراءات
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                <?php $__empty_1 = true; $__currentLoopData = $reviews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $review): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10">
                                <?php if($review->type == 'company'): ?>
                                    <img class="h-10 w-10 rounded-full" src="<?php echo e($review->jobSeeker->user->avatar ?? 'https://ui-avatars.com/api/?name=' . urlencode($review->jobSeeker->user->name) . '&background=random'); ?>" alt="">
                                <?php else: ?>
                                    <img class="h-10 w-10 rounded-full" src="<?php echo e($review->employer->user->avatar ?? 'https://ui-avatars.com/api/?name=' . urlencode($review->employer->user->name) . '&background=random'); ?>" alt="">
                                <?php endif; ?>
                            </div>
                            <div class="mr-4">
                                <div class="text-sm font-medium text-gray-900 dark:text-white">
                                    <?php if($review->type == 'company'): ?>
                                        <?php echo e($review->jobSeeker->user->name); ?>

                                    <?php else: ?>
                                        <?php echo e($review->employer->user->name); ?>

                                    <?php endif; ?>
                                </div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    <?php if($review->type == 'company'): ?>
                                        <?php echo e($review->jobSeeker->user->email); ?>

                                    <?php else: ?>
                                        <?php echo e($review->employer->user->email); ?>

                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <?php if($review->type == 'company'): ?>
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                                شركة
                            </span>
                        <?php else: ?>
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100">
                                مرشح
                            </span>
                        <?php endif; ?>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex text-yellow-400">
                            <?php for($i = 1; $i <= 5; $i++): ?>
                                <?php if($i <= $review->rating): ?>
                                    <i class="fas fa-star"></i>
                                <?php else: ?>
                                    <i class="far fa-star"></i>
                                <?php endif; ?>
                            <?php endfor; ?>
                        </div>
                    </td>
                    <td class="px-6 py-4">
                        <div class="text-sm text-gray-900 dark:text-white truncate max-w-xs"><?php echo e($review->comment); ?></div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <?php if(isset($review->status)): ?>
                            <?php if($review->status == 'approved'): ?>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                                    موافق عليها
                                </span>
                            <?php elseif($review->status == 'rejected'): ?>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100">
                                    مرفوضة
                                </span>
                            <?php else: ?>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100">
                                    قيد المراجعة
                                </span>
                            <?php endif; ?>
                        <?php else: ?>
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100">
                                قيد المراجعة
                            </span>
                        <?php endif; ?>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        <?php echo e($review->created_at->diffForHumans()); ?>

                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-left text-sm font-medium">
                        <a href="<?php echo e(route('admin.reviews.show', $review->id)); ?>" class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 ml-2">
                            <i class="fas fa-eye"></i>
                        </a>
                        <form action="<?php echo e(route('admin.reviews.update-status', $review->id)); ?>" method="POST" class="inline">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" name="status" value="approved">
                            <input type="hidden" name="type" value="<?php echo e($review->type); ?>">
                            <button type="submit" class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 ml-2">
                                <i class="fas fa-check"></i>
                            </button>
                        </form>
                        <form action="<?php echo e(route('admin.reviews.update-status', $review->id)); ?>" method="POST" class="inline">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" name="status" value="rejected">
                            <input type="hidden" name="type" value="<?php echo e($review->type); ?>">
                            <button type="submit" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                                <i class="fas fa-times"></i>
                            </button>
                        </form>
                    </td>
                </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                    <td colspan="7" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                        لا توجد مراجعات متاحة
                    </td>
                </tr>
                <?php endif; ?>
            </tbody>
        </table>

        <!-- Pagination -->
        <div class="bg-white dark:bg-gray-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6">
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700 dark:text-gray-300">
                        عرض
                        <span class="font-medium"><?php echo e(($currentPage - 1) * $perPage + 1); ?></span>
                        إلى
                        <span class="font-medium"><?php echo e(min($currentPage * $perPage, $total)); ?></span>
                        من أصل
                        <span class="font-medium"><?php echo e($total); ?></span>
                        نتيجة
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <?php if($currentPage > 1): ?>
                            <a href="<?php echo e(route('admin.reviews.index', array_merge(request()->except('page'), ['page' => $currentPage - 1]))); ?>" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600">
                                <span class="sr-only">السابق</span>
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        <?php endif; ?>

                        <?php for($i = 1; $i <= $lastPage; $i++): ?>
                            <a href="<?php echo e(route('admin.reviews.index', array_merge(request()->except('page'), ['page' => $i]))); ?>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 <?php echo e($i == $currentPage ? 'bg-primary text-white' : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600'); ?> text-sm font-medium">
                                <?php echo e($i); ?>

                            </a>
                        <?php endfor; ?>

                        <?php if($currentPage < $lastPage): ?>
                            <a href="<?php echo e(route('admin.reviews.index', array_merge(request()->except('page'), ['page' => $currentPage + 1]))); ?>" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600">
                                <span class="sr-only">التالي</span>
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        <?php endif; ?>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\Telegram Desktop\augment (10)\augment\resources\views/admin/reviews/index.blade.php ENDPATH**/ ?>