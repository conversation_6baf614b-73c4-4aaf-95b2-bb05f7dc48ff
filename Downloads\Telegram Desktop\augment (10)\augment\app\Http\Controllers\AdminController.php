<?php

namespace App\Http\Controllers;

use App\Models\Job;
use App\Models\Application;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class AdminController extends Controller
{
    /**
     * إنشاء مثيل جديد من الكنترولر
     */
    public function __construct()
    {
        // تطبيق middleware على الدوال التي تتطلب تسجيل دخول كمدير
        $this->middleware(['auth', 'role:admin']);
    }

    /**
     * عرض لوحة التحكم الرئيسية
     */
    public function index()
    {
        // الحصول على بيانات الشركة
        $company = Auth::user()->company ?? null;

        // إحصائيات الوظائف
        $activeJobsCount = Job::where('expires_at', '>', Carbon::now())->count();
        $jobsLastMonth = Job::where('expires_at', '>', Carbon::now()->subMonth())
            ->where('created_at', '<=', Carbon::now()->subMonth())
            ->count();
        $jobsPercentage = $jobsLastMonth > 0
            ? round((($activeJobsCount - $jobsLastMonth) / $jobsLastMonth) * 100)
            : 100;

        // إحصائيات الطلبات
        $totalApplications = Application::count();
        $applicationsLastMonth = Application::where('created_at', '<=', Carbon::now()->subMonth())
            ->count();
        $applicationsPercentage = $applicationsLastMonth > 0
            ? round((($totalApplications - $applicationsLastMonth) / $applicationsLastMonth) * 100)
            : 100;

        // إحصائيات المستخدمين النشطين
        $activeUsers = User::where('created_at', '>=', Carbon::now()->subDays(30))->count();
        $usersLastMonth = User::where('created_at', '>=', Carbon::now()->subDays(60))
            ->where('created_at', '<=', Carbon::now()->subDays(30))
            ->count();
        $usersPercentage = $usersLastMonth > 0
            ? round((($activeUsers - $usersLastMonth) / $usersLastMonth) * 100)
            : 100;

        // إحصائيات المقابلات
        $scheduledInterviews = Application::where('status', 'accepted')->count();
        $interviewsLastMonth = Application::where('status', 'accepted')
            ->where('created_at', '<=', Carbon::now()->subMonth())
            ->count();
        $interviewsPercentage = $interviewsLastMonth > 0
            ? round((($scheduledInterviews - $interviewsLastMonth) / $interviewsLastMonth) * 100)
            : 100;

        // إحصائيات التعيينات
        $newHires = Application::where('status', 'accepted')
            ->where('created_at', '>=', Carbon::now()->subMonth())
            ->count();
        $hiresLastMonth = Application::where('status', 'accepted')
            ->where('created_at', '<=', Carbon::now()->subMonth())
            ->where('created_at', '>=', Carbon::now()->subMonths(2))
            ->count();
        $hiresPercentage = $hiresLastMonth > 0
            ? round((($newHires - $hiresLastMonth) / $hiresLastMonth) * 100)
            : 100;

        // أحدث الطلبات (فقط الطلبات الصحيحة)
        $recentApplications = Application::with(['jobSeeker.user', 'job'])
            ->whereHas('job') // التأكد من وجود الوظيفة
            ->whereHas('jobSeeker.user') // التأكد من وجود المستخدم
            ->latest()
            ->take(4)
            ->get()
            ->map(function($application) {
                $timeAgo = Carbon::parse($application->created_at)->diffForHumans();
                $colors = ['blue', 'green', 'purple', 'red', 'yellow'];
                $color = $colors[array_rand($colors)];
                $avatar = $application->jobSeeker && $application->jobSeeker->user ? $application->jobSeeker->user->avatar : null;

                if (!$avatar) {
                    $name = $application->jobSeeker && $application->jobSeeker->user ? $application->jobSeeker->user->name : 'مجهول';
                    $avatar = 'https://ui-avatars.com/api/?name=' . urlencode($name) . '&background=random';
                }

                return [
                    'id' => $application->id,
                    'applicant_name' => $application->jobSeeker && $application->jobSeeker->user ? $application->jobSeeker->user->name : 'مجهول',
                    'job_title' => $application->job ? $application->job->title : 'وظيفة غير معروفة',
                    'time_ago' => $timeAgo,
                    'color' => $color,
                    'avatar' => $avatar,
                    'status' => $application->status
                ];
            });

        // أحدث الوظائف
        $recentJobs = Job::withCount('applications')
            ->latest()
            ->take(4)
            ->get()
            ->map(function($job) {
                $icons = ['code', 'paint-brush', 'mobile-alt', 'bullhorn', 'chart-bar'];
                $colors = ['gradient-bg', 'bg-green-500', 'bg-blue-500', 'bg-purple-500', 'bg-red-500'];

                // تحويل نوع الوظيفة إلى النص العربي المناسب
                $jobTypeText = 'غير محدد';
                switch($job->job_type) {
                    case 'full_time':
                        $jobTypeText = 'دوام كامل';
                        break;
                    case 'part_time':
                        $jobTypeText = 'دوام جزئي';
                        break;
                    case 'remote':
                        $jobTypeText = 'عن بعد';
                        break;
                    case 'freelance':
                        $jobTypeText = 'عمل حر';
                        break;
                }

                return [
                    'id' => $job->id,
                    'title' => $job->title,
                    'location' => $job->location,
                    'type' => $jobTypeText,
                    'applications_count' => $job->applications_count,
                    'icon' => $icons[array_rand($icons)],
                    'color_class' => $colors[array_rand($colors)]
                ];
            });

        // بيانات الطلبات الأسبوعية
        $weeklyApplicationsData = $this->getWeeklyApplicationsData();

        // أكثر الوظائف مشاهدة
        $topViewedJobs = Job::orderBy('views', 'desc')
            ->take(5)
            ->get()
            ->map(function($job) {
                $maxViews = Job::max('views') ?: 1;
                $percentage = round(($job->views / $maxViews) * 100);

                return [
                    'title' => $job->title,
                    'views' => $job->views,
                    'percentage' => $percentage
                ];
            });

        return view('admin.index', compact(
            'company',
            'activeJobsCount',
            'jobsPercentage',
            'totalApplications',
            'applicationsPercentage',
            'activeUsers',
            'usersPercentage',
            'scheduledInterviews',
            'interviewsPercentage',
            'newHires',
            'hiresPercentage',
            'recentApplications',
            'recentJobs',
            'weeklyApplicationsData',
            'topViewedJobs'
        ));
    }

    /**
     * الحصول على بيانات الطلبات الأسبوعية
     */
    private function getWeeklyApplicationsData()
    {
        $days = ['السبت', 'الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'];
        $data = [];

        // الحصول على عدد الطلبات لكل يوم من الأسبوع الحالي
        $startOfWeek = Carbon::now()->startOfWeek();

        foreach ($days as $index => $day) {
            $date = $startOfWeek->copy()->addDays($index);

            $count = Application::whereDate('created_at', $date)->count();
            $maxHeight = 60; // أقصى ارتفاع للرسم البياني
            $height = $count > 0 ? min($count * 4, $maxHeight) : 10; // تحويل العدد إلى ارتفاع مناسب

            $data[] = [
                'day' => $day,
                'height' => $height,
                'count' => $count
            ];
        }

        return $data;
    }

    /**
     * إنشاء حساب مدير جديد
     */
    public function createAdmin()
    {
        // التحقق من وجود مدير سابق
        $adminExists = User::where('role', 'admin')->exists();

        if (!$adminExists) {
            try {
                // إنشاء حساب مدير جديد
                $admin = User::create([
                    'name' => 'مدير النظام',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password123'),
                    'role' => 'admin'
                ]);

                // إذا كان هناك نظام صلاحيات، إعطاء صلاحيات المدير
                if (method_exists($admin, 'assignRole')) {
                    $admin->assignRole('admin');
                }

                return response()->json([
                    'success' => true,
                    'message' => 'تم إنشاء حساب المدير بنجاح',
                    'credentials' => [
                        'email' => '<EMAIL>',
                        'password' => 'password123'
                    ]
                ]);
            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => 'حدث خطأ أثناء إنشاء حساب المدير: ' . $e->getMessage()
                ]);
            }
        }

        return response()->json([
            'success' => false,
            'message' => 'يوجد حساب مدير بالفعل'
        ]);
    }
}
