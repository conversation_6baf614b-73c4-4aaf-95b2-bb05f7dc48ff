@extends('layouts.employer')

@section('title', 'المرشحون المحفوظون - Hire Me')
@section('header_title', 'المرشحون المحفوظون')

@section('content')
<div class="p-8 space-y-8">
    <!-- رسالة نجاح الحفظ -->
    <div id="saveSuccessMessage" class="hidden bg-gradient-to-r from-green-100 to-emerald-100 border-l-4 border-green-500 text-green-800 px-6 py-4 rounded-lg mb-6 shadow-lg">
        <div class="flex items-center gap-3">
            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                <i class="fas fa-check text-white text-sm"></i>
            </div>
            <div>
                <h4 class="font-semibold">تم الحفظ بنجاح! 🎉</h4>
                <p class="text-sm mt-1">تم إضافة المرشح إلى قائمة المحفوظين. يمكنك الآن مراجعة ملفه الشخصي والتواصل معه.</p>
            </div>
        </div>
    </div>

    <!-- Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">المرشحون المحفوظون</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">قائمة المرشحين الذين تم حفظهم للمراجعة لاحقاً</p>
        </div>

        <div class="flex gap-3 mt-4 md:mt-0">
            <a href="{{ route('employer.candidates.search') }}" class="px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-all duration-300 flex items-center gap-2">
                <i class="fas fa-search"></i>
                البحث عن مرشحين
            </a>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="glass-card rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي المحفوظين</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $stats['total'] ?? 0 }}</p>
                </div>
                <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-bookmark text-green-600 dark:text-green-400"></i>
                </div>
            </div>
        </div>

        <div class="glass-card rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">تم التواصل معهم</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">0</p>
                </div>
                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-envelope text-blue-600 dark:text-blue-400"></i>
                </div>
            </div>
        </div>

        <div class="glass-card rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">مرشحين جدد (7 أيام)</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $stats['recent'] ?? 0 }}</p>
                </div>
                <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-user-plus text-purple-600 dark:text-purple-400"></i>
                </div>
            </div>
        </div>

        <div class="glass-card rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">معدل الاستجابة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">0%</p>
                </div>
                <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-chart-line text-orange-600 dark:text-orange-400"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Candidates List -->
    <div class="glass-card rounded-xl p-6">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">قائمة المرشحين</h2>

            <div class="flex gap-3">
                <select class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    <option value="">جميع التخصصات</option>
                    <option value="development">تطوير البرمجيات</option>
                    <option value="design">التصميم</option>
                    <option value="marketing">التسويق</option>
                </select>

                <select class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    <option value="">جميع مستويات الخبرة</option>
                    <option value="entry">مبتدئ</option>
                    <option value="mid">متوسط</option>
                    <option value="senior">خبير</option>
                </select>
            </div>
        </div>

        @if($savedCandidates->count() > 0)
            <!-- Candidates Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($savedCandidates as $savedCandidate)
                    @php $candidate = $savedCandidate->jobSeeker @endphp
                    <div class="glass-card rounded-xl p-6 hover:shadow-lg transition-all duration-300 {{ isset($recentlySavedId) && $recentlySavedId == $candidate->id ? 'recently-saved ring-2 ring-green-400 bg-green-50 dark:bg-green-900/20' : '' }}" data-candidate-id="{{ $candidate->id }}">
                        <div class="flex items-start gap-4">
                            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
                                {{ substr($candidate->user->name, 0, 1) }}
                            </div>
                            <div class="flex-1">
                                <h3 class="font-semibold text-gray-900 dark:text-white">{{ $candidate->user->name }}</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">{{ $candidate->job_title ?? 'غير محدد' }}</p>
                                <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">
                                    <i class="fas fa-map-marker-alt"></i>
                                    {{ $candidate->location ?? 'غير محدد' }}
                                </p>
                            </div>
                        </div>

                        @if($candidate->skills)
                            <div class="mt-4">
                                <div class="flex flex-wrap gap-1">
                                    @foreach(array_slice(explode(',', $candidate->skills), 0, 3) as $skill)
                                        <span class="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full">
                                            {{ trim($skill) }}
                                        </span>
                                    @endforeach
                                    @if(count(explode(',', $candidate->skills)) > 3)
                                        <span class="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 text-xs rounded-full">
                                            +{{ count(explode(',', $candidate->skills)) - 3 }}
                                        </span>
                                    @endif
                                </div>
                            </div>
                        @endif

                        <div class="mt-4 flex items-center justify-between">
                            <div class="text-xs text-gray-500 dark:text-gray-500">
                                <i class="fas fa-clock"></i>
                                محفوظ منذ {{ $savedCandidate->created_at->diffForHumans() }}
                            </div>
                            <div class="flex gap-2">
                                <a href="{{ route('employer.candidates.show', $candidate->id) }}" class="px-3 py-1 bg-blue-600 text-white text-xs rounded-lg hover:bg-blue-700 transition-colors">
                                    عرض
                                </a>
                                <button onclick="unsaveCandidate({{ $candidate->id }})" class="px-3 py-1 bg-red-600 text-white text-xs rounded-lg hover:bg-red-700 transition-colors">
                                    إلغاء
                                </button>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-8">
                {{ $savedCandidates->links() }}
            </div>
        @else
            <!-- Empty State -->
            <div class="text-center py-12">
                <div class="w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-user-tie text-4xl text-gray-400"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">لا توجد مرشحين محفوظين</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-6">ابدأ بالبحث عن المرشحين وحفظ الملفات الشخصية المناسبة</p>

                <a href="{{ route('employer.candidates.search') }}" class="px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-all duration-300 flex items-center gap-2 mx-auto">
                    <i class="fas fa-search"></i>
                    البحث عن مرشحين
                </a>
            </div>
        @endif
    </div>
</div>

<style>
.glass-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-card {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
}
</style>

@push('scripts')
<script>
function unsaveCandidate(candidateId) {
    if (confirm('هل أنت متأكد من إلغاء حفظ هذا المرشح؟')) {
        fetch(`/employer/candidates/${candidateId}/unsave`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'حدث خطأ');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
    }

    // إظهار رسالة النجاح إذا جاء المستخدم من صفحة البحث
    document.addEventListener('DOMContentLoaded', function() {
        // فحص إذا كان هناك parameter في URL يشير إلى نجاح الحفظ
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('saved') === 'success') {
            const message = document.getElementById('saveSuccessMessage');
            if (message) {
                message.classList.remove('hidden');

                // إخفاء الرسالة بعد 5 ثوان
                setTimeout(() => {
                    message.style.opacity = '0';
                    message.style.transition = 'opacity 0.5s';
                    setTimeout(() => {
                        message.classList.add('hidden');
                    }, 500);
                }, 5000);
            }

            // إزالة parameter من URL بدون إعادة تحميل
            const newUrl = window.location.pathname;
            window.history.replaceState({}, document.title, newUrl);
        }
    });
}

// إظهار رسالة النجاح عند الحفظ
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const saved = urlParams.get('saved');
    const recentlySaved = urlParams.get('recently_saved');
    const candidateName = urlParams.get('candidate_name');

    if (saved === 'success') {
        const successMessage = document.getElementById('saveSuccessMessage');
        if (successMessage) {
            // تخصيص الرسالة حسب اسم المرشح
            if (candidateName) {
                const messageContent = successMessage.querySelector('div > div');
                if (messageContent) {
                    messageContent.innerHTML = `
                        <h4 class="font-semibold">تم حفظ ${decodeURIComponent(candidateName)} بنجاح! 🎉</h4>
                        <p class="text-sm mt-1">تم إضافة المرشح إلى قائمة المحفوظين. يمكنك الآن مراجعة ملفه الشخصي والتواصل معه.</p>
                    `;
                }
            }

            successMessage.classList.remove('hidden');

            // إضافة تأثير انيميشن جميل
            successMessage.style.opacity = '0';
            successMessage.style.transform = 'translateY(-30px) scale(0.95)';

            setTimeout(() => {
                successMessage.style.transition = 'all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1)';
                successMessage.style.opacity = '1';
                successMessage.style.transform = 'translateY(0) scale(1)';
            }, 100);

            // إضافة تأثير وميض للإشارة للنجاح
            setTimeout(() => {
                successMessage.style.boxShadow = '0 0 20px rgba(34, 197, 94, 0.3)';
                setTimeout(() => {
                    successMessage.style.boxShadow = '0 10px 25px -5px rgba(0, 0, 0, 0.1)';
                }, 300);
            }, 600);

            // إخفاء الرسالة بعد 6 ثوان
            setTimeout(() => {
                successMessage.style.transition = 'all 0.5s ease-out';
                successMessage.style.opacity = '0';
                successMessage.style.transform = 'translateY(-20px) scale(0.95)';

                setTimeout(() => {
                    successMessage.classList.add('hidden');
                }, 500);
            }, 6000);

            // تنظيف URL من parameters
            const newUrl = window.location.pathname;
            window.history.replaceState({}, document.title, newUrl);
        }
    }

    // تمييز المرشح المحفوظ حديثاً
    if (recentlySaved) {
        const candidateCard = document.querySelector(`[data-candidate-id="${recentlySaved}"]`);
        if (candidateCard) {
            // إضافة تأثيرات بصرية جميلة
            candidateCard.classList.add('ring-4', 'ring-green-400', 'bg-gradient-to-r', 'from-green-50', 'to-emerald-50', 'shadow-lg', 'transform', 'scale-105');

            // إضافة أيقونة نجاح مؤقتة
            const successIcon = document.createElement('div');
            successIcon.className = 'absolute -top-2 -right-2 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm animate-bounce';
            successIcon.innerHTML = '<i class="fas fa-check"></i>';
            candidateCard.style.position = 'relative';
            candidateCard.appendChild(successIcon);

            // التمرير إلى المرشح مع تأخير بسيط
            setTimeout(() => {
                candidateCard.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                    inline: 'nearest'
                });
            }, 1000);

            // إزالة التمييز تدريجياً
            setTimeout(() => {
                // إزالة الأيقونة أولاً
                if (successIcon) {
                    successIcon.style.transition = 'all 0.3s ease-out';
                    successIcon.style.opacity = '0';
                    successIcon.style.transform = 'scale(0)';
                    setTimeout(() => successIcon.remove(), 300);
                }

                // ثم إزالة التأثيرات
                candidateCard.style.transition = 'all 0.5s ease-out';
                candidateCard.classList.remove('ring-4', 'ring-green-400', 'bg-gradient-to-r', 'from-green-50', 'to-emerald-50', 'shadow-lg', 'transform', 'scale-105');
            }, 4000);
        }
    }
});
</script>
@endpush

@endsection
