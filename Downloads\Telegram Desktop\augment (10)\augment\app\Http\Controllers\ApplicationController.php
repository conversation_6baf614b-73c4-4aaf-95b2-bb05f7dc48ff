<?php

namespace App\Http\Controllers;

use App\Models\Application;
use App\Models\Job;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class ApplicationController extends Controller
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->middleware('auth');
        $this->notificationService = $notificationService;
    }

    /**
     * عرض نموذج إنشاء طلب توظيف جديد
     */
    public function create(Job $job)
    {
        // التحقق من أن المستخدم باحث عن عمل
        $this->middleware('role:job_seeker');

        // التحقق من أن المستخدم لم يتقدم بالفعل لهذه الوظيفة
        $user = Auth::user();
        if (!$user->jobSeeker) {
            // إنشاء ملف باحث عن عمل تلقائياً إذا كان المستخدم من نوع job_seeker
            if ($user->role === 'job_seeker') {
                \App\Models\JobSeeker::create([
                    'user_id' => $user->id,
                    'job_title' => 'باحث عن عمل',
                    'location' => 'الرياض',
                    'is_available' => true,
                ]);
            } else {
                return redirect()->back()->with('error', 'يجب أن تكون باحثاً عن عمل للتقديم');
            }
        }

        // إعادة جلب المستخدم مع العلاقة
        $user = Auth::user();
        if (!$user->jobSeeker) {
            return redirect()->back()->with('error', 'حدث خطأ في إنشاء ملف الباحث عن عمل');
        }

        $exists = Application::where('job_id', $job->id)
                            ->where('job_seeker_id', $user->jobSeeker->id)
                            ->exists();

        if ($exists) {
            return redirect()->back()->with('error', 'لقد تقدمت بالفعل لهذه الوظيفة');
        }

        return view('applications.create', compact('job'));
    }

    public function store(Request $request, Job $job)
    {
        $this->middleware('role:job_seeker');

        // Check if user has already applied
        $user = Auth::user();
        if (!$user->jobSeeker) {
            // إنشاء ملف باحث عن عمل تلقائياً إذا كان المستخدم من نوع job_seeker
            if ($user->role === 'job_seeker') {
                \App\Models\JobSeeker::create([
                    'user_id' => $user->id,
                    'job_title' => 'باحث عن عمل',
                    'location' => 'الرياض',
                    'is_available' => true,
                ]);
            } else {
                return redirect()->back()->with('error', 'يجب أن تكون باحثاً عن عمل للتقديم');
            }
        }

        // إعادة جلب المستخدم مع العلاقة
        $user = Auth::user();
        if (!$user->jobSeeker) {
            return redirect()->back()->with('error', 'حدث خطأ في إنشاء ملف الباحث عن عمل');
        }

        $exists = Application::where('job_id', $job->id)
                            ->where('job_seeker_id', $user->jobSeeker->id)
                            ->exists();

        if ($exists) {
            return redirect()->back()->with('error', 'لقد تقدمت بالفعل لهذه الوظيفة');
        }

        $request->validate([
            'cover_letter' => 'required|string',
            'resume' => 'nullable|mimes:pdf,doc,docx|max:2048',
        ]);

        $application = new Application();
        $application->job_id = $job->id;
        $application->job_seeker_id = $user->jobSeeker->id;
        $application->cover_letter = $request->cover_letter;

        if ($request->hasFile('resume')) {
            $path = $request->file('resume')->store('applications');
            $application->resume = $path;
        }

        $application->save();

        // إرسال إشعار لصاحب العمل
        if ($job->employer && $job->employer->user) {
            $this->notificationService->jobApplicationReceived($job->employer->user, $application);
        }

        return redirect()->route('job-seeker.applications')->with('success', 'Application submitted successfully');
    }

    public function show(Application $application)
    {
        if (Auth::user()->role === 'employer') {
            $this->authorize('view', $application);
            return view('employers.application-details', compact('application'));
        } else {
            $this->authorize('view', $application);
            return view('job-seekers.application-details', compact('application'));
        }
    }

    public function updateStatus(Request $request, Application $application)
    {
        $this->middleware('role:employer');
        $this->authorize('update', $application);

        $request->validate([
            'status' => 'required|in:pending,reviewed,shortlisted,rejected,hired',
            'employer_notes' => 'nullable|string',
        ]);

        $application->status = $request->status;
        $application->employer_notes = $request->employer_notes;
        $application->save();

        // إرسال إشعار للباحث عن عمل
        if ($application->jobSeeker && $application->jobSeeker->user) {
            $this->notificationService->applicationStatusUpdated($application->jobSeeker->user, $application, $request->status);
        }

        return redirect()->back()->with('success', 'Application status updated successfully');
    }

    public function downloadResume(Application $application)
    {
        $this->authorize('view', $application);

        if ($application->resume) {
            return Storage::download($application->resume);
        } else if ($application->jobSeeker && $application->jobSeeker->resume) {
            return Storage::download($application->jobSeeker->resume);
        }

        return redirect()->back()->with('error', 'No resume found');
    }
}
