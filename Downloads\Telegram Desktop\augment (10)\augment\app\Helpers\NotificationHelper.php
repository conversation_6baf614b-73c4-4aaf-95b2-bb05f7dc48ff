<?php

if (!function_exists('getNotificationIcon')) {
    function getNotificationIcon($type) {
        $icons = [
            'job_application' => 'file-alt',
            'application_update' => 'edit',
            'job_posted' => 'briefcase',
            'job_expired' => 'clock',
            'payment_success' => 'credit-card',
            'payment_failed' => 'exclamation-triangle',
            'profile_rating' => 'star',
            'company_rating' => 'building',
            'system_announcement' => 'bullhorn',
            'urgent' => 'exclamation-triangle',
            'info' => 'info-circle',
            'warning' => 'exclamation-triangle',
            'account_verification' => 'user-check',
            'password_reset' => 'key',
            'interview_scheduled' => 'calendar-alt',
            'job_recommendation' => 'lightbulb',
        ];

        return $icons[$type] ?? 'bell';
    }
}

if (!function_exists('getNotificationTypeInArabic')) {
    function getNotificationTypeInArabic($type) {
        $types = [
            'job_application' => 'طلب توظيف',
            'application_update' => 'تحديث طلب',
            'job_posted' => 'وظيفة جديدة',
            'job_expired' => 'انتهاء وظيفة',
            'payment_success' => 'دفع ناجح',
            'payment_failed' => 'فشل دفع',
            'profile_rating' => 'تقييم ملف',
            'company_rating' => 'تقييم شركة',
            'system_announcement' => 'إعلان النظام',
            'urgent' => 'عاجل',
            'info' => 'معلومات',
            'warning' => 'تحذير',
            'account_verification' => 'تحقق حساب',
            'password_reset' => 'إعادة تعيين كلمة مرور',
            'interview_scheduled' => 'مقابلة مجدولة',
            'job_recommendation' => 'توصية وظيفة',
        ];

        return $types[$type] ?? $type;
    }
}

if (!function_exists('getNotificationTypeName')) {
    function getNotificationTypeName($type) {
        return getNotificationTypeInArabic($type);
    }
}

if (!function_exists('getNotificationTypeColor')) {
    function getNotificationTypeColor($type) {
        $colors = [
            'job_application' => 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
            'application_update' => 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
            'job_posted' => 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400',
            'job_expired' => 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400',
            'payment_success' => 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
            'payment_failed' => 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
            'profile_rating' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
            'company_rating' => 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400',
            'system_announcement' => 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400',
            'urgent' => 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
            'info' => 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
            'warning' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
            'account_verification' => 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
            'password_reset' => 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400',
            'interview_scheduled' => 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400',
            'job_recommendation' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
        ];

        return $colors[$type] ?? 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
}

if (!function_exists('getJobTypeInArabic')) {
    function getJobTypeInArabic($type) {
        $types = [
            'full_time' => 'دوام كامل',
            'part_time' => 'دوام جزئي',
            'contract' => 'عقد',
            'freelance' => 'عمل حر',
            'internship' => 'تدريب',
        ];

        return $types[$type] ?? $type;
    }
}

if (!function_exists('getApplicationStatusInArabic')) {
    function getApplicationStatusInArabic($status) {
        $statuses = [
            'pending' => 'قيد المراجعة',
            'reviewed' => 'تمت المراجعة',
            'accepted' => 'مقبول',
            'rejected' => 'مرفوض',
            'interview' => 'مقابلة',
            'hired' => 'تم التوظيف',
        ];

        return $statuses[$status] ?? $status;
    }
}

if (!function_exists('getApplicationStatusColor')) {
    function getApplicationStatusColor($status) {
        $colors = [
            'pending' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
            'reviewed' => 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
            'accepted' => 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
            'rejected' => 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
            'interview' => 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400',
            'hired' => 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-400',
        ];

        return $colors[$status] ?? 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
}
