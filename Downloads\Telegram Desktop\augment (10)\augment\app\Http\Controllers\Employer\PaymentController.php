<?php

namespace App\Http\Controllers\Employer;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PaymentController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:employer');
    }

    /**
     * الاشتراك في باقة
     */
    public function subscribe(Request $request, $package)
    {
        $user = Auth::user();
        $employer = $user->employer;
        if (!$employer) {
            return redirect()->route('employer.packages')->with('error', 'يجب إكمال ملف الشركة أولاً');
        }

        // التحقق من صحة نوع الباقة
        $validPackages = ['basic', 'premium', 'enterprise'];
        if (!in_array($package, $validPackages)) {
            return redirect()->route('employer.packages')->with('error', 'نوع الباقة غير صحيح');
        }

        // إلغاء الاشتراك الحالي
        $currentSubscription = $employer->currentSubscription();
        if ($currentSubscription) {
            $currentSubscription->update(['is_active' => false]);
        }

        // إنشاء اشتراك جديد
        $packageDetails = $this->getPackageDetails($package);

        $subscription = \App\Models\Subscription::create([
            'employer_id' => $employer->id,
            'package_type' => $package,
            'price' => $packageDetails['price'],
            'job_limit' => $packageDetails['job_limit'],
            'jobs_posted_this_month' => 0,
            'starts_at' => now()->toDateString(),
            'ends_at' => now()->addMonth()->toDateString(),
            'is_active' => true,
            'features' => $packageDetails['features'],
            'last_reset_at' => now(),
        ]);

        // حفظ الباقة المختارة في جدول الأصحاب العمل أيضاً (للتوافق مع الكود القديم)
        $employer->package_id = $package;
        $employer->save();

        // هنا يمكن إضافة منطق الدفع الفعلي لاحقاً
        // إذا كانت الباقة مدفوعة، يجب توجيه المستخدم لصفحة الدفع

        $message = $package === 'basic'
            ? 'تم التبديل للباقة المجانية بنجاح'
            : 'تم الاشتراك في الباقة بنجاح';

        return redirect()->route('employer.packages')
                        ->with('success', $message);
    }

    /**
     * الحصول على تفاصيل الباقة
     */
    private function getPackageDetails($package)
    {
        $packages = [
            'basic' => [
                'name' => 'الباقة المجانية',
                'price' => 0,
                'job_limit' => 3,
                'features' => [
                    'نشر 3 وظائف شهرياً',
                    'عرض الطلبات الأساسي',
                    'دعم فني محدود'
                ]
            ],
            'premium' => [
                'name' => 'الباقة المميزة',
                'price' => 299,
                'job_limit' => 15,
                'features' => [
                    'نشر 15 وظيفة شهرياً',
                    'إبراز الوظائف في النتائج',
                    'تحليلات مفصلة',
                    'دعم فني أولوية',
                    'تصدير البيانات'
                ]
            ],
            'enterprise' => [
                'name' => 'باقة الشركات',
                'price' => 599,
                'job_limit' => -1, // غير محدود
                'features' => [
                    'وظائف غير محدودة',
                    'صفحة شركة مخصصة',
                    'مدير حساب مخصص',
                    'تكامل API',
                    'تقارير مخصصة',
                    'دعم فني 24/7'
                ]
            ]
        ];

        return $packages[$package] ?? $packages['basic'];
    }
}
