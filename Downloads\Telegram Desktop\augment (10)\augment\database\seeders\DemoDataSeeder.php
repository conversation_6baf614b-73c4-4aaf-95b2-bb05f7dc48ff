<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Job;
use App\Models\JobSeeker;
use App\Models\Employer;
use App\Models\Category;
use App\Models\Application;
use App\Models\Review;
use App\Models\Notification;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class DemoDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء مستخدم مدير
        $adminUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'مدير النظام',
                'password' => Hash::make('Pp123456'),
                'role' => 'admin',
            ]
        );

        // إنشاء مستخدمين باحثين عن عمل
        $jobSeekerNames = [
            'أحمد محمد العلي',
            'فاطمة عبدالله السعيد',
            'محمد خالد الأحمد',
            'نورة سعد المطيري',
            'عبدالرحمن علي القحطاني',
            'سارة محمد الزهراني'
        ];

        $jobTitles = [
            'مطور ويب',
            'مصمم جرافيك',
            'محاسب مالي',
            'مسوق رقمي',
            'مطور تطبيقات',
            'محلل بيانات'
        ];

        $skills = [
            'PHP, Laravel, JavaScript, Vue.js, MySQL',
            'Photoshop, Illustrator, InDesign, Figma',
            'Excel, QuickBooks, SAP, Financial Analysis',
            'Google Ads, Facebook Ads, SEO, Content Marketing',
            'React Native, Flutter, Swift, Kotlin',
            'Python, SQL, Tableau, Power BI'
        ];

        $experiences = [
            '3 سنوات خبرة في تطوير المواقع الإلكترونية',
            '5 سنوات خبرة في التصميم الجرافيكي',
            '4 سنوات خبرة في المحاسبة والتمويل',
            '2 سنة خبرة في التسويق الرقمي',
            '3 سنوات خبرة في تطوير التطبيقات',
            '4 سنوات خبرة في تحليل البيانات'
        ];

        for ($i = 0; $i < 6; $i++) {
            $user = User::firstOrCreate(
                ['email' => "jobseeker" . ($i + 1) . "@example.com"],
                [
                    'name' => $jobSeekerNames[$i],
                    'password' => Hash::make('password'),
                    'role' => 'job_seeker',
                ]
            );

            JobSeeker::firstOrCreate(
                ['user_id' => $user->id],
                [
                    'skills' => $skills[$i],
                    'experience' => $experiences[$i],
                    'education' => 'بكالوريوس في ' . ($i % 2 == 0 ? 'علوم الحاسب' : 'إدارة الأعمال'),
                    'job_title' => $jobTitles[$i],
                    'location' => ['طرابلس', 'مصراتة', 'بنغازي', 'سبها', 'الزاوية', 'زليتن'][$i] . ', ليبيا',
                    'is_available' => true,
                ]
            );
        }

        // إنشاء مستخدمين أصحاب عمل وشركات
        $companyNames = [
            'شركة الحلول التقنية المتقدمة',
            'مجموعة الابتكار الرقمي',
            'شركة تطوير المواقع الإلكترونية',
            'بيت البرمجيات السعودي',
            'شركة الخبراء التقنيين',
            'مؤسسة التسويق الرقمي',
            'شركة التصميم الإبداعي'
        ];

        $industries = [
            'تقنية المعلومات',
            'تطوير البرمجيات',
            'التجارة الإلكترونية',
            'التسويق الرقمي',
            'الاستشارات التقنية',
            'التصميم والإعلان',
            'الحلول المالية'
        ];

        $companyDescriptions = [
            'شركة رائدة في مجال تقنية المعلومات تقدم حلولاً متطورة للشركات والمؤسسات',
            'متخصصون في تطوير التطبيقات والمواقع الإلكترونية بأحدث التقنيات',
            'نقدم خدمات تطوير المواقع الإلكترونية والتجارة الإلكترونية',
            'بيت خبرة في تطوير البرمجيات المخصصة وحلول الأعمال',
            'فريق من الخبراء التقنيين لتقديم أفضل الحلول التقنية',
            'متخصصون في التسويق الرقمي ووسائل التواصل الاجتماعي',
            'نقدم خدمات التصميم الجرافيكي والهوية البصرية'
        ];

        $employerNames = [
            'أحمد سعد المدير',
            'فاطمة محمد الرئيسة التنفيذية',
            'خالد عبدالله مدير الموارد البشرية',
            'نورة علي مديرة التطوير',
            'محمد حسن المدير العام',
            'سارة أحمد مديرة التسويق',
            'عبدالرحمن سالم المدير التقني'
        ];

        for ($i = 0; $i < 7; $i++) {
            $user = User::firstOrCreate(
                ['email' => "employer" . ($i + 1) . "@example.com"],
                [
                    'name' => $employerNames[$i],
                    'password' => Hash::make('password'),
                    'role' => 'employer',
                ]
            );

            Employer::firstOrCreate(
                ['user_id' => $user->id],
                [
                    'company_name' => $companyNames[$i],
                    'company_description' => $companyDescriptions[$i],
                    'industry' => $industries[$i],
                    'website' => 'https://www.company' . ($i + 1) . '.ly',
                    'location' => ['طرابلس', 'مصراتة', 'بنغازي', 'سبها', 'الزاوية', 'زليتن', 'أجدابيا'][$i] . ', ليبيا',
                    'company_size' => ['11-50', '51-200', '51-200', '201-500', '51-200', '51-200', '201-500'][$i],
                    'founded_year' => [2010, 2015, 2012, 2008, 2018, 2016, 2005][$i],
                ]
            );
        }

        // إنشاء وظائف
        $jobData = [
            [
                'title' => 'مطور PHP متقدم',
                'category' => 'تقنية المعلومات',
                'description' => 'نبحث عن مطور PHP متقدم للانضمام إلى فريقنا التقني. المرشح المثالي يجب أن يكون لديه خبرة قوية في Laravel وتطوير التطبيقات الويب.',
                'requirements' => "- خبرة لا تقل عن 5 سنوات في تطوير PHP\n- إتقان Laravel Framework\n- معرفة بقواعد البيانات MySQL\n- خبرة في Git وأدوات التطوير\n- شهادة جامعية في علوم الحاسب أو ما يعادلها",
                'salary_range' => '1200 - 2250 دينار ليبي'
            ],
            [
                'title' => 'مطور واجهات أمامية',
                'category' => 'تقنية المعلومات',
                'description' => 'نبحث عن مطور واجهات أمامية مبدع لتطوير واجهات مستخدم متجاوبة وجذابة باستخدام أحدث التقنيات.',
                'requirements' => "- خبرة في HTML, CSS, JavaScript\n- إتقان React أو Vue.js\n- معرفة بـ Tailwind CSS\n- خبرة في تطوير المواقع المتجاوبة\n- حس فني وإبداعي",
                'salary_range' => '6000 - 12000 ريال سعودي'
            ],
            [
                'title' => 'مصمم جرافيك',
                'category' => 'التصميم والإبداع',
                'description' => 'نبحث عن مصمم جرافيك مبدع لإنشاء تصاميم بصرية مميزة للعلامات التجارية والحملات التسويقية.',
                'requirements' => "- إتقان Adobe Creative Suite\n- خبرة في تصميم الهوية البصرية\n- معرفة بأساسيات التصميم\n- القدرة على العمل تحت الضغط\n- ملف أعمال قوي",
                'salary_range' => '5000 - 10000 ريال سعودي'
            ],
            [
                'title' => 'محاسب مالي',
                'category' => 'التمويل والمحاسبة',
                'description' => 'نبحث عن محاسب مالي مؤهل لإدارة الحسابات المالية وإعداد التقارير المالية للشركة.',
                'requirements' => "- شهادة في المحاسبة أو المالية\n- خبرة في برامج المحاسبة\n- معرفة بالمعايير المحاسبية الليبية\n- دقة في العمل والتفاصيل\n- مهارات تحليلية قوية",
                'salary_range' => '1050 - 1950 دينار ليبي'
            ],
            [
                'title' => 'أخصائي تسويق رقمي',
                'category' => 'التسويق والمبيعات',
                'description' => 'نبحث عن أخصائي تسويق رقمي لإدارة الحملات التسويقية الرقمية وزيادة الوعي بالعلامة التجارية.',
                'requirements' => "- خبرة في إدارة حملات Google Ads\n- معرفة بوسائل التواصل الاجتماعي\n- مهارات في تحليل البيانات\n- إبداع في إنشاء المحتوى\n- شهادات في التسويق الرقمي مفضلة",
                'salary_range' => '900 - 1650 دينار ليبي'
            ]
        ];

        $jobTypes = ['full_time', 'part_time', 'remote', 'freelance'];
        $locations = ['طرابلس', 'مصراتة', 'بنغازي', 'سبها', 'الزاوية'];

        // التأكد من وجود التصنيفات المطلوبة
        $requiredCategories = [
            'تقنية المعلومات',
            'التصميم والإبداع',
            'التمويل والمحاسبة',
            'التسويق والمبيعات'
        ];

        foreach ($requiredCategories as $categoryName) {
            Category::firstOrCreate(
                ['name' => $categoryName],
                [
                    'slug' => match($categoryName) {
                        'تقنية المعلومات' => 'information-technology',
                        'التصميم والإبداع' => 'design-creativity',
                        'التمويل والمحاسبة' => 'finance-accounting',
                        'التسويق والمبيعات' => 'marketing-sales',
                        default => 'general'
                    },
                    'icon' => match($categoryName) {
                        'تقنية المعلومات' => 'laptop-code',
                        'التصميم والإبداع' => 'palette',
                        'التمويل والمحاسبة' => 'hand-holding-dollar',
                        'التسويق والمبيعات' => 'bullhorn',
                        default => 'briefcase'
                    },
                    'description' => match($categoryName) {
                        'تقنية المعلومات' => 'وظائف في مجال تقنية المعلومات وتطوير البرمجيات',
                        'التصميم والإبداع' => 'وظائف في مجال التصميم الجرافيكي والإبداع',
                        'التمويل والمحاسبة' => 'وظائف في مجال المالية والمحاسبة والتمويل',
                        'التسويق والمبيعات' => 'وظائف في مجال التسويق والمبيعات والعلاقات العامة',
                        default => 'وظائف متنوعة'
                    }
                ]
            );
        }

        $employers = Employer::all();
        $categories = Category::all();

        foreach ($employers as $employerIndex => $employer) {
            // كل صاحب عمل ينشر 2-3 وظائف
            $jobCount = rand(2, 3);
            for ($i = 0; $i < $jobCount; $i++) {
                $jobInfo = $jobData[($employerIndex * $jobCount + $i) % count($jobData)];
                $jobType = $jobTypes[array_rand($jobTypes)];
                $location = $locations[array_rand($locations)];

                // العثور على التصنيف المناسب
                $category = $categories->where('name', $jobInfo['category'])->first();

                // تاريخ النشر بين اليوم و30 يوم مضت
                $postedAt = Carbon::now()->subDays(rand(0, 30));

                // تاريخ انتهاء الصلاحية بين 30 و90 يوم من تاريخ النشر
                $expiresAt = (clone $postedAt)->addDays(rand(30, 90));

                Job::create([
                    'employer_id' => $employer->id,
                    'category_id' => $category ? $category->id : null,
                    'title' => $jobInfo['title'],
                    'description' => $jobInfo['description'],
                    'requirements' => $jobInfo['requirements'],
                    'salary_range' => $jobInfo['salary_range'],
                    'location' => $location . ', ليبيا',
                    'job_type' => $jobType,
                    'posted_at' => $postedAt,
                    'expires_at' => $expiresAt,
                    'is_active' => true,
                    'is_featured' => rand(0, 1) == 1,
                    'views' => rand(10, 500),
                ]);
            }
        }

        // إنشاء بعض طلبات التوظيف
        $jobs = Job::all();
        $jobSeekers = JobSeeker::all();

        foreach ($jobSeekers as $jobSeeker) {
            // كل باحث عن عمل يقدم على 2-4 وظائف
            $applicationCount = rand(2, 4);
            $appliedJobs = $jobs->random($applicationCount);

            foreach ($appliedJobs as $job) {
                Application::create([
                    'job_id' => $job->id,
                    'job_seeker_id' => $jobSeeker->id,
                    'cover_letter' => 'أتقدم بطلب للحصول على هذه الوظيفة لأنني أعتقد أن مهاراتي وخبرتي تتناسب مع متطلبات المنصب. أتطلع للمساهمة في نجاح شركتكم.',
                    'status' => ['pending', 'accepted', 'rejected'][rand(0, 2)],
                    'applied_at' => Carbon::now()->subDays(rand(1, 20)),
                ]);
            }
        }

        // إنشاء تقييمات تجريبية
        $this->createSampleReviews();

        // إنشاء إشعارات تجريبية
        $this->createSampleNotifications();
    }

    /**
     * إنشاء تقييمات تجريبية
     */
    private function createSampleReviews()
    {
        $jobSeekers = User::where('role', 'job_seeker')->get();
        $employers = User::where('role', 'employer')->get();

        $reviewComments = [
            'شركة ممتازة وبيئة عمل رائعة. أنصح بالعمل معهم بشدة.',
            'تجربة جيدة في العمل مع الشركة. هناك مجال للتحسين في بعض الجوانب.',
            'شركة احترافية وفريق عمل متعاون. راتب مناسب وساعات عمل مرنة.',
            'إدارة ممتازة وفرص تطوير مهني جيدة. أنصح بالتقديم.',
            'تجربة متوسطة. الراتب مناسب لكن ساعات العمل طويلة أحياناً.',
            'شركة رائدة في مجالها. تقدر الموظفين وتوفر بيئة عمل محفزة.'
        ];

        foreach ($employers->take(5) as $employer) {
            // كل صاحب عمل يحصل على 2-4 تقييمات
            $reviewCount = rand(2, 4);
            $selectedJobSeekers = $jobSeekers->random($reviewCount);

            foreach ($selectedJobSeekers as $jobSeeker) {
                Review::create([
                    'reviewer_id' => $jobSeeker->id,
                    'reviewed_id' => $employer->id,
                    'type' => 'company',
                    'rating' => rand(3, 5),
                    'comment' => $reviewComments[array_rand($reviewComments)],
                    'status' => ['approved', 'pending'][rand(0, 1)],
                    'approved_at' => rand(0, 1) ? Carbon::now()->subDays(rand(1, 10)) : null,
                    'created_at' => Carbon::now()->subDays(rand(1, 30)),
                ]);
            }
        }
    }

    /**
     * إنشاء إشعارات تجريبية
     */
    private function createSampleNotifications()
    {
        $employers = User::where('role', 'employer')->get();
        $jobSeekers = User::where('role', 'job_seeker')->get();

        $notificationTypes = [
            'job_application_received',
            'application_status_update',
            'profile_rated',
            'company_rated'
        ];

        $notificationMessages = [
            'job_application_received' => 'تم استلام طلب توظيف جديد للوظيفة: {job_title}',
            'application_status_update' => 'تم تحديث حالة طلب التوظيف الخاص بك',
            'profile_rated' => 'تم تقييم ملفك الشخصي من قبل أحد أصحاب العمل',
            'company_rated' => 'تم تقييم شركتك بـ {rating} نجوم من قبل أحد المتقدمين'
        ];

        // إشعارات لأصحاب العمل
        foreach ($employers->take(3) as $employer) {
            for ($i = 0; $i < rand(3, 6); $i++) {
                $type = $notificationTypes[array_rand($notificationTypes)];
                $message = $notificationMessages[$type];

                if ($type === 'job_application_received') {
                    $message = str_replace('{job_title}', 'مطور PHP متقدم', $message);
                } elseif ($type === 'company_rated') {
                    $message = str_replace('{rating}', rand(4, 5), $message);
                }

                Notification::create([
                    'user_id' => $employer->id,
                    'type' => $type,
                    'message' => $message,
                    'link' => match($type) {
                        'job_application_received' => '/employer/applications',
                        'company_rated' => '/employer/reviews',
                        default => null
                    },
                    'is_read' => rand(0, 1) == 1,
                    'created_at' => Carbon::now()->subDays(rand(0, 15)),
                ]);
            }
        }

        // إشعارات للباحثين عن العمل
        foreach ($jobSeekers->take(3) as $jobSeeker) {
            for ($i = 0; $i < rand(2, 4); $i++) {
                $type = ['application_status_update', 'profile_rated'][rand(0, 1)];
                $message = $notificationMessages[$type];

                Notification::create([
                    'user_id' => $jobSeeker->id,
                    'type' => $type,
                    'message' => $message,
                    'link' => '/job-seeker/applications',
                    'is_read' => rand(0, 1) == 1,
                    'created_at' => Carbon::now()->subDays(rand(0, 10)),
                ]);
            }
        }
    }
}
