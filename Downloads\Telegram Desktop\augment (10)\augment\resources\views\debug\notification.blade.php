<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة الإشعارات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 font-sans">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h1 class="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-2">
                    <i class="fas fa-bug text-red-500"></i>
                    تشخيص مشكلة الإشعارات
                </h1>

                <!-- نموذج البحث -->
                <div class="mb-6 p-4 bg-blue-50 rounded-lg">
                    <h2 class="text-lg font-semibold mb-3">البحث عن إشعار محدد:</h2>
                    <form method="GET" class="flex gap-2">
                        <input type="number" name="id" value="{{ request('id', 253) }}" 
                               placeholder="رقم الإشعار" 
                               class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                            <i class="fas fa-search"></i> بحث
                        </button>
                    </form>
                </div>

                @php
                    $notificationId = request('id', 253);
                    $notification = \App\Models\Notification::find($notificationId);
                @endphp

                <!-- نتائج التشخيص -->
                <div class="space-y-6">
                    <!-- معلومات الإشعار -->
                    <div class="p-4 border rounded-lg">
                        <h3 class="text-lg font-semibold mb-3 flex items-center gap-2">
                            <i class="fas fa-bell"></i>
                            معلومات الإشعار رقم {{ $notificationId }}
                        </h3>
                        
                        @if($notification)
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="space-y-2">
                                    <p><strong>الحالة:</strong> <span class="text-green-600">✅ موجود</span></p>
                                    <p><strong>المستخدم:</strong> {{ $notification->user_id }}</p>
                                    <p><strong>النوع:</strong> {{ $notification->type }}</p>
                                    <p><strong>مقروء:</strong> {{ $notification->is_read ? 'نعم' : 'لا' }}</p>
                                </div>
                                <div class="space-y-2">
                                    <p><strong>تاريخ الإنشاء:</strong> {{ $notification->created_at }}</p>
                                    <p><strong>الرابط:</strong> {{ $notification->link ?? 'لا يوجد' }}</p>
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <p><strong>الرسالة:</strong></p>
                                <div class="p-3 bg-gray-50 rounded border mt-2">
                                    {{ $notification->message }}
                                </div>
                            </div>

                            @if($notification->user)
                                <div class="mt-4 p-3 bg-green-50 rounded border">
                                    <p><strong>معلومات المستخدم:</strong></p>
                                    <p>الاسم: {{ $notification->user->name }}</p>
                                    <p>الدور: {{ $notification->user->role }}</p>
                                    <p>البريد الإلكتروني: {{ $notification->user->email }}</p>
                                </div>
                            @else
                                <div class="mt-4 p-3 bg-red-50 rounded border">
                                    <p class="text-red-600">❌ المستخدم غير موجود!</p>
                                </div>
                            @endif
                        @else
                            <div class="p-4 bg-red-50 border border-red-200 rounded">
                                <p class="text-red-600">❌ الإشعار رقم {{ $notificationId }} غير موجود</p>
                            </div>
                        @endif
                    </div>

                    <!-- اختبار الـ Routes -->
                    <div class="p-4 border rounded-lg">
                        <h3 class="text-lg font-semibold mb-3 flex items-center gap-2">
                            <i class="fas fa-route"></i>
                            اختبار الـ Routes
                        </h3>
                        
                        @php
                            $routes = [
                                'notifications.mark-read' => 'الإشعارات العامة',
                                'employer.notifications.mark-read' => 'إشعارات أصحاب العمل',
                                'admin.notifications.mark-read' => 'إشعارات الإدارة',
                            ];
                        @endphp

                        <div class="space-y-2">
                            @foreach($routes as $routeName => $description)
                                @try
                                    @php $url = route($routeName, ['notification' => $notificationId]); @endphp
                                    <div class="flex items-center justify-between p-2 bg-green-50 rounded">
                                        <span>✅ {{ $description }}</span>
                                        <a href="{{ $url }}" class="text-blue-600 hover:underline text-sm">{{ $url }}</a>
                                    </div>
                                @catch(Exception $e)
                                    <div class="flex items-center justify-between p-2 bg-red-50 rounded">
                                        <span>❌ {{ $description }}</span>
                                        <span class="text-red-600 text-sm">{{ $e->getMessage() }}</span>
                                    </div>
                                @endtry
                            @endforeach
                        </div>
                    </div>

                    <!-- إحصائيات عامة -->
                    <div class="p-4 border rounded-lg">
                        <h3 class="text-lg font-semibold mb-3 flex items-center gap-2">
                            <i class="fas fa-chart-bar"></i>
                            إحصائيات عامة
                        </h3>
                        
                        @php
                            $totalNotifications = \App\Models\Notification::count();
                            $unreadNotifications = \App\Models\Notification::where('is_read', false)->count();
                            $latestNotifications = \App\Models\Notification::latest()->limit(5)->get();
                        @endphp

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            <div class="text-center p-3 bg-blue-50 rounded">
                                <div class="text-2xl font-bold text-blue-600">{{ $totalNotifications }}</div>
                                <div class="text-sm text-gray-600">إجمالي الإشعارات</div>
                            </div>
                            <div class="text-center p-3 bg-yellow-50 rounded">
                                <div class="text-2xl font-bold text-yellow-600">{{ $unreadNotifications }}</div>
                                <div class="text-sm text-gray-600">غير مقروءة</div>
                            </div>
                            <div class="text-center p-3 bg-green-50 rounded">
                                <div class="text-2xl font-bold text-green-600">{{ $totalNotifications - $unreadNotifications }}</div>
                                <div class="text-sm text-gray-600">مقروءة</div>
                            </div>
                        </div>

                        <h4 class="font-semibold mb-2">آخر 5 إشعارات:</h4>
                        <div class="space-y-1">
                            @foreach($latestNotifications as $notif)
                                <div class="text-sm p-2 bg-gray-50 rounded flex justify-between">
                                    <span>ID: {{ $notif->id }} - {{ $notif->type }}</span>
                                    <span class="text-gray-500">{{ $notif->created_at->diffForHumans() }}</span>
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- أدوات مفيدة -->
                    <div class="p-4 border rounded-lg">
                        <h3 class="text-lg font-semibold mb-3 flex items-center gap-2">
                            <i class="fas fa-tools"></i>
                            أدوات مفيدة
                        </h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <a href="{{ route('notifications.index') }}" class="block p-3 bg-blue-50 rounded text-center hover:bg-blue-100">
                                <i class="fas fa-list mb-2"></i><br>
                                صفحة الإشعارات
                            </a>
                            <a href="{{ route('system.status') }}" class="block p-3 bg-green-50 rounded text-center hover:bg-green-100">
                                <i class="fas fa-heartbeat mb-2"></i><br>
                                حالة النظام
                            </a>
                            <a href="{{ route('debug.notification', ['id' => 1]) }}" class="block p-3 bg-yellow-50 rounded text-center hover:bg-yellow-100">
                                <i class="fas fa-search mb-2"></i><br>
                                تشخيص إشعار آخر
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
