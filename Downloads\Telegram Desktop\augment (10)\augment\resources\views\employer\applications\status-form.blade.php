@extends('layouts.employer')

@section('title', 'تغيير حالة الطلب - Hire Me')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-bold">تغيير حالة الطلب</h2>
                <p class="text-gray-600 dark:text-gray-400 mt-1">تغيير حالة طلب {{ $application->jobSeeker->user->name }}</p>
            </div>
            <div class="flex gap-3">
                <a href="{{ route('employer.applications.show', $application) }}" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors flex items-center gap-2">
                    <i class="fas fa-arrow-right"></i>
                    <span>العودة</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg">
            <div class="flex items-center">
                <i class="fas fa-check-circle ml-2"></i>
                {{ session('success') }}
            </div>
        </div>
    @endif

    @if(session('error'))
        <div class="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
            <div class="flex items-center">
                <i class="fas fa-exclamation-circle ml-2"></i>
                {{ session('error') }}
            </div>
        </div>
    @endif

    <!-- Application Info -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">معلومات الطلب</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <span class="text-gray-600 dark:text-gray-400">المتقدم:</span>
                <span class="font-medium text-gray-900 dark:text-white mr-2">{{ $application->jobSeeker->user->name }}</span>
            </div>
            <div>
                <span class="text-gray-600 dark:text-gray-400">الوظيفة:</span>
                <span class="font-medium text-gray-900 dark:text-white mr-2">{{ $application->job->title }}</span>
            </div>
            <div>
                <span class="text-gray-600 dark:text-gray-400">تاريخ التقديم:</span>
                <span class="font-medium text-gray-900 dark:text-white mr-2">{{ $application->created_at->format('Y/m/d H:i') }}</span>
            </div>
            <div>
                <span class="text-gray-600 dark:text-gray-400">الحالة الحالية:</span>
                <span class="px-3 py-1 rounded-full text-sm font-medium mr-2
                    @if($application->status === 'pending') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300
                    @elseif($application->status === 'reviewed') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300
                    @elseif($application->status === 'accepted') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300
                    @elseif($application->status === 'rejected') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300
                    @else bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300 @endif">
                    @if($application->status === 'pending') قيد المراجعة
                    @elseif($application->status === 'reviewed') تمت المراجعة
                    @elseif($application->status === 'accepted') مقبول
                    @elseif($application->status === 'rejected') مرفوض
                    @else {{ $application->status }} @endif
                </span>
            </div>
        </div>
    </div>

    <!-- Status Change Form -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">تغيير حالة الطلب</h3>
        
        <form action="{{ route('employer.applications.update-status', $application) }}" method="POST">
            @csrf
            @method('PATCH')

            <!-- Status Selection -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
                    اختر الحالة الجديدة <span class="text-red-500">*</span>
                </label>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Pending -->
                    <label class="flex items-center p-4 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        <input type="radio" name="status" value="pending" {{ $application->status === 'pending' ? 'checked' : '' }} class="text-yellow-600 focus:ring-yellow-500">
                        <div class="mr-3">
                            <div class="flex items-center gap-2">
                                <i class="fas fa-clock text-yellow-600"></i>
                                <span class="font-medium text-gray-900 dark:text-white">قيد المراجعة</span>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">الطلب في انتظار المراجعة</p>
                        </div>
                    </label>

                    <!-- Reviewed -->
                    <label class="flex items-center p-4 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        <input type="radio" name="status" value="reviewed" {{ $application->status === 'reviewed' ? 'checked' : '' }} class="text-blue-600 focus:ring-blue-500">
                        <div class="mr-3">
                            <div class="flex items-center gap-2">
                                <i class="fas fa-eye text-blue-600"></i>
                                <span class="font-medium text-gray-900 dark:text-white">تمت المراجعة</span>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">تم مراجعة الطلب</p>
                        </div>
                    </label>

                    <!-- Accepted -->
                    <label class="flex items-center p-4 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        <input type="radio" name="status" value="accepted" {{ $application->status === 'accepted' ? 'checked' : '' }} class="text-green-600 focus:ring-green-500">
                        <div class="mr-3">
                            <div class="flex items-center gap-2">
                                <i class="fas fa-check text-green-600"></i>
                                <span class="font-medium text-gray-900 dark:text-white">مقبول</span>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">تم قبول الطلب</p>
                        </div>
                    </label>

                    <!-- Rejected -->
                    <label class="flex items-center p-4 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        <input type="radio" name="status" value="rejected" {{ $application->status === 'rejected' ? 'checked' : '' }} class="text-red-600 focus:ring-red-500">
                        <div class="mr-3">
                            <div class="flex items-center gap-2">
                                <i class="fas fa-times text-red-600"></i>
                                <span class="font-medium text-gray-900 dark:text-white">مرفوض</span>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">تم رفض الطلب</p>
                        </div>
                    </label>

                    <!-- Interview -->
                    <label class="flex items-center p-4 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        <input type="radio" name="status" value="interview" {{ $application->status === 'interview' ? 'checked' : '' }} class="text-purple-600 focus:ring-purple-500">
                        <div class="mr-3">
                            <div class="flex items-center gap-2">
                                <i class="fas fa-users text-purple-600"></i>
                                <span class="font-medium text-gray-900 dark:text-white">مقابلة شخصية</span>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">دعوة لمقابلة شخصية</p>
                        </div>
                    </label>

                    <!-- Hired -->
                    <label class="flex items-center p-4 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        <input type="radio" name="status" value="hired" {{ $application->status === 'hired' ? 'checked' : '' }} class="text-indigo-600 focus:ring-indigo-500">
                        <div class="mr-3">
                            <div class="flex items-center gap-2">
                                <i class="fas fa-handshake text-indigo-600"></i>
                                <span class="font-medium text-gray-900 dark:text-white">تم التعيين</span>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">تم تعيين المتقدم</p>
                        </div>
                    </label>
                </div>
                
                @error('status')
                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Notes -->
            <div class="mb-6">
                <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    ملاحظات (اختياري)
                </label>
                <textarea id="notes" name="notes" rows="4" 
                          class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                          placeholder="أضف أي ملاحظات حول تغيير الحالة...">{{ old('notes') }}</textarea>
                @error('notes')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Form Actions -->
            <div class="flex gap-3">
                <button type="submit" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2">
                    <i class="fas fa-save"></i>
                    حفظ التغييرات
                </button>
                
                <a href="{{ route('employer.applications.show', $application) }}" class="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors flex items-center gap-2">
                    <i class="fas fa-times"></i>
                    إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<script>
// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const selectedStatus = document.querySelector('input[name="status"]:checked');
    
    if (!selectedStatus) {
        e.preventDefault();
        alert('يرجى اختيار حالة جديدة للطلب');
        return;
    }
    
    // Confirmation for critical actions
    const criticalStatuses = ['rejected', 'hired'];
    if (criticalStatuses.includes(selectedStatus.value)) {
        const statusText = selectedStatus.value === 'rejected' ? 'رفض' : 'تعيين';
        if (!confirm(`هل أنت متأكد من ${statusText} هذا الطلب؟ هذا الإجراء مهم ويجب التأكد منه.`)) {
            e.preventDefault();
            return;
        }
    }
});

// Auto-save draft (optional)
let saveTimeout;
function autoSave() {
    clearTimeout(saveTimeout);
    saveTimeout = setTimeout(() => {
        console.log('Auto-saving draft...');
        // Could implement auto-save functionality here
    }, 2000);
}

// Add auto-save listeners
document.querySelectorAll('input[type="radio"], textarea').forEach(element => {
    element.addEventListener('change', autoSave);
});
</script>
@endsection
