@extends('layouts.admin')

@section('title', 'لوحة تحكم المدير - Hire Me')

@section('content')
<!-- Dashboard Content -->
<div class="p-6">
    <div class="mb-8">
        <h2 class="text-2xl font-bold mb-2">مرحبًا، {{ Auth::user()->name ?? 'المدير' }}!</h2>
        <p class="text-gray-600 dark:text-gray-400">هذه نظرة عامة على نشاط المنصة</p>
        <div class="mt-4">
            <a href="{{ route('admin.index') }}" class="px-4 py-2 gradient-bg text-white rounded-lg hover:opacity-90 transition-colors">الذهاب إلى الصفحة الرئيسية</a>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Stat Card 1 -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <div class="flex justify-between items-start mb-4">
                <div>
                    <p class="text-gray-500 dark:text-gray-400 text-sm">الوظائف النشطة</p>
                    <h3 class="text-3xl font-bold">{{ $activeJobsCount ?? '12' }}</h3>
                </div>
                <div class="w-12 h-12 rounded-lg gradient-bg flex items-center justify-center text-white">
                    <i class="fas fa-briefcase text-xl"></i>
                </div>
            </div>
            <div class="flex items-center text-green-600 dark:text-green-400">
                <i class="fas fa-arrow-up ml-1"></i>
                <span class="text-sm">{{ $jobsPercentage ?? '20%' }} أعلى من الشهر الماضي</span>
            </div>
        </div>

        <!-- Stat Card 2 -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <div class="flex justify-between items-start mb-4">
                <div>
                    <p class="text-gray-500 dark:text-gray-400 text-sm">إجمالي الطلبات</p>
                    <h3 class="text-3xl font-bold">{{ $totalApplications ?? '143' }}</h3>
                </div>
                <div class="w-12 h-12 rounded-lg bg-blue-500 flex items-center justify-center text-white">
                    <i class="fas fa-file-alt text-xl"></i>
                </div>
            </div>
            <div class="flex items-center text-green-600 dark:text-green-400">
                <i class="fas fa-arrow-up ml-1"></i>
                <span class="text-sm">{{ $applicationsPercentage ?? '15%' }} أعلى من الشهر الماضي</span>
            </div>
        </div>

        <!-- Stat Card 3 -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <div class="flex justify-between items-start mb-4">
                <div>
                    <p class="text-gray-500 dark:text-gray-400 text-sm">المستخدمين النشطين</p>
                    <h3 class="text-3xl font-bold">{{ $activeUsers ?? '256' }}</h3>
                </div>
                <div class="w-12 h-12 rounded-lg bg-green-500 flex items-center justify-center text-white">
                    <i class="fas fa-users text-xl"></i>
                </div>
            </div>
            <div class="flex items-center text-green-600 dark:text-green-400">
                <i class="fas fa-arrow-up ml-1"></i>
                <span class="text-sm">{{ $usersPercentage ?? '12%' }} أعلى من الشهر الماضي</span>
            </div>
        </div>

        <!-- Stat Card 4 -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <div class="flex justify-between items-start mb-4">
                <div>
                    <p class="text-gray-500 dark:text-gray-400 text-sm">الإيرادات</p>
                    <h3 class="text-3xl font-bold">{{ $revenue ?? '$5,240' }}</h3>
                </div>
                <div class="w-12 h-12 rounded-lg bg-purple-500 flex items-center justify-center text-white">
                    <i class="fas fa-dollar-sign text-xl"></i>
                </div>
            </div>
            <div class="flex items-center text-green-600 dark:text-green-400">
                <i class="fas fa-arrow-up ml-1"></i>
                <span class="text-sm">{{ $revenuePercentage ?? '8%' }} أعلى من الشهر الماضي</span>
            </div>
        </div>
    </div>

    <!-- Recent Activities -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6 mb-8">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-bold">آخر النشاطات</h3>
            <a href="#" class="text-primary text-sm hover:underline">عرض الكل</a>
        </div>

        <div class="space-y-4">
            <!-- Activity 1 -->
            <div class="flex items-center gap-4 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-user-plus text-blue-600 dark:text-blue-400"></i>
                </div>
                <div class="flex-1">
                    <p class="font-medium">تسجيل مستخدم جديد</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">قام أحمد محمد بالتسجيل كباحث عن عمل</p>
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">
                    منذ 10 دقائق
                </div>
            </div>

            <!-- Activity 2 -->
            <div class="flex items-center gap-4 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                <div class="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-briefcase text-green-600 dark:text-green-400"></i>
                </div>
                <div class="flex-1">
                    <p class="font-medium">وظيفة جديدة</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">تم نشر وظيفة "مطور واجهات أمامية" من قبل شركة التقنية المتطورة</p>
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">
                    منذ 30 دقيقة
                </div>
            </div>

            <!-- Activity 3 -->
            <div class="flex items-center gap-4 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-file-alt text-purple-600 dark:text-purple-400"></i>
                </div>
                <div class="flex-1">
                    <p class="font-medium">طلب توظيف جديد</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">تقدم سارة عبدالله بطلب لوظيفة "مصمم واجهات المستخدم"</p>
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">
                    منذ ساعة
                </div>
            </div>

            <!-- Activity 4 -->
            <div class="flex items-center gap-4 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                <div class="w-10 h-10 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-credit-card text-red-600 dark:text-red-400"></i>
                </div>
                <div class="flex-1">
                    <p class="font-medium">اشتراك جديد</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">قامت شركة الإبداع التقني بترقية اشتراكها إلى الباقة المتقدمة</p>
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">
                    منذ 3 ساعات
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Users Chart -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-bold">إحصائيات المستخدمين</h3>
                <select class="bg-gray-100 dark:bg-gray-800 border-0 rounded-lg px-3 py-2 text-sm">
                    <option>آخر 7 أيام</option>
                    <option>آخر 30 يوم</option>
                    <option>آخر 90 يوم</option>
                </select>
            </div>

            <div class="h-64 flex items-end justify-between">
                @foreach([
                    ['day' => 'السبت', 'height' => 40],
                    ['day' => 'الأحد', 'height' => 28],
                    ['day' => 'الإثنين', 'height' => 48],
                    ['day' => 'الثلاثاء', 'height' => 32],
                    ['day' => 'الأربعاء', 'height' => 56],
                    ['day' => 'الخميس', 'height' => 36],
                    ['day' => 'الجمعة', 'height' => 20]
                ] as $data)
                    <div class="flex flex-col items-center">
                        <div class="gradient-bg w-8 rounded-t-lg" style="height: {!! $data['height'] !!}px;"></div>
                        <span class="mt-2 text-xs text-gray-500 dark:text-gray-400">{{ $data['day'] }}</span>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Revenue Chart -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-bold">الإيرادات الشهرية</h3>
                <select class="bg-gray-100 dark:bg-gray-800 border-0 rounded-lg px-3 py-2 text-sm">
                    <option>هذا العام</option>
                    <option>العام الماضي</option>
                </select>
            </div>

            <div class="space-y-4">
                @foreach([
                    ['month' => 'يناير', 'amount' => 3200, 'percentage' => 60],
                    ['month' => 'فبراير', 'amount' => 4100, 'percentage' => 75],
                    ['month' => 'مارس', 'amount' => 5200, 'percentage' => 90],
                    ['month' => 'أبريل', 'amount' => 4800, 'percentage' => 85],
                    ['month' => 'مايو', 'amount' => 5500, 'percentage' => 95]
                ] as $revenue)
                    <div>
                        <div class="flex justify-between mb-1">
                            <span class="font-medium">{{ $revenue['month'] }}</span>
                            <span class="font-medium">${{ $revenue['amount'] }}</span>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                            <div class="gradient-bg h-2.5 rounded-full" style="width: {!! $revenue['percentage'] !!}%;"></div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</div>
@endsection
