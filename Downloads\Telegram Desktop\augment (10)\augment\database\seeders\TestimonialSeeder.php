<?php

namespace Database\Seeders;

use App\Models\Testimonial;
use App\Models\User;
use Illuminate\Database\Seeder;

class TestimonialSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $testimonials = [
            [
                'content' => 'وجدت وظيفتي الحالية من خلال Hire Me في أقل من أسبوعين. واجهة الموقع سهلة الاستخدام وساعدتني في العثور على وظائف تناسب مهاراتي بدقة.',
                'rating' => 5,
                'position' => 'مطور واجهات أمامية',
                'company' => 'شركة التقنية المتطورة',
                'is_featured' => true,
            ],
            [
                'content' => 'كمديرة موارد بشرية، أصبح استخدام Hire Me جزءًا أساسيًا من عملية التوظيف لدينا. نستطيع العثور على مرشحين مؤهلين بسرعة وسهولة.',
                'rating' => 4,
                'position' => 'مديرة موارد بشرية',
                'company' => 'شركة البيانات المتقدمة',
                'is_featured' => true,
            ],
            [
                'content' => 'بعد عدة محاولات في مواقع توظيف أخرى، استطعت العثور على وظيفة أحلامي خلال Hire Me. الشركات الموجودة على المنصة رائدة في مجالاتها.',
                'rating' => 5,
                'position' => 'مدير تسويق',
                'company' => 'شركة الحلول التسويقية',
                'is_featured' => true,
            ],
        ];

        // Get some users or create them if they don't exist
        $users = User::take(3)->get();
        
        if ($users->count() < 3) {
            // If we don't have enough users, create some
            for ($i = $users->count(); $i < 3; $i++) {
                $users->push(User::factory()->create([
                    'name' => ['أحمد محمد', 'نورة عبدالله', 'خالد سعيد'][$i],
                    'email' => ['<EMAIL>', '<EMAIL>', '<EMAIL>'][$i],
                ]));
            }
        }

        foreach ($testimonials as $index => $testimonial) {
            Testimonial::firstOrCreate(
                [
                    'user_id' => $users[$index]->id,
                    'content' => $testimonial['content']
                ],
                [
                    'rating' => $testimonial['rating'],
                    'position' => $testimonial['position'],
                    'company' => $testimonial['company'],
                    'is_featured' => $testimonial['is_featured'],
                ]
            );
        }
    }
}
