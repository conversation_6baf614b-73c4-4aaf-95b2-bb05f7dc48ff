@extends('layouts.admin')

@section('title', 'التقارير والإحصائيات - Hire Me')
@section('header_title', 'التقارير والإحصائيات')

@section('content')
<div class="p-6">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
            <h2 class="text-2xl font-bold">التقارير والإحصائيات</h2>
            <p class="text-gray-600 dark:text-gray-400 mt-1">تحليل أداء المنصة والوظائف والطلبات</p>
        </div>
        <div class="mt-4 md:mt-0">
            <a href="{{ route('admin.reports.scheduled') }}" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors flex items-center gap-2">
                <i class="fas fa-calendar-alt"></i>
                <span>التقارير المجدولة</span>
            </a>
        </div>
    </div>

    <!-- Report Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <!-- Jobs Report Card -->
        <a href="{{ route('admin.reports.jobs') }}" class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center gap-4 mb-4">
                <div class="w-14 h-14 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                    <i class="fas fa-briefcase text-blue-600 dark:text-blue-400 text-xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-bold">تقارير الوظائف</h3>
                    <p class="text-gray-600 dark:text-gray-400 text-sm">تحليل أداء الوظائف والطلبات</p>
                </div>
            </div>
            <div class="flex justify-between items-center text-sm">
                <span class="text-gray-600 dark:text-gray-400">{{ $activeJobs }} وظيفة نشطة</span>
                <span class="text-primary">
                    <i class="fas fa-arrow-right"></i>
                </span>
            </div>
        </a>

        <!-- Applications Report Card -->
        <a href="{{ route('admin.reports.applications') }}" class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center gap-4 mb-4">
                <div class="w-14 h-14 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                    <i class="fas fa-file-alt text-green-600 dark:text-green-400 text-xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-bold">تقارير الطلبات</h3>
                    <p class="text-gray-600 dark:text-gray-400 text-sm">تحليل طلبات التوظيف والمتقدمين</p>
                </div>
            </div>
            <div class="flex justify-between items-center text-sm">
                <span class="text-gray-600 dark:text-gray-400">{{ $totalApplications }} طلب توظيف</span>
                <span class="text-primary">
                    <i class="fas fa-arrow-right"></i>
                </span>
            </div>
        </a>

        <!-- Financial Report Card -->
        <a href="{{ route('admin.reports.financial') }}" class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center gap-4 mb-4">
                <div class="w-14 h-14 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
                    <i class="fas fa-chart-line text-purple-600 dark:text-purple-400 text-xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-bold">التقارير المالية</h3>
                    <p class="text-gray-600 dark:text-gray-400 text-sm">تحليل الإيرادات والمدفوعات</p>
                </div>
            </div>
            <div class="flex justify-between items-center text-sm">
                <span class="text-gray-600 dark:text-gray-400">إيرادات متوقعة</span>
                <span class="text-primary">
                    <i class="fas fa-arrow-right"></i>
                </span>
            </div>
        </a>
    </div>

    <!-- Platform Overview -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6 mb-6">
        <h3 class="text-lg font-bold mb-4">نظرة عامة على المنصة</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="flex flex-col">
                <span class="text-gray-600 dark:text-gray-400 text-sm">إجمالي المستخدمين</span>
                <span class="text-2xl font-bold">{{ number_format($totalUsers) }}</span>
                <div class="flex items-center gap-1 text-sm mt-1 text-green-600 dark:text-green-400">
                    <i class="fas fa-arrow-up"></i>
                    <span>{{ $usersThisMonth }}</span>
                    <span class="text-gray-600 dark:text-gray-400 mr-1">هذا الشهر</span>
                </div>
            </div>

            <div class="flex flex-col">
                <span class="text-gray-600 dark:text-gray-400 text-sm">إجمالي الشركات</span>
                <span class="text-2xl font-bold">{{ number_format($totalEmployers) }}</span>
                <div class="flex items-center gap-1 text-sm mt-1 text-blue-600 dark:text-blue-400">
                    <i class="fas fa-building"></i>
                    <span>أصحاب عمل</span>
                </div>
            </div>

            <div class="flex flex-col">
                <span class="text-gray-600 dark:text-gray-400 text-sm">إجمالي الباحثين عن عمل</span>
                <span class="text-2xl font-bold">{{ number_format($totalJobSeekers) }}</span>
                <div class="flex items-center gap-1 text-sm mt-1 text-purple-600 dark:text-purple-400">
                    <i class="fas fa-users"></i>
                    <span>باحث عن عمل</span>
                </div>
            </div>

            <div class="flex flex-col">
                <span class="text-gray-600 dark:text-gray-400 text-sm">إجمالي الوظائف</span>
                <span class="text-2xl font-bold">{{ number_format($totalJobs) }}</span>
                <div class="flex items-center gap-1 text-sm mt-1 text-green-600 dark:text-green-400">
                    <i class="fas fa-briefcase"></i>
                    <span>{{ $jobsThisMonth }}</span>
                    <span class="text-gray-600 dark:text-gray-400 mr-1">هذا الشهر</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-bold">النشاط الأخير</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="w-full text-right text-sm">
                <thead class="bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-300">
                    <tr>
                        <th class="px-6 py-3">النشاط</th>
                        <th class="px-6 py-3">المستخدم</th>
                        <th class="px-6 py-3">النوع</th>
                        <th class="px-6 py-3">التاريخ</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse($recentJobs as $job)
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                        <td class="px-6 py-4">تم إنشاء وظيفة جديدة: {{ $job->title }}</td>
                        <td class="px-6 py-4">{{ $job->employer->user->name }}</td>
                        <td class="px-6 py-4">
                            <span class="px-2.5 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 rounded-full text-xs">وظيفة</span>
                        </td>
                        <td class="px-6 py-4">{{ $job->created_at->diffForHumans() }}</td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="4" class="px-6 py-4 text-center text-gray-500">لا توجد وظائف حديثة</td>
                    </tr>
                    @endforelse

                    @forelse($recentApplications as $application)
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                        <td class="px-6 py-4">تم تقديم طلب توظيف لوظيفة: {{ $application->job->title }}</td>
                        <td class="px-6 py-4">{{ $application->jobSeeker->user->name }}</td>
                        <td class="px-6 py-4">
                            <span class="px-2.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">طلب</span>
                        </td>
                        <td class="px-6 py-4">{{ $application->created_at->diffForHumans() }}</td>
                    </tr>
                    @empty
                    @if($recentJobs->isEmpty())
                    <tr>
                        <td colspan="4" class="px-6 py-4 text-center text-gray-500">لا توجد طلبات حديثة</td>
                    </tr>
                    @endif
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>
@endsection
