@extends('layouts.app')

@section('title', 'تفاصيل طلب التوظيف')

@section('content')
<div class="container mx-auto px-4 py-12">
    <div class="max-w-7xl mx-auto">
        <!-- Application Header -->
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <div>
                <h1 class="text-3xl font-bold">طلب التوظيف</h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">
                    <span>{{ $application->job->title }}</span> •
                    <span>تاريخ التقديم: {{ $application->created_at->format('Y/m/d') }}</span>
                </p>
            </div>
            <div class="mt-4 md:mt-0">
                <a href="{{ route('job-seeker.applications') }}" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors flex items-center gap-2">
                    <i class="fas fa-arrow-right"></i>
                    <span>العودة للطلبات</span>
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Application Details -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Application Status -->
                <div class="bg-white dark:bg-dark-card rounded-xl shadow-md p-6">
                    <h2 class="text-xl font-bold mb-4">حالة الطلب</h2>
                    <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                @if($application->status == 'pending')
                                    <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center ml-4">
                                        <i class="fas fa-clock text-yellow-600 dark:text-yellow-400 text-xl"></i>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-bold text-yellow-600 dark:text-yellow-400">قيد المراجعة</h3>
                                        <p class="text-gray-600 dark:text-gray-400">طلبك قيد المراجعة من قبل صاحب العمل</p>
                                    </div>
                                @elseif($application->status == 'reviewed')
                                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center ml-4">
                                        <i class="fas fa-check-circle text-blue-600 dark:text-blue-400 text-xl"></i>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-bold text-blue-600 dark:text-blue-400">تمت المراجعة</h3>
                                        <p class="text-gray-600 dark:text-gray-400">تمت مراجعة طلبك من قبل صاحب العمل</p>
                                    </div>
                                @elseif($application->status == 'shortlisted')
                                    <div class="w-12 h-12 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center ml-4">
                                        <i class="fas fa-list-alt text-indigo-600 dark:text-indigo-400 text-xl"></i>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-bold text-indigo-600 dark:text-indigo-400">القائمة المختصرة</h3>
                                        <p class="text-gray-600 dark:text-gray-400">تم اختيارك ضمن القائمة المختصرة للمرشحين</p>
                                    </div>
                                @elseif($application->status == 'rejected')
                                    <div class="w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center ml-4">
                                        <i class="fas fa-times-circle text-red-600 dark:text-red-400 text-xl"></i>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-bold text-red-600 dark:text-red-400">مرفوض</h3>
                                        <p class="text-gray-600 dark:text-gray-400">نأسف، تم رفض طلبك لهذه الوظيفة</p>
                                    </div>
                                @elseif($application->status == 'hired')
                                    <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center ml-4">
                                        <i class="fas fa-user-check text-green-600 dark:text-green-400 text-xl"></i>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-bold text-green-600 dark:text-green-400">تم التعيين</h3>
                                        <p class="text-gray-600 dark:text-gray-400">تهانينا! تم قبولك للعمل في هذه الوظيفة</p>
                                    </div>
                                @endif
                            </div>

                            <div>
                                @if($application->status == 'pending')
                                    <span class="px-3 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 rounded-full text-xs">
                                        قيد المراجعة
                                    </span>
                                @elseif($application->status == 'reviewed')
                                    <span class="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded-full text-xs">
                                        تمت المراجعة
                                    </span>
                                @elseif($application->status == 'shortlisted')
                                    <span class="px-3 py-1 bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-300 rounded-full text-xs">
                                        القائمة المختصرة
                                    </span>
                                @elseif($application->status == 'rejected')
                                    <span class="px-3 py-1 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 rounded-full text-xs">
                                        مرفوض
                                    </span>
                                @elseif($application->status == 'hired')
                                    <span class="px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 rounded-full text-xs">
                                        تم التعيين
                                    </span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Cover Letter -->
                <div class="bg-white dark:bg-dark-card rounded-xl shadow-md p-6">
                    <h2 class="text-xl font-bold mb-4">خطاب التقديم</h2>
                    <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <p class="text-gray-700 dark:text-gray-300 whitespace-pre-line">{{ $application->cover_letter }}</p>
                    </div>
                </div>

                <!-- Resume -->
                @if($application->resume)
                <div class="bg-white dark:bg-dark-card rounded-xl shadow-md p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-bold">السيرة الذاتية المرفقة</h2>
                        <a href="{{ asset('storage/' . $application->resume) }}" target="_blank" class="text-primary hover:underline text-sm flex items-center gap-1">
                            <i class="fas fa-download"></i>
                            <span>تحميل السيرة الذاتية</span>
                        </a>
                    </div>

                    <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg flex items-center justify-center">
                        <div class="text-center">
                            <i class="far fa-file-pdf text-5xl text-red-500 mb-2"></i>
                            <p class="text-gray-700 dark:text-gray-300">انقر على الرابط أعلاه لتحميل السيرة الذاتية</p>
                        </div>
                    </div>
                </div>
                @endif

                <!-- Employer Notes -->
                @if($application->employer_notes)
                <div class="bg-white dark:bg-dark-card rounded-xl shadow-md p-6">
                    <h2 class="text-xl font-bold mb-4">ملاحظات صاحب العمل</h2>
                    <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <p class="text-gray-700 dark:text-gray-300 whitespace-pre-line">{{ $application->employer_notes }}</p>
                    </div>
                </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Job Details -->
                <div class="bg-white dark:bg-dark-card rounded-xl shadow-md p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-bold">تفاصيل الوظيفة</h3>
                        <a href="{{ route('jobs.show', $application->job) }}" class="text-primary hover:underline text-sm flex items-center gap-1">
                            <i class="fas fa-external-link-alt"></i>
                            <span>عرض الوظيفة</span>
                        </a>
                    </div>

                    <div class="flex items-start gap-4 mb-4">
                        <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-briefcase text-primary"></i>
                        </div>
                        <div>
                            <h2 class="text-xl font-bold">{{ $application->job->title }}</h2>
                            <div class="flex flex-wrap items-center gap-3 mt-1 text-gray-600 dark:text-gray-400 text-sm">
                                @if($application->job->employer)
                                    <span class="flex items-center gap-1">
                                        <i class="fas fa-building"></i>
                                        {{ $application->job->employer->company_name }}
                                    </span>
                                @endif

                                <span class="flex items-center gap-1">
                                    <i class="fas fa-map-marker-alt"></i>
                                    {{ $application->job->location }}
                                </span>

                                <span class="flex items-center gap-1">
                                    <i class="fas fa-clock"></i>
                                    @if($application->job->job_type == 'full_time')
                                        دوام كامل
                                    @elseif($application->job->job_type == 'part_time')
                                        دوام جزئي
                                    @elseif($application->job->job_type == 'remote')
                                        عن بعد
                                    @elseif($application->job->job_type == 'freelance')
                                        عمل حر
                                    @endif
                                </span>
                            </div>
                        </div>
                    </div>

                    <ul class="space-y-3 mt-6">
                        <li class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">تاريخ النشر:</span>
                            <span class="font-medium">{{ $application->job->created_at->format('Y/m/d') }}</span>
                        </li>
                        <li class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">تاريخ الانتهاء:</span>
                            <span class="font-medium">{{ $application->job->expires_at->format('Y/m/d') }}</span>
                        </li>
                        @if($application->job->salary_range)
                        <li class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">نطاق الراتب:</span>
                            <span class="font-medium">{{ $application->job->salary_range }}</span>
                        </li>
                        @endif
                    </ul>
                </div>

                <!-- Company Details -->
                <div class="bg-white dark:bg-dark-card rounded-xl shadow-md p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-bold">معلومات الشركة</h3>
                        <a href="{{ route('employers.show', $application->job->employer) }}" class="text-primary hover:underline text-sm flex items-center gap-1">
                            <i class="fas fa-external-link-alt"></i>
                            <span>عرض الشركة</span>
                        </a>
                    </div>

                    <div class="flex items-center mb-4">
                        <div class="w-16 h-16 bg-primary/10 dark:bg-primary/5 rounded-full flex items-center justify-center ml-4">
                            @if($application->job->employer && $application->job->employer->company_logo)
                                <img src="{{ asset('storage/' . $application->job->employer->company_logo) }}" alt="{{ $application->job->employer->company_name ?? 'شركة' }}" class="w-14 h-14 rounded-full object-cover">
                            @else
                                <i class="fas fa-building text-2xl text-primary"></i>
                            @endif
                        </div>
                        <div>
                            <h3 class="text-xl font-bold">{{ $application->job->employer->company_name ?? 'غير محدد' }}</h3>
                            <p class="text-gray-600 dark:text-gray-400">{{ $application->job->employer->industry ?? 'غير محدد' }}</p>
                        </div>
                    </div>

                    <ul class="space-y-3 mt-4">
                        @if($application->job->employer && $application->job->employer->location)
                        <li class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">الموقع:</span>
                            <span class="font-medium">{{ $application->job->employer->location }}</span>
                        </li>
                        @endif

                        @if($application->job->employer->website)
                        <li class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">الموقع الإلكتروني:</span>
                            <a href="{{ $application->job->employer->website }}" target="_blank" class="font-medium text-primary hover:underline">{{ $application->job->employer->website }}</a>
                        </li>
                        @endif

                        @if($application->job->employer->company_size)
                        <li class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">حجم الشركة:</span>
                            <span class="font-medium">{{ $application->job->employer->company_size }} موظف</span>
                        </li>
                        @endif
                    </ul>
                </div>

                <!-- Actions -->
                <div class="bg-white dark:bg-dark-card rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-bold mb-4">الإجراءات</h3>
                    <div class="space-y-3">
                        <a href="{{ route('jobs.show', $application->job) }}" class="w-full py-2 px-4 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors flex items-center justify-center gap-2">
                            <i class="fas fa-eye"></i>
                            <span>عرض الوظيفة</span>
                        </a>

                        <a href="{{ route('employers.show', $application->job->employer) }}" class="w-full py-2 px-4 border border-primary text-primary rounded-lg hover:bg-primary hover:text-white transition-colors flex items-center justify-center gap-2">
                            <i class="fas fa-building"></i>
                            <span>عرض الشركة</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
