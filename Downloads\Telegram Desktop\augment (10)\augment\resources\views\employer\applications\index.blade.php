@extends('layouts.employer')

@section('title', 'طلبات التوظيف')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
    <!-- خلفية متحركة -->
    <div class="floating-shapes"></div>

    <div class="container mx-auto px-4 py-8 relative z-10">
        <!-- Header Section -->
        <div class="mb-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div class="space-y-2">
                    <div class="flex items-center gap-3">
                        <div class="icon-modern">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                                طلبات التوظيف
                            </h1>
                            <p class="text-gray-600 dark:text-gray-300 mt-1">
                                إدارة ومراجعة طلبات التوظيف المقدمة للوظائف
                            </p>
                        </div>
                    </div>
                </div>

                <div class="flex flex-wrap gap-3">
                    <a href="{{ route('employer.jobs.index') }}"
                       class="btn-modern bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700">
                        <i class="fas fa-briefcase"></i>
                        <span>الوظائف</span>
                    </a>
                    <button onclick="toggleFilters()"
                            class="btn-modern bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700">
                        <i class="fas fa-filter"></i>
                        <span>فلترة</span>
                    </button>
                    <button onclick="showExportModal()"
                            class="btn-modern bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700"
                            title="تصدير البيانات إلى ملف Excel">
                        <i class="fas fa-download"></i>
                        <span>تصدير</span>
                    </button>
                    <button onclick="refreshData()"
                            class="btn-modern bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700"
                            title="تحديث البيانات (Ctrl+R)">
                        <i class="fas fa-sync-alt"></i>
                        <span>تحديث</span>
                    </button>
                    <button onclick="printApplications()"
                            class="btn-modern bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700"
                            title="طباعة القائمة">
                        <i class="fas fa-print"></i>
                        <span>طباعة</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 fade-in">
            <!-- إجمالي الطلبات -->
            <div class="enhanced-card stat-card group">
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wider">
                                إجمالي الطلبات
                            </p>
                            <p class="text-3xl font-bold text-gray-900 dark:text-white mt-2" data-stat="total">
                                {{ $stats['total'] ?? 0 }}
                            </p>
                            <div class="flex items-center mt-2">
                                <span class="text-sm text-green-600 dark:text-green-400">
                                    <i class="fas fa-arrow-up text-xs"></i>
                                    +12% من الشهر الماضي
                                </span>
                            </div>
                        </div>
                        <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-file-alt text-white text-2xl"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- طلبات مقبولة -->
            <div class="enhanced-card stat-card group">
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wider">
                                طلبات مقبولة
                            </p>
                            <p class="text-3xl font-bold text-gray-900 dark:text-white mt-2" data-stat="accepted">
                                {{ $stats['accepted'] ?? 0 }}
                            </p>
                            <div class="flex items-center mt-2">
                                <span class="text-sm text-green-600 dark:text-green-400">
                                    <i class="fas fa-arrow-up text-xs"></i>
                                    +8% من الشهر الماضي
                                </span>
                            </div>
                        </div>
                        <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-check-circle text-white text-2xl"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قيد المراجعة -->
            <div class="enhanced-card stat-card group">
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wider">
                                قيد المراجعة
                            </p>
                            <p class="text-3xl font-bold text-gray-900 dark:text-white mt-2" data-stat="pending">
                                {{ $stats['pending'] ?? 0 }}
                            </p>
                            <div class="flex items-center mt-2">
                                <span class="text-sm text-yellow-600 dark:text-yellow-400">
                                    <i class="fas fa-clock text-xs"></i>
                                    يحتاج مراجعة
                                </span>
                            </div>
                        </div>
                        <div class="w-16 h-16 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-clock text-white text-2xl"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- طلبات مرفوضة -->
            <div class="enhanced-card stat-card group">
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wider">
                                طلبات مرفوضة
                            </p>
                            <p class="text-3xl font-bold text-gray-900 dark:text-white mt-2" data-stat="rejected">
                                {{ $stats['rejected'] ?? 0 }}
                            </p>
                            <div class="flex items-center mt-2">
                                <span class="text-sm text-red-600 dark:text-red-400">
                                    <i class="fas fa-arrow-down text-xs"></i>
                                    -5% من الشهر الماضي
                                </span>
                            </div>
                        </div>
                        <div class="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-times-circle text-white text-2xl"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Panel -->
        <div id="filterPanel" class="hidden mb-8">
            <div class="enhanced-card filter-panel">
                <div class="p-6">
                    <div class="flex items-center gap-3 mb-6">
                        <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-filter text-white text-sm"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">فلترة النتائج</h3>
                    </div>

                    <form method="GET" action="{{ route('employer.applications.index') }}" class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            <!-- البحث -->
                            <div class="space-y-2">
                                <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    البحث
                                </label>
                                <div class="relative">
                                    <input type="text"
                                           id="search"
                                           name="search"
                                           value="{{ request('search') }}"
                                           placeholder="اسم المتقدم أو البريد الإلكتروني..."
                                           class="modern-input pl-10">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-search text-gray-400"></i>
                                    </div>
                                </div>
                            </div>

                            <!-- الحالة -->
                            <div class="space-y-2">
                                <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    الحالة
                                </label>
                                <select id="status" name="status" class="modern-input">
                                    <option value="">جميع الحالات</option>
                                    <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>قيد المراجعة</option>
                                    <option value="reviewed" {{ request('status') == 'reviewed' ? 'selected' : '' }}>تمت المراجعة</option>
                                    <option value="accepted" {{ request('status') == 'accepted' ? 'selected' : '' }}>مقبول</option>
                                    <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>مرفوض</option>
                                </select>
                            </div>

                            <!-- الوظيفة -->
                            <div class="space-y-2">
                                <label for="job_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    الوظيفة
                                </label>
                                <select id="job_id" name="job_id" class="modern-input">
                                    <option value="">جميع الوظائف</option>
                                    @foreach($jobs ?? [] as $job)
                                        <option value="{{ $job->id }}" {{ request('job_id') == $job->id ? 'selected' : '' }}>
                                            {{ $job->title }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- أزرار التحكم -->
                            <div class="space-y-2">
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    الإجراءات
                                </label>
                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="btn-modern bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 flex-1">
                                        <i class="fas fa-search"></i>
                                        <span>بحث</span>
                                    </button>
                                    <a href="{{ route('employer.applications.index') }}"
                                       class="btn-modern bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 flex-1">
                                        <i class="fas fa-times"></i>
                                        <span>إعادة تعيين</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Applications Table -->
        <div class="enhanced-card slide-up">
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center gap-3">
                        <div class="w-8 h-8 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-list text-white text-sm"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">قائمة طلبات التوظيف</h3>
                    </div>

                    <div class="flex items-center gap-4">
                        <span class="text-sm text-gray-600 dark:text-gray-400">
                            {{ $applications->total() }} طلب
                        </span>
                        <span id="selectedCount" class="text-sm text-blue-600 dark:text-blue-400 hidden">
                            <i class="fas fa-check-square mr-1"></i>
                            <span id="selectedNumber">0</span> محدد
                        </span>

                        <!-- Quick Actions -->
                        <div class="flex items-center gap-2">
                            <button onclick="selectAll()"
                                    class="text-xs px-3 py-1 bg-blue-100 text-blue-600 rounded-lg hover:bg-blue-200 transition-colors">
                                تحديد الكل
                            </button>
                            <button onclick="bulkAction('accept')"
                                    class="text-xs px-3 py-1 bg-green-100 text-green-600 rounded-lg hover:bg-green-200 transition-colors hidden bulk-action-btn"
                                    disabled>
                                <i class="fas fa-check mr-1"></i>
                                قبول المحدد
                            </button>
                            <button onclick="bulkAction('reject')"
                                    class="text-xs px-3 py-1 bg-red-100 text-red-600 rounded-lg hover:bg-red-200 transition-colors hidden bulk-action-btn"
                                    disabled>
                                <i class="fas fa-times mr-1"></i>
                                رفض المحدد
                            </button>
                        </div>
                    </div>
                </div>

                @if($applications->count() > 0)
                    <div class="overflow-hidden rounded-xl border border-gray-200 dark:border-gray-700">
                        <div class="modern-table applications-table">
                            <table class="w-full custom-scrollbar">
                                <thead>
                                    <tr>
                                        <th class="px-6 py-4 text-right text-sm font-semibold text-white">
                                            <input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()"
                                                   class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                        </th>
                                        <th class="px-6 py-4 text-right text-sm font-semibold text-white">المتقدم</th>
                                        <th class="px-6 py-4 text-right text-sm font-semibold text-white">الوظيفة</th>
                                        <th class="px-6 py-4 text-right text-sm font-semibold text-white">تاريخ التقديم</th>
                                        <th class="px-6 py-4 text-right text-sm font-semibold text-white">الحالة</th>
                                        <th class="px-6 py-4 text-right text-sm font-semibold text-white">التقييم</th>
                                        <th class="px-6 py-4 text-right text-sm font-semibold text-white">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                                    @foreach($applications as $application)
                                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-200" data-application-id="{{ $application->id }}">
                                            <!-- Checkbox -->
                                            <td class="px-6 py-4">
                                                <input type="checkbox" class="application-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                                       value="{{ $application->id }}">
                                            </td>

                                            <!-- المتقدم -->
                                            <td class="px-6 py-4">
                                                <div class="flex items-center gap-4">
                                                    @if($application->jobSeeker && $application->jobSeeker->user)
                                                        <div class="relative avatar-container">
                                                            @if($application->jobSeeker->user->avatar)
                                                                <img src="{{ Storage::url($application->jobSeeker->user->avatar) }}"
                                                                     alt="{{ $application->jobSeeker->user->name }}"
                                                                     class="w-12 h-12 rounded-full object-cover border-2 border-white shadow-lg">
                                                            @else
                                                                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg">
                                                                    <span class="text-white font-bold text-lg">
                                                                        {{ substr($application->applicant_name, 0, 1) }}
                                                                    </span>
                                                                </div>
                                                            @endif
                                                            <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
                                                        </div>
                                                        <div class="flex-1 min-w-0">
                                                            <p class="text-sm font-semibold text-gray-900 dark:text-white truncate">
                                                                {{ $application->applicant_name }}
                                                            </p>
                                                            <p class="text-sm text-gray-500 dark:text-gray-400 truncate">
                                                                {{ $application->applicant_email }}
                                                            </p>
                                                        </div>
                                                    @else
                                                        <div class="relative">
                                                            <div class="w-12 h-12 bg-gradient-to-br from-gray-400 to-gray-500 rounded-full flex items-center justify-center shadow-lg">
                                                                <span class="text-white font-bold text-lg">؟</span>
                                                            </div>
                                                        </div>
                                                        <div class="flex-1 min-w-0">
                                                            <p class="text-sm font-semibold text-gray-500 dark:text-gray-400 truncate">
                                                                {{ $application->applicant_name }}
                                                            </p>
                                                            <p class="text-sm text-gray-400 dark:text-gray-500 truncate">
                                                                {{ $application->applicant_email }}
                                                            </p>
                                                        </div>
                                                    @endif
                                                </div>
                                            </td>

                                            <!-- الوظيفة -->
                                            <td class="px-6 py-4">
                                                <div class="space-y-1">
                                                    <p class="text-sm font-semibold text-gray-900 dark:text-white">
                                                        {{ $application->job->title }}
                                                    </p>
                                                    <p class="text-sm text-gray-500 dark:text-gray-400 flex items-center gap-1">
                                                        <i class="fas fa-map-marker-alt text-xs"></i>
                                                        {{ $application->job->location }}
                                                    </p>
                                                </div>
                                            </td>

                                            <!-- تاريخ التقديم -->
                                            <td class="px-6 py-4">
                                                <div class="space-y-1">
                                                    <p class="text-sm font-medium text-gray-900 dark:text-white">
                                                        {{ $application->created_at->format('Y/m/d') }}
                                                    </p>
                                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                                        {{ $application->created_at->format('H:i') }}
                                                    </p>
                                                </div>
                                            </td>

                                            <!-- الحالة -->
                                            <td class="px-6 py-4">
                                                @switch($application->status)
                                                    @case('pending')
                                                        <span class="status-badge pending inline-flex items-center px-3 py-1 rounded-full text-xs font-medium text-white">
                                                            <i class="fas fa-clock mr-1"></i>
                                                            قيد المراجعة
                                                        </span>
                                                        @break
                                                    @case('reviewed')
                                                        <span class="status-badge reviewed inline-flex items-center px-3 py-1 rounded-full text-xs font-medium text-white">
                                                            <i class="fas fa-eye mr-1"></i>
                                                            تمت المراجعة
                                                        </span>
                                                        @break
                                                    @case('accepted')
                                                        <span class="status-badge accepted inline-flex items-center px-3 py-1 rounded-full text-xs font-medium text-white">
                                                            <i class="fas fa-check mr-1"></i>
                                                            مقبول
                                                        </span>
                                                        @break
                                                    @case('rejected')
                                                        <span class="status-badge rejected inline-flex items-center px-3 py-1 rounded-full text-xs font-medium text-white">
                                                            <i class="fas fa-times mr-1"></i>
                                                            مرفوض
                                                        </span>
                                                        @break
                                                    @default
                                                        <span class="status-badge inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                                                            <i class="fas fa-question mr-1"></i>
                                                            غير محدد
                                                        </span>
                                                @endswitch
                                            </td>

                                            <!-- التقييم -->
                                            <td class="px-6 py-4">
                                                <div class="flex items-center gap-1">
                                                    @for($i = 1; $i <= 5; $i++)
                                                        <i class="fas fa-star text-xs {{ $i <= 4 ? 'text-yellow-400' : 'text-gray-300' }}"></i>
                                                    @endfor
                                                    <span class="text-xs text-gray-500 ml-1">(4.0)</span>
                                                </div>
                                            </td>

                                            <!-- الإجراءات -->
                                            <td class="px-6 py-4">
                                                <div class="flex items-center gap-2">
                                                    <a href="{{ route('employer.applications.show', $application) }}"
                                                       class="action-button inline-flex items-center justify-center w-8 h-8 rounded-lg bg-blue-100 text-blue-600 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:hover:bg-blue-800 focus-ring"
                                                       title="عرض التفاصيل">
                                                        <i class="fas fa-eye text-sm"></i>
                                                    </a>

                                                    @if($application->status == 'pending')
                                                        <button type="button"
                                                                onclick="updateStatus({{ $application->id }}, 'accepted')"
                                                                class="action-button inline-flex items-center justify-center w-8 h-8 rounded-lg bg-green-100 text-green-600 hover:bg-green-200 dark:bg-green-900 dark:text-green-300 dark:hover:bg-green-800 focus-ring"
                                                                title="قبول الطلب">
                                                            <i class="fas fa-check text-sm"></i>
                                                        </button>
                                                        <button type="button"
                                                                onclick="updateStatus({{ $application->id }}, 'rejected')"
                                                                class="action-button inline-flex items-center justify-center w-8 h-8 rounded-lg bg-red-100 text-red-600 hover:bg-red-200 dark:bg-red-900 dark:text-red-300 dark:hover:bg-red-800 focus-ring"
                                                                title="رفض الطلب">
                                                            <i class="fas fa-times text-sm"></i>
                                                        </button>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-6 flex justify-center">
                        {{ $applications->links() }}
                    </div>
                @else
                    <!-- Empty State -->
                    <div class="text-center py-16">
                        <div class="w-24 h-24 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-full flex items-center justify-center mx-auto mb-6">
                            <i class="fas fa-file-alt text-4xl text-gray-400 dark:text-gray-500"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                            لا توجد طلبات توظيف
                        </h3>
                        <p class="text-gray-600 dark:text-gray-400 mb-8 max-w-md mx-auto">
                            لم يتم تقديم أي طلبات للوظائف المنشورة بعد. ابدأ بنشر وظائف جديدة لجذب المرشحين المناسبين.
                        </p>
                        <a href="{{ route('employer.jobs.index') }}"
                           class="btn-modern bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700">
                            <i class="fas fa-briefcase"></i>
                            <span>عرض الوظائف</span>
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50 hidden">
    <div class="flex items-center justify-center h-full">
        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-2xl">
            <div class="flex items-center gap-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span class="text-gray-900 dark:text-white font-medium">جاري التحديث...</span>
            </div>
        </div>
    </div>
</div>

<!-- Export Modal -->
<div id="exportModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
    <div class="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-md w-full mx-4">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">تصدير طلبات التوظيف</h3>
            <button onclick="hideExportModal()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    اختر صيغة التصدير
                </label>
                <div class="space-y-2">
                    <label class="flex items-center">
                        <input type="radio" name="export_format" value="csv" checked class="text-green-600 focus:ring-green-500">
                        <span class="mr-2 text-gray-700 dark:text-gray-300">CSV (Excel) - جميع البيانات</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="export_format" value="filtered" class="text-green-600 focus:ring-green-500">
                        <span class="mr-2 text-gray-700 dark:text-gray-300">CSV مع الفلاتر الحالية</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="export_format" value="pdf" class="text-green-600 focus:ring-green-500">
                        <span class="mr-2 text-gray-700 dark:text-gray-300">PDF - تقرير مفصل</span>
                    </label>
                </div>
            </div>

            <div class="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                <div class="flex items-start gap-2">
                    <i class="fas fa-info-circle text-blue-500 mt-0.5"></i>
                    <div class="text-sm text-blue-700 dark:text-blue-300">
                        <p class="font-medium mb-1">سيتم تصدير:</p>
                        <ul class="text-xs space-y-1">
                            <li>• أسماء المتقدمين وبياناتهم</li>
                            <li>• تفاصيل الوظائف المتقدم إليها</li>
                            <li>• حالات الطلبات وتواريخ التقديم</li>
                            <li>• التقييمات والملاحظات</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="flex gap-3 pt-4">
                <button onclick="exportApplications()" class="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-download mr-2"></i>
                    تصدير
                </button>
                <button onclick="hideExportModal()" class="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    إلغاء
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Toggle filter panel
function toggleFilters() {
    const panel = document.getElementById('filterPanel');
    panel.classList.toggle('hidden');

    if (!panel.classList.contains('hidden')) {
        panel.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
}

// Show export modal
function showExportModal() {
    document.getElementById('exportModal').classList.remove('hidden');
}

// Hide export modal
function hideExportModal() {
    document.getElementById('exportModal').classList.add('hidden');
}

// Export data
function exportData(format) {
    const currentUrl = new URL(window.location);
    const exportUrl = new URL('{{ route("employer.applications.export") }}', window.location.origin);

    // Copy current filters to export URL
    currentUrl.searchParams.forEach((value, key) => {
        exportUrl.searchParams.append(key, value);
    });

    // Add format parameter
    exportUrl.searchParams.set('format', format);

    // Create temporary link and click it
    const link = document.createElement('a');
    link.href = exportUrl.toString();
    link.download = `applications_${new Date().toISOString().split('T')[0]}.${format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Hide modal
    hideExportModal();

    // Show success message
    showNotification('تم تصدير البيانات بنجاح', 'success');
}

// Refresh data
function refreshData() {
    showLoadingOverlay();
    setTimeout(() => {
        window.location.reload();
    }, 500);
}

// Show loading overlay
function showLoadingOverlay() {
    document.getElementById('loadingOverlay').classList.remove('hidden');
}

// Hide loading overlay
function hideLoadingOverlay() {
    document.getElementById('loadingOverlay').classList.add('hidden');
}

// Select all checkboxes
function selectAll() {
    const checkboxes = document.querySelectorAll('.application-checkbox');
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });

    if (selectAllCheckbox) {
        selectAllCheckbox.checked = true;
    }

    updateBulkActionButtons();
}

// Toggle select all
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const checkboxes = document.querySelectorAll('.application-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateBulkActionButtons();
}

// Update bulk action buttons visibility
function updateBulkActionButtons() {
    const selectedCheckboxes = document.querySelectorAll('.application-checkbox:checked');
    const bulkButtons = document.querySelectorAll('.bulk-action-btn');
    const selectedCount = document.getElementById('selectedCount');
    const selectedNumber = document.getElementById('selectedNumber');

    if (selectedCheckboxes.length > 0) {
        bulkButtons.forEach(button => {
            button.classList.remove('hidden');
            button.disabled = false;
        });

        // Show selected count
        if (selectedCount && selectedNumber) {
            selectedCount.classList.remove('hidden');
            selectedNumber.textContent = selectedCheckboxes.length;
        }
    } else {
        bulkButtons.forEach(button => {
            button.classList.add('hidden');
            button.disabled = true;
        });

        // Hide selected count
        if (selectedCount) {
            selectedCount.classList.add('hidden');
        }
    }
}

// Bulk action
function bulkAction(action) {
    const selectedCheckboxes = document.querySelectorAll('.application-checkbox:checked');
    const selectedIds = Array.from(selectedCheckboxes).map(cb => cb.value);

    if (selectedIds.length === 0) {
        showNotification('يرجى تحديد طلب واحد على الأقل', 'warning');
        return;
    }

    const actionText = action === 'accept' ? 'قبول' : 'رفض';
    const confirmMessage = `هل أنت متأكد من ${actionText} ${selectedIds.length} طلب؟`;

    if (confirm(confirmMessage)) {
        showLoadingOverlay();

        fetch('{{ route("employer.applications.bulk-update") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                application_ids: selectedIds,
                status: action === 'accept' ? 'accepted' : 'rejected'
            })
        })
        .then(response => response.json())
        .then(data => {
            hideLoadingOverlay();
            if (data.success) {
                showNotification(`تم ${actionText} ${selectedIds.length} طلب بنجاح`, 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification('حدث خطأ أثناء تحديث الطلبات', 'error');
            }
        })
        .catch(error => {
            hideLoadingOverlay();
            console.error('Error:', error);
            showNotification('حدث خطأ أثناء تحديث الطلبات', 'error');
        });
    }
}

// Update application status with enhanced UI
function updateStatus(applicationId, status) {
    const statusText = {
        'accepted': 'قبول',
        'rejected': 'رفض'
    };

    const confirmMessage = `هل أنت متأكد من ${statusText[status]} هذا الطلب؟`;

    if (confirm(confirmMessage)) {
        // Show loading overlay
        document.getElementById('loadingOverlay').classList.remove('hidden');

        fetch(`/employer/applications/${applicationId}/status`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ status: status })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success notification
                showNotification('تم تحديث حالة الطلب بنجاح', 'success');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showNotification('حدث خطأ أثناء تحديث الحالة', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('حدث خطأ أثناء تحديث الحالة', 'error');
        })
        .finally(() => {
            // Hide loading overlay
            document.getElementById('loadingOverlay').classList.add('hidden');
        });
    }
}

// Show notification function
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification-toast ${type}`;
    notification.innerHTML = `
        <div class="flex items-center gap-3">
            <div class="flex-shrink-0">
                ${type === 'success' ? '<i class="fas fa-check-circle text-green-600"></i>' :
                  type === 'error' ? '<i class="fas fa-exclamation-circle text-red-600"></i>' :
                  type === 'warning' ? '<i class="fas fa-exclamation-triangle text-yellow-600"></i>' :
                  '<i class="fas fa-info-circle text-blue-600"></i>'}
            </div>
            <div class="flex-1">
                <p class="text-sm font-medium text-gray-900 dark:text-white">${message}</p>
            </div>
            <button onclick="this.parentElement.parentElement.remove()" class="flex-shrink-0 hover:bg-gray-100 dark:hover:bg-gray-700 rounded p-1 transition-colors">
                <i class="fas fa-times text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"></i>
            </button>
        </div>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.style.animation = 'slideOutToRight 0.3s ease-in forwards';
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }
    }, 5000);
}

// Bulk actions
function selectAll() {
    const checkboxes = document.querySelectorAll('.application-checkbox');
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    selectAllCheckbox.checked = true;
}

function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const checkboxes = document.querySelectorAll('.application-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
}

function bulkAction(action) {
    const selectedIds = [];
    const checkboxes = document.querySelectorAll('.application-checkbox:checked');

    checkboxes.forEach(checkbox => {
        selectedIds.push(checkbox.value);
    });

    if (selectedIds.length === 0) {
        showNotification('يرجى تحديد طلب واحد على الأقل', 'warning');
        return;
    }

    const actionText = action === 'accept' ? 'قبول' : 'رفض';
    if (confirm(`هل أنت متأكد من ${actionText} ${selectedIds.length} طلب؟`)) {
        // Show loading
        document.getElementById('loadingOverlay').classList.remove('hidden');

        // Process each application
        Promise.all(selectedIds.map(id =>
            fetch(`/employer/applications/${id}/status`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ status: action === 'accept' ? 'accepted' : 'rejected' })
            })
        )).then(() => {
            showNotification(`تم ${actionText} ${selectedIds.length} طلب بنجاح`, 'success');
            setTimeout(() => location.reload(), 1000);
        }).catch(() => {
            showNotification('حدث خطأ أثناء معالجة الطلبات', 'error');
        }).finally(() => {
            document.getElementById('loadingOverlay').classList.add('hidden');
        });
    }
}

// Show export modal
function showExportModal() {
    document.getElementById('exportModal').classList.remove('hidden');
}

// Hide export modal
function hideExportModal() {
    document.getElementById('exportModal').classList.add('hidden');
}

// Export applications
function exportApplications() {
    const selectedFormat = document.querySelector('input[name="export_format"]:checked').value;
    const button = event.target.closest('button');
    const originalText = button.innerHTML;

    // إظهار رسالة تحميل
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>جاري التصدير...';
    button.disabled = true;

    showNotification('جاري تحضير ملف التصدير...', 'info');

    // بناء URL مع المعاملات
    let exportUrl = '/employer/applications/export';
    const urlParams = new URLSearchParams();

    // إضافة نوع التصدير
    if (selectedFormat === 'pdf') {
        urlParams.append('format', 'pdf');
    }

    if (selectedFormat === 'filtered' || selectedFormat === 'pdf') {
        // إضافة الفلاتر الحالية

        // فلتر الحالة
        const statusFilter = document.querySelector('select[name="status"]');
        if (statusFilter && statusFilter.value) {
            urlParams.append('status', statusFilter.value);
        }

        // فلتر الوظيفة
        const jobFilter = document.querySelector('select[name="job_id"]');
        if (jobFilter && jobFilter.value) {
            urlParams.append('job_id', jobFilter.value);
        }

        // البحث
        const searchInput = document.querySelector('input[name="search"]');
        if (searchInput && searchInput.value) {
            urlParams.append('search', searchInput.value);
        }
    }

    if (urlParams.toString()) {
        exportUrl += '?' + urlParams.toString();
    }

    // تحميل الملف
    window.location.href = exportUrl;

    // إعادة تعيين الزر وإخفاء modal
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
        hideExportModal();
        showNotification('تم تصدير الملف بنجاح!', 'success');
    }, 2000);
}

// Refresh data
function refreshData() {
    showNotification('جاري تحديث البيانات...', 'info');
    setTimeout(() => {
        location.reload();
    }, 500);
}

// Add smooth animations on page load
document.addEventListener('DOMContentLoaded', function() {
    // Animate cards on scroll
    const cards = document.querySelectorAll('.enhanced-card');
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animation = 'scaleIn 0.6s ease-out forwards';
            }
        });
    });

    cards.forEach(card => {
        observer.observe(card);
    });

    // Add real-time updates
    setInterval(updateStats, 30000); // Update every 30 seconds
});

// Real-time stats update
function updateStats() {
    fetch('/employer/applications/stats')
        .then(response => response.json())
        .then(data => {
            // Update stats cards with animation
            Object.keys(data).forEach(key => {
                const element = document.querySelector(`[data-stat="${key}"]`);
                if (element && element.textContent !== data[key].toString()) {
                    element.style.animation = 'pulse 0.5s ease-in-out';
                    element.textContent = data[key];
                    setTimeout(() => {
                        element.style.animation = '';
                    }, 500);
                }
            });
        })
        .catch(error => console.log('Stats update failed:', error));
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + F for filter toggle
    if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
        e.preventDefault();
        toggleFilters();
    }

    // Ctrl/Cmd + A for select all
    if ((e.ctrlKey || e.metaKey) && e.key === 'a' && e.target.tagName !== 'INPUT') {
        e.preventDefault();
        selectAll();
    }

    // Escape to close filter panel
    if (e.key === 'Escape') {
        const filterPanel = document.getElementById('filterPanel');
        if (!filterPanel.classList.contains('hidden')) {
            toggleFilters();
        }
    }
});

// Auto-save filter preferences
function saveFilterPreferences() {
    const filters = {
        search: document.getElementById('search').value,
        status: document.getElementById('status').value,
        job_id: document.getElementById('job_id').value
    };
    localStorage.setItem('applicationFilters', JSON.stringify(filters));
}

// Load filter preferences
function loadFilterPreferences() {
    const saved = localStorage.getItem('applicationFilters');
    if (saved) {
        const filters = JSON.parse(saved);
        if (document.getElementById('search')) document.getElementById('search').value = filters.search || '';
        if (document.getElementById('status')) document.getElementById('status').value = filters.status || '';
        if (document.getElementById('job_id')) document.getElementById('job_id').value = filters.job_id || '';
    }
}

// Print functionality
function printApplications() {
    window.print();
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    loadFilterPreferences();

    // Add event listeners to checkboxes
    document.querySelectorAll('.application-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActionButtons);
    });

    // Initialize bulk action buttons state
    updateBulkActionButtons();

    // Save filters on change
    ['search', 'status', 'job_id'].forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', saveFilterPreferences);
        }
    });

    // Add loading states to buttons
    document.querySelectorAll('.btn-modern, .action-button').forEach(button => {
        button.addEventListener('click', function() {
            if (!this.classList.contains('loading')) {
                this.classList.add('loading');
                const icon = this.querySelector('i');
                if (icon) {
                    icon.classList.add('loading-spinner');
                }

                setTimeout(() => {
                    this.classList.remove('loading');
                    if (icon) {
                        icon.classList.remove('loading-spinner');
                    }
                }, 1000);
            }
        });
    });

    // Add smooth scroll to top button
    const scrollButton = document.createElement('button');
    scrollButton.innerHTML = '<i class="fas fa-arrow-up"></i>';
    scrollButton.className = 'fixed bottom-6 left-6 w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 z-50 opacity-0 pointer-events-none';
    scrollButton.onclick = () => window.scrollTo({ top: 0, behavior: 'smooth' });
    document.body.appendChild(scrollButton);

    window.addEventListener('scroll', () => {
        if (window.scrollY > 300) {
            scrollButton.style.opacity = '1';
            scrollButton.style.pointerEvents = 'auto';
        } else {
            scrollButton.style.opacity = '0';
            scrollButton.style.pointerEvents = 'none';
        }
    });

    // إغلاق modal عند النقر خارجه
    document.getElementById('exportModal').addEventListener('click', function(e) {
        if (e.target === this) {
            hideExportModal();
        }
    });
});
</script>

@push('styles')
<style>
/* تحسينات modal التصدير */
#exportModal {
    backdrop-filter: blur(4px);
}

#exportModal .bg-white {
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسينات أزرار الراديو */
input[type="radio"] {
    width: 18px;
    height: 18px;
}

/* تحسينات الأزرار */
.btn-modern {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn-modern.loading {
    pointer-events: none;
    opacity: 0.7;
}

.btn-modern.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* تحسينات أزرار الإجراءات المجمعة */
.bulk-action-btn {
    animation: fadeIn 0.3s ease-out;
}

.bulk-action-btn.hidden {
    animation: fadeOut 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.8);
    }
}

/* تحسينات الجدول */
.applications-table {
    border-radius: 12px;
    overflow: hidden;
}

.applications-table thead {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.applications-table tbody tr:hover {
    background: linear-gradient(90deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
}

/* تحسينات أزرار الإجراءات */
.action-button {
    transition: all 0.2s ease;
    position: relative;
}

.action-button:hover {
    transform: scale(1.1);
}

.action-button:active {
    transform: scale(0.95);
}

/* تحسينات الإشعارات */
.notification-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    padding: 16px 20px;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    animation: slideInRight 0.3s ease-out;
}

.notification-toast.success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.notification-toast.error {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.notification-toast.warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* تحسينات البطاقات الإحصائية */
.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* تحسينات الفلاتر */
.filter-panel {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(135deg, #667eea, #764ba2) border-box;
}

.dark .filter-panel {
    background: linear-gradient(var(--dark-card), var(--dark-card)) padding-box,
                linear-gradient(135deg, #667eea, #764ba2) border-box;
}
</style>
@endpush

@endsection
