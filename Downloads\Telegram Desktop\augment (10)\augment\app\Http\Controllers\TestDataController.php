<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\JobSeeker;
use App\Models\Employer;
use App\Models\Job;
use App\Models\Application;
use App\Models\Category;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class TestDataController extends Controller
{
    public function showForm()
    {
        return view('test-data');
    }

    public function createTestData(Request $request)
    {
        try {
            $request->validate([
                'job_seekers_count' => 'required|integer|min:1|max:20',
                'employers_count' => 'required|integer|min:1|max:15',
                'jobs_per_employer' => 'required|integer|min:1|max:10',
                'applications_per_seeker' => 'required|integer|min:1|max:10',
            ]);

            // إنشاء مدير النظام
            if ($request->has('create_admin')) {
                User::firstOrCreate(
                    ['email' => '<EMAIL>'],
                    [
                        'name' => 'مدير النظام',
                        'password' => Hash::make('Pp123456'),
                        'role' => 'admin',
                    ]
                );
            }

            // إنشاء حساب اختبار للمستخدم الحالي
            if ($request->has('create_test_user')) {
                $testUser = User::firstOrCreate(
                    ['email' => '<EMAIL>'],
                    [
                        'name' => 'shahoda22',
                        'password' => Hash::make('Pp123456'),
                        'role' => 'job_seeker',
                    ]
                );

                JobSeeker::firstOrCreate(
                    ['user_id' => $testUser->id],
                    [
                        'skills' => 'PHP, Laravel, JavaScript, Vue.js, MySQL, HTML, CSS',
                        'experience' => 'مطور ويب بخبرة 3 سنوات في تطوير تطبيقات الويب باستخدام Laravel و Vue.js. خبرة في تطوير أنظمة إدارة المحتوى وتطبيقات التجارة الإلكترونية.',
                        'education' => 'بكالوريوس في علوم الحاسب - جامعة طرابلس',
                        'job_title' => 'مطور ويب متقدم',
                        'location' => 'طرابلس, ليبيا',
                        'is_available' => true,
                    ]
                );
            }

            // إنشاء التصنيفات
            $categories = [
                ['name' => 'تقنية المعلومات', 'slug' => 'information-technology', 'icon' => 'laptop-code'],
                ['name' => 'التصميم والإبداع', 'slug' => 'design-creativity', 'icon' => 'palette'],
                ['name' => 'التمويل والمحاسبة', 'slug' => 'finance-accounting', 'icon' => 'hand-holding-dollar'],
                ['name' => 'التسويق والمبيعات', 'slug' => 'marketing-sales', 'icon' => 'bullhorn'],
            ];

            foreach ($categories as $categoryData) {
                Category::firstOrCreate(
                    ['name' => $categoryData['name']],
                    $categoryData
                );
            }

            // إنشاء باحثين عن عمل
            $jobSeekerNames = [
                'أحمد محمد العلي', 'فاطمة عبدالله السعيد', 'محمد خالد الأحمد',
                'نورة سعد المطيري', 'عبدالرحمن علي القحطاني', 'سارة محمد الزهراني',
                'يوسف أحمد الغامدي', 'مريم عبدالعزيز الشهري', 'عبدالله محمد الحربي',
                'هند سالم القرني', 'خالد عبدالرحمن العتيبي', 'نوال أحمد الدوسري'
            ];

            $jobTitles = [
                'مطور ويب', 'مصمم جرافيك', 'محاسب مالي', 'مسوق رقمي',
                'مطور تطبيقات', 'محلل بيانات', 'مهندس شبكات', 'مصمم UI/UX',
                'مطور Frontend', 'مطور Backend', 'مدير مشاريع', 'محلل أنظمة'
            ];

            $skills = [
                'PHP, Laravel, MySQL, JavaScript',
                'Photoshop, Illustrator, InDesign, Figma',
                'Excel, QuickBooks, SAP, تحليل مالي',
                'Google Ads, Facebook Ads, SEO, تحليل البيانات',
                'React Native, Flutter, Swift, Kotlin',
                'Python, R, SQL, Power BI',
                'Cisco, Windows Server, Linux, VMware',
                'Figma, Adobe XD, Sketch, Principle',
                'React, Vue.js, Angular, TypeScript',
                'Node.js, Python, Java, MongoDB',
                'Agile, Scrum, JIRA, MS Project',
                'UML, تحليل الأنظمة, قواعد البيانات'
            ];

            for ($i = 0; $i < $request->job_seekers_count; $i++) {
                $user = User::create([
                    'name' => $jobSeekerNames[$i % count($jobSeekerNames)],
                    'email' => "jobseeker" . ($i + 1) . "@example.com",
                    'password' => Hash::make('password'),
                    'role' => 'job_seeker',
                ]);

                JobSeeker::create([
                    'user_id' => $user->id,
                    'skills' => $skills[$i % count($skills)],
                    'experience' => 'خبرة ' . rand(1, 8) . ' سنوات في المجال',
                    'education' => 'بكالوريوس في ' . ($i % 2 == 0 ? 'علوم الحاسب' : 'إدارة الأعمال'),
                    'job_title' => $jobTitles[$i % count($jobTitles)],
                    'location' => ['طرابلس', 'مصراتة', 'بنغازي', 'سبها', 'الزاوية', 'زليتن'][$i % 6] . ', ليبيا',
                    'is_available' => true,
                ]);
            }

            // إنشاء أصحاب عمل
            $companyNames = [
                'شركة التقنية المتقدمة', 'مؤسسة الإبداع الرقمي', 'شركة الحلول الذكية',
                'مجموعة النجاح التجارية', 'شركة الابتكار التقني', 'مؤسسة التطوير الحديث',
                'شركة الرؤية المستقبلية', 'مجموعة الخبرة المهنية'
            ];

            for ($i = 0; $i < $request->employers_count; $i++) {
                $user = User::create([
                    'name' => $companyNames[$i % count($companyNames)],
                    'email' => "employer" . ($i + 1) . "@example.com",
                    'password' => Hash::make('password'),
                    'role' => 'employer',
                ]);

                $employer = Employer::create([
                    'user_id' => $user->id,
                    'company_name' => $companyNames[$i % count($companyNames)],
                    'company_description' => 'شركة رائدة في مجال التقنية والحلول الرقمية',
                    'industry' => ['تقنية المعلومات', 'التصميم والإبداع', 'التمويل والمحاسبة'][$i % 3],
                    'website' => 'https://www.company' . ($i + 1) . '.ly',
                    'location' => ['طرابلس', 'مصراتة', 'بنغازي'][$i % 3] . ', ليبيا',
                    'company_size' => ['11-50', '51-200', '201-500'][$i % 3],
                    'founded_year' => rand(2005, 2020),
                ]);

                // إنشاء وظائف لكل صاحب عمل
                $jobData = [
                    ['title' => 'مطور PHP متقدم', 'category' => 'تقنية المعلومات'],
                    ['title' => 'مصمم جرافيك', 'category' => 'التصميم والإبداع'],
                    ['title' => 'محاسب مالي', 'category' => 'التمويل والمحاسبة'],
                    ['title' => 'مسوق رقمي', 'category' => 'التسويق والمبيعات'],
                    ['title' => 'مطور تطبيقات الجوال', 'category' => 'تقنية المعلومات'],
                ];

                for ($j = 0; $j < $request->jobs_per_employer; $j++) {
                    $jobInfo = $jobData[$j % count($jobData)];
                    $category = Category::where('name', $jobInfo['category'])->first();

                    Job::create([
                        'employer_id' => $employer->id,
                        'category_id' => $category->id,
                        'title' => $jobInfo['title'],
                        'description' => 'وصف تفصيلي للوظيفة ومتطلباتها',
                        'requirements' => 'المتطلبات والمهارات المطلوبة',
                        'salary_range' => rand(2000, 8000) . ' - ' . rand(8000, 15000) . ' دينار ليبي',
                        'location' => $employer->location,
                        'job_type' => ['full_time', 'part_time', 'remote'][$j % 3],
                        'posted_at' => Carbon::now()->subDays(rand(1, 30)),
                        'expires_at' => Carbon::now()->addDays(rand(30, 90)),
                        'is_active' => true,
                        'is_featured' => rand(0, 1) == 1,
                        'views' => rand(10, 500),
                    ]);
                }
            }

            // إنشاء طلبات التوظيف
            $jobs = Job::all();
            $jobSeekers = JobSeeker::all();

            foreach ($jobSeekers as $jobSeeker) {
                $applicationCount = min($request->applications_per_seeker, $jobs->count());
                $appliedJobs = $jobs->random($applicationCount);

                foreach ($appliedJobs as $job) {
                    Application::create([
                        'job_id' => $job->id,
                        'job_seeker_id' => $jobSeeker->id,
                        'cover_letter' => 'أتقدم بطلب للحصول على هذه الوظيفة لأنني أعتقد أن مهاراتي وخبرتي تتناسب مع متطلبات المنصب.',
                        'status' => ['pending', 'accepted', 'rejected'][rand(0, 2)],
                        'applied_at' => Carbon::now()->subDays(rand(1, 20)),
                    ]);
                }
            }

            return redirect()->back()->with('success', 'تم إنشاء البيانات التجريبية بنجاح! يمكنك الآن تسجيل الدخول واستكشاف النظام.');

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'حدث خطأ أثناء إنشاء البيانات: ' . $e->getMessage());
        }
    }
}
