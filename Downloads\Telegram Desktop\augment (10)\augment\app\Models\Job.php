<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Job extends Model
{
    use HasFactory;

    protected $fillable = [
        'employer_id',
        'title',
        'description',
        'requirements',
        'salary_range',
        'salary_min',
        'salary_max',
        'salary_currency',
        'location',
        'job_type',
        'posted_at',
        'expires_at',
        'views',
        'category_id',
        'is_active',
        'is_featured',
    ];

    protected $casts = [
        'posted_at' => 'datetime',
        'expires_at' => 'datetime',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'salary_min' => 'decimal:2',
        'salary_max' => 'decimal:2',
    ];

    /**
     * Get the employer that owns the job.
     */
    public function employer(): BelongsTo
    {
        return $this->belongsTo(Employer::class);
    }

    /**
     * Get the category that the job belongs to.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the applications for the job.
     */
    public function applications(): HasMany
    {
        return $this->hasMany(Application::class);
    }

    /**
     * Scope a query to only include active jobs.
     */
    public function scopeActive($query)
    {
        return $query->where('expires_at', '>', now());
    }

    /**
     * Scope a query to filter by job type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('job_type', $type);
    }

    /**
     * Scope a query to filter by location.
     */
    public function scopeInLocation($query, $location)
    {
        return $query->where('location', 'like', "%{$location}%");
    }

    /**
     * Get formatted salary range
     */
    public function getSalaryRangeAttribute()
    {
        // التحقق من وجود الحقول الجديدة
        if (isset($this->attributes['salary_min']) || isset($this->attributes['salary_max'])) {
            $currency = $this->attributes['salary_currency'] ?? 'LYD';
            $currencySymbol = getCurrencySymbol($currency);

            if ($this->attributes['salary_min'] && $this->attributes['salary_max']) {
                return number_format($this->attributes['salary_min']) . ' - ' . number_format($this->attributes['salary_max']) . ' ' . $currencySymbol;
            } elseif ($this->attributes['salary_min']) {
                return 'من ' . number_format($this->attributes['salary_min']) . ' ' . $currencySymbol;
            } elseif ($this->attributes['salary_max']) {
                return 'حتى ' . number_format($this->attributes['salary_max']) . ' ' . $currencySymbol;
            }
        }

        // العودة للحقل القديم إذا كان موجوداً
        if (isset($this->attributes['salary_range']) && !empty($this->attributes['salary_range'])) {
            return $this->attributes['salary_range'];
        }

        return 'غير محدد';
    }

    /**
     * Get formatted salary range with full currency name
     */
    public function getSalaryRangeWithCurrencyAttribute()
    {
        // التحقق من وجود الحقول الجديدة
        if (isset($this->attributes['salary_min']) || isset($this->attributes['salary_max'])) {
            $currency = $this->attributes['salary_currency'] ?? 'LYD';
            $currencyName = getCurrencyNameInArabic($currency);

            if ($this->attributes['salary_min'] && $this->attributes['salary_max']) {
                return number_format($this->attributes['salary_min']) . ' - ' . number_format($this->attributes['salary_max']) . ' ' . $currencyName;
            } elseif ($this->attributes['salary_min']) {
                return 'من ' . number_format($this->attributes['salary_min']) . ' ' . $currencyName;
            } elseif ($this->attributes['salary_max']) {
                return 'حتى ' . number_format($this->attributes['salary_max']) . ' ' . $currencyName;
            }
        }

        // العودة للحقل القديم إذا كان موجوداً
        if (isset($this->attributes['salary_range']) && !empty($this->attributes['salary_range'])) {
            return $this->attributes['salary_range'];
        }

        return 'غير محدد';
    }
}