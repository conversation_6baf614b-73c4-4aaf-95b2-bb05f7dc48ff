// تحسينات JavaScript للوحة الإدارة

document.addEventListener("DOMContentLoaded", function () {
    // تأثيرات الحركة عند التمرير
    const observerOptions = {
        threshold: 0.1,
        rootMargin: "0px 0px -50px 0px",
    };

    const observer = new IntersectionObserver(function (entries) {
        entries.forEach((entry) => {
            if (entry.isIntersecting) {
                entry.target.classList.add("animate-fade-in");
                entry.target.style.opacity = "1";
                entry.target.style.transform = "translateY(0)";
            }
        });
    }, observerOptions);

    // مراقبة العناصر للحركة
    document
        .querySelectorAll(".stat-card, .glass-card, .enhanced-card")
        .forEach((el) => {
            el.style.opacity = "0";
            el.style.transform = "translateY(20px)";
            el.style.transition = "all 0.6s ease-out";
            observer.observe(el);
        });

    // تأثيرات الهوفر المحسنة للبطاقات
    document.querySelectorAll(".stat-card").forEach((card) => {
        card.addEventListener("mouseenter", function () {
            this.style.transform = "translateY(-8px) scale(1.02)";
            this.style.boxShadow = "0 20px 40px rgba(0, 0, 0, 0.15)";
        });

        card.addEventListener("mouseleave", function () {
            this.style.transform = "translateY(0) scale(1)";
            this.style.boxShadow = "0 8px 32px rgba(0, 0, 0, 0.1)";
        });
    });

    // تحسين تأثيرات الأزرار
    document.querySelectorAll(".btn-gradient, .btn-modern").forEach((btn) => {
        btn.addEventListener("click", function (e) {
            // تأثير الموجة عند النقر
            const ripple = document.createElement("span");
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + "px";
            ripple.style.left = x + "px";
            ripple.style.top = y + "px";
            ripple.classList.add("ripple");

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // تحسين الشريط الجانبي
    const sidebarItems = document.querySelectorAll(".nav-item-hover");
    sidebarItems.forEach((item, index) => {
        item.style.animationDelay = `${index * 0.1}s`;

        item.addEventListener("mouseenter", function () {
            this.style.transform = "translateX(8px) scale(1.02)";
            this.style.background =
                "linear-gradient(135deg, rgba(14, 165, 233, 0.05) 0%, rgba(217, 70, 239, 0.05) 100%)";
        });

        item.addEventListener("mouseleave", function () {
            this.style.transform = "translateX(0) scale(1)";
            this.style.background = "transparent";
        });
    });

    // تحميل تفضيل الوضع المظلم عند تحميل الصفحة
    // الوضع الفاتح هو الافتراضي
    const savedDarkMode = localStorage.getItem("darkMode");
    if (savedDarkMode === "true") {
        document.documentElement.classList.add("dark");
    } else {
        document.documentElement.classList.remove("dark");
    }

    // إضافة تأثيرات للوضع المظلم
    const darkModeToggle = document.getElementById("darkModeToggle");
    if (darkModeToggle) {
        // تأثير إضافي عند النقر
        darkModeToggle.addEventListener("mouseenter", function () {
            this.style.transform = "scale(1.1)";
        });

        darkModeToggle.addEventListener("mouseleave", function () {
            this.style.transform = "scale(1)";
        });
    }

    // تحسين الإشعارات
    function showNotification(message, type = "info", duration = 5000) {
        const notification = document.createElement("div");
        notification.className = `notification-modern ${type} fixed top-4 right-4 z-50 max-w-sm`;
        notification.innerHTML = `
            <div class="flex items-center gap-3">
                <div class="flex-shrink-0">
                    <i class="fas fa-${getNotificationIcon(type)} text-lg"></i>
                </div>
                <div class="flex-1">
                    <p class="font-medium">${message}</p>
                </div>
                <button class="flex-shrink-0 text-gray-400 hover:text-gray-600" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        document.body.appendChild(notification);

        // إزالة الإشعار تلقائياً
        setTimeout(() => {
            notification.style.opacity = "0";
            notification.style.transform = "translateX(100%)";
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, duration);
    }

    function getNotificationIcon(type) {
        const icons = {
            success: "check-circle",
            warning: "exclamation-triangle",
            error: "times-circle",
            info: "info-circle",
        };
        return icons[type] || icons["info"];
    }

    // تحسين الجداول
    document.querySelectorAll(".modern-table tr").forEach((row) => {
        row.addEventListener("mouseenter", function () {
            this.style.transform = "scale(1.01)";
            this.style.boxShadow = "0 4px 12px rgba(0, 0, 0, 0.1)";
        });

        row.addEventListener("mouseleave", function () {
            this.style.transform = "scale(1)";
            this.style.boxShadow = "none";
        });
    });

    // تحسين النماذج
    document.querySelectorAll(".modern-input").forEach((input) => {
        input.addEventListener("focus", function () {
            this.parentElement.classList.add("focused");
        });

        input.addEventListener("blur", function () {
            this.parentElement.classList.remove("focused");
        });
    });

    // تأثيرات التحميل
    function showLoadingState(element) {
        element.classList.add("loading-shimmer");
        element.style.pointerEvents = "none";
    }

    function hideLoadingState(element) {
        element.classList.remove("loading-shimmer");
        element.style.pointerEvents = "auto";
    }

    // تحسين الرسوم البيانية
    function animateChart(chartElement) {
        const bars = chartElement.querySelectorAll('[class*="gradient-"]');
        bars.forEach((bar, index) => {
            bar.style.transform = "scaleY(0)";
            bar.style.transformOrigin = "bottom";
            bar.style.transition = `transform 0.6s ease ${index * 0.1}s`;

            setTimeout(() => {
                bar.style.transform = "scaleY(1)";
            }, 100);
        });
    }

    // تطبيق تأثيرات الرسوم البيانية
    document.querySelectorAll(".chart-container").forEach((chart) => {
        observer.observe(chart);
        chart.addEventListener("animationstart", () => animateChart(chart));
    });

    // تحسين الأيقونات
    document.querySelectorAll(".icon-modern").forEach((icon) => {
        icon.addEventListener("mouseenter", function () {
            this.style.transform = "scale(1.1) rotate(5deg)";
        });

        icon.addEventListener("mouseleave", function () {
            this.style.transform = "scale(1) rotate(0deg)";
        });
    });

    // تحسين الشاشات الصغيرة
    function handleMobileInteractions() {
        if (window.innerWidth <= 768) {
            // تقليل تأثيرات الحركة على الشاشات الصغيرة
            document.querySelectorAll(".stat-card").forEach((card) => {
                card.addEventListener("touchstart", function () {
                    this.style.transform = "scale(0.98)";
                });

                card.addEventListener("touchend", function () {
                    this.style.transform = "scale(1)";
                });
            });
        }
    }

    // تطبيق تحسينات الشاشات الصغيرة
    handleMobileInteractions();
    window.addEventListener("resize", handleMobileInteractions);

    // تحسين الأداء
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // تحسين أحداث التمرير
    const handleScroll = debounce(() => {
        const scrolled = window.pageYOffset;
        const header = document.querySelector("header");

        if (header) {
            if (scrolled > 100) {
                header.style.backdropFilter = "blur(20px)";
                header.style.background = "rgba(255, 255, 255, 0.95)";
            } else {
                header.style.backdropFilter = "blur(10px)";
                header.style.background = "rgba(255, 255, 255, 0.9)";
            }
        }
    }, 10);

    window.addEventListener("scroll", handleScroll);

    // إضافة الأنماط المطلوبة للتأثيرات
    const style = document.createElement("style");
    style.textContent = `
        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }

        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        .focused {
            transform: scale(1.02);
        }
    `;
    document.head.appendChild(style);

    // إظهار رسالة ترحيب للأدمن الجديد
    const isFirstVisit = !localStorage.getItem("hasVisited");
    if (isFirstVisit) {
        setTimeout(() => {
            showNotification(
                "مرحباً بك في لوحة تحكم الأدمن! إدارة شاملة للنظام 👨‍💼✨",
                "success",
                8000
            );
            localStorage.setItem("hasVisited", "true");
        }, 1000);
    }
});

// تصدير الدوال للاستخدام العام
window.AdminEnhancements = {
    showNotification: function (message, type, duration) {
        // يمكن استدعاؤها من أي مكان في التطبيق
        showNotification(message, type, duration);
    },
};
