<!-- CTA Section -->
<section class="py-20 gradient-bg relative overflow-hidden">
    <!-- Background Elements -->
    <div class="absolute inset-0">
        <div class="absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full blur-xl animate-pulse"></div>
        <div class="absolute bottom-10 right-10 w-48 h-48 bg-white/5 rounded-full blur-2xl animate-pulse" style="animation-delay: 1s"></div>
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-white/5 rounded-full blur-3xl animate-pulse" style="animation-delay: 2s"></div>
    </div>

    <div class="relative p-6">
        <div class="max-w-4xl mx-auto text-center">
            <div class="mb-8">
                <h2 class="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
                    ابدأ رحلتك المهنية الآن
                </h2>
                <p class="text-xl md:text-2xl text-white/90 max-w-3xl mx-auto leading-relaxed">
                    انضم إلى الآلاف من الباحثين عن عمل وأصحاب العمل في منصة Hire Me واغتنم الفرصة للنمو والتطور
                </p>
            </div>

            <!-- Statistics -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
                <div class="text-center">
                    <div class="text-3xl md:text-4xl font-bold text-white mb-2">{{ \App\Models\Job::count() }}+</div>
                    <div class="text-white/80">وظيفة متاحة</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl md:text-4xl font-bold text-white mb-2">{{ \App\Models\Employer::count() }}+</div>
                    <div class="text-white/80">شركة مسجلة</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl md:text-4xl font-bold text-white mb-2">{{ \App\Models\User::where('role', 'job_seeker')->count() }}+</div>
                    <div class="text-white/80">باحث عن عمل</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl md:text-4xl font-bold text-white mb-2">{{ \App\Models\Application::count() }}+</div>
                    <div class="text-white/80">طلب توظيف</div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                @guest
                <a href="{{ route('register') }}" class="px-8 py-4 bg-white text-primary rounded-xl hover:bg-gray-100 hover:scale-105 transition-all duration-300 text-lg font-semibold shadow-xl">
                    <i class="fas fa-rocket mr-2"></i>
                    إنشاء حساب مجاني
                </a>
                @else
                @if(auth()->user()->isJobSeeker())
                <a href="{{ route('job-seeker.applications') }}" class="px-8 py-4 bg-white text-primary rounded-xl hover:bg-gray-100 hover:scale-105 transition-all duration-300 text-lg font-semibold shadow-xl">
                    <i class="fas fa-file-alt mr-2"></i>
                    طلباتي
                </a>
                @elseif(auth()->user()->isEmployer())
                <a href="{{ route('employer.jobs.create') }}" class="px-8 py-4 bg-white text-primary rounded-xl hover:bg-gray-100 hover:scale-105 transition-all duration-300 text-lg font-semibold shadow-xl">
                    <i class="fas fa-plus mr-2"></i>
                    نشر وظيفة جديدة
                </a>
                @endif
                @endguest
                <a href="{{ route('jobs.index') }}" class="px-8 py-4 bg-white/20 backdrop-blur-sm border-2 border-white text-white rounded-xl hover:bg-white/30 hover:scale-105 transition-all duration-300 text-lg font-semibold">
                    <i class="fas fa-search mr-2"></i>
                    استكشاف الوظائف
                </a>
            </div>
        </div>
    </div>
</section>