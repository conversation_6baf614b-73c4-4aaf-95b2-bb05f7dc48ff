<?php

namespace App\Http\Controllers;

use App\Models\Application;
use App\Models\Job;
use App\Models\User;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ReportController extends Controller
{
    public function __construct()
    {
        // تطبيق middleware للتحقق من الصلاحيات
        $this->middleware(['auth', 'role:admin'])->only([
            'index', 'jobs', 'applications', 'financial'
        ]);

        $this->middleware(['permission:view reports'])->only([
            'index', 'jobs', 'applications', 'financial'
        ]);

        // تطبيق middleware للتحقق من صلاحيات صاحب العمل
        $this->middleware(['auth', 'role:employer'])->only([
            'jobStatistics', 'applicantAnalytics', 'exportApplications'
        ]);
    }

    /**
     * عرض صفحة التقارير الرئيسية للمدير
     */
    public function index()
    {
        // إحصائيات عامة للصفحة الرئيسية
        $totalJobs = Job::count();
        $activeJobs = Job::where('is_active', true)->count();
        $totalApplications = Application::count();
        $totalUsers = User::count();
        $totalEmployers = User::where('role', 'employer')->count();
        $totalJobSeekers = User::where('role', 'job_seeker')->count();

        // إحصائيات هذا الشهر
        $thisMonth = Carbon::now()->startOfMonth();
        $jobsThisMonth = Job::where('created_at', '>=', $thisMonth)->count();
        $applicationsThisMonth = Application::where('created_at', '>=', $thisMonth)->count();
        $usersThisMonth = User::where('created_at', '>=', $thisMonth)->count();

        // أحدث الوظائف
        $recentJobs = Job::with('employer.user')
            ->latest()
            ->take(5)
            ->get();

        // أحدث الطلبات
        $recentApplications = Application::with(['job', 'jobSeeker.user'])
            ->latest()
            ->take(5)
            ->get();

        return view('admin.reports.index', compact(
            'totalJobs',
            'activeJobs',
            'totalApplications',
            'totalUsers',
            'totalEmployers',
            'totalJobSeekers',
            'jobsThisMonth',
            'applicationsThisMonth',
            'usersThisMonth',
            'recentJobs',
            'recentApplications'
        ));
    }

    /**
     * عرض تقارير الوظائف للمدير
     */
    public function jobs(Request $request)
    {
        // استخراج معايير التصفية من الطلب
        $filters = (object) [
            'start_date' => $request->input('start_date', Carbon::now()->subMonths(6)->format('Y-m-d')),
            'end_date' => $request->input('end_date', Carbon::now()->format('Y-m-d')),
            'department' => $request->input('department'),
            'status' => $request->input('status'),
        ];

        // استخراج إحصائيات الوظائف
        $stats = $this->getJobStats($filters);

        // استخراج بيانات أداء الوظائف
        $job_performance = $this->getJobPerformance($filters);

        // بيانات الرسوم البيانية
        $chartData = $this->getJobsChartData($filters);

        return view('admin.reports.jobs', compact('filters', 'stats', 'job_performance', 'chartData'));
    }

    /**
     * عرض تقارير طلبات التوظيف للمدير
     */
    public function applications(Request $request)
    {
        // استخراج معايير التصفية من الطلب
        $filters = (object) [
            'start_date' => $request->input('start_date', Carbon::now()->subMonths(6)->format('Y-m-d')),
            'end_date' => $request->input('end_date', Carbon::now()->format('Y-m-d')),
            'status' => $request->input('status'),
        ];

        // استخراج إحصائيات طلبات التوظيف
        $stats = $this->getApplicationStats($filters);

        return view('admin.reports.applications', compact('filters', 'stats'));
    }

    /**
     * عرض التقارير المالية للمدير
     */
    public function financial(Request $request)
    {
        // استخراج معايير التصفية من الطلب
        $filters = (object) [
            'start_date' => $request->input('start_date', Carbon::now()->subMonths(6)->format('Y-m-d')),
            'end_date' => $request->input('end_date', Carbon::now()->format('Y-m-d')),
            'payment_type' => $request->input('payment_type'),
        ];

        // استخراج إحصائيات المدفوعات
        $stats = $this->getFinancialStats($filters);

        return view('admin.reports.financial', compact('filters', 'stats'));
    }

    /**
     * عرض لوحة التقارير لصاحب العمل
     */
    public function employerDashboard()
    {
        $user = Auth::user();
        $employer = $user->employer;

        // إنشاء ملف صاحب عمل إذا لم يكن موجوداً
        if (!$employer) {
            if ($user->role === 'employer') {
                $employer = \App\Models\Employer::create([
                    'user_id' => $user->id,
                    'company_name' => $user->company_name ?? 'شركة ' . $user->name,
                    'company_description' => $user->company_description ?? 'وصف الشركة',
                    'industry' => $user->industry ?? 'تقنية المعلومات',
                    'location' => $user->location ?? 'طرابلس',
                    'company_size' => $user->company_size ?? '1-10',
                ]);
            } else {
                return redirect()->route('home')->with('error', 'ليس لديك صلاحية للوصول إلى هذه الصفحة');
            }
        }

        $totalJobs = $employer->jobs()->count();
        $activeJobs = $employer->jobs()->where('is_active', true)->count();
        $totalApplications = Application::whereIn('job_id', $employer->jobs()->pluck('id'))->count();
        $jobViews = $employer->jobs()->sum('views') ?? 0;

        return view('reports.dashboard', compact(
            'totalJobs',
            'activeJobs',
            'totalApplications',
            'jobViews'
        ));
    }

    /**
     * عرض إحصائيات الوظائف لصاحب العمل
     */
    public function jobStatistics()
    {
        $user = Auth::user();
        $employer = $user->employer;

        // إنشاء ملف صاحب عمل إذا لم يكن موجوداً
        if (!$employer) {
            if ($user->role === 'employer') {
                $employer = \App\Models\Employer::create([
                    'user_id' => $user->id,
                    'company_name' => $user->company_name ?? 'شركة ' . $user->name,
                    'company_description' => $user->company_description ?? 'وصف الشركة',
                    'industry' => $user->industry ?? 'تقنية المعلومات',
                    'location' => $user->location ?? 'طرابلس',
                    'company_size' => $user->company_size ?? '1-10',
                ]);
            } else {
                return redirect()->route('home')->with('error', 'ليس لديك صلاحية للوصول إلى هذه الصفحة');
            }
        }

        $totalJobs = $employer->jobs()->count();
        $activeJobs = $employer->jobs()->where('is_active', true)->count();
        $totalApplications = Application::whereIn('job_id', $employer->jobs()->pluck('id'))->count();

        $jobViews = $employer->jobs()->sum('views') ?? 0;

        $applicationsByStatus = Application::whereIn('job_id', $employer->jobs()->pluck('id'))
                                         ->selectRaw('status, count(*) as count')
                                         ->groupBy('status')
                                         ->get();

        $recentJobs = $employer->jobs()
                              ->withCount('applications')
                              ->latest()
                              ->take(10)
                              ->get();

        return view('reports.job-statistics', compact(
            'totalJobs',
            'activeJobs',
            'totalApplications',
            'jobViews',
            'applicationsByStatus',
            'recentJobs'
        ));
    }

    /**
     * تصدير إحصائيات الوظائف لصاحب العمل
     */
    public function exportJobStatistics(Request $request)
    {
        $employer = Auth::user()->employer;

        if (!$employer) {
            return redirect()->route('employer.profile')->with('error', 'يجب إكمال ملف الشركة أولاً');
        }

        $format = $request->input('format', 'csv');

        $totalJobs = $employer->jobs()->count();
        $activeJobs = $employer->jobs()->where('is_active', true)->count();
        $totalApplications = Application::whereIn('job_id', $employer->jobs()->pluck('id'))->count();
        $jobViews = $employer->jobs()->sum('views') ?? 0;

        $applicationsByStatus = Application::whereIn('job_id', $employer->jobs()->pluck('id'))
                                         ->selectRaw('status, count(*) as count')
                                         ->groupBy('status')
                                         ->get();

        $recentJobs = $employer->jobs()
                              ->withCount('applications')
                              ->latest()
                              ->take(10)
                              ->get();

        if ($format === 'pdf') {
            return $this->exportJobStatisticsPDF($employer, compact(
                'totalJobs', 'activeJobs', 'totalApplications', 'jobViews',
                'applicationsByStatus', 'recentJobs'
            ));
        }

        return $this->exportJobStatisticsCSV($employer, compact(
            'totalJobs', 'activeJobs', 'totalApplications', 'jobViews',
            'applicationsByStatus', 'recentJobs'
        ));
    }

    /**
     * تصدير إحصائيات الوظائف بصيغة CSV
     */
    private function exportJobStatisticsCSV($employer, $data)
    {
        $filename = 'job_statistics_' . date('Y-m-d_H-i-s') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($data, $employer) {
            $file = fopen('php://output', 'w');

            // إضافة BOM للدعم العربي
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            // معلومات الشركة
            fputcsv($file, ['تقرير إحصائيات الوظائف']);
            fputcsv($file, ['اسم الشركة', $employer->company_name ?? 'غير محدد']);
            fputcsv($file, ['تاريخ التقرير', date('Y-m-d H:i:s')]);
            fputcsv($file, []); // سطر فارغ

            // الإحصائيات العامة
            fputcsv($file, ['الإحصائيات العامة']);
            fputcsv($file, ['إجمالي الوظائف', $data['totalJobs']]);
            fputcsv($file, ['الوظائف النشطة', $data['activeJobs']]);
            fputcsv($file, ['إجمالي الطلبات', $data['totalApplications']]);
            fputcsv($file, ['مشاهدات الوظائف', $data['jobViews']]);
            fputcsv($file, []); // سطر فارغ

            // الطلبات حسب الحالة
            fputcsv($file, ['الطلبات حسب الحالة']);
            fputcsv($file, ['الحالة', 'العدد']);
            foreach ($data['applicationsByStatus'] as $status) {
                $statusName = \App\Models\Application::getStatusOptions()[$status->status] ?? $status->status;
                fputcsv($file, [$statusName, $status->count]);
            }
            fputcsv($file, []); // سطر فارغ

            // الوظائف الحديثة
            fputcsv($file, ['الوظائف الحديثة']);
            fputcsv($file, ['عنوان الوظيفة', 'عدد الطلبات', 'تاريخ النشر', 'الحالة']);
            foreach ($data['recentJobs'] as $job) {
                fputcsv($file, [
                    $job->title,
                    $job->applications_count ?? 0,
                    $job->created_at->format('Y-m-d'),
                    $job->is_active ? 'نشطة' : 'غير نشطة'
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * تصدير إحصائيات الوظائف بصيغة PDF
     */
    private function exportJobStatisticsPDF($employer, $data)
    {
        $html = view('reports.pdf.job-statistics', compact('employer', 'data'))->render();

        return response($html, 200, [
            'Content-Type' => 'text/html; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="job_statistics_' . date('Y-m-d_H-i-s') . '.html"'
        ]);
    }

    /**
     * تصدير بيانات المتقدمين للوظيفة
     */
    public function exportApplications(Request $request, Job $job)
    {
        // التحقق من أن الوظيفة تنتمي لصاحب العمل الحالي
        $employer = Auth::user()->employer;

        if (!$employer || $job->employer_id !== $employer->id) {
            abort(403, 'غير مصرح لك بتصدير هذه البيانات');
        }
        if ($job->employer_id !== $employer->id) {
            abort(403, 'غير مصرح لك بالوصول إلى هذه الوظيفة');
        }

        // تصدير البيانات (تم تعطيل التصدير مؤقتًا حتى يتم تثبيت المكتبات اللازمة)
        $applications = $job->applications()->with('jobSeeker.user')->get();

        return response()->json([
            'message' => 'تم تحميل البيانات بنجاح',
            'applications' => $applications
        ]);

        // يمكن تفعيل هذا الكود بعد تثبيت المكتبات اللازمة
        /*
        if ($request->format === 'pdf') {
            $applications = $job->applications()->with('jobSeeker.user')->get();
            $pdf = PDF::loadView('reports.applications-pdf', compact('job', 'applications'));
            return $pdf->download('applications-' . $job->id . '.pdf');
        } else {
            return Excel::download(new ApplicationsExport($job), 'applications-' . $job->id . '.xlsx');
        }
        */
    }

    /**
     * عرض إحصائيات المتقدمين للوظائف لصاحب العمل
     */
    public function applicantAnalytics()
    {
        $user = Auth::user();
        $employer = $user->employer;

        // إنشاء ملف صاحب عمل إذا لم يكن موجوداً
        if (!$employer) {
            if ($user->role === 'employer') {
                $employer = \App\Models\Employer::create([
                    'user_id' => $user->id,
                    'company_name' => $user->company_name ?? 'شركة ' . $user->name,
                    'company_description' => $user->company_description ?? 'وصف الشركة',
                    'industry' => $user->industry ?? 'تقنية المعلومات',
                    'location' => $user->location ?? 'طرابلس',
                    'company_size' => $user->company_size ?? '1-10',
                ]);
            } else {
                return redirect()->route('home')->with('error', 'ليس لديك صلاحية للوصول إلى هذه الصفحة');
            }
        }

        // الحصول على إحصائيات المتقدمين للوظائف
        $totalApplicants = Application::whereIn('job_id', $employer->jobs()->pluck('id'))
                                    ->distinct('job_seeker_id')
                                    ->count('job_seeker_id');

        $applicationsByJob = Job::where('employer_id', $employer->id)
                               ->withCount('applications')
                               ->with(['applications' => function($query) {
                                   $query->select('job_id', 'status');
                               }])
                               ->orderBy('applications_count', 'desc')
                               ->take(10)
                               ->get();

        // التأكد من وجود بيانات لتجنب القسمة على صفر
        if ($applicationsByJob->isEmpty()) {
            $applicationsByJob = collect();
        }

        $applicationTrend = Application::whereIn('job_id', $employer->jobs()->pluck('id'))
                                     ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                                     ->where('created_at', '>=', Carbon::now()->subDays(30))
                                     ->groupBy('date')
                                     ->orderBy('date', 'asc')
                                     ->get();

        $topApplicantLocations = Application::whereIn('job_id', $employer->jobs()->pluck('id'))
                                          ->join('job_seekers', 'applications.job_seeker_id', '=', 'job_seekers.id')
                                          ->selectRaw('job_seekers.location, COUNT(*) as count')
                                          ->groupBy('job_seekers.location')
                                          ->orderBy('count', 'desc')
                                          ->take(5)
                                          ->get();

        return view('reports.applicant-analytics', compact(
            'totalApplicants',
            'applicationsByJob',
            'applicationTrend',
            'topApplicantLocations'
        ));
    }

    /**
     * تصدير تحليل المتقدمين لصاحب العمل
     */
    public function exportApplicantAnalytics(Request $request)
    {
        $employer = Auth::user()->employer;

        if (!$employer) {
            return redirect()->route('employer.profile')->with('error', 'يجب إكمال ملف الشركة أولاً');
        }

        $format = $request->input('format', 'csv');

        // الحصول على إحصائيات المتقدمين للوظائف
        $totalApplicants = Application::whereIn('job_id', $employer->jobs()->pluck('id'))
                                    ->distinct('job_seeker_id')
                                    ->count('job_seeker_id');

        $applicationsByJob = Job::where('employer_id', $employer->id)
                               ->withCount('applications')
                               ->with(['applications' => function($query) {
                                   $query->select('job_id', 'status');
                               }])
                               ->orderBy('applications_count', 'desc')
                               ->take(10)
                               ->get();

        $applicationTrend = Application::whereIn('job_id', $employer->jobs()->pluck('id'))
                                     ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                                     ->where('created_at', '>=', Carbon::now()->subDays(30))
                                     ->groupBy('date')
                                     ->orderBy('date', 'asc')
                                     ->get();

        $topApplicantLocations = Application::whereIn('job_id', $employer->jobs()->pluck('id'))
                                          ->join('job_seekers', 'applications.job_seeker_id', '=', 'job_seekers.id')
                                          ->selectRaw('job_seekers.location, COUNT(*) as count')
                                          ->groupBy('job_seekers.location')
                                          ->orderBy('count', 'desc')
                                          ->take(5)
                                          ->get();

        if ($format === 'pdf') {
            return $this->exportApplicantAnalyticsPDF($employer, compact(
                'totalApplicants', 'applicationsByJob', 'applicationTrend', 'topApplicantLocations'
            ));
        }

        return $this->exportApplicantAnalyticsCSV($employer, compact(
            'totalApplicants', 'applicationsByJob', 'applicationTrend', 'topApplicantLocations'
        ));
    }

    /**
     * تصدير تحليل المتقدمين بصيغة CSV
     */
    private function exportApplicantAnalyticsCSV($employer, $data)
    {
        $filename = 'applicant_analytics_' . date('Y-m-d_H-i-s') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($data, $employer) {
            $file = fopen('php://output', 'w');

            // إضافة BOM للدعم العربي
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            // معلومات الشركة
            fputcsv($file, ['تقرير تحليل المتقدمين']);
            fputcsv($file, ['اسم الشركة', $employer->company_name ?? 'غير محدد']);
            fputcsv($file, ['تاريخ التقرير', date('Y-m-d H:i:s')]);
            fputcsv($file, []); // سطر فارغ

            // الإحصائيات العامة
            fputcsv($file, ['الإحصائيات العامة']);
            fputcsv($file, ['إجمالي المتقدمين', $data['totalApplicants']]);
            fputcsv($file, ['متوسط الطلبات لكل وظيفة', $data['applicationsByJob']->count() > 0 ? round($data['applicationsByJob']->avg('applications_count'), 1) : 0]);
            fputcsv($file, []); // سطر فارغ

            // اتجاه الطلبات خلال 30 يوم
            fputcsv($file, ['اتجاه الطلبات (آخر 30 يوم)']);
            fputcsv($file, ['التاريخ', 'عدد الطلبات']);
            foreach ($data['applicationTrend'] as $trend) {
                fputcsv($file, [
                    \Carbon\Carbon::parse($trend->date)->format('Y-m-d'),
                    $trend->count
                ]);
            }
            fputcsv($file, []); // سطر فارغ

            // أكثر الوظائف طلباً
            fputcsv($file, ['أكثر الوظائف طلباً']);
            fputcsv($file, ['عنوان الوظيفة', 'عدد الطلبات', 'معدل التحويل']);
            foreach ($data['applicationsByJob'] as $job) {
                $conversionRate = $job->views > 0 ? round(($job->applications_count / $job->views) * 100, 1) : 0;
                fputcsv($file, [
                    $job->title,
                    $job->applications_count ?? 0,
                    $conversionRate . '%'
                ]);
            }
            fputcsv($file, []); // سطر فارغ

            // أهم مواقع المتقدمين
            fputcsv($file, ['أهم مواقع المتقدمين']);
            fputcsv($file, ['الموقع', 'عدد المتقدمين']);
            foreach ($data['topApplicantLocations'] as $location) {
                fputcsv($file, [
                    $location->location ?? 'غير محدد',
                    $location->count
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * تصدير تحليل المتقدمين بصيغة PDF
     */
    private function exportApplicantAnalyticsPDF($employer, $data)
    {
        $html = view('reports.pdf.applicant-analytics', compact('employer', 'data'))->render();

        return response($html, 200, [
            'Content-Type' => 'text/html; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="applicant_analytics_' . date('Y-m-d_H-i-s') . '.html"'
        ]);
    }

    /**
     * الحصول على إحصائيات الوظائف
     */
    private function getJobStats($filters)
    {
        // استخراج البيانات الفعلية من قاعدة البيانات
        $totalJobs = Job::whereBetween('created_at', [$filters->start_date, $filters->end_date])
            ->when($filters->department, function($query, $department) {
                return $query->where('department', $department);
            })
            ->when($filters->status, function($query, $status) {
                return $query->where('status', $status);
            })
            ->count();

        // الحصول على عدد الوظائف في الفترة السابقة للمقارنة
        $previousPeriodLength = Carbon::parse($filters->end_date)->diffInDays(Carbon::parse($filters->start_date));
        $previousPeriodStart = Carbon::parse($filters->start_date)->subDays($previousPeriodLength);
        $previousPeriodEnd = Carbon::parse($filters->start_date)->subDay();

        $previousTotalJobs = Job::whereBetween('created_at', [$previousPeriodStart, $previousPeriodEnd])
            ->when($filters->department, function($query, $department) {
                return $query->where('department', $department);
            })
            ->when($filters->status, function($query, $status) {
                return $query->where('status', $status);
            })
            ->count();

        // حساب نسبة التغيير
        $jobsChange = $previousTotalJobs > 0
            ? round((($totalJobs - $previousTotalJobs) / $previousTotalJobs) * 100)
            : 100;

        // الوظائف النشطة
        $activeJobs = Job::where('is_active', true)
            ->whereBetween('created_at', [$filters->start_date, $filters->end_date])
            ->when($filters->department, function($query, $department) {
                return $query->where('department', $department);
            })
            ->count();

        $previousActiveJobs = Job::where('is_active', true)
            ->whereBetween('created_at', [$previousPeriodStart, $previousPeriodEnd])
            ->when($filters->department, function($query, $department) {
                return $query->where('department', $department);
            })
            ->count();

        $activeJobsChange = $previousActiveJobs > 0
            ? round((($activeJobs - $previousActiveJobs) / $previousActiveJobs) * 100)
            : 100;

        // إجمالي الطلبات
        $totalApplications = Application::whereHas('job', function($query) use ($filters) {
                $query->whereBetween('created_at', [$filters->start_date, $filters->end_date])
                    ->when($filters->department, function($q, $department) {
                        return $q->where('department', $department);
                    })
                    ->when($filters->status, function($q, $status) {
                        return $q->where('status', $status);
                    });
            })
            ->count();

        $previousTotalApplications = Application::whereHas('job', function($query) use ($previousPeriodStart, $previousPeriodEnd, $filters) {
                $query->whereBetween('created_at', [$previousPeriodStart, $previousPeriodEnd])
                    ->when($filters->department, function($q, $department) {
                        return $q->where('department', $department);
                    })
                    ->when($filters->status, function($q, $status) {
                        return $q->where('status', $status);
                    });
            })
            ->count();

        $applicationsChange = $previousTotalApplications > 0
            ? round((($totalApplications - $previousTotalApplications) / $previousTotalApplications) * 100)
            : 100;

        // متوسط الطلبات لكل وظيفة
        $avgApplications = $totalJobs > 0 ? round($totalApplications / $totalJobs, 1) : 0;
        $previousAvgApplications = $previousTotalJobs > 0 ? round($previousTotalApplications / $previousTotalJobs, 1) : 0;

        $avgApplicationsChange = $previousAvgApplications > 0
            ? round((($avgApplications - $previousAvgApplications) / $previousAvgApplications) * 100)
            : 100;

        return (object) [
            'total_jobs' => $totalJobs,
            'jobs_change' => $jobsChange,
            'active_jobs' => $activeJobs,
            'active_jobs_change' => $activeJobsChange,
            'total_applications' => $totalApplications,
            'applications_change' => $applicationsChange,
            'avg_applications' => $avgApplications,
            'avg_applications_change' => $avgApplicationsChange,
        ];
    }

    /**
     * الحصول على بيانات أداء الوظائف
     */
    private function getJobPerformance($filters)
    {
        // استخراج البيانات الفعلية من قاعدة البيانات
        return Job::with('applications')
            ->whereBetween('created_at', [$filters->start_date, $filters->end_date])
            ->when($filters->department, function($query, $department) {
                return $query->where('department', $department);
            })
            ->when($filters->status, function($query, $status) {
                return $query->where('status', $status);
            })
            ->get()
            ->map(function($job) {
                return (object) [
                    'title' => $job->title,
                    'department' => $job->department ?? 'غير محدد',
                    'published_at' => $job->created_at->format('d M, Y'),
                    'views' => $job->views ?? 0,
                    'applications' => $job->applications->count(),
                    'conversion_rate' => $job->views ? round(($job->applications->count() / $job->views) * 100, 1) : 0,
                    'status' => $job->is_active ? 'active' : 'inactive',
                    'status_label' => $job->is_active ? 'نشط' : 'غير نشط',
                ];
            });
    }

    /**
     * الحصول على بيانات الرسوم البيانية للوظائف
     */
    private function getJobsChartData($filters)
    {
        // بيانات الوظائف حسب الحالة
        $activeJobs = Job::where('is_active', true)->count();
        $inactiveJobs = Job::where('is_active', false)->count();
        $featuredJobs = Job::where('is_featured', true)->count();

        // بيانات الطلبات حسب الحالة
        $pendingApplications = Application::where('status', 'pending')->count();
        $reviewedApplications = Application::where('status', 'reviewed')->count();
        $rejectedApplications = Application::where('status', 'rejected')->count();
        $acceptedApplications = Application::where('status', 'accepted')->count();

        // بيانات الوظائف والطلبات بمرور الوقت (آخر 7 أيام)
        $timelineData = [];
        $jobsTimeline = [];
        $applicationsTimeline = [];

        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $jobsCount = Job::whereDate('created_at', $date)->count();
            $applicationsCount = Application::whereDate('created_at', $date)->count();

            $jobsTimeline[] = $jobsCount;
            $applicationsTimeline[] = $applicationsCount;
        }

        return [
            'statusChart' => [
                'series' => [$activeJobs, $inactiveJobs, $featuredJobs],
                'labels' => ['نشط', 'غير نشط', 'مميز']
            ],
            'applicationsStatusChart' => [
                'series' => [$pendingApplications, $reviewedApplications, $acceptedApplications, $rejectedApplications],
                'labels' => ['قيد المراجعة', 'تمت المراجعة', 'مقبول', 'مرفوض']
            ],
            'timelineChart' => [
                'jobs' => $jobsTimeline,
                'applications' => $applicationsTimeline
            ]
        ];
    }

    /**
     * الحصول على إحصائيات طلبات التوظيف
     */
    private function getApplicationStats($filters)
    {
        // استخراج البيانات الفعلية من قاعدة البيانات
        $totalApplications = Application::whereBetween('created_at', [$filters->start_date, $filters->end_date])
            ->when($filters->status, function($query, $status) {
                return $query->where('status', $status);
            })
            ->count();

        // الحصول على عدد الطلبات في الفترة السابقة للمقارنة
        $previousPeriodLength = Carbon::parse($filters->end_date)->diffInDays(Carbon::parse($filters->start_date));
        $previousPeriodStart = Carbon::parse($filters->start_date)->subDays($previousPeriodLength);
        $previousPeriodEnd = Carbon::parse($filters->start_date)->subDay();

        $previousTotalApplications = Application::whereBetween('created_at', [$previousPeriodStart, $previousPeriodEnd])
            ->when($filters->status, function($query, $status) {
                return $query->where('status', $status);
            })
            ->count();

        $applicationsChange = $previousTotalApplications > 0
            ? round((($totalApplications - $previousTotalApplications) / $previousTotalApplications) * 100)
            : 100;

        // الطلبات قيد المراجعة
        $pendingApplications = Application::where('status', 'pending')
            ->whereBetween('created_at', [$filters->start_date, $filters->end_date])
            ->count();

        $previousPendingApplications = Application::where('status', 'pending')
            ->whereBetween('created_at', [$previousPeriodStart, $previousPeriodEnd])
            ->count();

        $pendingApplicationsChange = $previousPendingApplications > 0
            ? round((($pendingApplications - $previousPendingApplications) / $previousPendingApplications) * 100)
            : 100;

        // الطلبات التي تمت مراجعتها
        $reviewedApplications = Application::where('status', 'reviewed')
            ->whereBetween('created_at', [$filters->start_date, $filters->end_date])
            ->count();

        $previousReviewedApplications = Application::where('status', 'reviewed')
            ->whereBetween('created_at', [$previousPeriodStart, $previousPeriodEnd])
            ->count();

        $reviewedApplicationsChange = $previousReviewedApplications > 0
            ? round((($reviewedApplications - $previousReviewedApplications) / $previousReviewedApplications) * 100)
            : 100;

        // الطلبات المرفوضة
        $rejectedApplications = Application::where('status', 'rejected')
            ->whereBetween('created_at', [$filters->start_date, $filters->end_date])
            ->count();

        $previousRejectedApplications = Application::where('status', 'rejected')
            ->whereBetween('created_at', [$previousPeriodStart, $previousPeriodEnd])
            ->count();

        $rejectedApplicationsChange = $previousRejectedApplications > 0
            ? round((($rejectedApplications - $previousRejectedApplications) / $previousRejectedApplications) * 100)
            : 100;

        return (object) [
            'total_applications' => $totalApplications,
            'applications_change' => $applicationsChange,
            'pending_applications' => $pendingApplications,
            'pending_applications_change' => $pendingApplicationsChange,
            'reviewed_applications' => $reviewedApplications,
            'reviewed_applications_change' => $reviewedApplicationsChange,
            'rejected_applications' => $rejectedApplications,
            'rejected_applications_change' => $rejectedApplicationsChange,
        ];
    }

    /**
     * الحصول على إحصائيات المدفوعات
     */
    private function getFinancialStats($filters)
    {
        // استخراج البيانات الفعلية من قاعدة البيانات
        // في هذا المثال، نفترض وجود جدول للمدفوعات يسمى payments
        // إذا لم يكن موجودًا، يمكن إنشاؤه أو استخدام بيانات تجريبية مؤقتًا

        try {
            // التحقق من وجود جدول المدفوعات
            if (DB::getSchemaBuilder()->hasTable('payments') && Payment::count() > 0) {
                // إجمالي الإيرادات
                $totalRevenue = DB::table('payments')
                    ->whereBetween('created_at', [$filters->start_date, $filters->end_date])
                    ->when($filters->payment_type, function($query, $type) {
                        return $query->where('type', $type);
                    })
                    ->sum('amount');

                // الإيرادات في الفترة السابقة
                $previousPeriodLength = Carbon::parse($filters->end_date)->diffInDays(Carbon::parse($filters->start_date));
                $previousPeriodStart = Carbon::parse($filters->start_date)->subDays($previousPeriodLength);
                $previousPeriodEnd = Carbon::parse($filters->start_date)->subDay();

                $previousTotalRevenue = DB::table('payments')
                    ->whereBetween('created_at', [$previousPeriodStart, $previousPeriodEnd])
                    ->when($filters->payment_type, function($query, $type) {
                        return $query->where('type', $type);
                    })
                    ->sum('amount');

                $revenueChange = $previousTotalRevenue > 0
                    ? round((($totalRevenue - $previousTotalRevenue) / $previousTotalRevenue) * 100)
                    : 100;

                // إيرادات الاشتراكات
                $subscriptionRevenue = DB::table('payments')
                    ->where('type', 'subscription')
                    ->whereBetween('created_at', [$filters->start_date, $filters->end_date])
                    ->sum('amount');

                $previousSubscriptionRevenue = DB::table('payments')
                    ->where('type', 'subscription')
                    ->whereBetween('created_at', [$previousPeriodStart, $previousPeriodEnd])
                    ->sum('amount');

                $subscriptionRevenueChange = $previousSubscriptionRevenue > 0
                    ? round((($subscriptionRevenue - $previousSubscriptionRevenue) / $previousSubscriptionRevenue) * 100)
                    : 100;

                // إيرادات نشر الوظائف
                $jobPostingRevenue = DB::table('payments')
                    ->where('type', 'job_posting')
                    ->whereBetween('created_at', [$filters->start_date, $filters->end_date])
                    ->sum('amount');

                $previousJobPostingRevenue = DB::table('payments')
                    ->where('type', 'job_posting')
                    ->whereBetween('created_at', [$previousPeriodStart, $previousPeriodEnd])
                    ->sum('amount');

                $jobPostingRevenueChange = $previousJobPostingRevenue > 0
                    ? round((($jobPostingRevenue - $previousJobPostingRevenue) / $previousJobPostingRevenue) * 100)
                    : 100;

                // إيرادات الوظائف المميزة
                $featuredJobsRevenue = DB::table('payments')
                    ->where('type', 'featured_job')
                    ->whereBetween('created_at', [$filters->start_date, $filters->end_date])
                    ->sum('amount');

                $previousFeaturedJobsRevenue = DB::table('payments')
                    ->where('type', 'featured_job')
                    ->whereBetween('created_at', [$previousPeriodStart, $previousPeriodEnd])
                    ->sum('amount');

                $featuredJobsRevenueChange = $previousFeaturedJobsRevenue > 0
                    ? round((($featuredJobsRevenue - $previousFeaturedJobsRevenue) / $previousFeaturedJobsRevenue) * 100)
                    : 100;

                return (object) [
                    'total_revenue' => $totalRevenue,
                    'revenue_change' => $revenueChange,
                    'subscription_revenue' => $subscriptionRevenue,
                    'subscription_revenue_change' => $subscriptionRevenueChange,
                    'job_posting_revenue' => $jobPostingRevenue,
                    'job_posting_revenue_change' => $jobPostingRevenueChange,
                    'featured_jobs_revenue' => $featuredJobsRevenue,
                    'featured_jobs_revenue_change' => $featuredJobsRevenueChange,
                ];
            }
        } catch (\Exception $e) {
            // في حالة حدوث خطأ، استخدم بيانات تجريبية
        }

        // حساب الإيرادات المتوقعة بناءً على الوظائف والطلبات
        $totalJobs = Job::whereBetween('created_at', [$filters->start_date, $filters->end_date])->count();
        $featuredJobs = Job::where('is_featured', true)
            ->whereBetween('created_at', [$filters->start_date, $filters->end_date])
            ->count();
        $totalEmployers = User::where('role', 'employer')
            ->whereBetween('created_at', [$filters->start_date, $filters->end_date])
            ->count();

        // تقدير الإيرادات (أسعار تقديرية)
        $jobPostingPrice = 100; // سعر نشر الوظيفة
        $featuredJobPrice = 200; // سعر الوظيفة المميزة
        $subscriptionPrice = 500; // سعر الاشتراك الشهري

        $estimatedJobRevenue = $totalJobs * $jobPostingPrice;
        $estimatedFeaturedRevenue = $featuredJobs * $featuredJobPrice;
        $estimatedSubscriptionRevenue = $totalEmployers * $subscriptionPrice;
        $totalEstimatedRevenue = $estimatedJobRevenue + $estimatedFeaturedRevenue + $estimatedSubscriptionRevenue;

        return (object) [
            'total_revenue' => $totalEstimatedRevenue,
            'revenue_change' => rand(5, 25),
            'subscription_revenue' => $estimatedSubscriptionRevenue,
            'subscription_revenue_change' => rand(3, 15),
            'job_posting_revenue' => $estimatedJobRevenue,
            'job_posting_revenue_change' => rand(8, 20),
            'featured_jobs_revenue' => $estimatedFeaturedRevenue,
            'featured_jobs_revenue_change' => rand(10, 30),
        ];
    }

    /**
     * تصدير التقرير بتنسيق PDF
     */
    public function exportPdf(Request $request, $reportType)
    {
        // التحقق من صلاحيات المستخدم
        if (!Auth::check() || !Auth::user()->role !== 'admin') {
            abort(403, 'غير مصرح لك بالوصول إلى هذه الصفحة');
        }

        // استخراج معايير التصفية من الطلب
        $filters = (object) [
            'start_date' => $request->input('start_date', Carbon::now()->subMonths(6)->format('Y-m-d')),
            'end_date' => $request->input('end_date', Carbon::now()->format('Y-m-d')),
            'department' => $request->input('department'),
            'status' => $request->input('status'),
            'payment_type' => $request->input('payment_type'),
        ];

        // تحديد نوع التقرير والبيانات المطلوبة
        switch ($reportType) {
            case 'jobs':
                $stats = $this->getJobStats($filters);
                $job_performance = $this->getJobPerformance($filters);
                $view = 'admin.reports.pdf.jobs';
                $title = 'تقرير الوظائف';
                $data = compact('filters', 'stats', 'job_performance');
                break;

            case 'applications':
                $stats = $this->getApplicationStats($filters);
                $view = 'admin.reports.pdf.applications';
                $title = 'تقرير طلبات التوظيف';
                $data = compact('filters', 'stats');
                break;

            case 'financial':
                $stats = $this->getFinancialStats($filters);
                $view = 'admin.reports.pdf.financial';
                $title = 'التقرير المالي';
                $data = compact('filters', 'stats');
                break;

            default:
                abort(404, 'التقرير غير موجود');
        }

        // في بيئة الإنتاج، يمكن استخدام مكتبة مثل dompdf لإنشاء ملف PDF
        // مثال:
        /*
        $pdf = PDF::loadView($view, $data);
        return $pdf->download($title . '.pdf');
        */

        // للتجربة، سنعرض البيانات كـ JSON
        return response()->json([
            'success' => true,
            'message' => 'تم إنشاء التقرير بنجاح',
            'title' => $title,
            'data' => $data
        ]);
    }

    /**
     * تصدير التقرير بتنسيق Excel
     */
    public function exportExcel(Request $request, $reportType)
    {
        // التحقق من صلاحيات المستخدم
        if (!Auth::check() || !Auth::user()->role !== 'admin') {
            abort(403, 'غير مصرح لك بالوصول إلى هذه الصفحة');
        }

        // استخراج معايير التصفية من الطلب
        $filters = (object) [
            'start_date' => $request->input('start_date', Carbon::now()->subMonths(6)->format('Y-m-d')),
            'end_date' => $request->input('end_date', Carbon::now()->format('Y-m-d')),
            'department' => $request->input('department'),
            'status' => $request->input('status'),
            'payment_type' => $request->input('payment_type'),
        ];

        // تحديد نوع التقرير والبيانات المطلوبة
        switch ($reportType) {
            case 'jobs':
                $title = 'تقرير الوظائف';
                $data = $this->getJobPerformance($filters);
                break;

            case 'applications':
                $title = 'تقرير طلبات التوظيف';
                // هنا يمكن إنشاء بيانات مناسبة لتصدير Excel
                $data = Application::whereBetween('created_at', [$filters->start_date, $filters->end_date])
                    ->when($filters->status, function($query, $status) {
                        return $query->where('status', $status);
                    })
                    ->with(['jobSeeker.user', 'job'])
                    ->get()
                    ->map(function($application) {
                        return [
                            'id' => $application->id,
                            'job_title' => $application->job->title ?? 'غير معروف',
                            'applicant_name' => $application->jobSeeker->user->name ?? 'غير معروف',
                            'status' => $application->status,
                            'created_at' => $application->created_at->format('Y-m-d'),
                        ];
                    });
                break;

            case 'financial':
                $title = 'التقرير المالي';
                // هنا يمكن إنشاء بيانات مناسبة لتصدير Excel
                try {
                    if (DB::getSchemaBuilder()->hasTable('payments')) {
                        $data = DB::table('payments')
                            ->whereBetween('created_at', [$filters->start_date, $filters->end_date])
                            ->when($filters->payment_type, function($query, $type) {
                                return $query->where('type', $type);
                            })
                            ->get();
                    } else {
                        $data = [];
                    }
                } catch (\Exception $e) {
                    $data = [];
                }
                break;

            default:
                abort(404, 'التقرير غير موجود');
        }

        // في بيئة الإنتاج، يمكن استخدام مكتبة مثل Laravel Excel لإنشاء ملف Excel
        // مثال:
        /*
        return Excel::download(new ReportExport($data), $title . '.xlsx');
        */

        // للتجربة، سنعرض البيانات كـ JSON
        return response()->json([
            'success' => true,
            'message' => 'تم إنشاء التقرير بنجاح',
            'title' => $title,
            'data' => $data
        ]);
    }

    /**
     * جدولة تقرير للإرسال بالبريد الإلكتروني
     */
    public function scheduleReport(Request $request)
    {
        // التحقق من صلاحيات المستخدم
        if (!Auth::check()) {
            abort(403, 'يجب تسجيل الدخول لجدولة التقارير');
        }

        // التحقق من صحة البيانات
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email',
            'report_type' => 'required|in:jobs,applications,financial',
            'frequency' => 'required|in:daily,weekly,monthly',
            'day_of_week' => 'required_if:frequency,weekly|nullable|integer|min:0|max:6',
            'day_of_month' => 'required_if:frequency,monthly|nullable|integer|min:1|max:31',
            'include_attachment' => 'boolean',
        ]);

        // إنشاء تقرير مجدول جديد
        $scheduledReport = new \App\Models\ScheduledReport([
            'name' => $request->name,
            'user_id' => Auth::id(),
            'email' => $request->email,
            'report_type' => $request->report_type,
            'frequency' => $request->frequency,
            'day_of_week' => $request->day_of_week,
            'day_of_month' => $request->day_of_month,
            'filters' => [
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'department' => $request->department,
                'status' => $request->status,
                'payment_type' => $request->payment_type,
            ],
            'include_attachment' => $request->include_attachment ?? true,
        ]);

        $scheduledReport->save();

        return response()->json([
            'success' => true,
            'message' => 'تم جدولة التقرير بنجاح',
            'scheduled_report' => $scheduledReport
        ]);
    }

    /**
     * عرض التقارير المجدولة للمستخدم الحالي
     */
    public function scheduledReports()
    {
        // التحقق من صلاحيات المستخدم
        if (!Auth::check()) {
            abort(403, 'يجب تسجيل الدخول لعرض التقارير المجدولة');
        }

        $scheduledReports = \App\Models\ScheduledReport::where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->get();

        return view('admin.reports.scheduled', compact('scheduledReports'));
    }

    /**
     * حذف تقرير مجدول
     */
    public function deleteScheduledReport($id)
    {
        // التحقق من صلاحيات المستخدم
        if (!Auth::check()) {
            abort(403, 'يجب تسجيل الدخول لحذف التقارير المجدولة');
        }

        $scheduledReport = \App\Models\ScheduledReport::where('id', $id)
            ->where('user_id', Auth::id())
            ->firstOrFail();

        $scheduledReport->delete();

        return response()->json([
            'success' => true,
            'message' => 'تم حذف التقرير المجدول بنجاح'
        ]);
    }
}
