@extends('layouts.admin')

@section('title', 'الصفحة غير موجودة - 404')
@section('header_title', 'خطأ 404')

@section('content')
<div class="min-h-screen flex items-center justify-center p-8">
    <!-- خلفية متحركة -->
    <div class="floating-shapes"></div>
    
    <!-- بطاقة الخطأ الرئيسية -->
    <div class="max-w-2xl w-full text-center">
        <div class="enhanced-card rounded-3xl p-12 animate-bounce-in">
            <!-- رقم الخطأ -->
            <div class="mb-8">
                <h1 class="text-9xl font-bold text-gradient mb-4">404</h1>
                <div class="w-32 h-32 mx-auto gradient-danger rounded-full flex items-center justify-center shadow-2xl glow-effect animate-float">
                    <i class="fas fa-search text-5xl text-white"></i>
                </div>
            </div>

            <!-- رسالة الخطأ -->
            <h2 class="text-3xl font-bold text-gray-800 dark:text-gray-200 mb-4">
                عذراً، الصفحة غير موجودة! 😔
            </h2>
            
            <p class="text-lg text-gray-600 dark:text-gray-400 mb-8 leading-relaxed">
                يبدو أن الصفحة التي تبحث عنها قد تم نقلها أو حذفها أو أنك أدخلت رابطاً خاطئاً.
                لا تقلق، يمكنك العودة إلى الصفحة الرئيسية أو البحث عما تريد.
            </p>

            <!-- اقتراحات مفيدة -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div class="enhanced-card p-6 rounded-2xl">
                    <div class="w-16 h-16 mx-auto mb-4 gradient-primary rounded-2xl flex items-center justify-center">
                        <i class="fas fa-home text-2xl text-white"></i>
                    </div>
                    <h3 class="font-bold mb-2">العودة للرئيسية</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">ارجع إلى لوحة التحكم الرئيسية</p>
                    <a href="{{ route('admin.index') }}" class="btn-modern text-sm">
                        <i class="fas fa-arrow-right"></i>
                        لوحة التحكم
                    </a>
                </div>

                <div class="enhanced-card p-6 rounded-2xl">
                    <div class="w-16 h-16 mx-auto mb-4 gradient-accent rounded-2xl flex items-center justify-center">
                        <i class="fas fa-briefcase text-2xl text-white"></i>
                    </div>
                    <h3 class="font-bold mb-2">إدارة الوظائف</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">تصفح وإدارة الوظائف المتاحة</p>
                    <a href="{{ route('admin.jobs.index') }}" class="btn-modern text-sm" style="background: linear-gradient(135d, #d946ef 0%, #c026d3 100%);">
                        <i class="fas fa-arrow-right"></i>
                        الوظائف
                    </a>
                </div>
            </div>

            <!-- شريط البحث -->
            <div class="mb-8">
                <h3 class="text-xl font-bold mb-4 text-gray-800 dark:text-gray-200">أو ابحث عما تريد</h3>
                <div class="relative max-w-md mx-auto">
                    <input type="text" 
                           placeholder="ابحث في النظام..." 
                           class="modern-input pr-12 text-center"
                           id="searchInput">
                    <div class="absolute right-4 top-1/2 transform -translate-y-1/2">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                </div>
            </div>

            <!-- روابط سريعة -->
            <div class="border-t border-gray-200 dark:border-gray-700 pt-8">
                <h3 class="text-lg font-bold mb-4 text-gray-800 dark:text-gray-200">روابط سريعة</h3>
                <div class="flex flex-wrap justify-center gap-4">
                    <a href="{{ route('admin.applications.index') }}" class="text-primary-600 hover:text-primary-700 font-medium transition-colors">
                        <i class="fas fa-file-alt ml-1"></i>
                        طلبات التوظيف
                    </a>
                    <a href="{{ route('admin.candidates.index') }}" class="text-primary-600 hover:text-primary-700 font-medium transition-colors">
                        <i class="fas fa-users ml-1"></i>
                        المرشحون
                    </a>
                    <a href="{{ route('admin.reports.index') }}" class="text-primary-600 hover:text-primary-700 font-medium transition-colors">
                        <i class="fas fa-chart-bar ml-1"></i>
                        التقارير
                    </a>
                    <a href="{{ route('admin.settings.general') }}" class="text-primary-600 hover:text-primary-700 font-medium transition-colors">
                        <i class="fas fa-cog ml-1"></i>
                        الإعدادات
                    </a>
                </div>
            </div>

            <!-- معلومات الدعم -->
            <div class="mt-8 p-6 bg-gradient-to-r from-primary-50 to-accent-50 dark:from-primary-900/20 dark:to-accent-900/20 rounded-2xl">
                <h3 class="text-lg font-bold mb-2 text-gray-800 dark:text-gray-200">
                    <i class="fas fa-life-ring text-primary-600 ml-2"></i>
                    هل تحتاج مساعدة؟
                </h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                    إذا كنت تواجه مشكلة مستمرة، فريق الدعم جاهز لمساعدتك!
                </p>
                <div class="flex flex-wrap gap-4 justify-center">
                    <a href="mailto:<EMAIL>" class="text-primary-600 hover:text-primary-700 font-medium">
                        <i class="fas fa-envelope ml-1"></i>
                        تواصل معنا
                    </a>
                    <a href="/" class="text-primary-600 hover:text-primary-700 font-medium">
                        <i class="fas fa-globe ml-1"></i>
                        الموقع الرئيسي
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@section('styles')
<style>
    .animate-bounce-in {
        animation: bounceIn 0.8s ease-out;
    }
    
    .animate-float {
        animation: float 3s ease-in-out infinite;
    }
    
    @keyframes bounceIn {
        0% {
            transform: scale(0.3);
            opacity: 0;
        }
        50% {
            transform: scale(1.05);
        }
        70% {
            transform: scale(0.9);
        }
        100% {
            transform: scale(1);
            opacity: 1;
        }
    }
    
    @keyframes float {
        0%, 100% { 
            transform: translateY(0px) rotate(0deg); 
        }
        50% { 
            transform: translateY(-20px) rotate(10deg); 
        }
    }
</style>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحسين شريط البحث
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const query = this.value.trim();
                if (query) {
                    // يمكن إضافة منطق البحث هنا
                    window.location.href = `{{ route('admin.index') }}?search=${encodeURIComponent(query)}`;
                }
            }
        });
    }
    
    // تأثيرات إضافية للروابط
    document.querySelectorAll('a[href]').forEach(link => {
        link.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        link.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});
</script>
@endsection
@endsection
