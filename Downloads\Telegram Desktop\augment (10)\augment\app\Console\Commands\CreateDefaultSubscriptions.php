<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Employer;
use App\Models\Subscription;

class CreateDefaultSubscriptions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscriptions:create-default';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create default subscriptions for existing employers';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Creating default subscriptions for existing employers...');
        
        $employers = Employer::whereDoesntHave('subscriptions')->get();
        $count = 0;
        
        foreach ($employers as $employer) {
            Subscription::createDefault($employer);
            $count++;
            $this->info("Created subscription for employer: {$employer->company_name}");
        }
        
        $this->info("Created {$count} default subscriptions successfully!");
        
        return 0;
    }
}
