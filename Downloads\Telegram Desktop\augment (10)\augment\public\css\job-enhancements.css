/* تحسينات صفحة عرض الوظيفة */

/* الخلفية المتدرجة */
.gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* تأثيرات الحركة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* تطبيق الحركات */
.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.animate-pulse-hover:hover {
    animation: pulse 0.3s ease-in-out;
}

/* تحسينات البطاقات */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* تحسينات الأزرار */
.btn-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: all 0.3s ease;
}

.btn-gradient:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

/* تحسينات النصوص */
.text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* تحسينات الأيقونات */
.icon-bounce {
    transition: transform 0.3s ease;
}

.icon-bounce:hover {
    transform: scale(1.2) rotate(5deg);
}

/* تحسينات الشريط الجانبي */
.sidebar-sticky {
    position: sticky;
    top: 2rem;
}

/* تحسينات الوضع المظلم */
.dark .bg-dark-card {
    background-color: #1e293b;
    border-color: #334155;
}

.dark .text-dark-primary {
    color: #e2e8f0;
}

.dark .text-dark-secondary {
    color: #94a3b8;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .sidebar-sticky {
        position: static;
    }
    
    .animate-fade-in-up,
    .animate-slide-in-right {
        animation: none;
    }
}

/* تحسينات الطباعة */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
    
    .bg-gradient-to-br,
    .bg-gradient-to-r {
        background: white !important;
    }
    
    .text-white {
        color: black !important;
    }
    
    .shadow-sm,
    .shadow-lg,
    .shadow-xl {
        box-shadow: none !important;
    }
}

/* تحسينات إضافية للتفاعل */
.interactive-element {
    cursor: pointer;
    transition: all 0.2s ease;
}

.interactive-element:hover {
    transform: scale(1.02);
}

.interactive-element:active {
    transform: scale(0.98);
}

/* تحسينات الألوان */
.bg-success-light {
    background-color: rgba(34, 197, 94, 0.1);
}

.bg-warning-light {
    background-color: rgba(245, 158, 11, 0.1);
}

.bg-error-light {
    background-color: rgba(239, 68, 68, 0.1);
}

.bg-info-light {
    background-color: rgba(59, 130, 246, 0.1);
}

/* تحسينات الحدود */
.border-gradient {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(135deg, #667eea, #764ba2) border-box;
}

/* تحسينات التمرير */
.smooth-scroll {
    scroll-behavior: smooth;
}

/* تحسينات التركيز */
.focus-ring:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* تحسينات الحالة المحملة */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #667eea;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* تحسينات الإشعارات */
.notification-enter {
    animation: slideInRight 0.3s ease-out;
}

.notification-exit {
    animation: slideOutRight 0.3s ease-in;
}

@keyframes slideOutRight {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

/* تحسينات الشبكة */
.grid-auto-fit {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

/* تحسينات النماذج */
.form-floating {
    position: relative;
}

.form-floating input:focus + label,
.form-floating input:not(:placeholder-shown) + label {
    transform: translateY(-1.5rem) scale(0.85);
    color: #667eea;
}

.form-floating label {
    position: absolute;
    top: 0.75rem;
    left: 0.75rem;
    transition: all 0.2s ease;
    pointer-events: none;
}
