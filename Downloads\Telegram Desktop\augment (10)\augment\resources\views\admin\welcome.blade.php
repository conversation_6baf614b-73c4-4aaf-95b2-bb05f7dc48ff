@extends('layouts.admin')

@section('title', 'مرحباً بك في Hire Me')
@section('header_title', 'مرحباً بك!')

@section('content')
<div class="min-h-screen flex items-center justify-center p-8">
    <!-- خلفية متحركة -->
    <div class="floating-shapes"></div>
    
    <!-- بطاقة الترحيب الرئيسية -->
    <div class="max-w-4xl w-full">
        <div class="enhanced-card rounded-3xl p-12 text-center animate-scaleIn">
            <!-- الأيقونة الرئيسية -->
            <div class="mb-8">
                <div class="w-32 h-32 mx-auto gradient-primary rounded-full flex items-center justify-center shadow-2xl glow-effect animate-float">
                    <i class="fas fa-rocket text-5xl text-white"></i>
                </div>
            </div>

            <!-- العنوان الرئيسي -->
            <h1 class="text-5xl font-bold text-gradient mb-6">
                مرحباً بك في Hire Me! 🎉
            </h1>
            
            <!-- النص الترحيبي -->
            <p class="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto leading-relaxed">
                نحن سعداء لانضمامك إلينا! منصة Hire Me هي الحل الأمثل لإدارة عمليات التوظيف بكفاءة وسهولة.
                دعنا نساعدك في العثور على أفضل المواهب لشركتك.
            </p>

            <!-- الميزات الرئيسية -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                <div class="group">
                    <div class="w-20 h-20 mx-auto mb-4 gradient-accent rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-accent-500/25 transition-all duration-300 group-hover:scale-110">
                        <i class="fas fa-users text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2 text-gray-800 dark:text-gray-200">إدارة المرشحين</h3>
                    <p class="text-gray-600 dark:text-gray-400">تتبع وإدارة جميع طلبات التوظيف بسهولة</p>
                </div>

                <div class="group">
                    <div class="w-20 h-20 mx-auto mb-4 gradient-success rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-success-500/25 transition-all duration-300 group-hover:scale-110">
                        <i class="fas fa-chart-line text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2 text-gray-800 dark:text-gray-200">تقارير متقدمة</h3>
                    <p class="text-gray-600 dark:text-gray-400">احصل على إحصائيات مفصلة عن عمليات التوظيف</p>
                </div>

                <div class="group">
                    <div class="w-20 h-20 mx-auto mb-4 gradient-warning rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-warning-500/25 transition-all duration-300 group-hover:scale-110">
                        <i class="fas fa-cog text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2 text-gray-800 dark:text-gray-200">سهولة الاستخدام</h3>
                    <p class="text-gray-600 dark:text-gray-400">واجهة بسيطة وسهلة الاستخدام للجميع</p>
                </div>
            </div>

            <!-- أزرار العمل -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <a href="{{ route('admin.index') }}" class="btn-modern">
                    <i class="fas fa-tachometer-alt"></i>
                    انتقل إلى لوحة التحكم
                </a>
                
                <a href="{{ route('admin.jobs.create') }}" class="btn-modern" style="background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%); box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);">
                    <i class="fas fa-plus"></i>
                    أنشئ وظيفة جديدة
                </a>
            </div>

            <!-- خطوات البداية -->
            <div class="mt-16 text-right">
                <h2 class="text-2xl font-bold text-gradient mb-8">خطوات البداية السريعة</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="enhanced-card p-6 rounded-2xl">
                        <div class="w-12 h-12 gradient-primary rounded-xl flex items-center justify-center text-white font-bold text-xl mb-4">
                            1
                        </div>
                        <h3 class="font-bold mb-2">أكمل ملف الشركة</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">أضف معلومات شركتك وشعارها</p>
                        <a href="{{ route('admin.companies.show', 1) }}" class="text-primary-600 text-sm hover:underline mt-2 inline-block">
                            ابدأ الآن →
                        </a>
                    </div>

                    <div class="enhanced-card p-6 rounded-2xl">
                        <div class="w-12 h-12 gradient-accent rounded-xl flex items-center justify-center text-white font-bold text-xl mb-4">
                            2
                        </div>
                        <h3 class="font-bold mb-2">أنشئ أول وظيفة</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">أضف الوظائف المتاحة في شركتك</p>
                        <a href="{{ route('admin.jobs.create') }}" class="text-primary-600 text-sm hover:underline mt-2 inline-block">
                            ابدأ الآن →
                        </a>
                    </div>

                    <div class="enhanced-card p-6 rounded-2xl">
                        <div class="w-12 h-12 gradient-success rounded-xl flex items-center justify-center text-white font-bold text-xl mb-4">
                            3
                        </div>
                        <h3 class="font-bold mb-2">راجع الطلبات</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">تابع طلبات المرشحين وقيمها</p>
                        <a href="{{ route('admin.applications.index') }}" class="text-primary-600 text-sm hover:underline mt-2 inline-block">
                            ابدأ الآن →
                        </a>
                    </div>

                    <div class="enhanced-card p-6 rounded-2xl">
                        <div class="w-12 h-12 gradient-warning rounded-xl flex items-center justify-center text-white font-bold text-xl mb-4">
                            4
                        </div>
                        <h3 class="font-bold mb-2">اطلع على التقارير</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">تابع إحصائيات التوظيف</p>
                        <a href="{{ route('admin.reports.index') }}" class="text-primary-600 text-sm hover:underline mt-2 inline-block">
                            ابدأ الآن →
                        </a>
                    </div>
                </div>
            </div>

            <!-- معلومات الدعم -->
            <div class="mt-12 p-6 bg-gradient-to-r from-primary-50 to-accent-50 dark:from-primary-900/20 dark:to-accent-900/20 rounded-2xl">
                <h3 class="text-lg font-bold mb-2 text-gray-800 dark:text-gray-200">
                    <i class="fas fa-question-circle text-primary-600 ml-2"></i>
                    هل تحتاج مساعدة؟
                </h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                    فريق الدعم متاح لمساعدتك في أي وقت. لا تتردد في التواصل معنا!
                </p>
                <div class="flex flex-wrap gap-4 justify-center">
                    <a href="mailto:<EMAIL>" class="text-primary-600 hover:text-primary-700 font-medium">
                        <i class="fas fa-envelope ml-1"></i>
                        البريد الإلكتروني
                    </a>
                    <a href="tel:+966123456789" class="text-primary-600 hover:text-primary-700 font-medium">
                        <i class="fas fa-phone ml-1"></i>
                        الهاتف
                    </a>
                    <a href="#" class="text-primary-600 hover:text-primary-700 font-medium">
                        <i class="fas fa-comments ml-1"></i>
                        الدردشة المباشرة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@section('styles')
<style>
    .animate-scaleIn {
        animation: scaleIn 0.6s ease-out;
    }
    
    .animate-float {
        animation: float 3s ease-in-out infinite;
    }
    
    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }
</style>
@endsection
@endsection
