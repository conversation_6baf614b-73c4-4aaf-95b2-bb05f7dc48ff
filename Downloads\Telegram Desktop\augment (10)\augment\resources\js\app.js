import './bootstrap';
import './dark-mode';
import Alpine from 'alpinejs';

window.Alpine = Alpine;
Alpine.start();

// Dark mode functionality
document.addEventListener("DOMContentLoaded", () => {
    const darkModeToggle = document.getElementById("dark-mode-toggle");

    // Check for saved theme preference or use the system preference
    if (
        localStorage.theme === "dark" ||
        (!("theme" in localStorage) &&
            window.matchMedia("(prefers-color-scheme: dark)").matches)
    ) {
        document.documentElement.classList.add("dark");
    } else {
        document.documentElement.classList.remove("dark");
    }

    // Toggle dark/light mode
    if (darkModeToggle) {
        darkModeToggle.addEventListener("click", () => {
            document.documentElement.classList.toggle("dark");

            // Save preference to localStorage
            if (document.documentElement.classList.contains("dark")) {
                localStorage.theme = "dark";
            } else {
                localStorage.theme = "light";
            }
        });
    }
});

Alpine.start();
