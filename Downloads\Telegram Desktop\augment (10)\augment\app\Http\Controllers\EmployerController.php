<?php

namespace App\Http\Controllers;

use App\Models\Employer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class EmployerController extends Controller
{
    public function __construct()
    {
        // تطبيق middleware على الدوال التي تتطلب تسجيل دخول كصاحب عمل
        $this->middleware(['auth', 'role:employer'])->only([
            'profile', 'edit', 'update', 'jobs', 'applications', 'profileRatings'
        ]);
    }

    // الدوال العامة التي لا تتطلب تسجيل دخول
    public function index(Request $request)
    {
        $query = Employer::with('user');

        // البحث حسب اسم الشركة
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function($q) use ($searchTerm) {
                $q->where('company_name', 'like', '%' . $searchTerm . '%')
                  ->orWhere('company_description', 'like', '%' . $searchTerm . '%');
            });
        }

        // التصفية حسب المجال
        if ($request->filled('industry')) {
            $industry = $request->industry;
            $query->where(function($q) use ($industry) {
                $q->where('industry', $industry)
                  ->orWhere('industry', 'like', '%' . $industry . '%');
            });
        }

        // التصفية حسب الموقع
        if ($request->filled('location')) {
            $location = $request->location;
            $query->where(function($q) use ($location) {
                $q->where('location', $location)
                  ->orWhere('location', 'like', '%' . $location . '%');
            });
        }

        $employers = $query->latest()->paginate(12)->withQueryString();
        return view('employers.index', compact('employers'));
    }

    public function show(Employer $employer)
    {
        return view('employers.show', compact('employer'));
    }

    public function profile()
    {
        $employer = Auth::user()->employer;
        return view('employers.profile', compact('employer'));
    }

    public function edit()
    {
        $employer = Auth::user()->employer;
        return view('employers.edit', compact('employer'));
    }

    public function update(Request $request)
    {
        $request->validate([
            'company_name' => 'required|string|max:255',
            'company_description' => 'nullable|string',
            'industry' => 'nullable|string',
            'website' => 'nullable|url',
            'location' => 'nullable|string',
            'company_size' => 'nullable|string',
            'founded_year' => 'nullable|integer|min:1900|max:' . date('Y'),
            'company_logo' => 'nullable|image|max:2048',
        ]);

        $employer = Auth::user()->employer;

        if ($request->hasFile('company_logo')) {
            if ($employer->company_logo) {
                Storage::delete($employer->company_logo);
            }
            $path = $request->file('company_logo')->store('company_logos');
            $employer->company_logo = $path;
        }

        $employer->company_name = $request->company_name;
        $employer->company_description = $request->company_description;
        $employer->industry = $request->industry;
        $employer->website = $request->website;
        $employer->location = $request->location;
        $employer->company_size = $request->company_size;
        $employer->founded_year = $request->founded_year;
        $employer->save();

        return redirect()->route('employer.profile')->with('success', 'Company profile updated successfully');
    }

    public function jobs()
    {
        $jobs = Auth::user()->employer->jobs()->latest()->paginate(10);
        return view('employers.jobs', compact('jobs'));
    }

    public function applications(Request $request)
    {
        $employer = Auth::user()->employer;

        // بناء الاستعلام الأساسي
        $query = $employer->jobs()->with(['applications' => function($q) use ($request) {
            $q->with('jobSeeker.user');

            // فلترة حسب البحث
            if ($request->filled('search')) {
                $search = $request->search;
                $q->whereHas('jobSeeker.user', function($userQuery) use ($search) {
                    $userQuery->where('name', 'like', "%{$search}%")
                             ->orWhere('email', 'like', "%{$search}%");
                });
            }

            // فلترة حسب الحالة
            if ($request->filled('status')) {
                $q->where('status', $request->status);
            }
        }]);

        // فلترة حسب الوظيفة
        if ($request->filled('job')) {
            $query->where('id', $request->job);
        }

        $jobs = $query->latest()->paginate(10);

        // الحصول على جميع الوظائف للفلتر
        $allJobs = $employer->jobs()->select('id', 'title')->get();

        return view('employers.applications', compact('jobs', 'allJobs'));
    }

    /**
     * تصدير طلبات التوظيف
     */
    public function exportApplications(Request $request)
    {
        $employer = Auth::user()->employer;
        $format = $request->input('format', 'csv');

        // بناء الاستعلام مع نفس الفلاتر
        $query = $employer->jobs()->with(['applications' => function($q) use ($request) {
            $q->with('jobSeeker.user');

            // فلترة حسب البحث
            if ($request->filled('search')) {
                $search = $request->search;
                $q->whereHas('jobSeeker.user', function($userQuery) use ($search) {
                    $userQuery->where('name', 'like', "%{$search}%")
                             ->orWhere('email', 'like', "%{$search}%");
                });
            }

            // فلترة حسب الحالة
            if ($request->filled('status')) {
                $q->where('status', $request->status);
            }
        }]);

        // فلترة حسب الوظيفة
        if ($request->filled('job')) {
            $query->where('id', $request->job);
        }

        $jobs = $query->get();

        // جمع جميع الطلبات
        $applications = collect();
        foreach ($jobs as $job) {
            foreach ($job->applications as $application) {
                $applications->push($application);
            }
        }

        if ($format === 'csv') {
            return $this->exportApplicationsCSV($applications);
        } elseif ($format === 'pdf') {
            return $this->exportApplicationsPDF($applications, $employer);
        }

        return redirect()->back()->with('error', 'نوع التصدير غير مدعوم');
    }

    /**
     * تصدير الطلبات بصيغة CSV
     */
    private function exportApplicationsCSV($applications)
    {
        $filename = 'applications_' . date('Y-m-d_H-i-s') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0'
        ];

        $callback = function() use ($applications) {
            $file = fopen('php://output', 'w');

            // إضافة BOM للدعم العربي
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            // رؤوس الأعمدة
            fputcsv($file, [
                'اسم المتقدم',
                'البريد الإلكتروني',
                'الوظيفة',
                'تاريخ التقديم',
                'الحالة',
                'المهارات',
                'الخبرة'
            ]);

            // البيانات
            foreach ($applications as $application) {
                $statusText = [
                    'pending' => 'قيد المراجعة',
                    'reviewed' => 'تمت المراجعة',
                    'shortlisted' => 'القائمة المختصرة',
                    'accepted' => 'مقبول',
                    'rejected' => 'مرفوض',
                    'hired' => 'تم التعيين'
                ];

                fputcsv($file, [
                    $application->jobSeeker->user->name ?? 'غير محدد',
                    $application->jobSeeker->user->email ?? 'غير محدد',
                    $application->job->title ?? 'غير محدد',
                    $application->created_at->format('Y-m-d H:i:s'),
                    $statusText[$application->status] ?? $application->status,
                    $application->jobSeeker->skills ?? 'غير محدد',
                    $application->jobSeeker->experience ?? 'غير محدد'
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * تصدير الطلبات بصيغة PDF
     */
    private function exportApplicationsPDF($applications, $employer)
    {
        $data = [
            'applications' => $applications,
            'employer' => $employer,
            'totalApplications' => $applications->count(),
            'reportDate' => now()->format('Y-m-d H:i:s'),
        ];

        // إنشاء HTML بسيط للتقرير
        $html = '<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>تقرير طلبات التوظيف</title>
    <style>
        body { font-family: Arial, sans-serif; direction: rtl; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
        th { background-color: #f2f2f2; }
        .header { text-align: center; margin-bottom: 30px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>تقرير طلبات التوظيف</h1>
        <p>الشركة: ' . $employer->company_name . '</p>
        <p>تاريخ التقرير: ' . $data['reportDate'] . '</p>
        <p>إجمالي الطلبات: ' . $data['totalApplications'] . '</p>
    </div>

    <table>
        <thead>
            <tr>
                <th>اسم المتقدم</th>
                <th>البريد الإلكتروني</th>
                <th>الوظيفة</th>
                <th>تاريخ التقديم</th>
                <th>الحالة</th>
            </tr>
        </thead>
        <tbody>';

        foreach ($applications as $application) {
            $statusText = [
                'pending' => 'قيد المراجعة',
                'reviewed' => 'تمت المراجعة',
                'shortlisted' => 'القائمة المختصرة',
                'accepted' => 'مقبول',
                'rejected' => 'مرفوض',
                'hired' => 'تم التعيين'
            ];

            $html .= '<tr>
                <td>' . ($application->jobSeeker->user->name ?? 'غير محدد') . '</td>
                <td>' . ($application->jobSeeker->user->email ?? 'غير محدد') . '</td>
                <td>' . ($application->job->title ?? 'غير محدد') . '</td>
                <td>' . $application->created_at->format('Y-m-d H:i:s') . '</td>
                <td>' . ($statusText[$application->status] ?? $application->status) . '</td>
            </tr>';
        }

        $html .= '</tbody>
    </table>
</body>
</html>';

        return response($html, 200, [
            'Content-Type' => 'text/html; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="applications_report_' . date('Y-m-d_H-i-s') . '.html"'
        ]);
    }

    public function profileRatings()
    {
        $ratings = Auth::user()->employer->profileRatings()->with('jobSeeker.user')->latest()->paginate(10);
        return view('employers.profile-ratings', compact('ratings'));
    }
}
