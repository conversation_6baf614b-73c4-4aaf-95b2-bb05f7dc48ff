@extends('layouts.admin')

@section('title', 'النسخ الاحتياطية - Hire Me')
@section('header_title', 'النسخ الاحتياطية')

@section('content')
<div class="p-6">
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center gap-4">
                <a href="{{ route('admin.database.index') }}" class="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white">
                    <i class="fas fa-arrow-right text-xl"></i>
                </a>
                <div>
                    <h2 class="text-2xl font-bold">النسخ الاحتياطية</h2>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">إدارة النسخ الاحتياطية لقاعدة البيانات</p>
                </div>
            </div>
            
            <form action="{{ route('admin.database.backup.create') }}" method="POST" class="inline">
                @csrf
                <button type="submit" class="btn-gradient text-white px-6 py-3 rounded-lg hover:opacity-90 transition-colors flex items-center gap-2" onclick="return confirm('هل تريد إنشاء نسخة احتياطية جديدة؟')">
                    <i class="fas fa-plus"></i>
                    <span>إنشاء نسخة احتياطية</span>
                </button>
            </form>
        </div>
    </div>

    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg">
            <div class="flex items-center">
                <i class="fas fa-check-circle ml-2"></i>
                {{ session('success') }}
            </div>
        </div>
    @endif

    @if(session('error'))
        <div class="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
            <div class="flex items-center">
                <i class="fas fa-exclamation-circle ml-2"></i>
                {{ session('error') }}
            </div>
        </div>
    @endif

    <!-- Backup Information -->
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                    <i class="fas fa-database text-blue-600 dark:text-blue-400 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm text-gray-500 dark:text-gray-400">إجمالي النسخ</p>
                    <p class="text-2xl font-bold">{{ $backups->count() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                    <i class="fas fa-hdd text-green-600 dark:text-green-400 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm text-gray-500 dark:text-gray-400">الحجم الإجمالي</p>
                    <p class="text-2xl font-bold">{{ number_format($backups->sum('size') / 1024 / 1024, 2) }} MB</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
                    <i class="fas fa-clock text-purple-600 dark:text-purple-400 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm text-gray-500 dark:text-gray-400">آخر نسخة</p>
                    <p class="text-lg font-bold">{{ $backups->first() ? \Carbon\Carbon::parse($backups->first()['created_at'])->diffForHumans() : 'لا توجد' }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center">
                    <i class="fas fa-shield-alt text-orange-600 dark:text-orange-400 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm text-gray-500 dark:text-gray-400">الحالة</p>
                    <p class="text-lg font-bold text-green-600">آمن</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Backup Guidelines -->
    <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-6 mb-8">
        <div class="flex items-start gap-4">
            <i class="fas fa-info-circle text-blue-600 dark:text-blue-400 text-xl mt-1"></i>
            <div>
                <h3 class="text-lg font-bold text-blue-900 dark:text-blue-100 mb-2">إرشادات النسخ الاحتياطية</h3>
                <div class="text-blue-800 dark:text-blue-200 space-y-2">
                    <p>• يُنصح بإنشاء نسخة احتياطية قبل أي تحديث مهم للنظام</p>
                    <p>• احتفظ بعدة نسخ احتياطية من فترات زمنية مختلفة</p>
                    <p>• تأكد من تحميل النسخ الاحتياطية وحفظها في مكان آمن خارج الخادم</p>
                    <p>• اختبر النسخ الاحتياطية بانتظام للتأكد من سلامتها</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Backups List -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-bold">قائمة النسخ الاحتياطية</h3>
        </div>
        
        @if($backups->isNotEmpty())
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-800">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">اسم الملف</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الحجم</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">تاريخ الإنشاء</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-dark-card divide-y divide-gray-200 dark:divide-gray-700">
                        @foreach($backups as $backup)
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <i class="fas fa-file-archive text-gray-400 ml-2"></i>
                                        <span class="text-sm font-medium text-gray-900 dark:text-white font-mono">{{ $backup['name'] }}</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                    {{ number_format($backup['size'] / 1024 / 1024, 2) }} MB
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                    {{ \Carbon\Carbon::parse($backup['created_at'])->format('Y/m/d H:i') }}
                                    <br>
                                    <span class="text-xs text-gray-400">{{ \Carbon\Carbon::parse($backup['created_at'])->diffForHumans() }}</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center gap-2">
                                        <a href="{{ route('admin.database.backup.download', $backup['name']) }}" 
                                           class="inline-flex items-center px-3 py-1.5 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors">
                                            <i class="fas fa-download ml-1"></i>
                                            تحميل
                                        </a>
                                        
                                        <form action="{{ route('admin.database.backup.delete', $backup['name']) }}" method="POST" class="inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" 
                                                    class="inline-flex items-center px-3 py-1.5 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors"
                                                    onclick="return confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟')">
                                                <i class="fas fa-trash ml-1"></i>
                                                حذف
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="px-6 py-12 text-center text-gray-500 dark:text-gray-400">
                <i class="fas fa-database text-4xl mb-4"></i>
                <p class="text-lg font-medium mb-2">لا توجد نسخ احتياطية</p>
                <p>ابدأ بإنشاء أول نسخة احتياطية لقاعدة البيانات</p>
                
                <form action="{{ route('admin.database.backup.create') }}" method="POST" class="inline-block mt-4">
                    @csrf
                    <button type="submit" class="btn-gradient text-white px-6 py-3 rounded-lg hover:opacity-90 transition-colors">
                        إنشاء نسخة احتياطية الآن
                    </button>
                </form>
            </div>
        @endif
    </div>
</div>
@endsection
