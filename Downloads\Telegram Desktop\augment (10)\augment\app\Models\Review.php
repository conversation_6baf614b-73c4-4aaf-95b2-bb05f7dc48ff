<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Review extends Model
{
    use HasFactory;

    protected $fillable = [
        'reviewer_id',
        'reviewed_id',
        'type',
        'rating',
        'comment',
        'status',
        'admin_notes',
        'approved_at',
        'approved_by',
        'employer_response',
        'responded_at'
    ];

    protected $casts = [
        'approved_at' => 'datetime',
        'responded_at' => 'datetime',
        'rating' => 'integer'
    ];

    /**
     * المراجع (الذي كتب التقييم)
     */
    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewer_id');
    }

    /**
     * المراجع (الذي تم تقييمه)
     */
    public function reviewed(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_id');
    }

    /**
     * المدير الذي وافق على التقييم
     */
    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Scope للتقييمات المعتمدة
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope للتقييمات المعلقة
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope لتقييمات الشركات
     */
    public function scopeCompanyReviews($query)
    {
        return $query->where('type', 'company');
    }

    /**
     * Scope لتقييمات الملفات الشخصية
     */
    public function scopeProfileReviews($query)
    {
        return $query->where('type', 'profile');
    }

    /**
     * الحصول على نص الحالة
     */
    public function getStatusTextAttribute()
    {
        return match($this->status) {
            'pending' => 'في الانتظار',
            'approved' => 'معتمد',
            'rejected' => 'مرفوض',
            default => 'غير محدد'
        };
    }

    /**
     * الحصول على لون الحالة
     */
    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'pending' => 'warning',
            'approved' => 'success',
            'rejected' => 'danger',
            default => 'secondary'
        };
    }
}
