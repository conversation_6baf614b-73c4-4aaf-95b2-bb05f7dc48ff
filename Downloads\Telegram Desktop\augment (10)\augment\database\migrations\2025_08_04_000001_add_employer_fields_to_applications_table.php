<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('applications', function (Blueprint $table) {
            $table->text('employer_notes')->nullable()->after('admin_notes');
            $table->integer('employer_rating')->nullable()->after('employer_notes');
            $table->timestamp('reviewed_at')->nullable()->after('employer_rating');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('applications', function (Blueprint $table) {
            $table->dropColumn(['employer_notes', 'employer_rating', 'reviewed_at']);
        });
    }
};
