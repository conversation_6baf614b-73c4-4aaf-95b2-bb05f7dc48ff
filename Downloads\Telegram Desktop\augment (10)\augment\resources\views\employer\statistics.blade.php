@extends('layouts.employer')

@section('title', 'الإحصائيات والتقارير - Hire Me')
@section('header_title', 'الإحصائيات والتقارير')

@section('content')
<div class="p-8 space-y-8">
    <!-- Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">الإحصائيات والتقارير</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">تحليل مفصل لأداء الوظائف والطلبات</p>
        </div>
        <div class="flex gap-3 mt-4 md:mt-0">
            <button onclick="showExportModal()" class="px-6 py-3 bg-green-600 text-white rounded-xl hover:bg-green-700 transition-all duration-300 flex items-center gap-2">
                <i class="fas fa-download"></i>
                تصدير التقرير
            </button>
        </div>
    </div>

    <!-- الإحصائيات السريعة -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- إجمالي الوظائف -->
        <div class="stat-card p-6 rounded-2xl hover:scale-105 transition-all duration-300 animate-fade-in" style="animation-delay: 0.1s">
            <div class="flex items-center gap-4">
                <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg glow-effect">
                    <i class="fas fa-briefcase text-white text-xl"></i>
                </div>
                <div class="flex-1">
                    <p class="text-sm text-gray-600 dark:text-gray-400 font-medium">إجمالي الوظائف</p>
                    <p class="text-3xl font-bold text-gradient">{{ number_format($totalJobs ?? 0) }}</p>
                    <div class="flex items-center gap-1 mt-1">
                        <i class="fas fa-arrow-up text-green-500 text-xs"></i>
                        <span class="text-xs text-green-500 font-medium">الوظائف النشطة</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- الوظائف النشطة -->
        <div class="stat-card p-6 rounded-2xl hover:scale-105 transition-all duration-300 animate-fade-in" style="animation-delay: 0.2s">
            <div class="flex items-center gap-4">
                <div class="w-14 h-14 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg glow-effect">
                    <i class="fas fa-check-circle text-white text-xl"></i>
                </div>
                <div class="flex-1">
                    <p class="text-sm text-gray-600 dark:text-gray-400 font-medium">الوظائف النشطة</p>
                    <p class="text-3xl font-bold text-gradient">{{ number_format($activeJobs ?? 0) }}</p>
                    <div class="flex items-center gap-1 mt-1">
                        <i class="fas fa-circle text-green-500 text-xs animate-pulse"></i>
                        <span class="text-xs text-gray-500 dark:text-gray-400">متاحة للتقديم</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- إجمالي الطلبات -->
        <div class="stat-card p-6 rounded-2xl hover:scale-105 transition-all duration-300 animate-fade-in" style="animation-delay: 0.3s">
            <div class="flex items-center gap-4">
                <div class="w-14 h-14 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg glow-effect">
                    <i class="fas fa-users text-white text-xl"></i>
                </div>
                <div class="flex-1">
                    <p class="text-sm text-gray-600 dark:text-gray-400 font-medium">إجمالي الطلبات</p>
                    <p class="text-3xl font-bold text-gradient">{{ number_format($totalApplications ?? 0) }}</p>
                    <div class="flex items-center gap-1 mt-1">
                        <i class="fas fa-chart-line text-purple-500 text-xs"></i>
                        <span class="text-xs text-purple-500 font-medium">نمو مستمر</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- إجمالي المشاهدات -->
        <div class="stat-card p-6 rounded-2xl hover:scale-105 transition-all duration-300 animate-fade-in" style="animation-delay: 0.4s">
            <div class="flex items-center gap-4">
                <div class="w-14 h-14 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center shadow-lg glow-effect">
                    <i class="fas fa-eye text-white text-xl"></i>
                </div>
                <div class="flex-1">
                    <p class="text-sm text-gray-600 dark:text-gray-400 font-medium">إجمالي المشاهدات</p>
                    <p class="text-3xl font-bold text-gradient">{{ number_format($totalViews ?? 0) }}</p>
                    <div class="flex items-center gap-1 mt-1">
                        <i class="fas fa-eye text-orange-500 text-xs"></i>
                        <span class="text-xs text-orange-500 font-medium">مشاهدات الوظائف</span>
                    </div>
                </div>
            </div>
        </div>
    </div>





    <!-- Monthly Applications Chart -->
    <div class="glass-card rounded-xl p-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
            <i class="fas fa-chart-line text-purple-600"></i>
            الطلبات الشهرية
        </h2>

        <div class="h-64 flex items-end justify-between gap-2">
            @if(isset($monthlyApplications))
                @foreach($monthlyApplications as $month)
                <div class="flex flex-col items-center flex-1">
                    <div class="w-full bg-gradient-to-t from-blue-500 to-blue-600 rounded-t-lg relative group cursor-pointer"
                         style="height: {{ $month['count'] > 0 ? ($month['count'] / max(array_column($monthlyApplications, 'count'))) * 200 : 5 }}px;">
                        <div class="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">
                            {{ $month['count'] }} طلب
                        </div>
                    </div>
                    <p class="text-xs text-gray-600 dark:text-gray-400 mt-2 text-center">{{ $month['month'] }}</p>
                </div>
                @endforeach
            @else
                <div class="w-full text-center py-8">
                    <p class="text-gray-500 dark:text-gray-400">لا توجد بيانات للعرض</p>
                </div>
            @endif
        </div>
    </div>

    <!-- Top Performing Jobs -->
    <div class="glass-card rounded-xl p-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
            <i class="fas fa-trophy text-yellow-600"></i>
            أفضل الوظائف أداءً
        </h2>

        @if(isset($topJobs) && $topJobs->count() > 0)
        <div class="space-y-4">
            @foreach($topJobs as $index => $job)
            <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <div class="flex items-center gap-4">
                    <div class="w-8 h-8 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                        {{ $index + 1 }}
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-900 dark:text-white">{{ $job->title }}</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">{{ $job->location }}</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="text-lg font-bold text-gray-900 dark:text-white">{{ $job->applications_count }}</p>
                    <p class="text-sm text-gray-600 dark:text-gray-400">طلب</p>
                </div>
            </div>
            @endforeach
        </div>
        @else
        <div class="text-center py-8">
            <i class="fas fa-chart-bar text-gray-400 text-4xl mb-4"></i>
            <p class="text-gray-500 dark:text-gray-400">لا توجد وظائف للعرض</p>
        </div>
        @endif
    </div>

    <!-- Performance Metrics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Response Rate -->
        <div class="glass-card rounded-xl p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                <i class="fas fa-reply text-blue-600"></i>
                معدل الاستجابة
            </h3>

            @php
                $totalApplications = $applicationStats['total'] ?? 0;
                $respondedApplications = ($applicationStats['reviewed'] ?? 0) + ($applicationStats['accepted'] ?? 0) + ($applicationStats['rejected'] ?? 0);
                $responseRate = $totalApplications > 0 ? round(($respondedApplications / $totalApplications) * 100, 1) : 0;
            @endphp

            <div class="flex items-center gap-4">
                <div class="flex-1">
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                        <div class="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full transition-all duration-500"
                             style="width: {{ $responseRate }}%"></div>
                    </div>
                </div>
                <span class="text-2xl font-bold text-gray-900 dark:text-white">{{ $responseRate }}%</span>
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">
                {{ $respondedApplications }} من {{ $totalApplications }} طلب تم الرد عليه
            </p>
        </div>

        <!-- Acceptance Rate -->
        <div class="glass-card rounded-xl p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                <i class="fas fa-check-circle text-green-600"></i>
                معدل القبول
            </h3>

            @php
                $acceptanceRate = $totalApplications > 0 ? round((($applicationStats['accepted'] ?? 0) / $totalApplications) * 100, 1) : 0;
            @endphp

            <div class="flex items-center gap-4">
                <div class="flex-1">
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                        <div class="bg-gradient-to-r from-green-500 to-green-600 h-3 rounded-full transition-all duration-500"
                             style="width: {{ $acceptanceRate }}%"></div>
                    </div>
                </div>
                <span class="text-2xl font-bold text-gray-900 dark:text-white">{{ $acceptanceRate }}%</span>
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">
                {{ $applicationStats['accepted'] ?? 0 }} طلب مقبول من {{ $totalApplications }}
            </p>
        </div>
    </div>
</div>

<!-- Export Modal -->
<div id="exportModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
    <div class="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-md w-full mx-4">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">تصدير التقرير</h3>
            <button onclick="hideExportModal()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    اختر صيغة التصدير
                </label>
                <div class="space-y-2">
                    <label class="flex items-center">
                        <input type="radio" name="export_format" value="csv" checked class="text-green-600 focus:ring-green-500">
                        <span class="mr-2 text-gray-700 dark:text-gray-300">CSV (Excel) - ملخص الإحصائيات</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="export_format" value="pdf" class="text-green-600 focus:ring-green-500">
                        <span class="mr-2 text-gray-700 dark:text-gray-300">PDF - تقرير مفصل</span>
                    </label>
                </div>
            </div>

            <div class="flex gap-3 pt-4">
                <button onclick="exportReport()" class="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-download mr-2"></i>
                    تصدير
                </button>
                <button onclick="hideExportModal()" class="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    إلغاء
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function showExportModal() {
    document.getElementById('exportModal').classList.remove('hidden');
}

function hideExportModal() {
    document.getElementById('exportModal').classList.add('hidden');
}

function exportReport() {
    // الحصول على الصيغة المختارة
    const selectedFormat = document.querySelector('input[name="export_format"]:checked').value;

    // إظهار رسالة تحميل
    const button = event.target.closest('button');
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التصدير...';
    button.disabled = true;

    // إنشاء نموذج مخفي لإرسال طلب POST
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{{ route("employer.statistics.export") }}';

    // إضافة CSRF token
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = '{{ csrf_token() }}';
    form.appendChild(csrfToken);

    // إضافة صيغة التصدير
    const formatInput = document.createElement('input');
    formatInput.type = 'hidden';
    formatInput.name = 'format';
    formatInput.value = selectedFormat;
    form.appendChild(formatInput);

    // إضافة النموذج إلى الصفحة وإرساله
    document.body.appendChild(form);
    form.submit();

    // إخفاء modal وإعادة تعيين الزر
    setTimeout(() => {
        document.body.removeChild(form);
        button.innerHTML = originalText;
        button.disabled = false;
        hideExportModal();

        // إظهار رسالة نجاح
        showSuccessMessage('تم تصدير التقرير بنجاح!');
    }, 2000);
}

// إغلاق modal عند النقر خارجه
document.getElementById('exportModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideExportModal();
    }
});

// وظيفة إظهار رسالة النجاح
function showSuccessMessage(message) {
    // إنشاء عنصر الرسالة
    const successDiv = document.createElement('div');
    successDiv.className = 'success-message fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 flex items-center gap-2';
    successDiv.innerHTML = `
        <i class="fas fa-check-circle"></i>
        <span>${message}</span>
    `;

    // إضافة الرسالة إلى الصفحة
    document.body.appendChild(successDiv);

    // إزالة الرسالة بعد 3 ثوان
    setTimeout(() => {
        if (document.body.contains(successDiv)) {
            successDiv.style.animation = 'slideOutRight 0.3s ease-in forwards';
            setTimeout(() => {
                if (document.body.contains(successDiv)) {
                    document.body.removeChild(successDiv);
                }
            }, 300);
        }
    }, 3000);
}
</script>
@endsection

@push('styles')
<style>
.stat-card {
    transition: all 0.3s ease-in-out;
}

/* تحسينات modal التصدير */
#exportModal {
    backdrop-filter: blur(4px);
}

#exportModal .bg-white {
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسينات أزرار الراديو */
input[type="radio"] {
    width: 18px;
    height: 18px;
}

/* تحسينات رسائل النجاح */
.success-message {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOutRight {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100px);
    }
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

/* تأثيرات الإحصائيات السريعة */
.text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glow-effect {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.animate-fade-in {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
    transform: translateY(20px);
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
@endpush
