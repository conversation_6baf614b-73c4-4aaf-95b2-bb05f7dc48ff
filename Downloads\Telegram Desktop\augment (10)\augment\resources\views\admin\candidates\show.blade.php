@extends('layouts.admin')

@section('title', 'تفاصيل المرشح - Hire Me')
@section('header_title', 'تفاصيل المرشح')

@section('content')
<div class="p-6">
    <!-- Candidate Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
            <h2 class="text-2xl font-bold">{{ $candidate->name ?? 'أحمد محمد' }}</h2>
            <p class="text-gray-600 dark:text-gray-400 mt-1">
                <span>{{ $candidate->title ?? 'مطور واجهات أمامية' }}</span> • 
                <span>{{ $candidate->location ?? 'طرابلس، ليبيا' }}</span>
            </p>
        </div>
        <div class="mt-4 md:mt-0 flex gap-3">
            <button class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors flex items-center gap-2">
                <i class="fas fa-envelope"></i>
                <span>إرسال بريد</span>
            </button>
            <button class="px-4 py-2 gradient-bg text-white rounded-lg hover:opacity-90 transition-colors flex items-center gap-2">
                <i class="fas fa-user-plus"></i>
                <span>إنشاء طلب</span>
            </button>
        </div>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Candidate Details -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Resume Overview -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-bold">ملخص السيرة الذاتية</h3>
                    <div>
                        <a href="#" class="text-primary hover:underline text-sm flex items-center gap-1">
                            <i class="fas fa-download"></i>
                            <span>تحميل السيرة الذاتية</span>
                        </a>
                    </div>
                </div>
                
                <!-- Summary -->
                <div class="mb-6">
                    <h4 class="text-md font-semibold mb-2">نبذة مختصرة</h4>
                    <p class="text-gray-600 dark:text-gray-400">
                        {{ $candidate->summary ?? 'مطور واجهات أمامية ذو خبرة تزيد عن 5 سنوات في تطوير تطبيقات الويب التفاعلية. متخصص في React وVue.js وJavaScript وCSS. شغوف بتطوير واجهات مستخدم سلسة وسهلة الاستخدام مع الحفاظ على أفضل الممارسات وكتابة كود نظيف وقابل للصيانة. لدي خبرة في العمل ضمن فرق متعددة التخصصات وإنجاز المشاريع في المواعيد المحددة.' }}
                    </p>
                </div>
                
                <!-- Work Experience -->
                <div class="mb-6">
                    <h4 class="text-md font-semibold mb-4">الخبرات العملية</h4>
                    <div class="space-y-4">
                        @foreach($candidate->experiences ?? [] as $experience)
                            <div class="border-r-2 border-primary pr-4 relative">
                                <div class="absolute top-0 right-[-9px] w-4 h-4 rounded-full bg-primary"></div>
                                <h5 class="font-medium">{{ $experience->title }} <span class="text-gray-600 dark:text-gray-400 font-normal">في {{ $experience->company }}</span></h5>
                                <p class="text-sm text-gray-500 dark:text-gray-400">{{ $experience->period }}</p>
                                <p class="mt-1 text-gray-600 dark:text-gray-400">{{ $experience->description }}</p>
                            </div>
                        @endforeach
                        
                        @if(!isset($candidate->experiences) || count($candidate->experiences) == 0)
                            <div class="border-r-2 border-primary pr-4 relative">
                                <div class="absolute top-0 right-[-9px] w-4 h-4 rounded-full bg-primary"></div>
                                <h5 class="font-medium">مطور واجهات أمامية <span class="text-gray-600 dark:text-gray-400 font-normal">في شركة تقنية المستقبل</span></h5>
                                <p class="text-sm text-gray-500 dark:text-gray-400">يناير 2021 - حتى الآن</p>
                                <p class="mt-1 text-gray-600 dark:text-gray-400">تطوير واجهات المستخدم لمنصات الشركة باستخدام React.js. تحسين أداء التطبيقات وتجربة المستخدم، والعمل مع فريق خلفي لدمج واجهات برمجة التطبيقات.</p>
                            </div>
                            
                            <div class="border-r-2 border-primary pr-4 relative">
                                <div class="absolute top-0 right-[-9px] w-4 h-4 rounded-full bg-primary"></div>
                                <h5 class="font-medium">مطور ويب <span class="text-gray-600 dark:text-gray-400 font-normal">في شركة الحلول الرقمية</span></h5>
                                <p class="text-sm text-gray-500 dark:text-gray-400">يونيو 2018 - ديسمبر 2020</p>
                                <p class="mt-1 text-gray-600 dark:text-gray-400">تطوير مواقع ويب تفاعلية باستخدام HTML, CSS, JavaScript وVue.js. العمل على مشاريع متعددة لعملاء في قطاعات مختلفة.</p>
                            </div>
                            
                            <div class="border-r-2 border-primary pr-4 relative">
                                <div class="absolute top-0 right-[-9px] w-4 h-4 rounded-full bg-primary"></div>
                                <h5 class="font-medium">متدرب مطور ويب <span class="text-gray-600 dark:text-gray-400 font-normal">في شركة برمجيات الخليج</span></h5>
                                <p class="text-sm text-gray-500 dark:text-gray-400">يناير 2018 - مايو 2018</p>
                                <p class="mt-1 text-gray-600 dark:text-gray-400">تدريب عملي على تطوير الويب، المشاركة في تطوير مشاريع صغيرة واكتساب خبرة في JavaScript وHTML/CSS.</p>
                            </div>
                        @endif
                    </div>
                </div>
                
                <!-- Education -->
                <div class="mb-6">
                    <h4 class="text-md font-semibold mb-4">التعليم</h4>
                    <div class="space-y-4">
                        @foreach($candidate->education ?? [] as $edu)
                            <div class="border-r-2 border-primary pr-4 relative">
                                <div class="absolute top-0 right-[-9px] w-4 h-4 rounded-full bg-primary"></div>
                                <h5 class="font-medium">{{ $edu->degree }} <span class="text-gray-600 dark:text-gray-400 font-normal">من {{ $edu->institution }}</span></h5>
                                <p class="text-sm text-gray-500 dark:text-gray-400">{{ $edu->period }}</p>
                                @if($edu->description)
                                    <p class="mt-1 text-gray-600 dark:text-gray-400">{{ $edu->description }}</p>
                                @endif
                            </div>
                        @endforeach
                        
                        @if(!isset($candidate->education) || count($candidate->education) == 0)
                            <div class="border-r-2 border-primary pr-4 relative">
                                <div class="absolute top-0 right-[-9px] w-4 h-4 rounded-full bg-primary"></div>
                                <h5 class="font-medium">بكالوريوس علوم الحاسب <span class="text-gray-600 dark:text-gray-400 font-normal">من جامعة الملك سعود</span></h5>
                                <p class="text-sm text-gray-500 dark:text-gray-400">2014 - 2018</p>
                                <p class="mt-1 text-gray-600 dark:text-gray-400">تخصص في تطوير البرمجيات مع تركيز على تقنيات الويب. مشروع التخرج: تطوير منصة تعليمية تفاعلية.</p>
                            </div>
                        @endif
                    </div>
                </div>
                
                <!-- Skills -->
                <div>
                    <h4 class="text-md font-semibold mb-4">المهارات</h4>
                    <div class="flex flex-wrap gap-2">
                        @foreach($candidate->skills ?? ['HTML', 'CSS', 'JavaScript', 'React', 'Vue.js', 'Git', 'Responsive Design', 'UI/UX', 'Webpack', 'TypeScript'] as $skill)
                            <span class="px-3 py-1.5 bg-primary/10 text-primary rounded-full text-sm">{{ $skill }}</span>
                        @endforeach
                    </div>
                </div>
            </div>
            
            <!-- Files & Documents -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">الملفات والمستندات</h3>
                
                <div class="space-y-4">
                    @foreach($candidate->documents ?? [] as $document)
                        <div class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 rounded-lg bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
                                    <i class="fas fa-{{ $document->icon }} text-{{ $document->color }}-500"></i>
                                </div>
                                <div>
                                    <p class="font-medium">{{ $document->name }}</p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ $document->size }} • {{ $document->uploaded_at }}</p>
                                </div>
                            </div>
                            <a href="{{ $document->url }}" download class="text-primary hover:text-primary/80">
                                <i class="fas fa-download"></i>
                            </a>
                        </div>
                    @endforeach
                    
                    @if(!isset($candidate->documents) || count($candidate->documents) == 0)
                        <div class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 rounded-lg bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
                                    <i class="fas fa-file-pdf text-red-500"></i>
                                </div>
                                <div>
                                    <p class="font-medium">السيرة الذاتية</p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">2.4 MB • تم الرفع منذ 3 أيام</p>
                                </div>
                            </div>
                            <a href="#" download class="text-primary hover:text-primary/80">
                                <i class="fas fa-download"></i>
                            </a>
                        </div>
                        
                        <div class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 rounded-lg bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
                                    <i class="fas fa-file-word text-blue-500"></i>
                                </div>
                                <div>
                                    <p class="font-medium">خطاب تعريف</p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">500 KB • تم الرفع منذ 3 أيام</p>
                                </div>
                            </div>
                            <a href="#" download class="text-primary hover:text-primary/80">
                                <i class="fas fa-download"></i>
                            </a>
                        </div>
                        
                        <div class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 rounded-lg bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
                                    <i class="fas fa-file-image text-green-500"></i>
                                </div>
                                <div>
                                    <p class="font-medium">شهادة البكالوريوس</p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">1.2 MB • تم الرفع منذ 3 أيام</p>
                                </div>
                            </div>
                            <a href="#" download class="text-primary hover:text-primary/80">
                                <i class="fas fa-download"></i>
                            </a>
                        </div>
                    @endif
                </div>
            </div>
            
            <!-- Notes & Evaluation -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold">الملاحظات والتقييم</h3>
                    <div class="flex items-center gap-1">
                        <button class="px-2 py-1 text-sm text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-primary transition-colors" id="editNotes">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Rating -->
                <div class="flex items-center gap-2 mb-4">
                    <p class="text-gray-600 dark:text-gray-400">التقييم:</p>
                    <div class="flex items-center gap-1">
                        @for($i = 1; $i <= 5; $i++)
                            <button class="rating-star {{ $i <= ($candidate->rating ?? 4) ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600' }}" data-rating="{{ $i }}">
                                <i class="fas fa-star"></i>
                            </button>
                        @endfor
                    </div>
                </div>
                
                <!-- Notes -->
                <div id="notesDisplay" class="text-gray-600 dark:text-gray-400">
                    <p>{{ $candidate->notes ?? 'مرشح ممتاز مع خبرة قوية في تطوير الواجهات الأمامية. يمتلك مهارات تقنية متقدمة في React وVue.js، مع فهم جيد لتجربة المستخدم. أوصي بالنظر في ترشيحه للوظائف التي تتطلب مهارات متقدمة في تطوير الواجهات. من المحتمل أن يكون إضافة قيّمة للفريق بسبب خبرته المتنوعة ومهاراته التقنية المتميزة.' }}</p>
                </div>
                
                <!-- Edit Notes Form (hidden by default) -->
                <div id="notesForm" class="hidden mt-3">
                    <textarea id="notesInput" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" rows="4">{{ $candidate->notes ?? 'مرشح ممتاز مع خبرة قوية في تطوير الواجهات الأمامية. يمتلك مهارات تقنية متقدمة في React وVue.js، مع فهم جيد لتجربة المستخدم. أوصي بالنظر في ترشيحه للوظائف التي تتطلب مهارات متقدمة في تطوير الواجهات. من المحتمل أن يكون إضافة قيّمة للفريق بسبب خبرته المتنوعة ومهاراته التقنية المتميزة.' }}</textarea>
                    <div class="flex justify-end mt-3 gap-2">
                        <button id="cancelEdit" class="px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-sm">
                            إلغاء
                        </button>
                        <button id="saveNotes" class="px-3 py-1.5 gradient-bg text-white rounded-lg hover:opacity-90 transition-colors text-sm">
                            حفظ
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Timeline -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">النشاط</h3>
                
                <div class="space-y-6">
                    @foreach($candidate->activities ?? [] as $activity)
                        <div class="flex gap-4">
                            <div class="w-10 h-10 rounded-full bg-{{ $activity->color }}-100 dark:bg-{{ $activity->color }}-900/30 flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-{{ $activity->icon }} text-{{ $activity->color }}-600 dark:text-{{ $activity->color }}-400"></i>
                            </div>
                            <div>
                                <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1">
                                    <h4 class="font-medium">{{ $activity->title }}</h4>
                                    <span class="text-xs text-gray-500 dark:text-gray-400">{{ $activity->date }}</span>
                                </div>
                                <p class="text-gray-600 dark:text-gray-400 mt-1">{{ $activity->description }}</p>
                            </div>
                        </div>
                    @endforeach
                    
                    @if(!isset($candidate->activities) || count($candidate->activities) == 0)
                        <div class="flex gap-4">
                            <div class="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-plus text-blue-600 dark:text-blue-400"></i>
                            </div>
                            <div>
                                <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1">
                                    <h4 class="font-medium">تمت إضافة المرشح</h4>
                                    <span class="text-xs text-gray-500 dark:text-gray-400">منذ 3 أيام</span>
                                </div>
                                <p class="text-gray-600 dark:text-gray-400 mt-1">تمت إضافة المرشح إلى قاعدة البيانات بواسطة أحمد علي</p>
                            </div>
                        </div>
                        
                        <div class="flex gap-4">
                            <div class="w-10 h-10 rounded-full bg-yellow-100 dark:bg-yellow-900/30 flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-star text-yellow-600 dark:text-yellow-400"></i>
                            </div>
                            <div>
                                <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1">
                                    <h4 class="font-medium">تم تحديث التقييم</h4>
                                    <span class="text-xs text-gray-500 dark:text-gray-400">منذ 3 أيام</span>
                                </div>
                                <p class="text-gray-600 dark:text-gray-400 mt-1">تم تغيير تقييم المرشح إلى 4 نجوم</p>
                            </div>
                        </div>
                        
                        <div class="flex gap-4">
                            <div class="w-10 h-10 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-tags text-green-600 dark:text-green-400"></i>
                            </div>
                            <div>
                                <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1">
                                    <h4 class="font-medium">تمت إضافة وسم</h4>
                                    <span class="text-xs text-gray-500 dark:text-gray-400">منذ 3 أيام</span>
                                </div>
                                <p class="text-gray-600 dark:text-gray-400 mt-1">تمت إضافة وسم "مرشح محتمل" إلى المرشح</p>
                            </div>
                        </div>
                        
                        <div class="flex gap-4">
                            <div class="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-envelope text-purple-600 dark:text-purple-400"></i>
                            </div>
                            <div>
                                <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1">
                                    <h4 class="font-medium">تم إرسال بريد إلكتروني</h4>
                                    <span class="text-xs text-gray-500 dark:text-gray-400">منذ يومين</span>
                                </div>
                                <p class="text-gray-600 dark:text-gray-400 mt-1">تم إرسال بريد إلكتروني "دعوة للمقابلة" للمرشح</p>
                            </div>
                        </div>
                        
                        <div class="flex gap-4">
                            <div class="w-10 h-10 rounded-full bg-indigo-100 dark:bg-indigo-900/30 flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-sticky-note text-indigo-600 dark:text-indigo-400"></i>
                            </div>
                            <div>
                                <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1">
                                    <h4 class="font-medium">تم إضافة ملاحظة</h4>
                                    <span class="text-xs text-gray-500 dark:text-gray-400">منذ يوم</span>
                                </div>
                                <p class="text-gray-600 dark:text-gray-400 mt-1">تم تحديث ملاحظات المرشح</p>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Candidate Information -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <div class="flex items-center gap-4 mb-6">
                    <div class="w-16 h-16 bg-{{ $candidate->color ?? 'blue' }}-100 dark:bg-{{ $candidate->color ?? 'blue' }}-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-user text-{{ $candidate->color ?? 'blue' }}-600 dark:text-{{ $candidate->color ?? 'blue' }}-400 text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold">{{ $candidate->name ?? 'أحمد محمد' }}</h3>
                        <p class="text-gray-600 dark:text-gray-400 text-sm">{{ $candidate->title ?? 'مطور واجهات أمامية' }}</p>
                    </div>
                </div>
                
                <div class="space-y-4">
                    <div class="flex items-start gap-3">
                        <div class="w-8 text-center text-gray-500 dark:text-gray-400 pt-0.5">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div>
                            <p class="text-gray-500 dark:text-gray-400 text-xs">البريد الإلكتروني</p>
                            <p class="font-medium">{{ $candidate->email ?? '<EMAIL>' }}</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start gap-3">
                        <div class="w-8 text-center text-gray-500 dark:text-gray-400 pt-0.5">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div>
                            <p class="text-gray-500 dark:text-gray-400 text-xs">رقم الهاتف</p>
                            <p class="font-medium">{{ $candidate->phone ?? '+966 50 123 4567' }}</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start gap-3">
                        <div class="w-8 text-center text-gray-500 dark:text-gray-400 pt-0.5">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div>
                            <p class="text-gray-500 dark:text-gray-400 text-xs">الموقع</p>
                            <p class="font-medium">{{ $candidate->location ?? 'طرابلس، ليبيا' }}</p>
                        </div>
                    </div>
                    
                    @if($candidate->website ?? false)
                    <div class="flex items-start gap-3">
                        <div class="w-8 text-center text-gray-500 dark:text-gray-400 pt-0.5">
                            <i class="fas fa-globe"></i>
                        </div>
                        <div>
                            <p class="text-gray-500 dark:text-gray-400 text-xs">الموقع الإلكتروني</p>
                            <a href="{{ $candidate->website }}" target="_blank" class="font-medium text-primary hover:underline">{{ $candidate->website_display ?? $candidate->website }}</a>
                        </div>
                    </div>
                    @endif
                    
                    @if($candidate->linkedin ?? false)
                    <div class="flex items-start gap-3">
                        <div class="w-8 text-center text-gray-500 dark:text-gray-400 pt-0.5">
                            <i class="fab fa-linkedin"></i>
                        </div>
                        <div>
                            <p class="text-gray-500 dark:text-gray-400 text-xs">لينكد إن</p>
                            <a href="{{ $candidate->linkedin }}" target="_blank" class="font-medium text-primary hover:underline">{{ $candidate->linkedin_display ?? $candidate->linkedin }}</a>
                        </div>
                    </div>
                    @endif
                    
                    @if($candidate->github ?? false)
                    <div class="flex items-start gap-3">
                        <div class="w-8 text-center text-gray-500 dark:text-gray-400 pt-0.5">
                            <i class="fab fa-github"></i>
                        </div>
                        <div>
                            <p class="text-gray-500 dark:text-gray-400 text-xs">جيتهب</p>
                            <a href="{{ $candidate->github }}" target="_blank" class="font-medium text-primary hover:underline">{{ $candidate->github_display ?? $candidate->github }}</a>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
            
            <!-- Tags & Status -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">الوسوم والحالة</h3>
                
                <div class="space-y-4">
                    <div>
                        <p class="text-gray-500 dark:text-gray-400 text-sm mb-2">الوسوم:</p>
                        <div class="flex flex-wrap gap-2">
                            @foreach($candidate->tags ?? [['name' => 'مرشح محتمل', 'color' => 'green'], ['name' => 'للمقابلة', 'color' => 'purple']] as $tag)
                                <span class="px-2 py-1 bg-{{ $tag['color'] }}-100 dark:bg-{{ $tag['color'] }}-900/30 text-{{ $tag['color'] }}-700 dark:text-{{ $tag['color'] }}-400 rounded-full text-xs">{{ $tag['name'] }}</span>
                            @endforeach
                        </div>
                    </div>
                    
                    <div class="flex justify-between items-center text-sm">
                        <span class="text-gray-500 dark:text-gray-400">تم الإضافة في:</span>
                        <span>{{ $candidate->created_at ?? '15 مايو، 2023' }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center text-sm">
                        <span class="text-gray-500 dark:text-gray-400">آخر تحديث:</span>
                        <span>{{ $candidate->updated_at ?? '3 يونيو، 2023' }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center text-sm">
                        <span class="text-gray-500 dark:text-gray-400">تمت الإضافة بواسطة:</span>
                        <span>{{ $candidate->added_by ?? 'أحمد علي' }}</span>
                    </div>
                    
                    <div class="pt-4">
                        <button class="w-full px-4 py-2 border border-primary text-primary bg-white dark:bg-gray-800 rounded-lg hover:bg-primary/10 transition-colors flex items-center gap-2 justify-center">
                            <i class="fas fa-tags"></i>
                            <span>إدارة الوسوم</span>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Related Jobs -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">وظائف ذات صلة</h3>
                
                <div class="space-y-4">
                    @foreach($candidate->related_jobs ?? [] as $job)
                        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-3">
                            <div class="flex items-center gap-3 mb-2">
                                <div class="w-10 h-10 {{ $job->color_class ?? 'gradient-bg' }} rounded-lg flex items-center justify-center text-white flex-shrink-0">
                                    <i class="fas fa-{{ $job->icon ?? 'code' }}"></i>
                                </div>
                                <div>
                                    <a href="{{ route('admin.jobs.show', $job->id) }}" class="font-medium hover:text-primary">{{ $job->title }}</a>
                                    <p class="text-gray-500 dark:text-gray-400 text-xs">{{ $job->company }}</p>
                                </div>
                            </div>
                            <div class="flex items-center text-xs text-gray-500 dark:text-gray-400 gap-4">
                                <span><i class="fas fa-map-marker-alt ml-1"></i>{{ $job->location }}</span>
                                <span><i class="fas fa-clock ml-1"></i>{{ $job->type }}</span>
                            </div>
                        </div>
                    @endforeach
                    
                    @if(!isset($candidate->related_jobs) || count($candidate->related_jobs) == 0)
                        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-3">
                            <div class="flex items-center gap-3 mb-2">
                                <div class="w-10 h-10 gradient-bg rounded-lg flex items-center justify-center text-white flex-shrink-0">
                                    <i class="fas fa-code"></i>
                                </div>
                                <div>
                                    <a href="{{ route('admin.jobs.show', 1) }}" class="font-medium hover:text-primary">مطور واجهات أمامية</a>
                                    <p class="text-gray-500 dark:text-gray-400 text-xs">شركة التقنية المتطورة</p>
                                </div>
                            </div>
                            <div class="flex items-center text-xs text-gray-500 dark:text-gray-400 gap-4">
                                <span><i class="fas fa-map-marker-alt ml-1"></i>طرابلس</span>
                                <span><i class="fas fa-clock ml-1"></i>دوام كامل</span>
                            </div>
                        </div>
                        
                        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-3">
                            <div class="flex items-center gap-3 mb-2">
                                <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center text-white flex-shrink-0">
                                    <i class="fas fa-laptop-code"></i>
                                </div>
                                <div>
                                    <a href="{{ route('admin.jobs.show', 2) }}" class="font-medium hover:text-primary">مطور واجهات أمامية (React)</a>
                                    <p class="text-gray-500 dark:text-gray-400 text-xs">شركة البرمجيات العالمية</p>
                                </div>
                            </div>
                            <div class="flex items-center text-xs text-gray-500 dark:text-gray-400 gap-4">
                                <span><i class="fas fa-map-marker-alt ml-1"></i>جدة</span>
                                <span><i class="fas fa-clock ml-1"></i>عن بعد</span>
                            </div>
                        </div>
                        
                        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-3">
                            <div class="flex items-center gap-3 mb-2">
                                <div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center text-white flex-shrink-0">
                                    <i class="fas fa-paint-brush"></i>
                                </div>
                                <div>
                                    <a href="{{ route('admin.jobs.show', 3) }}" class="font-medium hover:text-primary">مصمم واجهات المستخدم / مطور</a>
                                    <p class="text-gray-500 dark:text-gray-400 text-xs">استوديو التصميم الرقمي</p>
                                </div>
                            </div>
                            <div class="flex items-center text-xs text-gray-500 dark:text-gray-400 gap-4">
                                <span><i class="fas fa-map-marker-alt ml-1"></i>الرياض</span>
                                <span><i class="fas fa-clock ml-1"></i>دوام كامل</span>
                            </div>
                        </div>
                    @endif
                    
                    <div>
                        <button class="w-full px-4 py-2 gradient-bg text-white rounded-lg hover:opacity-90 transition-colors flex items-center gap-2 justify-center">
                            <i class="fas fa-plus"></i>
                            <span>اقتراح وظائف للمرشح</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Rating stars
    const ratingStars = document.querySelectorAll('.rating-star');
    
    ratingStars.forEach(star => {
        star.addEventListener('click', () => {
            const rating = parseInt(star.getAttribute('data-rating'));
            
            // Update UI
            ratingStars.forEach((s, index) => {
                if (index < rating) {
                    s.classList.add('text-yellow-400');
                    s.classList.remove('text-gray-300', 'dark:text-gray-600');
                } else {
                    s.classList.remove('text-yellow-400');
                    s.classList.add('text-gray-300', 'dark:text-gray-600');
                }
            });
            
            // Here you would normally send an AJAX request to update the rating
            console.log(`Updating candidate rating to: ${rating}`);
        });
    });
    
    // Edit Notes
    const editNotes = document.getElementById('editNotes');
    const notesDisplay = document.getElementById('notesDisplay');
    const notesForm = document.getElementById('notesForm');
    const notesInput = document.getElementById('notesInput');
    const cancelEdit = document.getElementById('cancelEdit');
    const saveNotes = document.getElementById('saveNotes');
    
    editNotes.addEventListener('click', () => {
        notesDisplay.classList.add('hidden');
        notesForm.classList.remove('hidden');
    });
    
    cancelEdit.addEventListener('click', () => {
        notesDisplay.classList.remove('hidden');
        notesForm.classList.add('hidden');
    });
    
    saveNotes.addEventListener('click', () => {
        const newNotes = notesInput.value;
        
        // Update display
        notesDisplay.textContent = newNotes;
        
        // Hide form, show display
        notesDisplay.classList.remove('hidden');
        notesForm.classList.add('hidden');
        
        // Here you would normally send an AJAX request to update the notes
        console.log(`Updating candidate notes to: ${newNotes}`);
    });
</script>
@endsection