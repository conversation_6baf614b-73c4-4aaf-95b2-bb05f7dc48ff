<!-- resources/views/auth/register.blade.php -->
@extends('layouts.auth')

@section('title', 'إنشاء حساب جديد')

@section('content')
<!-- Register Form -->
<div id="registerForm" class="bg-white dark:bg-dark-card rounded-b-2xl shadow-lg p-8">
    <h2 class="text-2xl font-bold mb-6 text-center">انضم إلينا اليوم!</h2>
    <p class="text-gray-600 dark:text-gray-400 text-center mb-8">أنشئ حسابك وابدأ رحلتك المهنية معنا</p>

    <!-- User Type Selection -->
    <div class="mb-8 max-w-md mx-auto">
        <label class="block text-gray-700 dark:text-gray-300 mb-2 font-medium">نوع الحساب</label>
        <div class="grid grid-cols-2 gap-4">
            <div class="relative">
                <input type="radio" name="userType" id="jobSeeker" class="hidden peer" checked />
                <label for="jobSeeker" class="flex flex-col items-center p-4 border-2 border-gray-300 dark:border-gray-700 rounded-lg cursor-pointer peer-checked:border-primary peer-checked:bg-primary/5 dark:peer-checked:bg-primary/10 hover:bg-gray-50 dark:hover:bg-gray-800 transition">
                    <i class="fas fa-user-tie text-2xl mb-2 text-gray-600 dark:text-gray-400 peer-checked:text-primary"></i>
                    <span class="text-lg font-medium">باحث عن عمل</span>
                </label>
            </div>
            <div class="relative">
                <input type="radio" name="userType" id="employer" class="hidden peer" />
                <label for="employer" class="flex flex-col items-center p-4 border-2 border-gray-300 dark:border-gray-700 rounded-lg cursor-pointer peer-checked:border-primary peer-checked:bg-primary/5 dark:peer-checked:bg-primary/10 hover:bg-gray-50 dark:hover:bg-gray-800 transition">
                    <i class="fas fa-building text-2xl mb-2 text-gray-600 dark:text-gray-400 peer-checked:text-primary"></i>
                    <span class="text-lg font-medium">صاحب عمل</span>
                </label>
            </div>
        </div>
        @error('role')
            <p class="text-red-500 text-xs mt-1 text-center">{{ $message }}</p>
        @enderror
    </div>

    <form action="{{ route('register') }}" method="POST" class="space-y-4 max-w-md mx-auto">
        @csrf
        <div>
            <label for="name" class="block text-gray-700 dark:text-gray-300 mb-2 font-medium">الاسم الكامل</label>
            <input type="text" id="name" name="name" value="{{ old('name') }}" class="w-full px-4 py-3 bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-2 focus:ring-primary focus:outline-none text-base @error('name') border-red-500 @enderror" placeholder="أدخل اسمك الكامل" required />
            @error('name')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div>
            <label for="email" class="block text-gray-700 dark:text-gray-300 mb-2 font-medium">البريد الإلكتروني</label>
            <div class="relative">
                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <i class="fas fa-envelope text-gray-400"></i>
                </div>
                <input type="email" id="email" name="email" value="{{ old('email') }}" class="w-full pl-4 pr-10 py-3 bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-2 focus:ring-primary focus:outline-none text-base @error('email') border-red-500 @enderror" placeholder="أدخل بريدك الإلكتروني" required />
            </div>
            @error('email')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div>
            <label for="password" class="block text-gray-700 dark:text-gray-300 mb-2 font-medium">كلمة المرور</label>
            <div class="relative">
                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <i class="fas fa-lock text-gray-400"></i>
                </div>
                <input type="password" id="password" name="password" class="w-full pl-4 pr-10 py-3 bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-2 focus:ring-primary focus:outline-none text-base @error('password') border-red-500 @enderror" placeholder="أدخل كلمة المرور" required />
                <button type="button" id="togglePassword" class="absolute inset-y-0 left-0 flex items-center pl-3">
                    <i class="fas fa-eye text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"></i>
                </button>
            </div>
            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">يجب أن تحتوي كلمة المرور على 8 أحرف على الأقل وتتضمن حرفًا كبيرًا ورقمًا ورمزًا خاصًا</p>
            @error('password')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div>
            <label for="password_confirmation" class="block text-gray-700 dark:text-gray-300 mb-2 font-medium">تأكيد كلمة المرور</label>
            <div class="relative">
                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <i class="fas fa-lock text-gray-400"></i>
                </div>
                <input type="password" id="password_confirmation" name="password_confirmation" class="w-full pl-4 pr-10 py-3 bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-2 focus:ring-primary focus:outline-none text-base" placeholder="أعد إدخال كلمة المرور" required />
                <button type="button" id="togglePasswordConfirmation" class="absolute inset-y-0 left-0 flex items-center pl-3">
                    <i class="fas fa-eye text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"></i>
                </button>
            </div>
        </div>

        <input type="hidden" name="role" id="roleInput" value="job_seeker">

        <div class="flex items-start mt-4">
            <input type="checkbox" id="terms" name="terms" class="w-4 h-4 mt-1 text-primary bg-gray-100 dark:bg-gray-800 rounded border-gray-300 focus:ring-primary" required />
            <label for="terms" class="mr-2 text-gray-700 dark:text-gray-300">
                أوافق على <a href="#" class="text-primary hover:underline">شروط الاستخدام</a> و <a href="#" class="text-primary hover:underline">سياسة الخصوصية</a>
            </label>
        </div>

        <button type="submit" class="w-full py-3 gradient-bg text-white rounded-lg hover:opacity-90 transition mt-6 font-medium text-lg">
            إنشاء حساب
        </button>

        <div class="relative flex items-center gap-4 py-4">
            <div class="flex-grow border-t border-gray-300 dark:border-gray-700"></div>
            <span class="text-gray-500 dark:text-gray-400 text-sm">أو التسجيل باستخدام</span>
            <div class="flex-grow border-t border-gray-300 dark:border-gray-700"></div>
        </div>

        <div class="grid grid-cols-3 gap-4">
            <a href="{{ route('social.login', 'google') }}" class="flex justify-center items-center gap-2 py-2.5 border border-gray-300 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition">
                <i class="fab fa-google text-red-500"></i>
                <span>Google</span>
            </a>
            <a href="{{ route('social.login', 'facebook') }}" class="flex justify-center items-center gap-2 py-2.5 border border-gray-300 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition">
                <i class="fab fa-facebook text-blue-600"></i>
                <span>Facebook</span>
            </a>
            <a href="{{ route('social.login', 'linkedin') }}" class="flex justify-center items-center gap-2 py-2.5 border border-gray-300 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition">
                <i class="fab fa-linkedin text-blue-700"></i>
                <span>LinkedIn</span>
            </a>
        </div>

        @if ($errors->has('error'))
        <div class="mt-4 p-3 bg-red-100 text-red-700 rounded-lg text-center">
            {{ $errors->first('error') }}
        </div>
        @endif

        <p class="text-center mt-6 text-gray-600 dark:text-gray-400">
            لديك حساب بالفعل؟ <a href="{{ route('login') }}" class="text-primary font-medium hover:underline">تسجيل الدخول</a>
        </p>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Toggle password visibility for main password
    const togglePassword = document.getElementById('togglePassword');
    const password = document.getElementById('password');

    togglePassword.addEventListener('click', function() {
        const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
        password.setAttribute('type', type);

        // Toggle icon
        const eyeIcon = this.querySelector('i');
        eyeIcon.classList.toggle('fa-eye');
        eyeIcon.classList.toggle('fa-eye-slash');
    });

    // Toggle password visibility for confirmation password
    const togglePasswordConfirmation = document.getElementById('togglePasswordConfirmation');
    const passwordConfirmation = document.getElementById('password_confirmation');

    togglePasswordConfirmation.addEventListener('click', function() {
        const type = passwordConfirmation.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordConfirmation.setAttribute('type', type);

        // Toggle icon
        const eyeIcon = this.querySelector('i');
        eyeIcon.classList.toggle('fa-eye');
        eyeIcon.classList.toggle('fa-eye-slash');
    });

    // User type selection highlighting and role input update
    const jobSeeker = document.getElementById('jobSeeker');
    const employer = document.getElementById('employer');
    const roleInput = document.getElementById('roleInput');

    // Ensure the label changes appear when radio buttons are clicked
    jobSeeker.addEventListener('change', function() {
        if(this.checked) {
            document.querySelector('label[for="jobSeeker"] i').classList.add('text-primary');
            document.querySelector('label[for="employer"] i').classList.remove('text-primary');
            roleInput.value = 'job_seeker';
        }
    });

    employer.addEventListener('change', function() {
        if(this.checked) {
            document.querySelector('label[for="employer"] i').classList.add('text-primary');
            document.querySelector('label[for="jobSeeker"] i').classList.remove('text-primary');
            roleInput.value = 'employer';
        }
    });
</script>
@endsection