<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير الوظائف - {{ $employer->company_name ?? $employer->name }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #3b82f6;
        }

        .header h1 {
            color: #1e40af;
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header .company-name {
            font-size: 20px;
            color: #6b7280;
            margin-bottom: 5px;
        }

        .header .report-date {
            font-size: 14px;
            color: #9ca3af;
        }

        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .summary-card h3 {
            font-size: 14px;
            margin-bottom: 10px;
            opacity: 0.9;
        }

        .summary-card .number {
            font-size: 32px;
            font-weight: bold;
        }

        .section {
            margin-bottom: 30px;
        }

        .section h2 {
            color: #1e40af;
            font-size: 20px;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e5e7eb;
        }

        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        th {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 15px 10px;
            text-align: right;
            font-weight: 600;
            font-size: 14px;
        }

        td {
            padding: 12px 10px;
            border-bottom: 1px solid #e5e7eb;
            font-size: 13px;
        }

        tr:nth-child(even) {
            background-color: #f8fafc;
        }

        tr:hover {
            background-color: #f1f5f9;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 600;
            text-align: center;
            display: inline-block;
            min-width: 60px;
        }

        .status-active {
            background-color: #dcfce7;
            color: #166534;
        }

        .status-expired {
            background-color: #fef2f2;
            color: #dc2626;
        }

        .status-draft {
            background-color: #f3f4f6;
            color: #374151;
        }

        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #e5e7eb;
            text-align: center;
            color: #6b7280;
            font-size: 12px;
        }

        @media print {
            body {
                background-color: white;
                padding: 0;
            }
            
            .container {
                box-shadow: none;
                padding: 20px;
            }
            
            .summary {
                grid-template-columns: repeat(4, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>تقرير الوظائف</h1>
            <div class="company-name">{{ $employer->company_name ?? $employer->name }}</div>
            <div class="report-date">تاريخ التقرير: {{ $reportDate }}</div>
        </div>

        <!-- Summary -->
        <div class="summary">
            <div class="summary-card">
                <h3>إجمالي الوظائف</h3>
                <div class="number">{{ $totalJobs }}</div>
            </div>
            <div class="summary-card">
                <h3>الوظائف النشطة</h3>
                <div class="number">{{ $jobs->where('is_active', true)->where('expires_at', '>', now())->count() }}</div>
            </div>
            <div class="summary-card">
                <h3>الوظائف المنتهية</h3>
                <div class="number">{{ $jobs->where('expires_at', '<=', now())->count() }}</div>
            </div>
            <div class="summary-card">
                <h3>إجمالي الطلبات</h3>
                <div class="number">{{ $jobs->sum('applications_count') }}</div>
            </div>
        </div>

        <!-- Jobs Table -->
        <div class="section">
            <h2>قائمة الوظائف</h2>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>عنوان الوظيفة</th>
                            <th>الموقع</th>
                            <th>النوع</th>
                            <th>التصنيف</th>
                            <th>الحالة</th>
                            <th>عدد الطلبات</th>
                            <th>المشاهدات</th>
                            <th>تاريخ النشر</th>
                            <th>تاريخ الانتهاء</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($jobs as $job)
                        @php
                            $status = 'غير نشطة';
                            $statusClass = 'status-draft';
                            if ($job->is_active) {
                                if ($job->expires_at > now()) {
                                    $status = 'نشطة';
                                    $statusClass = 'status-active';
                                } else {
                                    $status = 'منتهية الصلاحية';
                                    $statusClass = 'status-expired';
                                }
                            }
                        @endphp
                        <tr>
                            <td><strong>{{ $job->title }}</strong></td>
                            <td>{{ $job->location ?? 'غير محدد' }}</td>
                            <td>{{ getJobTypeInArabic($job->job_type) }}</td>
                            <td>{{ $job->category->name ?? 'غير محدد' }}</td>
                            <td>
                                <span class="status-badge {{ $statusClass }}">
                                    {{ $status }}
                                </span>
                            </td>
                            <td>{{ $job->applications_count }}</td>
                            <td>{{ $job->views ?? 0 }}</td>
                            <td>{{ $job->created_at->format('Y-m-d') }}</td>
                            <td>{{ $job->expires_at ? $job->expires_at->format('Y-m-d') : 'غير محدد' }}</td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="9" style="text-align: center; color: #6b7280;">لا توجد وظائف</td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>تم إنشاء هذا التقرير بواسطة نظام Hire Me</p>
            <p>{{ now()->format('Y-m-d H:i:s') }}</p>
        </div>
    </div>
</body>
</html>
