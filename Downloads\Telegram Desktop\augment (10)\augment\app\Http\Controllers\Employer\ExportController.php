<?php

namespace App\Http\Controllers\Employer;

use App\Http\Controllers\Controller;
use App\Models\Application;
use App\Models\Job;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ExportController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:employer');
    }

    /**
     * تصدير بيانات الطلبات
     */
    public function applications(Request $request)
    {
        $user = Auth::user();
        $employer = $user->employer;

        if (!$employer) {
            return redirect()->route('employer.profile')->with('error', 'يجب إكمال ملف الشركة أولاً');
        }

        // تطبيق الفلاتر
        $query = Application::whereHas('job', function($q) use ($employer) {
            $q->where('employer_id', $employer->id);
        })->with(['jobSeeker.user', 'job']);

        // فلترة حسب الحالة
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // فلترة حسب الوظيفة
        if ($request->filled('job_id')) {
            $query->where('job_id', $request->job_id);
        }

        // فلترة حسب التاريخ
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $applications = $query->get();

        $format = $request->input('format', 'csv');

        if ($format === 'pdf') {
            return $this->exportApplicationsPDF($applications, $employer);
        } elseif ($format === 'json') {
            return $this->exportApplicationsJSON($applications);
        }

        // تصدير CSV (الافتراضي)
        return $this->exportApplicationsCSV($applications);
    }

    /**
     * تصدير الطلبات بصيغة CSV
     */
    private function exportApplicationsCSV($applications)
    {
        $filename = 'applications_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($applications) {
            $file = fopen('php://output', 'w');

            // إضافة BOM للدعم العربي
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            // العناوين
            fputcsv($file, [
                'اسم المتقدم',
                'البريد الإلكتروني',
                'رقم الهاتف',
                'الوظيفة',
                'الحالة',
                'تاريخ التقديم',
                'التقييم',
                'الملاحظات'
            ]);

            // البيانات
            foreach ($applications as $application) {
                $user = $application->jobSeeker && $application->jobSeeker->user ? $application->jobSeeker->user : null;
                fputcsv($file, [
                    $user ? $user->name : 'غير محدد',
                    $user ? $user->email : 'غير محدد',
                    $user ? ($user->phone ?? 'غير محدد') : 'غير محدد',
                    $application->job->title,
                    getApplicationStatusInArabic($application->status),
                    $application->created_at->format('Y-m-d H:i'),
                    $application->employer_rating ?? 'غير مقيم',
                    $application->employer_notes ?? ''
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * تصدير الطلبات بصيغة PDF
     */
    private function exportApplicationsPDF($applications, $employer)
    {
        $data = [
            'applications' => $applications,
            'employer' => $employer,
            'reportDate' => now()->format('Y-m-d H:i:s'),
            'totalApplications' => $applications->count()
        ];

        // إنشاء HTML للتقرير
        $html = view('employer.reports.applications-pdf', $data)->render();

        // للآن سنعيد HTML كاستجابة (يمكن تحسينه لاحقاً باستخدام مكتبة PDF)
        return response($html, 200, [
            'Content-Type' => 'text/html; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="applications_report_' . date('Y-m-d_H-i-s') . '.html"'
        ]);
    }

    /**
     * تصدير الطلبات بصيغة JSON
     */
    private function exportApplicationsJSON($applications)
    {
        $filename = 'applications_' . date('Y-m-d_H-i-s') . '.json';

        $data = $applications->map(function($application) {
            $user = $application->jobSeeker && $application->jobSeeker->user ? $application->jobSeeker->user : null;
            return [
                'applicant_name' => $user ? $user->name : 'غير محدد',
                'applicant_email' => $user ? $user->email : 'غير محدد',
                'applicant_phone' => $user ? ($user->phone ?? 'غير محدد') : 'غير محدد',
                'job_title' => $application->job->title,
                'status' => $application->status,
                'status_arabic' => getApplicationStatusInArabic($application->status),
                'applied_at' => $application->created_at->format('Y-m-d H:i'),
                'employer_rating' => $application->employer_rating,
                'employer_notes' => $application->employer_notes
            ];
        });

        return response()->json($data, 200, [
            'Content-Type' => 'application/json; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"'
        ]);
    }

    /**
     * تصدير بيانات الوظائف
     */
    public function jobs(Request $request)
    {
        $user = Auth::user();
        $employer = $user->employer;

        if (!$employer) {
            return redirect()->route('employer.profile')->with('error', 'يجب إكمال ملف الشركة أولاً');
        }

        // تطبيق الفلاتر
        $query = Job::where('employer_id', $employer->id)
                   ->withCount('applications')
                   ->with('category');

        // فلترة حسب الحالة
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where('is_active', true)->where('expires_at', '>', now());
            } elseif ($request->status === 'expired') {
                $query->where('expires_at', '<=', now());
            } elseif ($request->status === 'draft') {
                $query->where('is_active', false);
            }
        }

        // فلترة حسب التاريخ
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $jobs = $query->get();

        $format = $request->input('format', 'csv');

        if ($format === 'pdf') {
            return $this->exportJobsPDF($jobs, $employer);
        } elseif ($format === 'json') {
            return $this->exportJobsJSON($jobs);
        }

        // تصدير CSV (الافتراضي)
        return $this->exportJobsCSV($jobs);
    }

    /**
     * تصدير الوظائف بصيغة CSV
     */
    private function exportJobsCSV($jobs)
    {
        $filename = 'jobs_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($jobs) {
            $file = fopen('php://output', 'w');

            // إضافة BOM للدعم العربي
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            // العناوين
            fputcsv($file, [
                'عنوان الوظيفة',
                'الموقع',
                'نوع الوظيفة',
                'التصنيف',
                'الحالة',
                'عدد الطلبات',
                'عدد المشاهدات',
                'تاريخ النشر',
                'تاريخ الانتهاء',
                'نطاق الراتب'
            ]);

            // البيانات
            foreach ($jobs as $job) {
                $status = 'غير نشطة';
                if ($job->is_active) {
                    $status = $job->expires_at > now() ? 'نشطة' : 'منتهية الصلاحية';
                }

                fputcsv($file, [
                    $job->title,
                    $job->location ?? 'غير محدد',
                    getJobTypeInArabic($job->job_type),
                    $job->category->name ?? 'غير محدد',
                    $status,
                    $job->applications_count,
                    $job->views ?? 0,
                    $job->created_at->format('Y-m-d'),
                    $job->expires_at ? $job->expires_at->format('Y-m-d') : 'غير محدد',
                    $job->salary_range ?? 'غير محدد'
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * تصدير الوظائف بصيغة PDF
     */
    private function exportJobsPDF($jobs, $employer)
    {
        $data = [
            'jobs' => $jobs,
            'employer' => $employer,
            'reportDate' => now()->format('Y-m-d H:i:s'),
            'totalJobs' => $jobs->count()
        ];

        // إنشاء HTML للتقرير
        $html = view('employer.reports.jobs-pdf', $data)->render();

        // للآن سنعيد HTML كاستجابة (يمكن تحسينه لاحقاً باستخدام مكتبة PDF)
        return response($html, 200, [
            'Content-Type' => 'text/html; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="jobs_report_' . date('Y-m-d_H-i-s') . '.html"'
        ]);
    }

    /**
     * تصدير الوظائف بصيغة JSON
     */
    private function exportJobsJSON($jobs)
    {
        $filename = 'jobs_' . date('Y-m-d_H-i-s') . '.json';

        $data = $jobs->map(function($job) {
            $status = 'غير نشطة';
            if ($job->is_active) {
                $status = $job->expires_at > now() ? 'نشطة' : 'منتهية الصلاحية';
            }

            return [
                'title' => $job->title,
                'location' => $job->location,
                'job_type' => $job->job_type,
                'job_type_arabic' => getJobTypeInArabic($job->job_type),
                'category' => $job->category->name ?? null,
                'status' => $status,
                'applications_count' => $job->applications_count,
                'views' => $job->views ?? 0,
                'created_at' => $job->created_at->format('Y-m-d H:i'),
                'expires_at' => $job->expires_at ? $job->expires_at->format('Y-m-d H:i') : null,
                'salary_range' => $job->salary_range,
                'is_active' => $job->is_active
            ];
        });

        return response()->json($data, 200, [
            'Content-Type' => 'application/json; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"'
        ]);
    }
}
