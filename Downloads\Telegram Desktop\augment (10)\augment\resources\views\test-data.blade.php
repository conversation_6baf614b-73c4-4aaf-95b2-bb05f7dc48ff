@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-md p-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-6">إنشاء بيانات تجريبية</h1>
            
            <div class="space-y-6">
                <div class="p-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <h2 class="text-xl font-semibold text-blue-900 dark:text-blue-100 mb-3">
                        <i class="fas fa-info-circle ml-2"></i>
                        معلومات مهمة
                    </h2>
                    <p class="text-blue-800 dark:text-blue-200">
                        هذه الصفحة لإنشاء بيانات تجريبية لاختبار النظام. ستقوم بإنشاء:
                    </p>
                    <ul class="list-disc list-inside mt-3 text-blue-800 dark:text-blue-200 space-y-1">
                        <li>مستخدمين باحثين عن عمل</li>
                        <li>أصحاب عمل وشركات</li>
                        <li>وظائف متنوعة</li>
                        <li>طلبات توظيف</li>
                        <li>تقييمات وإشعارات</li>
                    </ul>
                </div>

                <form action="{{ route('create-test-data') }}" method="POST" class="space-y-6">
                    @csrf
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                عدد الباحثين عن عمل
                            </label>
                            <input type="number" name="job_seekers_count" value="6" min="1" max="20" 
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-800 dark:text-white">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                عدد أصحاب العمل
                            </label>
                            <input type="number" name="employers_count" value="5" min="1" max="15" 
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-800 dark:text-white">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                عدد الوظائف لكل صاحب عمل
                            </label>
                            <input type="number" name="jobs_per_employer" value="3" min="1" max="10" 
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-800 dark:text-white">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                عدد الطلبات لكل باحث عن عمل
                            </label>
                            <input type="number" name="applications_per_seeker" value="4" min="1" max="10" 
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-800 dark:text-white">
                        </div>
                    </div>

                    <div class="flex items-center">
                        <input type="checkbox" name="create_admin" id="create_admin" checked 
                               class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                        <label for="create_admin" class="mr-2 block text-sm text-gray-900 dark:text-gray-300">
                            إنشاء حساب مدير (<EMAIL> / Pp123456)
                        </label>
                    </div>

                    <div class="flex items-center">
                        <input type="checkbox" name="create_test_user" id="create_test_user" checked 
                               class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                        <label for="create_test_user" class="mr-2 block text-sm text-gray-900 dark:text-gray-300">
                            إنشاء حساب اختبار للمستخدم الحالي (<EMAIL> / Pp123456)
                        </label>
                    </div>

                    <div class="flex space-x-4">
                        <button type="submit" 
                                class="flex-1 bg-primary text-white py-3 px-6 rounded-lg hover:bg-primary-dark transition-colors font-medium">
                            <i class="fas fa-database ml-2"></i>
                            إنشاء البيانات التجريبية
                        </button>
                        
                        <a href="{{ route('home') }}" 
                           class="flex-1 bg-gray-500 text-white py-3 px-6 rounded-lg hover:bg-gray-600 transition-colors font-medium text-center">
                            <i class="fas fa-arrow-left ml-2"></i>
                            العودة للرئيسية
                        </a>
                    </div>
                </form>

                @if(session('success'))
                    <div class="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                        <div class="flex">
                            <i class="fas fa-check-circle text-green-400 ml-3 mt-0.5"></i>
                            <div>
                                <h3 class="text-sm font-medium text-green-800 dark:text-green-200">
                                    تم إنشاء البيانات بنجاح!
                                </h3>
                                <p class="mt-1 text-sm text-green-700 dark:text-green-300">
                                    {{ session('success') }}
                                </p>
                            </div>
                        </div>
                    </div>
                @endif

                @if(session('error'))
                    <div class="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                        <div class="flex">
                            <i class="fas fa-exclamation-circle text-red-400 ml-3 mt-0.5"></i>
                            <div>
                                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                                    حدث خطأ!
                                </h3>
                                <p class="mt-1 text-sm text-red-700 dark:text-red-300">
                                    {{ session('error') }}
                                </p>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
