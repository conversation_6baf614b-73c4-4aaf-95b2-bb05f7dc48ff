@extends('layouts.admin')

@section('title', 'إدارة الوظائف - Hire Me')
@section('header_title', 'إدارة الوظائف')

@section('content')
<div class="p-6">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
            <h2 class="text-2xl font-bold">قائمة الوظائف</h2>
            <p class="text-gray-600 dark:text-gray-400 mt-1">إدارة وتعديل الوظائف المنشورة</p>
        </div>
        <div class="mt-4 md:mt-0">
            <a href="{{ route('admin.jobs.create') }}" class="gradient-bg text-white px-5 py-2.5 rounded-lg inline-flex items-center gap-2 hover:bg-indigo-600 transition-colors">
                <i class="fas fa-plus"></i>
                <span>إضافة وظيفة جديدة</span>
            </a>
        </div>
    </div>

    <!-- Search and Filters -->
    <form action="{{ route('admin.jobs.index') }}" method="GET" class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6 mb-6">
        <div class="flex flex-col md:flex-row gap-4">
            <div class="flex-1">
                <div class="relative">
                    <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" name="search" id="search" value="{{ request('search') }}" class="block w-full pr-10 p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" placeholder="البحث عن وظيفة...">
                </div>
            </div>
            <div class="w-full md:w-48">
                <select name="status" id="statusFilter" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                    <option value="">الحالة</option>
                    <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>نشط</option>
                    <option value="expired" {{ request('status') == 'expired' ? 'selected' : '' }}>منتهي</option>
                </select>
            </div>
            <div class="w-full md:w-48">
                <select name="location" id="locationFilter" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                    <option value="">الموقع</option>
                    @foreach($locations ?? [] as $location)
                        <option value="{{ $location }}" {{ request('location') == $location ? 'selected' : '' }}>{{ $location }}</option>
                    @endforeach
                </select>
            </div>
            <div class="w-full md:w-48">
                <select name="type" id="jobTypeFilter" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                    <option value="">نوع الوظيفة</option>
                    @foreach($jobTypes ?? [] as $value => $label)
                        <option value="{{ $value }}" {{ request('type') == $value ? 'selected' : '' }}>{{ $label }}</option>
                    @endforeach
                </select>
            </div>
            <div class="w-full md:w-auto">
                <button type="submit" class="w-full md:w-auto px-4 py-2.5 gradient-bg text-white rounded-lg hover:opacity-90 transition-colors">
                    <i class="fas fa-filter ml-1"></i>
                    تصفية
                </button>
            </div>
        </div>
    </form>

    <!-- Jobs Table -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm overflow-hidden">
        <div class="overflow-x-auto">
            <table class="w-full text-right text-sm">
                <thead class="bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-300">
                    <tr>
                        <th class="px-6 py-4">عنوان الوظيفة</th>
                        <th class="px-6 py-4">الموقع</th>
                        <th class="px-6 py-4">نوع الوظيفة</th>
                        <th class="px-6 py-4">تاريخ النشر</th>
                        <th class="px-6 py-4">الطلبات</th>
                        <th class="px-6 py-4">الحالة</th>
                        <th class="px-6 py-4">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse($jobs ?? [] as $job)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">
                                <div class="flex items-center gap-3">
                                    @php
                                        $colors = ['gradient-bg', 'bg-green-500', 'bg-blue-500', 'bg-purple-500', 'bg-red-500'];
                                        $icons = ['code', 'paint-brush', 'mobile-alt', 'bullhorn', 'chart-bar'];
                                        $colorClass = $colors[array_rand($colors)];
                                        $icon = $icons[array_rand($icons)];
                                    @endphp
                                    <div class="w-10 h-10 rounded-lg flex items-center justify-center text-white {{ $colorClass }}">
                                        <i class="fas fa-{{ $icon }}"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium">{{ $job->title }}</p>
                                        <p class="text-gray-500 dark:text-gray-400 text-xs">
                                            {{ $job->category->name ?? 'بدون تصنيف' }}
                                        </p>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center gap-1 text-gray-600 dark:text-gray-400">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>{{ $job->location }}</span>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center gap-1 text-gray-600 dark:text-gray-400">
                                    <i class="fas fa-clock"></i>
                                    <span>
                                        @switch($job->job_type)
                                            @case('full_time')
                                                دوام كامل
                                                @break
                                            @case('part_time')
                                                دوام جزئي
                                                @break
                                            @case('remote')
                                                عن بعد
                                                @break
                                            @case('freelance')
                                                عمل حر
                                                @break
                                            @default
                                                {{ $job->job_type }}
                                        @endswitch
                                    </span>
                                </div>
                            </td>
                            <td class="px-6 py-4 text-gray-600 dark:text-gray-400">
                                {{ $job->posted_at ? $job->posted_at->format('d M, Y') : 'غير محدد' }}
                            </td>
                            <td class="px-6 py-4">
                                <span class="font-medium text-primary">{{ $job->applications_count ?? $job->applications->count() }} طلب</span>
                            </td>
                            <td class="px-6 py-4">
                                @if($job->expires_at > now())
                                    <span class="px-2.5 py-1 text-xs font-medium text-green-700 dark:text-green-400 bg-green-100 dark:bg-green-900/30 rounded-full">نشط</span>
                                @else
                                    <span class="px-2.5 py-1 text-xs font-medium text-red-700 dark:text-red-400 bg-red-100 dark:bg-red-900/30 rounded-full">منتهي</span>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                                    <a href="{{ route('admin.jobs.edit', $job->id) }}" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{{ route('admin.jobs.show', $job->id) }}" class="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <form action="{{ route('admin.jobs.destroy', $job->id) }}" method="POST" class="inline-block">
                                        @csrf
                                        @method('DELETE')
                                        <button type="button" class="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 delete-job" data-job-id="{{ $job->id }}">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" class="px-6 py-10 text-center text-gray-500 dark:text-gray-400">
                                <div class="flex flex-col items-center justify-center">
                                    <i class="fas fa-briefcase text-4xl mb-3 text-gray-300 dark:text-gray-600"></i>
                                    <p class="text-lg font-medium">لا توجد وظائف متاحة</p>
                                    <p class="mt-1">قم بإضافة وظيفة جديدة للبدء</p>
                                    <a href="{{ route('admin.jobs.create') }}" class="mt-3 px-4 py-2 gradient-bg text-white rounded-lg hover:opacity-90 transition-colors inline-flex items-center gap-2">
                                        <i class="fas fa-plus"></i>
                                        <span>إضافة وظيفة جديدة</span>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="px-6 py-4 flex items-center justify-between border-t border-gray-200 dark:border-gray-700">
            @if(isset($jobs) && $jobs->total() > 0)
                <div class="text-sm text-gray-700 dark:text-gray-300">
                    عرض <span class="font-medium">{{ $jobs->firstItem() ?? 0 }}</span> إلى <span class="font-medium">{{ $jobs->lastItem() ?? 0 }}</span> من <span class="font-medium">{{ $jobs->total() }}</span> النتائج
                </div>
                <div class="flex items-center space-x-2 rtl:space-x-reverse">
                    {{ $jobs->appends(request()->query())->links('pagination.tailwind') }}
                </div>
            @else
                <div class="text-sm text-gray-700 dark:text-gray-300">
                    لا توجد نتائج للعرض
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
        </div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white dark:bg-dark-card rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white dark:bg-dark-card px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/30 sm:mx-0 sm:h-10 sm:w-10">
                        <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400"></i>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:mr-4 sm:text-right">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                            حذف الوظيفة
                        </h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                هل أنت متأكد من رغبتك في حذف هذه الوظيفة؟ لا يمكن التراجع عن هذا الإجراء.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="confirmDelete" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                    حذف
                </button>
                <button type="button" id="cancelDelete" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    إلغاء
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Delete job confirmation
    const deleteButtons = document.querySelectorAll('.delete-job');
    const deleteModal = document.getElementById('deleteModal');
    const confirmDelete = document.getElementById('confirmDelete');
    const cancelDelete = document.getElementById('cancelDelete');
    let jobIdToDelete = null;

    // Show delete modal
    deleteButtons.forEach(button => {
        button.addEventListener('click', () => {
            jobIdToDelete = button.getAttribute('data-job-id');
            deleteModal.classList.remove('hidden');
        });
    });

    // Cancel delete
    cancelDelete.addEventListener('click', () => {
        deleteModal.classList.add('hidden');
        jobIdToDelete = null;
    });

    // Confirm delete
    confirmDelete.addEventListener('click', () => {
        if (jobIdToDelete) {
            // Find the form for this job and submit it
            const form = document.querySelector(`form[action*="${jobIdToDelete}"]`);
            if (form) {
                form.submit();
            }
            deleteModal.classList.add('hidden');
            jobIdToDelete = null;
        }
    });

    // Close modal when clicking outside
    deleteModal.addEventListener('click', (e) => {
        if (e.target === deleteModal) {
            deleteModal.classList.add('hidden');
            jobIdToDelete = null;
        }
    });

    // Filter jobs
    const searchInput = document.getElementById('search');
    const statusFilter = document.getElementById('statusFilter');
    const locationFilter = document.getElementById('locationFilter');
    const jobTypeFilter = document.getElementById('jobTypeFilter');

    // Add event listeners for filters (in a real app, these would trigger AJAX requests or form submissions)
    [searchInput, statusFilter, locationFilter, jobTypeFilter].forEach(element => {
        element.addEventListener('change', () => {
            console.log('Filtering jobs with:', {
                search: searchInput.value,
                status: statusFilter.value,
                location: locationFilter.value,
                jobType: jobTypeFilter.value
            });
        });
    });

    // Search as you type
    searchInput.addEventListener('input', () => {
        console.log('Searching for:', searchInput.value);
    });
</script>
@endsection