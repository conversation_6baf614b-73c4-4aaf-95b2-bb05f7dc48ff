<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class FixImagePaths
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // إذا كان الطلب للصور من storage
        if ($request->is('storage/*')) {
            $path = $request->path();
            $filePath = storage_path('app/public/' . str_replace('storage/', '', $path));
            
            if (file_exists($filePath)) {
                $mimeType = mime_content_type($filePath);
                
                return response()->file($filePath, [
                    'Content-Type' => $mimeType,
                    'Cache-Control' => 'public, max-age=31536000',
                ]);
            }
        }

        return $response;
    }
}
