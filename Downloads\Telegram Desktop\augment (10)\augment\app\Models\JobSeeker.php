<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class JobSeeker extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'resume',
        'skills',
        'experience',
        'education',
        'job_title',
        'location',
        'is_available',
    ];

    /**
     * Get the user that owns the job seeker profile.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the applications for the job seeker.
     */
    public function applications()
    {
        return $this->hasMany(Application::class);
    }

    /**
     * Get the company ratings given by the job seeker.
     */
    public function companyRatings()
    {
        return $this->hasMany(CompanyRating::class);
    }

    /**
     * Get the profile ratings received by the job seeker.
     */
    public function profileRatings()
    {
        return $this->hasMany(ProfileRating::class);
    }

    /**
     * Get the average rating of the job seeker.
     */
    public function getAverageRatingAttribute()
    {
        return $this->profileRatings()->avg('rating') ?: 0;
    }

    /**
     * Get the saved candidates records for this job seeker.
     */
    public function savedByEmployers()
    {
        return $this->hasMany(SavedCandidate::class);
    }
}
