<?php

namespace App\Http\Controllers;

use App\Models\Message;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MessageController extends Controller
{
    /**
     * عرض صندوق الوارد
     */
    public function inbox(Request $request)
    {
        $search = $request->get('search');
        
        $messages = Message::with(['sender'])
            ->inbox(Auth::id())
            ->when($search, function ($query, $search) {
                return $query->search($search);
            })
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        $unreadCount = Message::inbox(Auth::id())->unread()->count();

        return view('messages.inbox', compact('messages', 'unreadCount', 'search'));
    }

    /**
     * عرض الرسائل المرسلة
     */
    public function sent(Request $request)
    {
        $search = $request->get('search');
        
        $messages = Message::with(['receiver'])
            ->sent(Auth::id())
            ->when($search, function ($query, $search) {
                return $query->search($search);
            })
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return view('messages.sent', compact('messages', 'search'));
    }

    /**
     * عرض رسالة محددة
     */
    public function show(Message $message)
    {
        // التأكد من أن المستخدم له صلاحية قراءة الرسالة
        if ($message->sender_id !== Auth::id() && $message->receiver_id !== Auth::id()) {
            abort(403, 'ليس لديك صلاحية لقراءة هذه الرسالة');
        }

        // تحديد الرسالة كمقروءة إذا كان المستقبل
        if ($message->receiver_id === Auth::id()) {
            $message->markAsRead();
        }

        return view('messages.show', compact('message'));
    }

    /**
     * عرض نموذج إنشاء رسالة جديدة
     */
    public function create(Request $request)
    {
        $receiverId = $request->get('to');
        $receiver = null;
        
        if ($receiverId) {
            $receiver = User::find($receiverId);
        }

        // جلب قائمة المستخدمين للاختيار منهم
        $users = User::where('id', '!=', Auth::id())
            ->select('id', 'name', 'email', 'role')
            ->orderBy('name')
            ->get();

        return view('messages.create', compact('users', 'receiver'));
    }

    /**
     * إرسال رسالة جديدة
     */
    public function store(Request $request)
    {
        $request->validate([
            'receiver_id' => 'required|exists:users,id',
            'subject' => 'required|string|max:255',
            'body' => 'required|string',
        ], [
            'receiver_id.required' => 'يجب اختيار المستقبل',
            'receiver_id.exists' => 'المستقبل المحدد غير موجود',
            'subject.required' => 'يجب إدخال موضوع الرسالة',
            'subject.max' => 'موضوع الرسالة طويل جداً',
            'body.required' => 'يجب إدخال محتوى الرسالة',
        ]);

        // التأكد من أن المرسل لا يرسل لنفسه
        if ($request->receiver_id == Auth::id()) {
            return back()->withErrors(['receiver_id' => 'لا يمكنك إرسال رسالة لنفسك']);
        }

        Message::create([
            'sender_id' => Auth::id(),
            'receiver_id' => $request->receiver_id,
            'subject' => $request->subject,
            'body' => $request->body,
        ]);

        return redirect()->route('messages.sent')->with('success', 'تم إرسال الرسالة بنجاح');
    }

    /**
     * الرد على رسالة
     */
    public function reply(Message $message)
    {
        // التأكد من أن المستخدم له صلاحية الرد
        if ($message->receiver_id !== Auth::id()) {
            abort(403, 'ليس لديك صلاحية للرد على هذه الرسالة');
        }

        $receiver = $message->sender;
        $subject = 'رد: ' . $message->subject;

        $users = User::where('id', '!=', Auth::id())
            ->select('id', 'name', 'email', 'role')
            ->orderBy('name')
            ->get();

        return view('messages.create', compact('users', 'receiver', 'subject'));
    }

    /**
     * حذف رسالة
     */
    public function destroy(Message $message)
    {
        // التأكد من أن المستخدم له صلاحية حذف الرسالة
        if ($message->sender_id !== Auth::id() && $message->receiver_id !== Auth::id()) {
            abort(403, 'ليس لديك صلاحية لحذف هذه الرسالة');
        }

        $message->delete();

        return back()->with('success', 'تم حذف الرسالة بنجاح');
    }

    /**
     * تحديد رسالة كمقروءة/غير مقروءة
     */
    public function toggleRead(Message $message)
    {
        if ($message->receiver_id !== Auth::id()) {
            abort(403, 'ليس لديك صلاحية لتعديل هذه الرسالة');
        }

        $message->update([
            'is_read' => !$message->is_read,
            'read_at' => $message->is_read ? null : now(),
        ]);

        return back()->with('success', 'تم تحديث حالة الرسالة');
    }

    /**
     * عدد الرسائل غير المقروءة
     */
    public function unreadCount()
    {
        $count = Message::inbox(Auth::id())->unread()->count();
        
        return response()->json(['count' => $count]);
    }
}
