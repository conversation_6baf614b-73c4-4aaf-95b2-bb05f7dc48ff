<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إحصائيات الإشعارات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .stat-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .glass-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.8));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-800 mb-2">
                <i class="fas fa-chart-bar text-blue-500 mr-3"></i>
                إحصائيات الإشعارات
            </h1>
            <p class="text-gray-600">لوحة تحكم شاملة لمراقبة نشاط الإشعارات</p>
        </div>

        <!-- Overview Stats -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="stat-card p-6 rounded-xl bg-white shadow-lg">
                <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-bell text-white"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">إجمالي الإشعارات</p>
                        <p class="text-2xl font-bold text-gray-900" id="total-notifications">{{ $totalNotifications ?? 0 }}</p>
                    </div>
                </div>
            </div>

            <div class="stat-card p-6 rounded-xl bg-white shadow-lg">
                <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-exclamation-circle text-white"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">غير مقروءة</p>
                        <p class="text-2xl font-bold text-gray-900" id="unread-notifications">{{ $unreadNotifications ?? 0 }}</p>
                    </div>
                </div>
            </div>

            <div class="stat-card p-6 rounded-xl bg-white shadow-lg">
                <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-paper-plane text-white"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">أرسلتها الإدارة</p>
                        <p class="text-2xl font-bold text-gray-900" id="admin-notifications">{{ $adminSentNotifications ?? 0 }}</p>
                    </div>
                </div>
            </div>

            <div class="stat-card p-6 rounded-xl bg-white shadow-lg">
                <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-percentage text-white"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">معدل القراءة</p>
                        <p class="text-2xl font-bold text-gray-900" id="read-rate">
                            @php
                                $total = $totalNotifications ?? 0;
                                $unread = $unreadNotifications ?? 0;
                                $readRate = $total > 0 ? (($total - $unread) / $total) * 100 : 0;
                            @endphp
                            {{ number_format($readRate, 1) }}%
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Notifications by Type Chart -->
            <div class="glass-card rounded-xl p-6 shadow-lg">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">الإشعارات حسب النوع</h3>
                <div id="typeChart" class="h-64"></div>
            </div>

            <!-- Monthly Notifications Chart -->
            <div class="glass-card rounded-xl p-6 shadow-lg">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">الإشعارات الشهرية</h3>
                <div id="monthlyChart" class="h-64"></div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="text-center mb-8">
            <div class="flex flex-wrap justify-center gap-4">
                <button onclick="refreshStats()" class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-6 rounded-lg transition duration-200">
                    <i class="fas fa-sync-alt mr-2"></i>تحديث الإحصائيات
                </button>
                <a href="/test-notifications" class="bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-6 rounded-lg transition duration-200">
                    <i class="fas fa-plus mr-2"></i>إنشاء إشعارات تجريبية
                </a>
                <a href="/notifications-test-page" class="bg-purple-500 hover:bg-purple-600 text-white font-bold py-3 px-6 rounded-lg transition duration-200">
                    <i class="fas fa-cog mr-2"></i>لوحة التحكم
                </a>
            </div>
        </div>

        <!-- Status -->
        <div class="text-center">
            <div class="inline-flex items-center gap-2 bg-green-100 text-green-800 px-4 py-2 rounded-full">
                <i class="fas fa-check-circle"></i>
                <span>نظام الإشعارات يعمل بنجاح</span>
            </div>
        </div>
    </div>

    <script>
        // بيانات وهمية للاختبار
        const sampleTypeData = {
            'system_announcement': 45,
            'job_application': 32,
            'job_recommendation': 28,
            'payment_success': 15,
            'application_update': 12,
            'job_posted': 8
        };

        const sampleMonthlyData = {
            '2024-02': 45,
            '2024-03': 52,
            '2024-04': 38,
            '2024-05': 61,
            '2024-06': 73,
            '2024-07': 89
        };

        // Notifications by Type Chart
        const typeChartOptions = {
            series: Object.values(sampleTypeData),
            chart: {
                type: 'donut',
                height: 250,
                fontFamily: 'inherit',
            },
            labels: [
                'إعلان النظام',
                'طلب توظيف', 
                'توصية وظيفة',
                'دفع ناجح',
                'تحديث طلب',
                'وظيفة جديدة'
            ],
            colors: ['#3B82F6', '#8B5CF6', '#10B981', '#F59E0B', '#EF4444', '#6366F1'],
            legend: {
                position: 'bottom',
            },
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        width: 200
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }]
        };

        const typeChart = new ApexCharts(document.querySelector("#typeChart"), typeChartOptions);
        typeChart.render();

        // Monthly Notifications Chart
        const monthlyChartOptions = {
            series: [{
                name: 'الإشعارات',
                data: Object.values(sampleMonthlyData)
            }],
            chart: {
                type: 'area',
                height: 250,
                fontFamily: 'inherit',
                toolbar: {
                    show: false
                }
            },
            xaxis: {
                categories: Object.keys(sampleMonthlyData),
                labels: {
                    style: {
                        colors: '#6B7280'
                    }
                }
            },
            yaxis: {
                labels: {
                    style: {
                        colors: '#6B7280'
                    }
                }
            },
            colors: ['#3B82F6'],
            fill: {
                type: 'gradient',
                gradient: {
                    shadeIntensity: 1,
                    opacityFrom: 0.7,
                    opacityTo: 0.3,
                }
            },
            stroke: {
                curve: 'smooth',
                width: 2
            },
            grid: {
                borderColor: '#E5E7EB'
            }
        };

        const monthlyChart = new ApexCharts(document.querySelector("#monthlyChart"), monthlyChartOptions);
        monthlyChart.render();

        // تحديث الإحصائيات
        async function refreshStats() {
            try {
                const response = await fetch('/system-status');
                const data = await response.json();
                
                if (data.stats) {
                    document.getElementById('total-notifications').textContent = data.stats.notifications.total;
                    document.getElementById('unread-notifications').textContent = data.stats.notifications.unread;
                    
                    // حساب معدل القراءة
                    const total = data.stats.notifications.total;
                    const unread = data.stats.notifications.unread;
                    const readRate = total > 0 ? ((total - unread) / total) * 100 : 0;
                    document.getElementById('read-rate').textContent = readRate.toFixed(1) + '%';
                }
                
                alert('تم تحديث الإحصائيات بنجاح!');
            } catch (error) {
                alert('حدث خطأ أثناء تحديث الإحصائيات');
            }
        }
    </script>
</body>
</html>
