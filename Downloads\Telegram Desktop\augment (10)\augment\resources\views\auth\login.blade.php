<!-- resources/views/auth/login.blade.php -->
@extends('layouts.auth')

@section('title', 'تسجيل الدخول')

@section('content')
<!-- Login Form -->
<div id="loginForm" class="bg-white dark:bg-dark-card rounded-b-2xl shadow-lg p-8">
    <h2 class="text-2xl font-bold mb-6 text-center">مرحبًا بعودتك!</h2>
    <p class="text-gray-600 dark:text-gray-400 text-center mb-8">قم بتسجيل الدخول للوصول إلى حسابك ومتابعة فرص العمل</p>

    <form action="{{ route('login') }}" method="POST" class="space-y-4 max-w-md mx-auto">
        @csrf
        <div>
            <label for="email" class="block text-gray-700 dark:text-gray-300 mb-2 font-medium">البريد الإلكتروني</label>
            <div class="relative">
                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <i class="fas fa-envelope text-gray-400"></i>
                </div>
                <input type="email" id="email" name="email" value="{{ old('email') }}" class="w-full pl-4 pr-10 py-3 bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-2 focus:ring-primary focus:outline-none text-base @error('email') border-red-500 @enderror" placeholder="أدخل بريدك الإلكتروني" required />
            </div>
            @error('email')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div>
            <div class="flex justify-between mb-2">
                <label for="password" class="text-gray-700 dark:text-gray-300 font-medium">كلمة المرور</label>
                <a href="#" class="text-primary text-sm hover:underline">نسيت كلمة المرور؟</a>
            </div>
            <div class="relative">
                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <i class="fas fa-lock text-gray-400"></i>
                </div>
                <input type="password" id="password" name="password" class="w-full pl-4 pr-10 py-3 bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-2 focus:ring-primary focus:outline-none text-base @error('password') border-red-500 @enderror" placeholder="أدخل كلمة المرور" required />
                <button type="button" id="togglePassword" class="absolute inset-y-0 left-0 flex items-center pl-3">
                    <i class="fas fa-eye text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"></i>
                </button>
            </div>
            @error('password')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="flex items-center">
            <input type="checkbox" id="remember" name="remember" class="w-4 h-4 text-primary bg-gray-100 dark:bg-gray-800 rounded border-gray-300 focus:ring-primary" {{ old('remember') ? 'checked' : '' }} />
            <label for="remember" class="mr-2 text-gray-700 dark:text-gray-300">تذكرني</label>
        </div>

        <button type="submit" class="w-full py-3 gradient-bg text-white rounded-lg hover:opacity-90 transition mt-6 font-medium text-lg">
            تسجيل الدخول
        </button>

        <div class="relative flex items-center gap-4 py-4">
            <div class="flex-grow border-t border-gray-300 dark:border-gray-700"></div>
            <span class="text-gray-500 dark:text-gray-400 text-sm">أو تسجيل الدخول باستخدام</span>
            <div class="flex-grow border-t border-gray-300 dark:border-gray-700"></div>
        </div>

        <div class="grid grid-cols-3 gap-4">
            <a href="{{ route('social.login', 'google') }}" class="flex justify-center items-center gap-2 py-2.5 border border-gray-300 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition">
                <i class="fab fa-google text-red-500"></i>
                <span>Google</span>
            </a>
            <a href="{{ route('social.login', 'facebook') }}" class="flex justify-center items-center gap-2 py-2.5 border border-gray-300 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition">
                <i class="fab fa-facebook text-blue-600"></i>
                <span>Facebook</span>
            </a>
            <a href="{{ route('social.login', 'linkedin') }}" class="flex justify-center items-center gap-2 py-2.5 border border-gray-300 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition">
                <i class="fab fa-linkedin text-blue-700"></i>
                <span>LinkedIn</span>
            </a>
        </div>

        @if ($errors->has('error'))
        <div class="mt-4 p-3 bg-red-100 text-red-700 rounded-lg text-center">
            {{ $errors->first('error') }}
        </div>
        @endif

        <p class="text-center mt-6 text-gray-600 dark:text-gray-400">
            ليس لديك حساب؟ <a href="{{ route('register') }}" class="text-primary font-medium hover:underline">إنشاء حساب جديد</a>
        </p>
    </form>
</div>
@endsection

@section('scripts')
<script>
    // Toggle password visibility
    const togglePassword = document.getElementById('togglePassword');
    const password = document.getElementById('password');

    togglePassword.addEventListener('click', function() {
        const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
        password.setAttribute('type', type);

        // Toggle icon
        const eyeIcon = this.querySelector('i');
        eyeIcon.classList.toggle('fa-eye');
        eyeIcon.classList.toggle('fa-eye-slash');
    });
</script>
@endsection