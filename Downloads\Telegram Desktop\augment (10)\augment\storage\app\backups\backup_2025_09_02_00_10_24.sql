-- Database Backup
-- Generated on: 2025-09-02 00:10:24
-- Database: hire_me

SET FOREIGN_KEY_CHECKS=0;

-- Table structure for table `applications`
DROP TABLE IF EXISTS `applications`;
CREATE TABLE `applications` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `job_id` bigint unsigned NOT NULL,
  `job_seeker_id` bigint unsigned NOT NULL,
  `cover_letter` text NOT NULL,
  `status` varchar(125) NOT NULL DEFAULT 'pending',
  `admin_notes` text,
  `employer_notes` text,
  `employer_rating` int DEFAULT NULL,
  `reviewed_at` timestamp NULL DEFAULT NULL,
  `applied_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `applications_job_id_foreign` (`job_id`),
  KEY `applications_job_seeker_id_foreign` (`job_seeker_id`)
) ENGINE=MyISAM AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table `applications`
INSERT INTO `applications` VALUES
('1','1','1','أتقدم بطلب للحصول على هذه الوظيفة لأنني أعتقد أن مهاراتي وخبرتي تتناسب مع متطلبات المنصب. أتطلع للمساهمة في نجاح شركتكم.','rejected',NULL,NULL,NULL,NULL,'2025-08-20 00:04:04','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('2','4','1','أتقدم بطلب للحصول على هذه الوظيفة لأنني أعتقد أن مهاراتي وخبرتي تتناسب مع متطلبات المنصب. أتطلع للمساهمة في نجاح شركتكم.','rejected',NULL,NULL,NULL,NULL,'2025-08-21 00:04:04','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('3','6','1','أتقدم بطلب للحصول على هذه الوظيفة لأنني أعتقد أن مهاراتي وخبرتي تتناسب مع متطلبات المنصب. أتطلع للمساهمة في نجاح شركتكم.','pending',NULL,NULL,NULL,NULL,'2025-08-18 00:04:04','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('4','14','1','أتقدم بطلب للحصول على هذه الوظيفة لأنني أعتقد أن مهاراتي وخبرتي تتناسب مع متطلبات المنصب. أتطلع للمساهمة في نجاح شركتكم.','accepted',NULL,NULL,NULL,NULL,'2025-08-30 00:04:04','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('5','6','2','أتقدم بطلب للحصول على هذه الوظيفة لأنني أعتقد أن مهاراتي وخبرتي تتناسب مع متطلبات المنصب. أتطلع للمساهمة في نجاح شركتكم.','pending',NULL,NULL,NULL,NULL,'2025-08-24 00:04:04','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('6','9','2','أتقدم بطلب للحصول على هذه الوظيفة لأنني أعتقد أن مهاراتي وخبرتي تتناسب مع متطلبات المنصب. أتطلع للمساهمة في نجاح شركتكم.','pending',NULL,NULL,NULL,NULL,'2025-08-20 00:04:04','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('7','16','2','أتقدم بطلب للحصول على هذه الوظيفة لأنني أعتقد أن مهاراتي وخبرتي تتناسب مع متطلبات المنصب. أتطلع للمساهمة في نجاح شركتكم.','pending',NULL,NULL,NULL,NULL,'2025-08-24 00:04:04','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('8','1','3','أتقدم بطلب للحصول على هذه الوظيفة لأنني أعتقد أن مهاراتي وخبرتي تتناسب مع متطلبات المنصب. أتطلع للمساهمة في نجاح شركتكم.','pending',NULL,NULL,NULL,NULL,'2025-08-16 00:04:04','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('9','2','3','أتقدم بطلب للحصول على هذه الوظيفة لأنني أعتقد أن مهاراتي وخبرتي تتناسب مع متطلبات المنصب. أتطلع للمساهمة في نجاح شركتكم.','rejected',NULL,NULL,NULL,NULL,'2025-08-30 00:04:04','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('10','5','3','أتقدم بطلب للحصول على هذه الوظيفة لأنني أعتقد أن مهاراتي وخبرتي تتناسب مع متطلبات المنصب. أتطلع للمساهمة في نجاح شركتكم.','accepted',NULL,NULL,NULL,NULL,'2025-08-21 00:04:04','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('11','7','3','أتقدم بطلب للحصول على هذه الوظيفة لأنني أعتقد أن مهاراتي وخبرتي تتناسب مع متطلبات المنصب. أتطلع للمساهمة في نجاح شركتكم.','pending',NULL,NULL,NULL,NULL,'2025-08-30 00:04:04','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('12','9','4','أتقدم بطلب للحصول على هذه الوظيفة لأنني أعتقد أن مهاراتي وخبرتي تتناسب مع متطلبات المنصب. أتطلع للمساهمة في نجاح شركتكم.','accepted',NULL,NULL,NULL,NULL,'2025-08-22 00:04:04','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('13','15','4','أتقدم بطلب للحصول على هذه الوظيفة لأنني أعتقد أن مهاراتي وخبرتي تتناسب مع متطلبات المنصب. أتطلع للمساهمة في نجاح شركتكم.','pending',NULL,NULL,NULL,NULL,'2025-08-15 00:04:04','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('14','2','5','أتقدم بطلب للحصول على هذه الوظيفة لأنني أعتقد أن مهاراتي وخبرتي تتناسب مع متطلبات المنصب. أتطلع للمساهمة في نجاح شركتكم.','pending',NULL,NULL,NULL,NULL,'2025-08-31 00:04:04','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('15','6','5','أتقدم بطلب للحصول على هذه الوظيفة لأنني أعتقد أن مهاراتي وخبرتي تتناسب مع متطلبات المنصب. أتطلع للمساهمة في نجاح شركتكم.','pending',NULL,NULL,NULL,NULL,'2025-08-22 00:04:04','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('16','2','6','أتقدم بطلب للحصول على هذه الوظيفة لأنني أعتقد أن مهاراتي وخبرتي تتناسب مع متطلبات المنصب. أتطلع للمساهمة في نجاح شركتكم.','accepted',NULL,NULL,NULL,NULL,'2025-08-20 00:04:04','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('17','14','6','أتقدم بطلب للحصول على هذه الوظيفة لأنني أعتقد أن مهاراتي وخبرتي تتناسب مع متطلبات المنصب. أتطلع للمساهمة في نجاح شركتكم.','accepted',NULL,NULL,NULL,NULL,'2025-08-19 00:04:04','2025-09-02 00:04:04','2025-09-02 00:04:04');

-- Table structure for table `cache`
DROP TABLE IF EXISTS `cache`;
CREATE TABLE `cache` (
  `key` varchar(125) NOT NULL,
  `value` mediumtext NOT NULL,
  `expiration` int NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table structure for table `cache_locks`
DROP TABLE IF EXISTS `cache_locks`;
CREATE TABLE `cache_locks` (
  `key` varchar(125) NOT NULL,
  `owner` varchar(125) NOT NULL,
  `expiration` int NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table structure for table `categories`
DROP TABLE IF EXISTS `categories`;
CREATE TABLE `categories` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(125) NOT NULL,
  `slug` varchar(125) NOT NULL,
  `icon` varchar(125) DEFAULT NULL,
  `description` text,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `categories_slug_unique` (`slug`)
) ENGINE=MyISAM AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table `categories`
INSERT INTO `categories` VALUES
('1','تقنية المعلومات','tkny-almaalomat','laptop-code','وظائف في مجال تقنية المعلومات وتطوير البرمجيات','2025-09-02 00:03:58','2025-09-02 00:03:58'),
('2','التمويل والمحاسبة','altmoyl-oalmhasb','hand-holding-dollar','وظائف في مجال المالية والمحاسبة والتمويل','2025-09-02 00:03:58','2025-09-02 00:03:58'),
('3','التسويق والمبيعات','altsoyk-oalmbyaaat','bullhorn','وظائف في مجال التسويق والمبيعات والعلاقات العامة','2025-09-02 00:03:58','2025-09-02 00:03:58'),
('4','الرعاية الصحية','alraaay-alshy','user-doctor','وظائف في مجال الرعاية الصحية والطب','2025-09-02 00:03:58','2025-09-02 00:03:58'),
('5','التعليم والتدريب','altaalym-oaltdryb','graduation-cap','وظائف في مجال التعليم والتدريب والتطوير','2025-09-02 00:03:58','2025-09-02 00:03:58'),
('6','الإدارة والأعمال','aladar-oalaaamal','building','وظائف في مجال الإدارة وريادة الأعمال','2025-09-02 00:03:58','2025-09-02 00:03:58'),
('7','التصميم والإبداع','altsmym-oalabdaaa','palette','وظائف في مجال التصميم والفنون والإبداع','2025-09-02 00:03:58','2025-09-02 00:03:58'),
('8','القانون والاستشارات','alkanon-oalastsharat','gavel','وظائف في مجال القانون والاستشارات القانونية','2025-09-02 00:03:58','2025-09-02 00:03:58');

-- Table structure for table `company_ratings`
DROP TABLE IF EXISTS `company_ratings`;
CREATE TABLE `company_ratings` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `job_seeker_id` bigint unsigned NOT NULL,
  `employer_id` bigint unsigned NOT NULL,
  `rating` int NOT NULL,
  `comment` text,
  `status` varchar(125) NOT NULL DEFAULT 'pending',
  `admin_notes` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `company_ratings_job_seeker_id_employer_id_unique` (`job_seeker_id`,`employer_id`),
  KEY `company_ratings_employer_id_foreign` (`employer_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table structure for table `employers`
DROP TABLE IF EXISTS `employers`;
CREATE TABLE `employers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `company_name` varchar(125) DEFAULT NULL,
  `company_logo` varchar(125) DEFAULT NULL,
  `company_description` text,
  `industry` varchar(125) DEFAULT NULL,
  `website` varchar(125) DEFAULT NULL,
  `location` varchar(125) DEFAULT NULL,
  `company_size` varchar(125) DEFAULT NULL,
  `founded_year` int DEFAULT NULL,
  `package_id` varchar(125) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `employers_user_id_foreign` (`user_id`)
) ENGINE=MyISAM AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table `employers`
INSERT INTO `employers` VALUES
('1','11','شركة الحلول التقنية المتقدمة',NULL,'شركة رائدة في مجال تقنية المعلومات تقدم حلولاً متطورة للشركات والمؤسسات','تقنية المعلومات','https://www.company1.ly','طرابلس, ليبيا','11-50','2010',NULL,'2025-09-02 00:04:02','2025-09-02 00:04:02'),
('2','12','مجموعة الابتكار الرقمي',NULL,'متخصصون في تطوير التطبيقات والمواقع الإلكترونية بأحدث التقنيات','تطوير البرمجيات','https://www.company2.ly','مصراتة, ليبيا','51-200','2015',NULL,'2025-09-02 00:04:02','2025-09-02 00:04:02'),
('3','13','شركة تطوير المواقع الإلكترونية',NULL,'نقدم خدمات تطوير المواقع الإلكترونية والتجارة الإلكترونية','التجارة الإلكترونية','https://www.company3.ly','بنغازي, ليبيا','51-200','2012',NULL,'2025-09-02 00:04:02','2025-09-02 00:04:02'),
('4','14','بيت البرمجيات السعودي',NULL,'بيت خبرة في تطوير البرمجيات المخصصة وحلول الأعمال','التسويق الرقمي','https://www.company4.ly','سبها, ليبيا','201-500','2008',NULL,'2025-09-02 00:04:03','2025-09-02 00:04:03'),
('5','15','شركة الخبراء التقنيين',NULL,'فريق من الخبراء التقنيين لتقديم أفضل الحلول التقنية','الاستشارات التقنية','https://www.company5.ly','الزاوية, ليبيا','51-200','2018',NULL,'2025-09-02 00:04:03','2025-09-02 00:04:03'),
('6','16','مؤسسة التسويق الرقمي',NULL,'متخصصون في التسويق الرقمي ووسائل التواصل الاجتماعي','التصميم والإعلان','https://www.company6.ly','زليتن, ليبيا','51-200','2016',NULL,'2025-09-02 00:04:04','2025-09-02 00:04:04'),
('7','17','شركة التصميم الإبداعي',NULL,'نقدم خدمات التصميم الجرافيكي والهوية البصرية','الحلول المالية','https://www.company7.ly','أجدابيا, ليبيا','201-500','2005',NULL,'2025-09-02 00:04:04','2025-09-02 00:04:04');

-- Table structure for table `job_seekers`
DROP TABLE IF EXISTS `job_seekers`;
CREATE TABLE `job_seekers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `resume` varchar(125) DEFAULT NULL,
  `skills` text,
  `experience` text,
  `education` text,
  `job_title` varchar(125) DEFAULT NULL,
  `location` varchar(125) DEFAULT NULL,
  `is_available` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `job_seekers_user_id_foreign` (`user_id`)
) ENGINE=MyISAM AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table `job_seekers`
INSERT INTO `job_seekers` VALUES
('1','5',NULL,'PHP, Laravel, JavaScript, Vue.js, MySQL','3 سنوات خبرة في تطوير المواقع الإلكترونية','بكالوريوس في علوم الحاسب','مطور ويب','طرابلس, ليبيا','1','2025-09-02 00:03:59','2025-09-02 00:03:59'),
('2','6',NULL,'Photoshop, Illustrator, InDesign, Figma','5 سنوات خبرة في التصميم الجرافيكي','بكالوريوس في إدارة الأعمال','مصمم جرافيك','مصراتة, ليبيا','1','2025-09-02 00:04:00','2025-09-02 00:04:00'),
('3','7',NULL,'Excel, QuickBooks, SAP, Financial Analysis','4 سنوات خبرة في المحاسبة والتمويل','بكالوريوس في علوم الحاسب','محاسب مالي','بنغازي, ليبيا','1','2025-09-02 00:04:00','2025-09-02 00:04:00'),
('4','8',NULL,'Google Ads, Facebook Ads, SEO, Content Marketing','2 سنة خبرة في التسويق الرقمي','بكالوريوس في إدارة الأعمال','مسوق رقمي','سبها, ليبيا','1','2025-09-02 00:04:00','2025-09-02 00:04:00'),
('5','9',NULL,'React Native, Flutter, Swift, Kotlin','3 سنوات خبرة في تطوير التطبيقات','بكالوريوس في علوم الحاسب','مطور تطبيقات','الزاوية, ليبيا','1','2025-09-02 00:04:01','2025-09-02 00:04:01'),
('6','10',NULL,'Python, SQL, Tableau, Power BI','4 سنوات خبرة في تحليل البيانات','بكالوريوس في إدارة الأعمال','محلل بيانات','زليتن, ليبيا','1','2025-09-02 00:04:01','2025-09-02 00:04:01');

-- Table structure for table `jobs`
DROP TABLE IF EXISTS `jobs`;
CREATE TABLE `jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `employer_id` bigint unsigned NOT NULL,
  `category_id` bigint unsigned DEFAULT NULL,
  `title` varchar(125) NOT NULL,
  `description` text NOT NULL,
  `requirements` text NOT NULL,
  `salary_range` varchar(125) DEFAULT NULL,
  `salary_min` decimal(10,2) DEFAULT NULL,
  `salary_max` decimal(10,2) DEFAULT NULL,
  `salary_currency` varchar(3) NOT NULL DEFAULT 'LYD',
  `location` varchar(125) DEFAULT NULL,
  `job_type` enum('full_time','part_time','remote','freelance') NOT NULL,
  `posted_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `is_featured` tinyint(1) NOT NULL DEFAULT '0',
  `status` enum('draft','active','inactive','expired') NOT NULL DEFAULT 'active',
  `views` int NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `jobs_employer_id_foreign` (`employer_id`),
  KEY `jobs_category_id_foreign` (`category_id`)
) ENGINE=MyISAM AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table `jobs`
INSERT INTO `jobs` VALUES
('1','1','1','مطور PHP متقدم','نبحث عن مطور PHP متقدم للانضمام إلى فريقنا التقني. المرشح المثالي يجب أن يكون لديه خبرة قوية في Laravel وتطوير التطبيقات الويب.','- خبرة لا تقل عن 5 سنوات في تطوير PHP
- إتقان Laravel Framework
- معرفة بقواعد البيانات MySQL
- خبرة في Git وأدوات التطوير
- شهادة جامعية في علوم الحاسب أو ما يعادلها','1200 - 2250 دينار ليبي',NULL,NULL,'LYD','طرابلس, ليبيا','freelance','2025-08-09 00:04:04','2025-11-01 00:04:04','1','0','active','359','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('2','1','1','مطور واجهات أمامية','نبحث عن مطور واجهات أمامية مبدع لتطوير واجهات مستخدم متجاوبة وجذابة باستخدام أحدث التقنيات.','- خبرة في HTML, CSS, JavaScript
- إتقان React أو Vue.js
- معرفة بـ Tailwind CSS
- خبرة في تطوير المواقع المتجاوبة
- حس فني وإبداعي','6000 - 12000 ريال سعودي',NULL,NULL,'LYD','مصراتة, ليبيا','freelance','2025-08-17 00:04:04','2025-10-08 00:04:04','1','0','active','257','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('3','2','2','محاسب مالي','نبحث عن محاسب مالي مؤهل لإدارة الحسابات المالية وإعداد التقارير المالية للشركة.','- شهادة في المحاسبة أو المالية
- خبرة في برامج المحاسبة
- معرفة بالمعايير المحاسبية الليبية
- دقة في العمل والتفاصيل
- مهارات تحليلية قوية','1050 - 1950 دينار ليبي',NULL,NULL,'LYD','الزاوية, ليبيا','freelance','2025-09-02 00:04:04','2025-11-14 00:04:04','1','0','active','444','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('4','2','3','أخصائي تسويق رقمي','نبحث عن أخصائي تسويق رقمي لإدارة الحملات التسويقية الرقمية وزيادة الوعي بالعلامة التجارية.','- خبرة في إدارة حملات Google Ads
- معرفة بوسائل التواصل الاجتماعي
- مهارات في تحليل البيانات
- إبداع في إنشاء المحتوى
- شهادات في التسويق الرقمي مفضلة','900 - 1650 دينار ليبي',NULL,NULL,'LYD','طرابلس, ليبيا','full_time','2025-08-30 00:04:04','2025-10-31 00:04:04','1','0','active','75','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('5','2','1','مطور PHP متقدم','نبحث عن مطور PHP متقدم للانضمام إلى فريقنا التقني. المرشح المثالي يجب أن يكون لديه خبرة قوية في Laravel وتطوير التطبيقات الويب.','- خبرة لا تقل عن 5 سنوات في تطوير PHP
- إتقان Laravel Framework
- معرفة بقواعد البيانات MySQL
- خبرة في Git وأدوات التطوير
- شهادة جامعية في علوم الحاسب أو ما يعادلها','1200 - 2250 دينار ليبي',NULL,NULL,'LYD','طرابلس, ليبيا','part_time','2025-08-16 00:04:04','2025-11-09 00:04:04','1','0','active','148','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('6','3','3','أخصائي تسويق رقمي','نبحث عن أخصائي تسويق رقمي لإدارة الحملات التسويقية الرقمية وزيادة الوعي بالعلامة التجارية.','- خبرة في إدارة حملات Google Ads
- معرفة بوسائل التواصل الاجتماعي
- مهارات في تحليل البيانات
- إبداع في إنشاء المحتوى
- شهادات في التسويق الرقمي مفضلة','900 - 1650 دينار ليبي',NULL,NULL,'LYD','الزاوية, ليبيا','full_time','2025-08-09 00:04:04','2025-10-25 00:04:04','1','1','active','371','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('7','3','1','مطور PHP متقدم','نبحث عن مطور PHP متقدم للانضمام إلى فريقنا التقني. المرشح المثالي يجب أن يكون لديه خبرة قوية في Laravel وتطوير التطبيقات الويب.','- خبرة لا تقل عن 5 سنوات في تطوير PHP
- إتقان Laravel Framework
- معرفة بقواعد البيانات MySQL
- خبرة في Git وأدوات التطوير
- شهادة جامعية في علوم الحاسب أو ما يعادلها','1200 - 2250 دينار ليبي',NULL,NULL,'LYD','بنغازي, ليبيا','part_time','2025-08-29 00:04:04','2025-11-13 00:04:04','1','1','active','216','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('8','4','3','أخصائي تسويق رقمي','نبحث عن أخصائي تسويق رقمي لإدارة الحملات التسويقية الرقمية وزيادة الوعي بالعلامة التجارية.','- خبرة في إدارة حملات Google Ads
- معرفة بوسائل التواصل الاجتماعي
- مهارات في تحليل البيانات
- إبداع في إنشاء المحتوى
- شهادات في التسويق الرقمي مفضلة','900 - 1650 دينار ليبي',NULL,NULL,'LYD','مصراتة, ليبيا','full_time','2025-08-04 00:04:04','2025-09-17 00:04:04','1','1','active','120','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('9','4','1','مطور PHP متقدم','نبحث عن مطور PHP متقدم للانضمام إلى فريقنا التقني. المرشح المثالي يجب أن يكون لديه خبرة قوية في Laravel وتطوير التطبيقات الويب.','- خبرة لا تقل عن 5 سنوات في تطوير PHP
- إتقان Laravel Framework
- معرفة بقواعد البيانات MySQL
- خبرة في Git وأدوات التطوير
- شهادة جامعية في علوم الحاسب أو ما يعادلها','1200 - 2250 دينار ليبي',NULL,NULL,'LYD','مصراتة, ليبيا','part_time','2025-08-07 00:04:04','2025-10-07 00:04:04','1','0','active','371','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('10','4','1','مطور واجهات أمامية','نبحث عن مطور واجهات أمامية مبدع لتطوير واجهات مستخدم متجاوبة وجذابة باستخدام أحدث التقنيات.','- خبرة في HTML, CSS, JavaScript
- إتقان React أو Vue.js
- معرفة بـ Tailwind CSS
- خبرة في تطوير المواقع المتجاوبة
- حس فني وإبداعي','6000 - 12000 ريال سعودي',NULL,NULL,'LYD','طرابلس, ليبيا','freelance','2025-08-22 00:04:04','2025-10-22 00:04:04','1','0','active','151','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('11','5','2','محاسب مالي','نبحث عن محاسب مالي مؤهل لإدارة الحسابات المالية وإعداد التقارير المالية للشركة.','- شهادة في المحاسبة أو المالية
- خبرة في برامج المحاسبة
- معرفة بالمعايير المحاسبية الليبية
- دقة في العمل والتفاصيل
- مهارات تحليلية قوية','1050 - 1950 دينار ليبي',NULL,NULL,'LYD','الزاوية, ليبيا','freelance','2025-08-22 00:04:04','2025-10-09 00:04:04','1','1','active','353','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('12','5','3','أخصائي تسويق رقمي','نبحث عن أخصائي تسويق رقمي لإدارة الحملات التسويقية الرقمية وزيادة الوعي بالعلامة التجارية.','- خبرة في إدارة حملات Google Ads
- معرفة بوسائل التواصل الاجتماعي
- مهارات في تحليل البيانات
- إبداع في إنشاء المحتوى
- شهادات في التسويق الرقمي مفضلة','900 - 1650 دينار ليبي',NULL,NULL,'LYD','مصراتة, ليبيا','part_time','2025-08-24 00:04:04','2025-09-26 00:04:04','1','1','active','17','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('13','6','1','مطور PHP متقدم','نبحث عن مطور PHP متقدم للانضمام إلى فريقنا التقني. المرشح المثالي يجب أن يكون لديه خبرة قوية في Laravel وتطوير التطبيقات الويب.','- خبرة لا تقل عن 5 سنوات في تطوير PHP
- إتقان Laravel Framework
- معرفة بقواعد البيانات MySQL
- خبرة في Git وأدوات التطوير
- شهادة جامعية في علوم الحاسب أو ما يعادلها','1200 - 2250 دينار ليبي',NULL,NULL,'LYD','بنغازي, ليبيا','part_time','2025-08-06 00:04:04','2025-10-11 00:04:04','1','0','active','66','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('14','6','1','مطور واجهات أمامية','نبحث عن مطور واجهات أمامية مبدع لتطوير واجهات مستخدم متجاوبة وجذابة باستخدام أحدث التقنيات.','- خبرة في HTML, CSS, JavaScript
- إتقان React أو Vue.js
- معرفة بـ Tailwind CSS
- خبرة في تطوير المواقع المتجاوبة
- حس فني وإبداعي','6000 - 12000 ريال سعودي',NULL,NULL,'LYD','الزاوية, ليبيا','freelance','2025-08-09 00:04:04','2025-11-07 00:04:04','1','1','active','29','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('15','7','2','محاسب مالي','نبحث عن محاسب مالي مؤهل لإدارة الحسابات المالية وإعداد التقارير المالية للشركة.','- شهادة في المحاسبة أو المالية
- خبرة في برامج المحاسبة
- معرفة بالمعايير المحاسبية الليبية
- دقة في العمل والتفاصيل
- مهارات تحليلية قوية','1050 - 1950 دينار ليبي',NULL,NULL,'LYD','طرابلس, ليبيا','full_time','2025-08-05 00:04:04','2025-09-27 00:04:04','1','1','active','352','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('16','7','3','أخصائي تسويق رقمي','نبحث عن أخصائي تسويق رقمي لإدارة الحملات التسويقية الرقمية وزيادة الوعي بالعلامة التجارية.','- خبرة في إدارة حملات Google Ads
- معرفة بوسائل التواصل الاجتماعي
- مهارات في تحليل البيانات
- إبداع في إنشاء المحتوى
- شهادات في التسويق الرقمي مفضلة','900 - 1650 دينار ليبي',NULL,NULL,'LYD','سبها, ليبيا','full_time','2025-08-29 00:04:04','2025-10-11 00:04:04','1','0','active','353','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('17','7','1','مطور PHP متقدم','نبحث عن مطور PHP متقدم للانضمام إلى فريقنا التقني. المرشح المثالي يجب أن يكون لديه خبرة قوية في Laravel وتطوير التطبيقات الويب.','- خبرة لا تقل عن 5 سنوات في تطوير PHP
- إتقان Laravel Framework
- معرفة بقواعد البيانات MySQL
- خبرة في Git وأدوات التطوير
- شهادة جامعية في علوم الحاسب أو ما يعادلها','1200 - 2250 دينار ليبي',NULL,NULL,'LYD','طرابلس, ليبيا','freelance','2025-08-28 00:04:04','2025-10-15 00:04:04','1','0','active','95','2025-09-02 00:04:04','2025-09-02 00:04:04');

-- Table structure for table `messages`
DROP TABLE IF EXISTS `messages`;
CREATE TABLE `messages` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `sender_id` bigint unsigned NOT NULL,
  `receiver_id` bigint unsigned NOT NULL,
  `subject` varchar(125) NOT NULL,
  `body` text NOT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT '0',
  `read_at` timestamp NULL DEFAULT NULL,
  `attachments` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `messages_sender_id_created_at_index` (`sender_id`,`created_at`),
  KEY `messages_receiver_id_is_read_index` (`receiver_id`,`is_read`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table structure for table `migrations`
DROP TABLE IF EXISTS `migrations`;
CREATE TABLE `migrations` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(125) NOT NULL,
  `batch` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=42 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table `migrations`
INSERT INTO `migrations` VALUES
('1','2014_10_12_000000_create_users_table','1'),
('2','2023_01_01_000001_create_job_seekers_table','1'),
('3','2023_01_01_000002_create_employers_table','1'),
('4','2023_01_01_000003_create_jobs_table','1'),
('5','2023_01_01_000004_create_applications_table','1'),
('6','2023_01_01_000005_create_profile_ratings_table','1'),
('7','2023_01_01_000006_create_company_ratings_table','1'),
('8','2023_01_01_000007_create_payments_table','1'),
('9','2023_01_01_000008_create_notifications_table','1'),
('10','2023_01_01_000010_create_categories_table','1'),
('11','2023_01_01_000011_create_testimonials_table','1'),
('12','2023_06_01_000001_add_status_and_admin_notes_to_ratings_tables','1'),
('13','2023_06_20_000000_create_scheduled_reports_table','1'),
('14','2023_06_30_000000_add_views_column_to_jobs_table','1'),
('15','2023_06_30_000001_add_last_login_at_column_to_users_table','1'),
('16','2023_06_30_000002_update_existing_jobs_data','1'),
('17','2024_01_01_000000_create_saved_candidates_table','1'),
('18','2024_01_02_000000_create_messages_table','1'),
('19','2025_01_31_000001_add_salary_currency_fields_to_jobs_table','1'),
('20','2025_05_12_112754_create_permission_tables','1'),
('21','2025_05_12_160101_create_cache_table','1'),
('22','2025_05_17_231618_add_social_auth_columns_to_users_table','1'),
('23','2025_05_19_105244_add_is_active_column_to_jobs_table','1'),
('24','2025_05_19_113450_add_is_featured_column_to_jobs_table','1'),
('25','2025_05_19_114936_add_link_column_to_notifications_table','1'),
('26','2025_07_14_111446_add_data_column_to_notifications_table','1'),
('27','2025_07_14_111949_create_notification_settings_table','1'),
('28','2025_07_15_221237_add_employer_fields_to_users_table','1'),
('29','2025_07_16_134612_create_reviews_table','1'),
('30','2025_07_16_134851_add_response_fields_to_reviews_table','1'),
('31','2025_07_16_142956_change_company_size_to_string_in_employers_table','1'),
('32','2025_07_16_144135_update_applications_status_enum','1'),
('33','2025_07_16_144311_add_admin_notes_to_applications_table','1'),
('34','2025_07_30_000001_make_company_name_nullable_in_employers_table','1'),
('35','2025_07_31_000001_remove_expected_salary_from_job_seekers_table','1'),
('36','2025_08_03_000001_update_jobs_salary_fields','1'),
('37','2025_08_03_000002_create_saved_jobs_table','1'),
('38','2025_08_04_000001_add_employer_fields_to_applications_table','1'),
('39','2025_08_08_000001_add_status_column_to_jobs_table','1'),
('40','2025_08_08_193500_modify_jobs_posted_at_nullable','1'),
('41','2025_08_21_000000_add_package_id_to_employers_table','1');

-- Table structure for table `model_has_permissions`
DROP TABLE IF EXISTS `model_has_permissions`;
CREATE TABLE `model_has_permissions` (
  `permission_id` bigint unsigned NOT NULL,
  `model_type` varchar(125) NOT NULL,
  `model_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`permission_id`,`model_id`,`model_type`),
  KEY `model_has_permissions_model_id_model_type_index` (`model_id`,`model_type`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table structure for table `model_has_roles`
DROP TABLE IF EXISTS `model_has_roles`;
CREATE TABLE `model_has_roles` (
  `role_id` bigint unsigned NOT NULL,
  `model_type` varchar(125) NOT NULL,
  `model_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`role_id`,`model_id`,`model_type`),
  KEY `model_has_roles_model_id_model_type_index` (`model_id`,`model_type`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table structure for table `notification_settings`
DROP TABLE IF EXISTS `notification_settings`;
CREATE TABLE `notification_settings` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `type` varchar(125) NOT NULL,
  `web_enabled` tinyint(1) NOT NULL DEFAULT '1',
  `email_enabled` tinyint(1) NOT NULL DEFAULT '1',
  `push_enabled` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `notification_settings_user_id_type_unique` (`user_id`,`type`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table structure for table `notifications`
DROP TABLE IF EXISTS `notifications`;
CREATE TABLE `notifications` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `type` varchar(125) NOT NULL DEFAULT 'general',
  `message` text NOT NULL,
  `link` varchar(125) DEFAULT NULL,
  `data` json DEFAULT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `notifications_user_id_foreign` (`user_id`)
) ENGINE=MyISAM AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table `notifications`
INSERT INTO `notifications` VALUES
('1','11','profile_rated','تم تقييم ملفك الشخصي من قبل أحد أصحاب العمل',NULL,NULL,'0','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('2','11','profile_rated','تم تقييم ملفك الشخصي من قبل أحد أصحاب العمل',NULL,NULL,'1','2025-08-26 00:04:04','2025-09-02 00:04:04'),
('3','11','profile_rated','تم تقييم ملفك الشخصي من قبل أحد أصحاب العمل',NULL,NULL,'1','2025-08-19 00:04:04','2025-09-02 00:04:04'),
('4','11','profile_rated','تم تقييم ملفك الشخصي من قبل أحد أصحاب العمل',NULL,NULL,'1','2025-08-25 00:04:04','2025-09-02 00:04:04'),
('5','11','profile_rated','تم تقييم ملفك الشخصي من قبل أحد أصحاب العمل',NULL,NULL,'1','2025-08-31 00:04:04','2025-09-02 00:04:04'),
('6','12','job_application_received','تم استلام طلب توظيف جديد للوظيفة: مطور PHP متقدم','/employer/applications',NULL,'0','2025-08-18 00:04:04','2025-09-02 00:04:04'),
('7','12','company_rated','تم تقييم شركتك بـ 4 نجوم من قبل أحد المتقدمين','/employer/reviews',NULL,'0','2025-08-23 00:04:04','2025-09-02 00:04:04'),
('8','12','application_status_update','تم تحديث حالة طلب التوظيف الخاص بك',NULL,NULL,'0','2025-09-01 00:04:04','2025-09-02 00:04:04'),
('9','13','application_status_update','تم تحديث حالة طلب التوظيف الخاص بك',NULL,NULL,'0','2025-08-29 00:04:04','2025-09-02 00:04:04'),
('10','13','application_status_update','تم تحديث حالة طلب التوظيف الخاص بك',NULL,NULL,'0','2025-08-30 00:04:04','2025-09-02 00:04:04'),
('11','13','job_application_received','تم استلام طلب توظيف جديد للوظيفة: مطور PHP متقدم','/employer/applications',NULL,'1','2025-08-21 00:04:04','2025-09-02 00:04:04'),
('12','13','company_rated','تم تقييم شركتك بـ 4 نجوم من قبل أحد المتقدمين','/employer/reviews',NULL,'0','2025-08-18 00:04:04','2025-09-02 00:04:04'),
('13','1','profile_rated','تم تقييم ملفك الشخصي من قبل أحد أصحاب العمل','/job-seeker/applications',NULL,'0','2025-08-29 00:04:04','2025-09-02 00:04:04'),
('14','1','profile_rated','تم تقييم ملفك الشخصي من قبل أحد أصحاب العمل','/job-seeker/applications',NULL,'1','2025-08-24 00:04:04','2025-09-02 00:04:04'),
('15','2','profile_rated','تم تقييم ملفك الشخصي من قبل أحد أصحاب العمل','/job-seeker/applications',NULL,'0','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('16','2','application_status_update','تم تحديث حالة طلب التوظيف الخاص بك','/job-seeker/applications',NULL,'0','2025-08-24 00:04:04','2025-09-02 00:04:04'),
('17','2','application_status_update','تم تحديث حالة طلب التوظيف الخاص بك','/job-seeker/applications',NULL,'0','2025-09-02 00:04:04','2025-09-02 00:04:04'),
('18','3','profile_rated','تم تقييم ملفك الشخصي من قبل أحد أصحاب العمل','/job-seeker/applications',NULL,'1','2025-08-31 00:04:04','2025-09-02 00:04:04'),
('19','3','application_status_update','تم تحديث حالة طلب التوظيف الخاص بك','/job-seeker/applications',NULL,'0','2025-08-28 00:04:04','2025-09-02 00:04:04'),
('20','3','application_status_update','تم تحديث حالة طلب التوظيف الخاص بك','/job-seeker/applications',NULL,'1','2025-08-24 00:04:04','2025-09-02 00:04:04'),
('21','3','profile_rated','تم تقييم ملفك الشخصي من قبل أحد أصحاب العمل','/job-seeker/applications',NULL,'1','2025-09-01 00:04:04','2025-09-02 00:04:04');

-- Table structure for table `payments`
DROP TABLE IF EXISTS `payments`;
CREATE TABLE `payments` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `employer_id` bigint unsigned NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_method` enum('credit_card','sadad','bank_transfer') NOT NULL,
  `payment_status` enum('pending','completed','failed') NOT NULL DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `payments_employer_id_foreign` (`employer_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table structure for table `permissions`
DROP TABLE IF EXISTS `permissions`;
CREATE TABLE `permissions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(125) NOT NULL,
  `guard_name` varchar(125) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `permissions_name_guard_name_unique` (`name`,`guard_name`)
) ENGINE=MyISAM AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table `permissions`
INSERT INTO `permissions` VALUES
('1','create jobs','web','2025-09-02 00:03:58','2025-09-02 00:03:58'),
('2','edit jobs','web','2025-09-02 00:03:58','2025-09-02 00:03:58'),
('3','delete jobs','web','2025-09-02 00:03:58','2025-09-02 00:03:58'),
('4','view jobs','web','2025-09-02 00:03:58','2025-09-02 00:03:58'),
('5','create applications','web','2025-09-02 00:03:58','2025-09-02 00:03:58'),
('6','view applications','web','2025-09-02 00:03:58','2025-09-02 00:03:58'),
('7','manage applications','web','2025-09-02 00:03:58','2025-09-02 00:03:58'),
('8','edit profile','web','2025-09-02 00:03:58','2025-09-02 00:03:58'),
('9','view profiles','web','2025-09-02 00:03:58','2025-09-02 00:03:58'),
('10','rate profiles','web','2025-09-02 00:03:58','2025-09-02 00:03:58'),
('11','edit company','web','2025-09-02 00:03:58','2025-09-02 00:03:58'),
('12','view companies','web','2025-09-02 00:03:58','2025-09-02 00:03:58'),
('13','rate companies','web','2025-09-02 00:03:58','2025-09-02 00:03:58'),
('14','view reports','web','2025-09-02 00:03:58','2025-09-02 00:03:58'),
('15','export data','web','2025-09-02 00:03:58','2025-09-02 00:03:58'),
('16','make payments','web','2025-09-02 00:03:58','2025-09-02 00:03:58'),
('17','view payment history','web','2025-09-02 00:03:58','2025-09-02 00:03:58');

-- Table structure for table `profile_ratings`
DROP TABLE IF EXISTS `profile_ratings`;
CREATE TABLE `profile_ratings` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `employer_id` bigint unsigned NOT NULL,
  `job_seeker_id` bigint unsigned NOT NULL,
  `rating` int NOT NULL,
  `comment` text,
  `status` varchar(125) NOT NULL DEFAULT 'pending',
  `admin_notes` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `profile_ratings_employer_id_job_seeker_id_unique` (`employer_id`,`job_seeker_id`),
  KEY `profile_ratings_job_seeker_id_foreign` (`job_seeker_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table structure for table `reviews`
DROP TABLE IF EXISTS `reviews`;
CREATE TABLE `reviews` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `reviewer_id` bigint unsigned NOT NULL,
  `reviewed_id` bigint unsigned NOT NULL,
  `type` varchar(125) NOT NULL DEFAULT 'company',
  `rating` int unsigned NOT NULL,
  `comment` text,
  `status` enum('pending','approved','rejected') NOT NULL DEFAULT 'pending',
  `admin_notes` text,
  `employer_response` text,
  `responded_at` timestamp NULL DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `approved_by` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `reviews_approved_by_foreign` (`approved_by`),
  KEY `reviews_reviewed_id_type_status_index` (`reviewed_id`,`type`,`status`),
  KEY `reviews_reviewer_id_created_at_index` (`reviewer_id`,`created_at`)
) ENGINE=MyISAM AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table `reviews`
INSERT INTO `reviews` VALUES
('1','2','11','company','4','تجربة جيدة في العمل مع الشركة. هناك مجال للتحسين في بعض الجوانب.','approved',NULL,NULL,NULL,'2025-08-28 00:04:04',NULL,'2025-08-29 00:04:04','2025-09-02 00:04:04'),
('2','3','11','company','4','تجربة جيدة في العمل مع الشركة. هناك مجال للتحسين في بعض الجوانب.','approved',NULL,NULL,NULL,NULL,NULL,'2025-08-29 00:04:04','2025-09-02 00:04:04'),
('3','6','11','company','4','شركة احترافية وفريق عمل متعاون. راتب مناسب وساعات عمل مرنة.','approved',NULL,NULL,NULL,NULL,NULL,'2025-08-21 00:04:04','2025-09-02 00:04:04'),
('4','2','12','company','4','تجربة جيدة في العمل مع الشركة. هناك مجال للتحسين في بعض الجوانب.','approved',NULL,NULL,NULL,NULL,NULL,'2025-08-16 00:04:04','2025-09-02 00:04:04'),
('5','3','12','company','3','إدارة ممتازة وفرص تطوير مهني جيدة. أنصح بالتقديم.','approved',NULL,NULL,NULL,NULL,NULL,'2025-08-31 00:04:04','2025-09-02 00:04:04'),
('6','6','12','company','5','شركة رائدة في مجالها. تقدر الموظفين وتوفر بيئة عمل محفزة.','approved',NULL,NULL,NULL,'2025-08-27 00:04:04',NULL,'2025-08-11 00:04:04','2025-09-02 00:04:04'),
('7','10','12','company','4','إدارة ممتازة وفرص تطوير مهني جيدة. أنصح بالتقديم.','pending',NULL,NULL,NULL,NULL,NULL,'2025-08-05 00:04:04','2025-09-02 00:04:04'),
('8','1','13','company','5','تجربة جيدة في العمل مع الشركة. هناك مجال للتحسين في بعض الجوانب.','pending',NULL,NULL,NULL,'2025-08-25 00:04:04',NULL,'2025-08-31 00:04:04','2025-09-02 00:04:04'),
('9','3','13','company','3','تجربة متوسطة. الراتب مناسب لكن ساعات العمل طويلة أحياناً.','pending',NULL,NULL,NULL,'2025-08-25 00:04:04',NULL,'2025-08-21 00:04:04','2025-09-02 00:04:04'),
('10','6','13','company','4','شركة احترافية وفريق عمل متعاون. راتب مناسب وساعات عمل مرنة.','approved',NULL,NULL,NULL,NULL,NULL,'2025-08-05 00:04:04','2025-09-02 00:04:04'),
('11','7','13','company','4','شركة احترافية وفريق عمل متعاون. راتب مناسب وساعات عمل مرنة.','approved',NULL,NULL,NULL,NULL,NULL,'2025-08-24 00:04:04','2025-09-02 00:04:04'),
('12','6','14','company','5','شركة رائدة في مجالها. تقدر الموظفين وتوفر بيئة عمل محفزة.','approved',NULL,NULL,NULL,NULL,NULL,'2025-08-30 00:04:04','2025-09-02 00:04:04'),
('13','8','14','company','3','تجربة جيدة في العمل مع الشركة. هناك مجال للتحسين في بعض الجوانب.','approved',NULL,NULL,NULL,NULL,NULL,'2025-08-09 00:04:04','2025-09-02 00:04:04'),
('14','9','14','company','3','تجربة جيدة في العمل مع الشركة. هناك مجال للتحسين في بعض الجوانب.','approved',NULL,NULL,NULL,NULL,NULL,'2025-08-28 00:04:04','2025-09-02 00:04:04'),
('15','6','15','company','3','تجربة متوسطة. الراتب مناسب لكن ساعات العمل طويلة أحياناً.','approved',NULL,NULL,NULL,'2025-09-01 00:04:04',NULL,'2025-08-28 00:04:04','2025-09-02 00:04:04'),
('16','8','15','company','4','إدارة ممتازة وفرص تطوير مهني جيدة. أنصح بالتقديم.','approved',NULL,NULL,NULL,'2025-08-27 00:04:04',NULL,'2025-08-22 00:04:04','2025-09-02 00:04:04'),
('17','9','15','company','4','تجربة متوسطة. الراتب مناسب لكن ساعات العمل طويلة أحياناً.','pending',NULL,NULL,NULL,'2025-08-24 00:04:04',NULL,'2025-08-29 00:04:04','2025-09-02 00:04:04');

-- Table structure for table `role_has_permissions`
DROP TABLE IF EXISTS `role_has_permissions`;
CREATE TABLE `role_has_permissions` (
  `permission_id` bigint unsigned NOT NULL,
  `role_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`permission_id`,`role_id`),
  KEY `role_has_permissions_role_id_foreign` (`role_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table `role_has_permissions`
INSERT INTO `role_has_permissions` VALUES
('1','2'),
('1','3'),
('2','2'),
('2','3'),
('3','2'),
('3','3'),
('4','1'),
('4','2'),
('4','3'),
('5','1'),
('5','3'),
('6','1'),
('6','2'),
('6','3'),
('7','2'),
('7','3'),
('8','1'),
('8','3'),
('9','1'),
('9','2'),
('9','3'),
('10','2'),
('10','3'),
('11','2'),
('11','3'),
('12','1'),
('12','3'),
('13','1'),
('13','3'),
('14','2'),
('14','3'),
('15','2'),
('15','3'),
('16','2'),
('16','3'),
('17','2'),
('17','3');

-- Table structure for table `roles`
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(125) NOT NULL,
  `guard_name` varchar(125) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `roles_name_guard_name_unique` (`name`,`guard_name`)
) ENGINE=MyISAM AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table `roles`
INSERT INTO `roles` VALUES
('1','job_seeker','web','2025-09-02 00:03:58','2025-09-02 00:03:58'),
('2','employer','web','2025-09-02 00:03:58','2025-09-02 00:03:58'),
('3','admin','web','2025-09-02 00:03:58','2025-09-02 00:03:58');

-- Table structure for table `saved_candidates`
DROP TABLE IF EXISTS `saved_candidates`;
CREATE TABLE `saved_candidates` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `employer_id` bigint unsigned NOT NULL,
  `job_seeker_id` bigint unsigned NOT NULL,
  `notes` text,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `saved_candidates_employer_id_job_seeker_id_unique` (`employer_id`,`job_seeker_id`),
  KEY `saved_candidates_job_seeker_id_foreign` (`job_seeker_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table structure for table `saved_jobs`
DROP TABLE IF EXISTS `saved_jobs`;
CREATE TABLE `saved_jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `job_seeker_id` bigint unsigned NOT NULL,
  `job_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `saved_jobs_job_seeker_id_job_id_unique` (`job_seeker_id`,`job_id`),
  KEY `saved_jobs_job_id_foreign` (`job_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table structure for table `scheduled_reports`
DROP TABLE IF EXISTS `scheduled_reports`;
CREATE TABLE `scheduled_reports` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(125) NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `email` varchar(125) NOT NULL,
  `report_type` varchar(125) NOT NULL,
  `frequency` varchar(125) NOT NULL,
  `day_of_week` int DEFAULT NULL,
  `day_of_month` int DEFAULT NULL,
  `filters` json DEFAULT NULL,
  `include_attachment` tinyint(1) NOT NULL DEFAULT '1',
  `last_sent_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `scheduled_reports_user_id_foreign` (`user_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table structure for table `testimonials`
DROP TABLE IF EXISTS `testimonials`;
CREATE TABLE `testimonials` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `content` text NOT NULL,
  `rating` int NOT NULL,
  `position` varchar(125) DEFAULT NULL,
  `company` varchar(125) DEFAULT NULL,
  `is_featured` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `testimonials_user_id_foreign` (`user_id`)
) ENGINE=MyISAM AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table `testimonials`
INSERT INTO `testimonials` VALUES
('1','1','وجدت وظيفتي الحالية من خلال Hire Me في أقل من أسبوعين. واجهة الموقع سهلة الاستخدام وساعدتني في العثور على وظائف تناسب مهاراتي بدقة.','5','مطور واجهات أمامية','شركة التقنية المتطورة','1','2025-09-02 00:03:58','2025-09-02 00:03:58'),
('2','2','كمديرة موارد بشرية، أصبح استخدام Hire Me جزءًا أساسيًا من عملية التوظيف لدينا. نستطيع العثور على مرشحين مؤهلين بسرعة وسهولة.','4','مديرة موارد بشرية','شركة البيانات المتقدمة','1','2025-09-02 00:03:58','2025-09-02 00:03:58'),
('3','3','بعد عدة محاولات في مواقع توظيف أخرى، استطعت العثور على وظيفة أحلامي خلال Hire Me. الشركات الموجودة على المنصة رائدة في مجالاتها.','5','مدير تسويق','شركة الحلول التسويقية','1','2025-09-02 00:03:58','2025-09-02 00:03:58');

-- Table structure for table `users`
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(125) NOT NULL,
  `email` varchar(125) NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(125) NOT NULL,
  `role` enum('job_seeker','employer','admin') NOT NULL DEFAULT 'job_seeker',
  `remember_token` varchar(100) DEFAULT NULL,
  `last_login_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `provider` varchar(125) DEFAULT NULL,
  `provider_id` varchar(125) DEFAULT NULL,
  `avatar` varchar(125) DEFAULT NULL,
  `company_name` varchar(125) DEFAULT NULL,
  `company_description` text,
  `company_website` varchar(125) DEFAULT NULL,
  `company_size` varchar(125) DEFAULT NULL,
  `industry` varchar(125) DEFAULT NULL,
  `location` varchar(125) DEFAULT NULL,
  `phone` varchar(125) DEFAULT NULL,
  `settings` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`)
) ENGINE=MyISAM AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table `users`
INSERT INTO `users` VALUES
('1','أحمد محمد','<EMAIL>','2025-09-02 00:03:58','$2y$12$w.6NTP5e0yPpnqKEVFhRbezdbDhcHri08Ixq2h5/p4.LIwifzxhie','job_seeker','O0ROiUcXbo',NULL,'2025-09-02 00:03:58','2025-09-02 00:03:58',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('2','نورة عبدالله','<EMAIL>','2025-09-02 00:03:58','$2y$12$w.6NTP5e0yPpnqKEVFhRbezdbDhcHri08Ixq2h5/p4.LIwifzxhie','job_seeker','fhdAZL5Pkj',NULL,'2025-09-02 00:03:58','2025-09-02 00:03:58',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('3','خالد سعيد','<EMAIL>','2025-09-02 00:03:58','$2y$12$w.6NTP5e0yPpnqKEVFhRbezdbDhcHri08Ixq2h5/p4.LIwifzxhie','job_seeker','4iugp9eDG3',NULL,'2025-09-02 00:03:58','2025-09-02 00:03:58',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('4','مدير النظام','<EMAIL>',NULL,'$2y$12$ua//UIetNxYANBuaOgY3Qu0rI8kd2BybJ7QvAM/5eFjG0.k3Zwnvi','admin','AtRNwLlC9a5CeaMLM39ggQG1s5DQNBCVPcTB72pG6qQBc45WG5CbOmUgB26v',NULL,'2025-09-02 00:03:59','2025-09-02 00:03:59',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('5','أحمد محمد العلي','<EMAIL>',NULL,'$2y$12$9IGS9LymQeKdu/pkhDgFeOfxs2kwWsxxn68Fw8nhUjaca90TBu.4y','job_seeker',NULL,NULL,'2025-09-02 00:03:59','2025-09-02 00:03:59',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('6','فاطمة عبدالله السعيد','<EMAIL>',NULL,'$2y$12$Qj0q91X.v2YRqACxWDzYuOkROZRIXkNHeFOH1vPNfToLxxjRaWN.2','job_seeker',NULL,NULL,'2025-09-02 00:04:00','2025-09-02 00:04:00',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('7','محمد خالد الأحمد','<EMAIL>',NULL,'$2y$12$A2.Fbi.rJM5OZx9figUQ.O7zChU6Z1SyuIQJDGcV23ipSnTAbcY.K','job_seeker',NULL,NULL,'2025-09-02 00:04:00','2025-09-02 00:04:00',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('8','نورة سعد المطيري','<EMAIL>',NULL,'$2y$12$VBVa.lTZIR1gjl1yisOpNOL8301GhxuYiaQFIq7XkDtOVxYJQGLFW','job_seeker',NULL,NULL,'2025-09-02 00:04:00','2025-09-02 00:04:00',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('9','عبدالرحمن علي القحطاني','<EMAIL>',NULL,'$2y$12$.5jLIF5amfNAl.cRGKEiFukmw7WHNkgoomRKqFebZL7grVMb/kxBG','job_seeker',NULL,NULL,'2025-09-02 00:04:01','2025-09-02 00:04:01',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('10','سارة محمد الزهراني','<EMAIL>',NULL,'$2y$12$Nm1q.xr8jo1utCzK1RHnwOwr3u2zFJzdF.9TZlXbHvJR2NgbAKIsa','job_seeker',NULL,NULL,'2025-09-02 00:04:01','2025-09-02 00:04:01',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('11','أحمد سعد المدير','<EMAIL>',NULL,'$2y$12$7oonr2GJb3mx5VWUdFVJDeijC7ODbmlqkNNLcAUmpCwywgmvDXl12','employer',NULL,NULL,'2025-09-02 00:04:02','2025-09-02 00:04:02',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('12','فاطمة محمد الرئيسة التنفيذية','<EMAIL>',NULL,'$2y$12$BQkVuVEQLzA8owzJvdvgcugxAMM6LIHxrs23fwi6I35Q0MSWye6Y.','employer',NULL,NULL,'2025-09-02 00:04:02','2025-09-02 00:04:02',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('13','خالد عبدالله مدير الموارد البشرية','<EMAIL>',NULL,'$2y$12$DgUGcl0j.A.h7MHpTyekUeLLCvL88c.n30dPzn6HMkPkAd95/4ety','employer',NULL,NULL,'2025-09-02 00:04:02','2025-09-02 00:04:02',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('14','نورة علي مديرة التطوير','<EMAIL>',NULL,'$2y$12$txCmWau9VgSjN97pmgl3.O/6/r7gIYRJouhTwagIFerCwej8vukSa','employer',NULL,NULL,'2025-09-02 00:04:03','2025-09-02 00:04:03',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('15','محمد حسن المدير العام','<EMAIL>',NULL,'$2y$12$RhWaDFTUunQ4Ei1fp.VGvuVPOLnq6xpTXY8hmNUhEszoqwFsompWe','employer',NULL,NULL,'2025-09-02 00:04:03','2025-09-02 00:04:03',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('16','سارة أحمد مديرة التسويق','<EMAIL>',NULL,'$2y$12$uhX/c2NG6nJbS/6vvB9oE.7WUI9jJEXmZEEJcsVrD7uFJ29wthyUu','employer',NULL,NULL,'2025-09-02 00:04:04','2025-09-02 00:04:04',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('17','عبدالرحمن سالم المدير التقني','<EMAIL>',NULL,'$2y$12$VBEbqyIzrXb4v5VeeZbVW.H9o9e4BjSr0wqsOFjGuElr1Qxe0lP36','employer',NULL,NULL,'2025-09-02 00:04:04','2025-09-02 00:04:04',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);

SET FOREIGN_KEY_CHECKS=1;
