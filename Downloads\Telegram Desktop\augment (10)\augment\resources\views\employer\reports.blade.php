@extends('layouts.employer')

@section('title', 'لوحة التقارير - Hire Me')
@section('header_title', 'لوحة التقارير')

@section('content')
<div class="p-8 space-y-8">
    <!-- Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">لوحة التقارير</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">تحليل مفصل لأداء وظائفك والطلبات المقدمة عليها</p>
        </div>
        <div class="flex gap-3 mt-4 md:mt-0">
            <a href="{{ route('employer.statistics') }}" class="px-6 py-3 bg-green-600 text-white rounded-xl hover:bg-green-700 transition-all duration-300 flex items-center gap-2">
                <i class="fas fa-chart-line"></i>
                عرض التقارير المفصلة
            </a>
        </div>
    </div>

    <!-- الإحصائيات السريعة -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- مشاهدات الوظائف -->
        <div class="stat-card p-6 rounded-2xl hover:scale-105 transition-all duration-300 animate-fade-in" style="animation-delay: 0.1s">
            <div class="flex items-center gap-4">
                <div class="w-14 h-14 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center shadow-lg glow-effect">
                    <i class="fas fa-eye text-white text-xl"></i>
                </div>
                <div class="flex-1">
                    <p class="text-sm text-gray-600 dark:text-gray-400 font-medium">مشاهدات الوظائف</p>
                    <p class="text-3xl font-bold text-gradient">{{ number_format($totalViews ?? 450) }}</p>
                    <div class="flex items-center gap-1 mt-1">
                        <i class="fas fa-arrow-up text-orange-500 text-xs"></i>
                        <span class="text-xs text-orange-500 font-medium">مشاهدات الوظائف</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- إجمالي الطلبات -->
        <div class="stat-card p-6 rounded-2xl hover:scale-105 transition-all duration-300 animate-fade-in" style="animation-delay: 0.2s">
            <div class="flex items-center gap-4">
                <div class="w-14 h-14 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg glow-effect">
                    <i class="fas fa-users text-white text-xl"></i>
                </div>
                <div class="flex-1">
                    <p class="text-sm text-gray-600 dark:text-gray-400 font-medium">إجمالي الطلبات</p>
                    <p class="text-3xl font-bold text-gradient">{{ number_format($totalApplications ?? 0) }}</p>
                    <div class="flex items-center gap-1 mt-1">
                        <i class="fas fa-chart-line text-purple-500 text-xs"></i>
                        <span class="text-xs text-purple-500 font-medium">نمو مستمر</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- الوظائف النشطة -->
        <div class="stat-card p-6 rounded-2xl hover:scale-105 transition-all duration-300 animate-fade-in" style="animation-delay: 0.3s">
            <div class="flex items-center gap-4">
                <div class="w-14 h-14 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg glow-effect">
                    <i class="fas fa-check-circle text-white text-xl"></i>
                </div>
                <div class="flex-1">
                    <p class="text-sm text-gray-600 dark:text-gray-400 font-medium">الوظائف النشطة</p>
                    <p class="text-3xl font-bold text-gradient">{{ number_format($activeJobs ?? 8) }}</p>
                    <div class="flex items-center gap-1 mt-1">
                        <i class="fas fa-circle text-green-500 text-xs animate-pulse"></i>
                        <span class="text-xs text-gray-500 dark:text-gray-400">متاحة للتقديم</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- إجمالي الوظائف -->
        <div class="stat-card p-6 rounded-2xl hover:scale-105 transition-all duration-300 animate-fade-in" style="animation-delay: 0.4s">
            <div class="flex items-center gap-4">
                <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg glow-effect">
                    <i class="fas fa-briefcase text-white text-xl"></i>
                </div>
                <div class="flex-1">
                    <p class="text-sm text-gray-600 dark:text-gray-400 font-medium">إجمالي الوظائف</p>
                    <p class="text-3xl font-bold text-gradient">{{ number_format($totalJobs ?? 9) }}</p>
                    <div class="flex items-center gap-1 mt-1">
                        <i class="fas fa-arrow-up text-blue-500 text-xs"></i>
                        <span class="text-xs text-blue-500 font-medium">الوظائف المنشورة</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحليل المتقدمين وإحصائيات الوظائف -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- تحليل المتقدمين -->
        <div class="glass-card rounded-xl p-6">
            <div class="flex items-center gap-4 mb-6">
                <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-users text-white"></i>
                </div>
                <div>
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">تحليل المتقدمين</h2>
                    <p class="text-sm text-gray-600 dark:text-gray-400">تحليل بيانات المتقدمين والإحصائيات التفصيلية</p>
                </div>
            </div>
            <div class="text-center py-8">
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">آخر تحديث اليوم</p>
                <a href="{{ route('reports.applicant-analytics') }}" class="px-6 py-3 bg-green-600 text-white rounded-xl hover:bg-green-700 transition-all duration-300 flex items-center gap-2 mx-auto">
                    <i class="fas fa-arrow-left"></i>
                    عرض التحليل
                </a>
            </div>
        </div>

        <!-- إحصائيات الوظائف -->
        <div class="glass-card rounded-xl p-6">
            <div class="flex items-center gap-4 mb-6">
                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-chart-line text-white"></i>
                </div>
                <div>
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">إحصائيات الوظائف</h2>
                    <p class="text-sm text-gray-600 dark:text-gray-400">تحليل مفصل لأداء وظائفك والطلبات المقدمة عليها</p>
                </div>
            </div>
            <div class="text-center py-8">
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">آخر تحديث اليوم</p>
                <a href="{{ route('reports.job-statistics') }}" class="px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-all duration-300 flex items-center gap-2 mx-auto">
                    <i class="fas fa-arrow-left"></i>
                    عرض التقرير
                </a>
            </div>
        </div>
    </div>



    <!-- معلومات إضافية -->
    <div class="glass-card rounded-xl p-6 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-700">
        <div class="flex items-center gap-4">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                <i class="fas fa-info-circle text-white"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">كيفية الوصول للتقارير المفصلة</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-3">
                    <strong>"عرض التقارير المفصلة"</strong> - الإحصائيات الشاملة مع جميع الرسوم البيانية والتقارير التفاعلية وأفضل الوظائف أداءً.
                    <br><strong>"عرض التقرير"</strong> - إحصائيات الوظائف المتخصصة.
                    <br><strong>"عرض التحليل"</strong> - تحليل بيانات المتقدمين والاتجاهات.
                </p>
                <div class="flex flex-wrap gap-2">
                    <span class="px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100 rounded-full text-sm">
                        <i class="fas fa-chart-bar mr-1"></i>
                        إحصائيات شاملة
                    </span>
                    <span class="px-3 py-1 bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100 rounded-full text-sm">
                        <i class="fas fa-download mr-1"></i>
                        تصدير متعدد الصيغ
                    </span>
                    <span class="px-3 py-1 bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100 rounded-full text-sm">
                        <i class="fas fa-chart-line mr-1"></i>
                        رسوم بيانية تفاعلية
                    </span>
                    <span class="px-3 py-1 bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-100 rounded-full text-sm">
                        <i class="fas fa-trophy mr-1"></i>
                        أفضل الوظائف أداءً
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
/* تأثيرات الإحصائيات السريعة */
.text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glow-effect {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.animate-fade-in {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
    transform: translateY(20px);
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}
</style>
@endpush

@push('scripts')
<script>
// دالة لعرض نافذة تصدير التقرير
function showExportModal() {
    alert('سيتم إضافة ميزة تصدير التقارير قريباً');
}

// تأثيرات التحريك للإحصائيات
document.addEventListener('DOMContentLoaded', function() {
    const statCards = document.querySelectorAll('.stat-card');

    statCards.forEach((card, index) => {
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
@endpush
