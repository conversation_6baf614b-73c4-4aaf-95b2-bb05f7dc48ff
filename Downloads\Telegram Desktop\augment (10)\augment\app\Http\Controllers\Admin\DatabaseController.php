<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Storage;

class DatabaseController extends Controller
{
    /**
     * Display database overview.
     */
    public function index()
    {
        $stats = $this->getDatabaseStats();
        $tables = $this->getTableInfo();

        return view('admin.database.index', compact('stats', 'tables'));
    }

    /**
     * Show database backup page.
     */
    public function backup()
    {
        $backups = $this->getBackupFiles();

        return view('admin.database.backup', compact('backups'));
    }

    /**
     * Create database backup.
     */
    public function createBackup(Request $request)
    {
        try {
            $filename = 'backup_' . date('Y_m_d_H_i_s') . '.sql';
            $path = storage_path('app/backups/' . $filename);

            // Create backups directory if it doesn't exist
            if (!file_exists(dirname($path))) {
                mkdir(dirname($path), 0755, true);
            }

            // Get database configuration
            $database = config('database.connections.mysql.database');

            // Create SQL backup using PHP
            $sql = $this->generateSQLBackup();

            // Write backup to file
            file_put_contents($path, $sql);

            if (file_exists($path) && filesize($path) > 0) {
                return redirect()->route('admin.database.backup')
                    ->with('success', 'تم إنشاء النسخة الاحتياطية بنجاح: ' . $filename);
            } else {
                return redirect()->route('admin.database.backup')
                    ->with('error', 'فشل في إنشاء النسخة الاحتياطية');
            }
        } catch (\Exception $e) {
            return redirect()->route('admin.database.backup')
                ->with('error', 'خطأ: ' . $e->getMessage());
        }
    }

    /**
     * Download backup file.
     */
    public function downloadBackup($filename)
    {
        $path = storage_path('app/backups/' . $filename);

        if (!file_exists($path)) {
            abort(404, 'الملف غير موجود');
        }

        return response()->download($path);
    }

    /**
     * Delete backup file.
     */
    public function deleteBackup($filename)
    {
        $path = storage_path('app/backups/' . $filename);

        if (file_exists($path)) {
            unlink($path);
            return redirect()->route('admin.database.backup')
                ->with('success', 'تم حذف النسخة الاحتياطية بنجاح');
        }

        return redirect()->route('admin.database.backup')
            ->with('error', 'الملف غير موجود');
    }

    /**
     * Show migrations page.
     */
    public function migrations()
    {
        $migrations = $this->getMigrationStatus();

        return view('admin.database.migrations', compact('migrations'));
    }

    /**
     * Run pending migrations.
     */
    public function runMigrations()
    {
        try {
            Artisan::call('migrate', ['--force' => true]);
            $output = Artisan::output();

            return redirect()->route('admin.database.migrations')
                ->with('success', 'تم تشغيل الترحيلات بنجاح')
                ->with('output', $output);
        } catch (\Exception $e) {
            return redirect()->route('admin.database.migrations')
                ->with('error', 'خطأ في تشغيل الترحيلات: ' . $e->getMessage());
        }
    }

    /**
     * Rollback last migration batch.
     */
    public function rollbackMigrations()
    {
        try {
            Artisan::call('migrate:rollback', ['--force' => true]);
            $output = Artisan::output();

            return redirect()->route('admin.database.migrations')
                ->with('success', 'تم التراجع عن الترحيلات بنجاح')
                ->with('output', $output);
        } catch (\Exception $e) {
            return redirect()->route('admin.database.migrations')
                ->with('error', 'خطأ في التراجع عن الترحيلات: ' . $e->getMessage());
        }
    }

    /**
     * Get database statistics.
     */
    private function getDatabaseStats()
    {
        try {
            // Test database connection
            DB::connection()->getPdo();

            $connection = config('database.default');
            $stats = [
                'database_name' => config("database.connections.{$connection}.database") ?? 'غير محدد',
                'total_tables' => $this->getTotalTables(),
                'database_size' => $this->getDatabaseSize(),
                'connection_status' => 'متصل',
            ];

            // Get table counts for main tables
            $tables = ['users', 'jobs', 'applications', 'notifications'];
            $stats['table_counts'] = [];

            foreach ($tables as $table) {
                try {
                    if (Schema::hasTable($table)) {
                        $stats['table_counts'][$table] = DB::table($table)->count();
                    }
                } catch (\Exception $e) {
                    $stats['table_counts'][$table] = 0;
                }
            }

            return $stats;
        } catch (\Exception $e) {
            return [
                'database_name' => config('database.connections.mysql.database') ?? 'غير محدد',
                'total_tables' => 0,
                'database_size' => 0,
                'connection_status' => 'خطأ في الاتصال: ' . $e->getMessage(),
                'table_counts' => [],
                'error' => true
            ];
        }
    }

    /**
     * Get total number of tables.
     */
    private function getTotalTables()
    {
        try {
            $connection = config('database.default');
            $database = config("database.connections.{$connection}.database");

            if (config("database.connections.{$connection}.driver") === 'mysql') {
                $result = DB::select("
                    SELECT COUNT(*) as table_count
                    FROM information_schema.tables
                    WHERE table_schema = ?
                ", [$database]);

                return $result[0]->table_count ?? 0;
            } else {
                // For SQLite and other databases, use Laravel's Schema
                return count(Schema::getAllTables());
            }
        } catch (\Exception $e) {
            // Fallback to Schema method
            try {
                return count(Schema::getAllTables());
            } catch (\Exception $e2) {
                return 0;
            }
        }
    }

    /**
     * Get table information.
     */
    private function getTableInfo()
    {
        try {
            $database = config('database.connections.mysql.database');
            $tables = [];

            // Get table names from information_schema
            $tableNames = DB::select("
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = ?
                ORDER BY table_name
            ", [$database]);

            foreach ($tableNames as $tableObj) {
                $tableName = $tableObj->table_name;
                try {
                    $tables[] = [
                        'name' => $tableName,
                        'rows' => DB::table($tableName)->count(),
                        'size' => $this->getTableSize($tableName),
                    ];
                } catch (\Exception $e) {
                    $tables[] = [
                        'name' => $tableName,
                        'rows' => 0,
                        'size' => 0,
                    ];
                }
            }

            return collect($tables)->sortBy('name');
        } catch (\Exception $e) {
            return collect([]);
        }
    }

    /**
     * Get database size.
     */
    private function getDatabaseSize()
    {
        try {
            $database = config('database.connections.mysql.database');

            // Try to get database size from information_schema
            $result = DB::select("
                SELECT ROUND(SUM(IFNULL(data_length, 0) + IFNULL(index_length, 0)) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.tables
                WHERE table_schema = ?
            ", [$database]);

            $size = $result[0]->size_mb ?? 0;

            // If size is 0 or null, try alternative method
            if ($size == 0) {
                $result = DB::select("
                    SELECT ROUND(SUM(IFNULL(data_length, 0) + IFNULL(index_length, 0)) / 1024 / 1024, 2) AS size_mb
                    FROM information_schema.tables
                    WHERE table_schema = DATABASE()
                ");
                $size = $result[0]->size_mb ?? 0;
            }

            return max(0, (float)$size);
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get table size.
     */
    private function getTableSize($tableName)
    {
        try {
            $database = config('database.connections.mysql.database');
            $result = DB::select("
                SELECT ROUND((IFNULL(data_length, 0) + IFNULL(index_length, 0)) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.tables
                WHERE table_schema = ? AND table_name = ?
            ", [$database, $tableName]);

            return max(0, (float)($result[0]->size_mb ?? 0));
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Generate SQL backup using PHP.
     */
    private function generateSQLBackup()
    {
        $database = config('database.connections.mysql.database');
        $sql = "-- Database Backup\n";
        $sql .= "-- Generated on: " . date('Y-m-d H:i:s') . "\n";
        $sql .= "-- Database: {$database}\n\n";

        $sql .= "SET FOREIGN_KEY_CHECKS=0;\n\n";

        // Get all tables
        $tables = DB::select('SHOW TABLES');
        $tableKey = 'Tables_in_' . $database;

        foreach ($tables as $table) {
            $tableName = $table->$tableKey;

            // Get table structure
            $createTable = DB::select("SHOW CREATE TABLE `{$tableName}`");
            $sql .= "-- Table structure for table `{$tableName}`\n";
            $sql .= "DROP TABLE IF EXISTS `{$tableName}`;\n";
            $sql .= $createTable[0]->{'Create Table'} . ";\n\n";

            // Get table data
            $rows = DB::table($tableName)->get();
            if ($rows->count() > 0) {
                $sql .= "-- Dumping data for table `{$tableName}`\n";
                $sql .= "INSERT INTO `{$tableName}` VALUES\n";

                $values = [];
                foreach ($rows as $row) {
                    $rowData = [];
                    foreach ($row as $value) {
                        if (is_null($value)) {
                            $rowData[] = 'NULL';
                        } else {
                            $rowData[] = "'" . addslashes($value) . "'";
                        }
                    }
                    $values[] = '(' . implode(',', $rowData) . ')';
                }

                $sql .= implode(",\n", $values) . ";\n\n";
            }
        }

        $sql .= "SET FOREIGN_KEY_CHECKS=1;\n";

        return $sql;
    }

    /**
     * Get backup files.
     */
    private function getBackupFiles()
    {
        $backupPath = storage_path('app/backups');

        if (!is_dir($backupPath)) {
            return collect([]);
        }

        $files = glob($backupPath . '/*.sql');
        $backups = [];

        foreach ($files as $file) {
            $backups[] = [
                'name' => basename($file),
                'size' => filesize($file),
                'created_at' => date('Y-m-d H:i:s', filemtime($file)),
            ];
        }

        return collect($backups)->sortByDesc('created_at');
    }

    /**
     * Get migration status.
     */
    private function getMigrationStatus()
    {
        try {
            $ran = DB::table('migrations')->pluck('migration')->toArray();
            $migrationFiles = glob(database_path('migrations/*.php'));

            $migrations = [];
            foreach ($migrationFiles as $file) {
                $filename = basename($file, '.php');
                $migrations[] = [
                    'file' => $filename,
                    'status' => in_array($filename, $ran) ? 'مُنفذ' : 'معلق',
                    'batch' => in_array($filename, $ran) ?
                        DB::table('migrations')->where('migration', $filename)->value('batch') : null
                ];
            }

            return collect($migrations)->sortBy('file');
        } catch (\Exception $e) {
            return collect([]);
        }
    }
}
