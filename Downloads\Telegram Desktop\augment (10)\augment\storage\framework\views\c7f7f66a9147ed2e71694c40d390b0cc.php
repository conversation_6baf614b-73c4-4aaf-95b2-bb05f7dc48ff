<?php $__env->startSection('title', 'إدارة قاعدة البيانات - Hire Me'); ?>
<?php $__env->startSection('header_title', 'إدارة قاعدة البيانات'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <div class="mb-6">
        <h2 class="text-2xl font-bold">إدارة قاعدة البيانات</h2>
        <p class="text-gray-600 dark:text-gray-400 mt-1">مراقبة وإدارة قاعدة البيانات والنسخ الاحتياطية</p>
    </div>

    <!-- Success/Error Messages -->
    <?php if(session('success')): ?>
        <div class="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg">
            <div class="flex items-center">
                <i class="fas fa-check-circle ml-2"></i>
                <?php echo e(session('success')); ?>

            </div>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
            <div class="flex items-center">
                <i class="fas fa-exclamation-circle ml-2"></i>
                <?php echo e(session('error')); ?>

            </div>
        </div>
    <?php endif; ?>

    <!-- Database Overview -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- Database Stats -->
        <div class="lg:col-span-2">
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">معلومات قاعدة البيانات</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">اسم قاعدة البيانات</span>
                            <span class="text-sm text-gray-900 dark:text-white font-mono"><?php echo e($stats['database_name'] ?? 'غير محدد'); ?></span>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">عدد الجداول</span>
                            <span class="text-sm text-gray-900 dark:text-white">
                                <?php if(isset($stats['error'])): ?>
                                    <span class="text-red-500">خطأ</span>
                                <?php else: ?>
                                    <?php echo e(number_format($stats['total_tables'] ?? 0)); ?>

                                <?php endif; ?>
                            </span>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">حجم قاعدة البيانات</span>
                            <span class="text-sm text-gray-900 dark:text-white">
                                <?php if(isset($stats['error'])): ?>
                                    <span class="text-red-500">غير متاح</span>
                                <?php else: ?>
                                    <?php echo e(number_format($stats['database_size'] ?? 0, 2)); ?> MB
                                <?php endif; ?>
                            </span>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">حالة الاتصال</span>
                            <span class="text-sm <?php echo e(isset($stats['error']) ? 'text-red-600' : 'text-green-600'); ?>">
                                <i class="fas <?php echo e(isset($stats['error']) ? 'fa-times-circle' : 'fa-check-circle'); ?> ml-1"></i>
                                <?php echo e($stats['connection_status'] ?? 'غير معروف'); ?>

                            </span>
                        </div>

                        <?php if(isset($stats['table_counts']) && !empty($stats['table_counts'])): ?>
                            <?php $__currentLoopData = $stats['table_counts']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $table => $count): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        <?php if($table === 'users'): ?> المستخدمون
                                        <?php elseif($table === 'jobs'): ?> الوظائف
                                        <?php elseif($table === 'applications'): ?> الطلبات
                                        <?php elseif($table === 'notifications'): ?> الإشعارات
                                        <?php else: ?> <?php echo e(ucfirst($table)); ?>

                                        <?php endif; ?>
                                    </span>
                                    <span class="text-sm text-gray-900 dark:text-white"><?php echo e(number_format($count)); ?> سجل</span>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <div class="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg text-center">
                                <span class="text-sm text-gray-500 dark:text-gray-400">لا توجد بيانات متاحة</span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="space-y-6">
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">إجراءات سريعة</h3>

                <div class="space-y-3">
                    <a href="<?php echo e(route('admin.database.backup')); ?>" class="w-full flex items-center gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors">
                        <i class="fas fa-download"></i>
                        <span>النسخ الاحتياطية</span>
                    </a>

                    <a href="<?php echo e(route('admin.database.migrations')); ?>" class="w-full flex items-center gap-3 p-3 bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors">
                        <i class="fas fa-code-branch"></i>
                        <span>إدارة الترحيلات</span>
                    </a>

                    <button onclick="refreshStats()" class="w-full flex items-center gap-3 p-3 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors">
                        <i class="fas fa-sync-alt"></i>
                        <span>تحديث الإحصائيات</span>
                    </button>
                </div>
            </div>

            <!-- System Info -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">معلومات النظام</h3>

                <div class="space-y-3 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">إصدار PHP</span>
                        <span class="text-gray-900 dark:text-white"><?php echo e(PHP_VERSION); ?></span>
                    </div>

                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">إصدار Laravel</span>
                        <span class="text-gray-900 dark:text-white"><?php echo e(app()->version()); ?></span>
                    </div>

                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">البيئة</span>
                        <span class="text-gray-900 dark:text-white"><?php echo e(app()->environment()); ?></span>
                    </div>

                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">وضع التطوير</span>
                        <span class="text-gray-900 dark:text-white"><?php echo e(config('app.debug') ? 'مفعل' : 'معطل'); ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tables Information -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-bold">جداول قاعدة البيانات</h3>
        </div>

        <?php if($tables->isNotEmpty()): ?>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-800">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">اسم الجدول</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">عدد السجلات</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الحجم (MB)</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-dark-card divide-y divide-gray-200 dark:divide-gray-700">
                        <?php $__currentLoopData = $tables; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $table): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <i class="fas fa-table text-gray-400 ml-2"></i>
                                        <span class="text-sm font-medium text-gray-900 dark:text-white font-mono"><?php echo e($table['name']); ?></span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                    <?php echo e(number_format($table['rows'])); ?>

                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                    <?php if($table['size'] > 0): ?>
                                        <?php echo e(number_format($table['size'], 2)); ?>

                                    <?php else: ?>
                                        <span class="text-gray-400">0.00</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="px-6 py-12 text-center text-gray-500 dark:text-gray-400">
                <i class="fas fa-table text-4xl mb-4"></i>
                <p class="text-lg font-medium mb-2">لا توجد جداول</p>
                <p>لم يتم العثور على أي جداول في قاعدة البيانات</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php $__env->startSection('scripts'); ?>
<script>
    function refreshStats() {
        // Show loading state
        const button = event.target.closest('button');
        const icon = button.querySelector('i');
        const originalClass = icon.className;

        icon.className = 'fas fa-spinner fa-spin';
        button.disabled = true;

        // Reload page after a short delay
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    }
</script>
<?php $__env->stopSection(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\Telegram Desktop\augment (10)\augment\resources\views/admin/database/index.blade.php ENDPATH**/ ?>