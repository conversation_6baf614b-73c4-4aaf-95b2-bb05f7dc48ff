<!-- resources/views/layouts/app.blade.php -->
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hire Me - منصة التوظيف المتكاملة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="{{ asset('css/job-enhancements.css') }}">
    <link rel="stylesheet" href="{{ asset('css/theme-transitions.css') }}">
    <!-- نظام الثيم المحسن - تحميل مبكر -->
    <script>
        // تطبيق الثيم فوراً قبل تحميل أي شيء آخر
        (function() {
            const savedTheme = localStorage.getItem('theme');
            const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;

            if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        })();
    </script>
    <script src="{{ asset('js/theme-system.js') }}"></script>
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#5D5CDE',
                        secondary: '#F6F7FB',
                        'dark-bg': '#181818',
                        'dark-card': '#222222',
                    },
                    fontFamily: {
                        'tajawal': ['Tajawal', 'sans-serif']
                    }
                },
            }
        }

        // نظام الثيم يتم تحميله من ملف منفصل
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');

        body {
            font-family: 'Tajawal', sans-serif;
        }

        .gradient-bg {
            background: linear-gradient(90deg, #5D5CDE 0%, #8584E8 100%);
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: #5D5CDE;
            border-radius: 10px;
        }

        .dark ::-webkit-scrollbar-track {
            background: #333;
        }

        /* تحسينات للنوافذ المنبثقة */
        [x-cloak] {
            display: none !important;
        }

        .notification-dropdown {
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            will-change: transform, opacity;
        }

        .notification-item {
            transition: all 0.2s ease-in-out;
        }

        .notification-item:hover {
            transform: translateX(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* تحسينات للأجهزة المحمولة */
        @media (max-width: 768px) {
            .notification-dropdown {
                position: fixed !important;
                top: 60px !important;
                left: 10px !important;
                right: 10px !important;
                width: auto !important;
                max-width: none !important;
            }

            .notification-item:hover {
                transform: none;
                box-shadow: none;
            }
        }

        /* تحسين إمكانية الوصول */
        .notification-dropdown:focus-within {
            outline: 2px solid #3b82f6;
            outline-offset: 2px;
        }
    </style>
</head>
<body class="bg-white dark:bg-dark-bg text-gray-800 dark:text-gray-200 transition-colors duration-200">
    @include('layouts.header')

    <!-- Flash Messages -->
    @if(session('success'))
    <div id="success-message" class="fixed top-20 right-4 z-50 bg-green-500 text-white px-6 py-4 rounded-lg shadow-lg transform translate-x-full transition-transform duration-300">
        <div class="flex items-center gap-3">
            <i class="fas fa-check-circle text-xl"></i>
            <span>{{ session('success') }}</span>
            <button onclick="closeMessage('success-message')" class="mr-4 text-white hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
    @endif

    @if(session('error'))
    <div id="error-message" class="fixed top-20 right-4 z-50 bg-red-500 text-white px-6 py-4 rounded-lg shadow-lg transform translate-x-full transition-transform duration-300">
        <div class="flex items-center gap-3">
            <i class="fas fa-exclamation-circle text-xl"></i>
            <span>{{ session('error') }}</span>
            <button onclick="closeMessage('error-message')" class="mr-4 text-white hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
    @endif

    @yield('content')

    @include('layouts.footer')

    <script>
        // Flash Messages
        function closeMessage(messageId) {
            const message = document.getElementById(messageId);
            if (message) {
                message.classList.add('translate-x-full');
                setTimeout(() => {
                    message.remove();
                }, 300);
            }
        }

        // Show flash messages
        document.addEventListener('DOMContentLoaded', () => {
            const successMessage = document.getElementById('success-message');
            const errorMessage = document.getElementById('error-message');

            if (successMessage) {
                setTimeout(() => {
                    successMessage.classList.remove('translate-x-full');
                }, 100);
                setTimeout(() => {
                    closeMessage('success-message');
                }, 5000);
            }

            if (errorMessage) {
                setTimeout(() => {
                    errorMessage.classList.remove('translate-x-full');
                }, 100);
                setTimeout(() => {
                    closeMessage('error-message');
                }, 7000);
            }
        });

        // Animation for blobs
        document.addEventListener('DOMContentLoaded', () => {
            document.styleSheets[0].insertRule(`
                @keyframes blob {
                    0% {
                        transform: translate(0px, 0px) scale(1);
                    }
                    33% {
                        transform: translate(30px, -50px) scale(1.1);
                    }
                    66% {
                        transform: translate(-20px, 20px) scale(0.9);
                    }
                    100% {
                        transform: translate(0px, 0px) scale(1);
                    }
                }
            `);

            document.styleSheets[0].insertRule(`
                .animate-blob {
                    animation: blob 7s infinite;
                }
            `);

            document.styleSheets[0].insertRule(`
                .animation-delay-2000 {
                    animation-delay: 2s;
                }
            `);

            document.styleSheets[0].insertRule(`
                .animation-delay-4000 {
                    animation-delay: 4s;
                }
            `);

            document.styleSheets[0].insertRule(`
                .animate-spin-slow {
                    animation: spin 8s linear infinite;
                }
            `);
        });

        // تحسينات Alpine.js للإشعارات
        document.addEventListener('alpine:init', () => {
            console.log('Alpine.js initialized for user notifications');
        });

        // تحديث الإشعارات كل 30 ثانية
        setInterval(function() {
            fetch('/notifications/unread', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                }
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // تحديث عداد الإشعارات في الهيدر
                    const notificationBadge = document.querySelector('.notification-badge');
                    if (notificationBadge && data.count !== undefined) {
                        notificationBadge.textContent = data.count;
                        notificationBadge.style.display = data.count > 0 ? 'flex' : 'none';
                    }
                })
                .catch(error => {
                    console.error('Error fetching notifications:', error);
                    // في حالة الخطأ، لا نعرض أي شيء للمستخدم لتجنب الإزعاج
                });
        }, 30000);
    </script>
</body>
</html>
