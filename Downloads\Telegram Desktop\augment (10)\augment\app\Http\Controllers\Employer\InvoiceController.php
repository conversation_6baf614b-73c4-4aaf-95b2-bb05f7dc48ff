<?php

namespace App\Http\Controllers\Employer;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class InvoiceController extends Controller
{
    // تحميل الفاتورة
    public function download($id, Request $request)
    {
        $format = $request->query('format', 'pdf');

        // إنشاء محتوى الفاتورة الوهمية
        if ($format === 'excel') {
            return $this->generateExcelInvoice($id);
        } else {
            return $this->generatePdfInvoice($id);
        }
    }

    /**
     * إنشاء فاتورة PDF وهمية
     */
    private function generatePdfInvoice($id)
    {
        $invoiceData = $this->getInvoiceData($id);

        $html = '
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>فاتورة رقم ' . $id . '</title>
            <style>
                body { font-family: Arial, sans-serif; direction: rtl; }
                .header { text-align: center; margin-bottom: 30px; }
                .invoice-details { margin: 20px 0; }
                .table { width: 100%; border-collapse: collapse; }
                .table th, .table td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                .table th { background-color: #f2f2f2; }
                .total { font-weight: bold; background-color: #e8f4fd; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>فاتورة رقم ' . $id . '</h1>
                <p>تاريخ الإصدار: ' . now()->format('Y-m-d') . '</p>
            </div>

            <div class="invoice-details">
                <h3>تفاصيل الفاتورة:</h3>
                <p><strong>اسم الباقة:</strong> ' . $invoiceData['package_name'] . '</p>
                <p><strong>فترة الاشتراك:</strong> ' . $invoiceData['period'] . '</p>
                <p><strong>تاريخ البداية:</strong> ' . $invoiceData['start_date'] . '</p>
                <p><strong>تاريخ الانتهاء:</strong> ' . $invoiceData['end_date'] . '</p>
                <p><strong>حد الوظائف:</strong> ' . $invoiceData['job_limit'] . ' وظيفة شهرياً</p>
                <p><strong>الوظائف المستخدمة:</strong> ' . $invoiceData['jobs_used'] . ' وظيفة</p>
            </div>

            <table class="table">
                <thead>
                    <tr>
                        <th>الوصف</th>
                        <th>الكمية</th>
                        <th>السعر</th>
                        <th>المجموع</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>' . $invoiceData['package_name'] . '</td>
                        <td>1</td>
                        <td>' . $invoiceData['price'] . ' دينار ليبي</td>
                        <td>' . $invoiceData['price'] . ' دينار ليبي</td>
                    </tr>
                    <tr class="total">
                        <td colspan="3"><strong>المجموع الكلي</strong></td>
                        <td><strong>' . $invoiceData['price'] . ' دينار ليبي</strong></td>
                    </tr>
                </tbody>
            </table>

            <div style="margin-top: 30px;">
                <p><strong>ملاحظة:</strong> هذه فاتورة تجريبية لأغراض العرض فقط.</p>
            </div>
        </body>
        </html>';

        return response($html, 200, [
            'Content-Type' => 'text/html; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="invoice-' . $id . '.html"'
        ]);
    }

    /**
     * إنشاء فاتورة Excel وهمية
     */
    private function generateExcelInvoice($id)
    {
        $invoiceData = $this->getInvoiceData($id);

        $csvContent = "الوصف,الكمية,السعر,المجموع\n";
        $csvContent .= $invoiceData['package_name'] . ",1," . $invoiceData['price'] . " دينار ليبي," . $invoiceData['price'] . " دينار ليبي\n";
        $csvContent .= "المجموع الكلي,,,". $invoiceData['price'] . " دينار ليبي\n";

        return response($csvContent, 200, [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="invoice-' . $id . '.csv"'
        ]);
    }

    /**
     * الحصول على بيانات الفاتورة من الاشتراك الحالي
     */
    private function getInvoiceData($id)
    {
        $employer = auth()->user()->employer;

        if (!$employer) {
            return [
                'package_name' => 'الباقة المجانية',
                'price' => 0,
                'period' => 'شهر واحد',
                'start_date' => now()->format('Y-m-d'),
                'end_date' => now()->addMonth()->format('Y-m-d'),
            ];
        }

        $currentSubscription = $employer->currentSubscription();

        if (!$currentSubscription) {
            return [
                'package_name' => 'الباقة المجانية',
                'price' => 0,
                'period' => 'شهر واحد',
                'start_date' => now()->format('Y-m-d'),
                'end_date' => now()->addMonth()->format('Y-m-d'),
            ];
        }

        $packageDetails = $currentSubscription->getPackageDetails();

        return [
            'package_name' => $packageDetails['name'],
            'price' => $currentSubscription->price,
            'period' => 'من ' . $currentSubscription->starts_at->format('Y-m-d') . ' إلى ' . $currentSubscription->ends_at->format('Y-m-d'),
            'start_date' => $currentSubscription->starts_at->format('Y-m-d'),
            'end_date' => $currentSubscription->ends_at->format('Y-m-d'),
            'job_limit' => $currentSubscription->job_limit == -1 ? 'غير محدود' : $currentSubscription->job_limit,
            'jobs_used' => $currentSubscription->jobs_posted_this_month,
        ];
    }
}
