/* تحسينات الإشعارات - Notifications Enhancements */

/* Stat Cards */
.stat-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.dark .stat-card {
    background: rgba(31, 41, 55, 0.95);
    border: 1px solid rgba(75, 85, 99, 0.2);
}

/* Glass Card Effect */
.glass-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dark .glass-card {
    background: rgba(31, 41, 55, 0.95);
    border: 1px solid rgba(75, 85, 99, 0.2);
}

/* Dropdown Menu Styling */
.dropdown-menu {
    backdrop-filter: blur(10px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .dropdown-menu {
    border: 1px solid rgba(75, 85, 99, 0.2);
}

/* Notification Item Enhancements */
.notification-item {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.notification-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Unread Notification Indicator */
.notification-item.unread::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 60%;
    background: linear-gradient(to bottom, #3b82f6, #1d4ed8);
    border-radius: 0 2px 2px 0;
}

/* Button Enhancements */
.btn-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-gradient:hover {
    transform: translateY(-1px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.btn-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-gradient:hover::before {
    left: 100%;
}

/* Icon Animations */
.icon-bounce {
    animation: iconBounce 2s infinite;
}

@keyframes iconBounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-3px);
    }
    60% {
        transform: translateY(-2px);
    }
}

/* Fade In Animation */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Stagger Animation */
.stagger-item {
    animation: staggerFadeIn 0.6s ease-out;
    animation-fill-mode: both;
}

.stagger-item:nth-child(1) { animation-delay: 0.1s; }
.stagger-item:nth-child(2) { animation-delay: 0.2s; }
.stagger-item:nth-child(3) { animation-delay: 0.3s; }
.stagger-item:nth-child(4) { animation-delay: 0.4s; }

@keyframes staggerFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Pulse Effect for New Notifications */
.pulse-new {
    animation: pulseNew 2s infinite;
}

@keyframes pulseNew {
    0% {
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    }
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .stat-card {
        padding: 1rem;
    }

    .stat-card .flex {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .notification-item {
        padding: 1rem;
    }

    .notification-item .flex {
        flex-direction: column;
        gap: 0.75rem;
    }

    .notification-item .w-12 {
        width: 2.5rem;
        height: 2.5rem;
        align-self: center;
    }

    .dropdown-menu {
        position: fixed !important;
        left: 1rem !important;
        right: 1rem !important;
        width: auto !important;
        margin-top: 0.5rem;
    }
}

/* Dark Mode Enhancements */
@media (prefers-color-scheme: dark) {
    .notification-item.unread::before {
        background: linear-gradient(to bottom, #60a5fa, #3b82f6);
    }

    .btn-gradient {
        background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    }
}

/* Focus Styles for Accessibility */
.notification-item:focus,
button:focus,
a:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Loading State */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Smooth Transitions */
* {
    transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
}

/* Custom Scrollbar */
.notification-list::-webkit-scrollbar {
    width: 6px;
}

.notification-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.notification-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.notification-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.dark .notification-list::-webkit-scrollbar-track {
    background: #374151;
}

.dark .notification-list::-webkit-scrollbar-thumb {
    background: #6b7280;
}

.dark .notification-list::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* Enhanced Pagination */
.pagination {
    display: flex;
    gap: 0.25rem;
}

.pagination a,
.pagination span {
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.875rem;
}

.pagination a:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
    transform: translateY(-1px);
}

.pagination .active span {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border-color: #3b82f6;
}

.dark .pagination a,
.dark .pagination span {
    border-color: #374151;
    background: #1f2937;
    color: #d1d5db;
}

.dark .pagination a:hover {
    background: #374151;
    border-color: #6b7280;
}

/* Filter Badges */
.filter-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.filter-badge:hover {
    transform: scale(1.05);
}

.filter-badge a {
    color: inherit;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.filter-badge a:hover {
    opacity: 1;
}

/* Search Results Counter */
.search-results-info {
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
    border: 1px solid #0ea5e9;
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
}

.dark .search-results-info {
    background: linear-gradient(135deg, #0c4a6e, #075985);
    border-color: #0284c7;
}

/* Quick Filter Buttons */
.quick-filters {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.quick-filter-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    background: white;
    color: #374151;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.quick-filter-btn:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
}

.quick-filter-btn.active {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

.dark .quick-filter-btn {
    background: #1f2937;
    color: #d1d5db;
    border-color: #374151;
}

.dark .quick-filter-btn:hover {
    background: #374151;
}

/* Empty State Enhancement */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
}

.empty-state-icon {
    width: 4rem;
    height: 4rem;
    margin: 0 auto 1rem;
    background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #9ca3af;
}

.dark .empty-state-icon {
    background: linear-gradient(135deg, #374151, #4b5563);
    color: #6b7280;
}
