@extends('layouts.employer')

@section('title', 'البحث عن مرشحين - Hire Me')
@section('header_title', 'البحث عن مرشحين')

@push('head')
<meta name="csrf-token" content="{{ csrf_token() }}">
@endpush

@section('content')
<div class="p-8 space-y-8">
    <!-- Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">البحث عن مرشحين</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">ابحث عن المرشحين المناسبين لوظائفك</p>
        </div>

        <div class="flex gap-3 mt-4 md:mt-0">
            <a href="{{ route('employer.candidates.index') }}" class="px-6 py-3 bg-green-600 text-white rounded-xl hover:bg-green-700 transition-all duration-300 flex items-center gap-2">
                <i class="fas fa-bookmark"></i>
                المرشحون المحفوظون
            </a>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="glass-card rounded-xl p-6">
        <form method="GET" action="{{ route('employer.candidates.search') }}" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <input type="text" name="search" value="{{ request('search') }}"
                           placeholder="البحث بالاسم أو المسمى الوظيفي..."
                           class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                </div>

                <div>
                    <input type="text" name="skills" value="{{ request('skills') }}"
                           placeholder="المهارات..."
                           class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                </div>

                <div>
                    <input type="text" name="location" value="{{ request('location') }}"
                           placeholder="الموقع..."
                           class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                </div>

                <div>
                    <button type="submit" class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-search"></i>
                        بحث
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Results -->
    <div class="glass-card rounded-xl p-6">
        @if($candidates->count() > 0)
            <div class="mb-4">
                <p class="text-gray-600 dark:text-gray-400">
                    تم العثور على {{ $candidates->total() }} مرشح
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($candidates as $candidate)
                    <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
                        <div class="flex items-start gap-4">
                            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
                                {{ substr($candidate->user->name, 0, 1) }}
                            </div>
                            <div class="flex-1">
                                <h3 class="font-semibold text-gray-900 dark:text-white">{{ $candidate->user->name }}</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">{{ $candidate->job_title ?? 'غير محدد' }}</p>
                                <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">
                                    <i class="fas fa-map-marker-alt"></i>
                                    {{ $candidate->location ?? 'غير محدد' }}
                                </p>
                            </div>
                        </div>

                        @if($candidate->skills)
                            <div class="mt-4">
                                <div class="flex flex-wrap gap-1">
                                    @foreach(array_slice(explode(',', $candidate->skills), 0, 3) as $skill)
                                        <span class="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full">
                                            {{ trim($skill) }}
                                        </span>
                                    @endforeach
                                    @if(count(explode(',', $candidate->skills)) > 3)
                                        <span class="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 text-xs rounded-full">
                                            +{{ count(explode(',', $candidate->skills)) - 3 }}
                                        </span>
                                    @endif
                                </div>
                            </div>
                        @endif

                        <div class="mt-4 flex items-center justify-between">
                            <div class="text-xs text-gray-500 dark:text-gray-500">
                                <i class="fas fa-calendar"></i>
                                انضم {{ $candidate->created_at->diffForHumans() }}
                            </div>
                            <div class="flex gap-2">
                                <a href="{{ route('employer.candidates.show', $candidate->id) }}" class="px-3 py-1 bg-blue-600 text-white text-xs rounded-lg hover:bg-blue-700 transition-colors">
                                    عرض
                                </a>
                                @if(in_array($candidate->id, $savedCandidateIds))
                                    <span class="px-3 py-1 bg-green-600 text-white text-xs rounded-lg flex items-center gap-1">
                                        <i class="fas fa-check"></i>
                                        محفوظ
                                    </span>
                                @else
                                    <button onclick="saveCandidate({{ $candidate->id }})" class="save-btn px-3 py-1 bg-gradient-to-r from-blue-600 to-indigo-600 text-white text-xs rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 flex items-center gap-1 shadow-md hover:shadow-lg transform hover:scale-105" data-candidate-id="{{ $candidate->id }}">
                                        <i class="fas fa-bookmark"></i>
                                        حفظ
                                    </button>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-8">
                {{ $candidates->appends(request()->query())->links() }}
            </div>
        @else
            <!-- Empty State -->
            <div class="text-center py-12">
                <div class="w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-search text-4xl text-gray-400"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">لا توجد نتائج</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-6">جرب تعديل معايير البحث للعثور على مرشحين</p>
            </div>
        @endif
    </div>
</div>

<style>
.glass-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-card {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
}
</style>

@push('scripts')
<script>
function saveCandidate(candidateId) {
    console.log('🔄 بدء عملية حفظ المرشح:', candidateId);

    // العثور على الزر وتغيير حالته
    const button = document.querySelector(`button[data-candidate-id="${candidateId}"]`);
    if (button) {
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin text-yellow-300"></i> <span class="animate-pulse">جاري الحفظ...</span>';
        button.classList.add('opacity-75', 'cursor-not-allowed');
        button.classList.remove('hover:scale-105');
        button.style.background = 'linear-gradient(45deg, #6366f1, #8b5cf6)';
    }

    const csrfToken = document.querySelector('meta[name="csrf-token"]');
    if (!csrfToken) {
        console.error('❌ CSRF token غير موجود');
        showNotification('❌ خطأ في النظام: CSRF token غير موجود', 'error');
        resetButton(button, candidateId);
        return;
    }

    console.log('✅ CSRF Token موجود');

    const url = `/employer/candidates/${candidateId}/save`;
    console.log('📡 إرسال طلب إلى:', url);

    fetch(url, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': csrfToken.getAttribute('content'),
            'Content-Type': 'application/json',
            'Accept': 'application/json',
        }
    })
    .then(response => {
        console.log('📨 استجابة الخادم:', response.status, response.statusText);

        // معالجة حالات الخطأ المختلفة
        if (response.status === 401) {
            showNotification('🔐 يجب تسجيل الدخول أولاً', 'error');
            setTimeout(() => {
                window.location.href = '/login';
            }, 2000);
            return;
        }

        if (response.status === 403) {
            showNotification('🚫 ليس لديك صلاحية للقيام بهذا الإجراء', 'error');
            resetButton(button, candidateId);
            return;
        }

        if (response.status === 302) {
            showNotification('🔄 جاري إعادة التوجيه...', 'info');
            window.location.reload();
            return;
        }

        if (!response.ok) {
            throw new Error(`خطأ في الخادم: ${response.status}`);
        }

        return response.json();
    })
    .then(data => {
        if (!data) return; // في حالة redirect

        console.log('📄 بيانات الاستجابة:', data);

        if (data.success) {
            console.log('✅ تم الحفظ بنجاح');

            // إظهار رسالة نجاح مع اسم المرشح
            const candidateName = data.candidate_name || 'المرشح';
            showNotification(`🎉 تم حفظ ${candidateName} بنجاح! جاري التحويل...`, 'success');

            // التحويل إلى صفحة المرشحين المحفوظين
            setTimeout(() => {
                window.location.href = `/employer/candidates?saved=success&recently_saved=${candidateId}&candidate_name=${encodeURIComponent(candidateName)}`;
            }, 1500);

        } else {
            console.log('❌ فشل الحفظ:', data.message);
            showNotification('❌ ' + (data.message || 'حدث خطأ غير معروف'), 'error');
            resetButton(button, candidateId);
        }
    })
    .catch(error => {
        console.error('💥 خطأ في العملية:', error);
        showNotification('💥 حدث خطأ في الاتصال: ' + error.message, 'error');
        resetButton(button, candidateId);
    });
}

function resetButton(button, candidateId) {
    if (button) {
        button.disabled = false;
        button.innerHTML = '<i class="fas fa-bookmark"></i> حفظ';
        button.classList.remove('opacity-75', 'cursor-not-allowed');
        button.classList.add('hover:scale-105');
        button.style.background = '';
        button.className = button.className.replace(/bg-\S+/g, '') + ' bg-gradient-to-r from-blue-600 to-indigo-600';
    }
}

function showNotification(message, type = 'info') {
    // إنشاء إشعار مؤقت مع تصميم أجمل
    const notification = document.createElement('div');
    const bgColor = type === 'success' ? 'bg-gradient-to-r from-green-500 to-emerald-600' :
                    type === 'error' ? 'bg-gradient-to-r from-red-500 to-rose-600' :
                    'bg-gradient-to-r from-blue-500 to-indigo-600';

    notification.className = `fixed top-4 right-4 z-50 px-6 py-4 rounded-xl shadow-2xl text-white transition-all duration-500 transform translate-x-full ${bgColor}`;

    // إضافة أيقونة حسب النوع
    const icon = type === 'success' ? '🎉' : type === 'error' ? '❌' : 'ℹ️';
    notification.innerHTML = `
        <div class="flex items-center gap-3">
            <span class="text-xl">${icon}</span>
            <span class="font-medium">${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // إظهار الإشعار مع انيميشن
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // إضافة تأثير اهتزاز للنجاح
    if (type === 'success') {
        setTimeout(() => {
            notification.style.animation = 'pulse 0.5s ease-in-out';
        }, 500);
    }

    // إزالة الإشعار بعد 4 ثوان
    setTimeout(() => {
        notification.style.transform = 'translateX(100%) scale(0.8)';
        notification.style.opacity = '0';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 500);
    }, 4000);
}

// تشغيل عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 صفحة البحث عن المرشحين جاهزة');
    console.log('📊 عدد أزرار الحفظ:', document.querySelectorAll('.save-btn').length);
    console.log('✅ النظام جاهز للاستخدام');

    // إضافة تأثير hover للأزرار
    document.querySelectorAll('.save-btn').forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });
});


</script>
@endpush

@endsection
