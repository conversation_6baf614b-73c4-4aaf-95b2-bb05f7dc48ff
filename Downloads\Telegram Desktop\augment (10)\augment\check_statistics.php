<?php

require_once 'vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as Capsule;

// إعداد قاعدة البيانات
$capsule = new Capsule;

$capsule->addConnection([
    'driver' => 'mysql',
    'host' => 'localhost',
    'database' => 'augment',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'prefix' => '',
]);

$capsule->setAsGlobal();
$capsule->bootEloquent();

echo "=== فحص البيانات في قاعدة البيانات ===\n\n";

try {
    // فحص المستخدمين
    $totalUsers = Capsule::table('users')->count();
    echo "إجمالي المستخدمين: $totalUsers\n";
    
    $employers = Capsule::table('users')->where('role', 'employer')->count();
    echo "أصحاب العمل: $employers\n";
    
    $jobSeekers = Capsule::table('users')->where('role', 'job_seeker')->count();
    echo "الباحثين عن عمل: $jobSeekers\n\n";
    
    // فحص جدول employers
    $employerProfiles = Capsule::table('employers')->count();
    echo "ملفات أصحاب العمل: $employerProfiles\n";
    
    // فحص الوظائف
    $totalJobs = Capsule::table('jobs')->count();
    echo "إجمالي الوظائف: $totalJobs\n";
    
    $activeJobs = Capsule::table('jobs')->where('is_active', true)->count();
    echo "الوظائف النشطة: $activeJobs\n";
    
    // فحص الطلبات
    $totalApplications = Capsule::table('applications')->count();
    echo "إجمالي الطلبات: $totalApplications\n";
    
    $pendingApplications = Capsule::table('applications')->where('status', 'pending')->count();
    echo "الطلبات المعلقة: $pendingApplications\n\n";
    
    // فحص العلاقات
    echo "=== فحص العلاقات ===\n";
    
    // الوظائف مع أصحاب العمل
    $jobsWithEmployers = Capsule::table('jobs')
        ->join('employers', 'jobs.employer_id', '=', 'employers.id')
        ->count();
    echo "الوظائف المرتبطة بأصحاب العمل: $jobsWithEmployers\n";
    
    // الطلبات مع الوظائف
    $applicationsWithJobs = Capsule::table('applications')
        ->join('jobs', 'applications.job_id', '=', 'jobs.id')
        ->count();
    echo "الطلبات المرتبطة بالوظائف: $applicationsWithJobs\n";
    
    // عرض بعض البيانات التفصيلية
    echo "\n=== عينة من البيانات ===\n";
    
    $sampleEmployers = Capsule::table('employers')
        ->join('users', 'employers.user_id', '=', 'users.id')
        ->select('employers.id', 'employers.company_name', 'users.name', 'users.email')
        ->limit(5)
        ->get();
    
    echo "أصحاب العمل:\n";
    foreach ($sampleEmployers as $employer) {
        echo "- ID: {$employer->id}, الشركة: {$employer->company_name}, المستخدم: {$employer->name}\n";
    }
    
    $sampleJobs = Capsule::table('jobs')
        ->select('id', 'title', 'employer_id', 'is_active')
        ->limit(5)
        ->get();
    
    echo "\nالوظائف:\n";
    foreach ($sampleJobs as $job) {
        echo "- ID: {$job->id}, العنوان: {$job->title}, صاحب العمل: {$job->employer_id}, نشطة: " . ($job->is_active ? 'نعم' : 'لا') . "\n";
    }
    
    $sampleApplications = Capsule::table('applications')
        ->select('id', 'job_id', 'job_seeker_id', 'status')
        ->limit(5)
        ->get();
    
    echo "\nالطلبات:\n";
    foreach ($sampleApplications as $application) {
        echo "- ID: {$application->id}, الوظيفة: {$application->job_id}, الباحث: {$application->job_seeker_id}, الحالة: {$application->status}\n";
    }
    
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage() . "\n";
}
