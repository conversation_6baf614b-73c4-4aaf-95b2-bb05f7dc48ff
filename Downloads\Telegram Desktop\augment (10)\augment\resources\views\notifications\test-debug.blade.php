@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-12">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">اختبار الإشعارات</h1>
        
        <!-- معلومات المستخدم -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">معلومات المستخدم</h2>
            @auth
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <strong>الاسم:</strong> {{ auth()->user()->name }}
                    </div>
                    <div>
                        <strong>البريد الإلكتروني:</strong> {{ auth()->user()->email }}
                    </div>
                    <div>
                        <strong>الدور:</strong> {{ auth()->user()->role }}
                    </div>
                    <div>
                        <strong>ID:</strong> {{ auth()->user()->id }}
                    </div>
                </div>
            @else
                <p class="text-red-500">المستخدم غير مسجل دخول</p>
            @endauth
        </div>

        <!-- إحصائيات الإشعارات -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">إحصائيات الإشعارات</h2>
            @auth
                @php
                    $totalNotifications = auth()->user()->notifications()->count();
                    $unreadNotifications = auth()->user()->notifications()->where('is_read', false)->count();
                    $recentNotifications = auth()->user()->notifications()->latest()->limit(5)->get();
                @endphp
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div class="bg-blue-100 dark:bg-blue-900/30 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-blue-600">{{ $totalNotifications }}</div>
                        <div class="text-blue-800 dark:text-blue-300">إجمالي الإشعارات</div>
                    </div>
                    <div class="bg-orange-100 dark:bg-orange-900/30 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-orange-600">{{ $unreadNotifications }}</div>
                        <div class="text-orange-800 dark:text-orange-300">غير مقروءة</div>
                    </div>
                    <div class="bg-green-100 dark:bg-green-900/30 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-green-600">{{ $totalNotifications - $unreadNotifications }}</div>
                        <div class="text-green-800 dark:text-green-300">مقروءة</div>
                    </div>
                </div>

                <!-- آخر الإشعارات -->
                <h3 class="text-lg font-bold mb-4">آخر 5 إشعارات</h3>
                @if($recentNotifications->count() > 0)
                    <div class="space-y-3">
                        @foreach($recentNotifications as $notification)
                            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 {{ $notification->is_read ? 'bg-gray-50 dark:bg-gray-800' : 'bg-blue-50 dark:bg-blue-900/20' }}">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <p class="font-medium">{{ $notification->message }}</p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ $notification->created_at->diffForHumans() }}</p>
                                        @if($notification->type)
                                            <span class="inline-block bg-gray-200 dark:bg-gray-700 text-xs px-2 py-1 rounded mt-1">{{ $notification->type }}</span>
                                        @endif
                                    </div>
                                    <div>
                                        @if($notification->is_read)
                                            <span class="text-green-500 text-sm">مقروء</span>
                                        @else
                                            <span class="text-orange-500 text-sm">غير مقروء</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500 dark:text-gray-400">لا توجد إشعارات</p>
                @endif
            @else
                <p class="text-red-500">يجب تسجيل الدخول لعرض الإشعارات</p>
            @endauth
        </div>

        <!-- اختبار API -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">اختبار API الإشعارات</h2>
            <button id="testApiBtn" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-dark transition">
                اختبار /notifications/unread
            </button>
            <div id="apiResult" class="mt-4 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg hidden">
                <pre id="apiOutput"></pre>
            </div>
        </div>

        <!-- إنشاء إشعار تجريبي -->
        @auth
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-md p-6">
            <h2 class="text-xl font-bold mb-4">إنشاء إشعار تجريبي</h2>
            <form action="{{ route('notifications.test.create') }}" method="POST">
                @csrf
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium mb-2">نوع الإشعار</label>
                        <select name="type" class="w-full p-3 border border-gray-300 dark:border-gray-700 rounded-lg">
                            <option value="test">اختبار</option>
                            <option value="job_application">طلب وظيفة</option>
                            <option value="system_announcement">إعلان النظام</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2">الرسالة</label>
                        <input type="text" name="message" value="هذا إشعار تجريبي" class="w-full p-3 border border-gray-300 dark:border-gray-700 rounded-lg">
                    </div>
                </div>
                <button type="submit" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition">
                    إنشاء إشعار تجريبي
                </button>
            </form>
        </div>
        @endauth
    </div>
</div>

<script>
document.getElementById('testApiBtn').addEventListener('click', function() {
    const resultDiv = document.getElementById('apiResult');
    const outputPre = document.getElementById('apiOutput');
    
    resultDiv.classList.remove('hidden');
    outputPre.textContent = 'جاري الاختبار...';
    
    fetch('/notifications/unread', {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        }
    })
    .then(response => {
        return response.text().then(text => {
            try {
                const data = JSON.parse(text);
                return { status: response.status, data: data, raw: text };
            } catch (e) {
                return { status: response.status, error: 'Invalid JSON', raw: text };
            }
        });
    })
    .then(result => {
        outputPre.textContent = JSON.stringify(result, null, 2);
    })
    .catch(error => {
        outputPre.textContent = 'خطأ: ' + error.message;
    });
});
</script>
@endsection
