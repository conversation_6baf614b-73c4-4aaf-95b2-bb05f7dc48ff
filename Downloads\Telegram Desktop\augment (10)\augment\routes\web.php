<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\JobController;
use App\Http\Controllers\EmployerController;
use App\Http\Controllers\JobSeekerController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\Admin\ReviewController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Artisan;

Route::get('/', [HomeController::class, 'index'])->name('home');



// إنشاء بيانات تجريبية
Route::get('/test-data', [App\Http\Controllers\TestDataController::class, 'showForm'])->name('test-data');
Route::post('/test-data', [App\Http\Controllers\TestDataController::class, 'createTestData'])->name('create-test-data');





// مسارات للزوار فقط
Route::middleware('guest')->group(function () {
    Route::get('/register', [AuthController::class, 'showRegisterForm'])->name('register');
    Route::post('/register', [AuthController::class, 'register']);
    Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);

    // مسارات المصادقة الاجتماعية
    Route::get('/auth/{provider}', [AuthController::class, 'redirectToProvider'])->name('social.login');
    Route::get('/auth/{provider}/callback', [AuthController::class, 'handleProviderCallback'])->name('social.callback');
});

// مسارات للمستخدمين المسجلين
Route::middleware('auth')->group(function () {
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
    Route::get('/logout', [AuthController::class, 'logout']); // إضافة مسار GET لتسجيل الخروج

    // مسار عام للملف الشخصي يقوم بتوجيه المستخدم بناءً على دوره
    Route::get('/profile', function() {
        $user = Auth::user();
        if ($user->role === 'job_seeker') {
            return redirect()->route('job-seeker.profile');
        } else if ($user->role === 'employer') {
            return redirect()->route('employer.dashboard');
        } else if ($user->role === 'admin') {
            return redirect()->route('admin.reports.index');
        } else {
            return redirect()->route('home');
        }
    });

    // مسارات للمدير
    Route::middleware('role:admin')->prefix('admin')->name('admin.')->group(function () {
        Route::get('/', [AdminController::class, 'index'])->name('index');
        Route::get('/dashboard', [AdminController::class, 'index'])->name('dashboard');

        // إدارة الوظائف
        Route::prefix('jobs')->name('jobs.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\JobController::class, 'index'])->name('index');
            Route::get('/create', [App\Http\Controllers\Admin\JobController::class, 'create'])->name('create');
            Route::post('/', [App\Http\Controllers\Admin\JobController::class, 'store'])->name('store');
            Route::get('/{job}', [App\Http\Controllers\Admin\JobController::class, 'show'])->name('show');
            Route::get('/{job}/edit', [App\Http\Controllers\Admin\JobController::class, 'edit'])->name('edit');
            Route::put('/{job}', [App\Http\Controllers\Admin\JobController::class, 'update'])->name('update');
            Route::delete('/{job}', [App\Http\Controllers\Admin\JobController::class, 'destroy'])->name('destroy');
        });

        // إدارة طلبات التوظيف
        Route::prefix('applications')->name('applications.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\ApplicationController::class, 'index'])->name('index');
            Route::get('/{application}', [App\Http\Controllers\Admin\ApplicationController::class, 'show'])->name('show');
            Route::put('/{application}/status', [App\Http\Controllers\Admin\ApplicationController::class, 'updateStatus'])->name('update-status');
            Route::delete('/{application}', [App\Http\Controllers\Admin\ApplicationController::class, 'destroy'])->name('destroy');
            Route::get('/export', [App\Http\Controllers\Admin\ApplicationController::class, 'export'])->name('export');
        });

        // إدارة المرشحين
        Route::prefix('candidates')->name('candidates.')->group(function () {
            Route::get('/', function () {
                return view('admin.candidates.index');
            })->name('index');

            Route::get('/{id}', function ($id) {
                return view('admin.candidates.show', ['id' => $id]);
            })->name('show');
        });

        // إدارة الشركات
        Route::prefix('companies')->name('companies.')->group(function () {
            Route::get('/', function () {
                return view('admin.companies.index');
            })->name('index');

            Route::get('/{id}', function ($id) {
                return view('admin.companies.show', ['id' => $id]);
            })->name('show');
        });

        // إدارة الإعدادات
        Route::prefix('settings')->name('settings.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\SettingsController::class, 'index'])->name('index');
            Route::get('/general', [App\Http\Controllers\Admin\SettingsController::class, 'general'])->name('general');
            Route::post('/general', [App\Http\Controllers\Admin\SettingsController::class, 'updateGeneral'])->name('general.update');
            Route::get('/system', [App\Http\Controllers\Admin\SettingsController::class, 'system'])->name('system');
            Route::post('/system', [App\Http\Controllers\Admin\SettingsController::class, 'updateSystem'])->name('system.update');
            Route::get('/users', [App\Http\Controllers\Admin\SettingsController::class, 'users'])->name('users');
            Route::get('/payment', [App\Http\Controllers\Admin\SettingsController::class, 'payment'])->name('payment');
        });

        // إدارة المستخدمين
        Route::resource('users', App\Http\Controllers\Admin\UserManagementController::class);
        Route::get('users-export', [App\Http\Controllers\Admin\UserManagementController::class, 'export'])->name('users.export');
        Route::post('users/{user}/toggle-verification', [App\Http\Controllers\Admin\UserManagementController::class, 'toggleVerification'])->name('users.toggle-verification');

        // إدارة قاعدة البيانات
        Route::prefix('database')->name('database.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\DatabaseController::class, 'index'])->name('index');
            Route::get('/backup', [App\Http\Controllers\Admin\DatabaseController::class, 'backup'])->name('backup');
            Route::post('/backup', [App\Http\Controllers\Admin\DatabaseController::class, 'createBackup'])->name('backup.create');
            Route::get('/backup/{filename}/download', [App\Http\Controllers\Admin\DatabaseController::class, 'downloadBackup'])->name('backup.download');
            Route::delete('/backup/{filename}', [App\Http\Controllers\Admin\DatabaseController::class, 'deleteBackup'])->name('backup.delete');
            Route::get('/migrations', [App\Http\Controllers\Admin\DatabaseController::class, 'migrations'])->name('migrations');
            Route::post('/migrations/run', [App\Http\Controllers\Admin\DatabaseController::class, 'runMigrations'])->name('migrations.run');
            Route::post('/migrations/rollback', [App\Http\Controllers\Admin\DatabaseController::class, 'rollbackMigrations'])->name('migrations.rollback');
        });

        // إدارة المراجعات والتقييمات
        Route::prefix('reviews')->name('reviews.')->group(function () {
            Route::get('/', [ReviewController::class, 'index'])->name('index');
            Route::get('/{id}', [ReviewController::class, 'show'])->name('show');
            Route::post('/{id}/status', [ReviewController::class, 'updateStatus'])->name('update-status');
            Route::delete('/{id}', [ReviewController::class, 'destroy'])->name('destroy');
        });

        // إشعارات الإدارة
        Route::prefix('notifications')->name('notifications.')->group(function () {
            Route::get('/', [App\Http\Controllers\AdminNotificationController::class, 'index'])->name('index');
            Route::get('/create', [App\Http\Controllers\AdminNotificationController::class, 'create'])->name('create');
            Route::post('/', [App\Http\Controllers\AdminNotificationController::class, 'store'])->name('store');
            Route::get('/statistics', [App\Http\Controllers\AdminNotificationController::class, 'statistics'])->name('statistics');
            Route::get('/unread', [App\Http\Controllers\AdminNotificationController::class, 'getUnread'])->name('unread');
            Route::post('/mark-all-read', [App\Http\Controllers\AdminNotificationController::class, 'markAllAsRead'])->name('mark-all-read');
            Route::match(['GET', 'POST'], '/{notification}/mark-read', [App\Http\Controllers\AdminNotificationController::class, 'markAsRead'])->name('mark-read');
            Route::delete('/{notification}', [App\Http\Controllers\AdminNotificationController::class, 'destroy'])->name('destroy');
        });
    });

    // مسارات للباحثين عن عمل فقط
    Route::middleware('role:job_seeker')->prefix('job-seeker')->name('job-seeker.')->group(function () {
        Route::get('/profile', [JobSeekerController::class, 'profile'])->name('profile');
        Route::get('/profile/edit', [JobSeekerController::class, 'edit'])->name('profile.edit');
        Route::put('/profile', [JobSeekerController::class, 'update'])->name('profile.update');
        Route::delete('/profile/avatar', [JobSeekerController::class, 'removeAvatar'])->name('profile.remove-avatar');
        Route::get('/applications', [JobSeekerController::class, 'applications'])->name('applications');
    });

    // مسارات لأصحاب العمل فقط
    Route::middleware('role:employer')->prefix('employer')->name('employer.')->group(function () {
        // لوحة التحكم
        Route::get('/dashboard', [App\Http\Controllers\Employer\DashboardController::class, 'index'])->name('dashboard');
        Route::get('/statistics', [App\Http\Controllers\Employer\DashboardController::class, 'statistics'])->name('statistics');
        Route::get('/reports', [App\Http\Controllers\Employer\DashboardController::class, 'reports'])->name('reports');
        Route::post('/statistics/export', [App\Http\Controllers\Employer\DashboardController::class, 'exportStatistics'])->name('statistics.export');

        // الملف الشخصي والإعدادات
        Route::get('/profile', [App\Http\Controllers\Employer\DashboardController::class, 'profile'])->name('profile');
        Route::put('/profile', [App\Http\Controllers\Employer\DashboardController::class, 'updateProfile'])->name('profile.update');
        Route::get('/settings', [App\Http\Controllers\Employer\DashboardController::class, 'settings'])->name('settings');
        Route::put('/settings', [App\Http\Controllers\Employer\DashboardController::class, 'updateSettings'])->name('settings.update');

        // إدارة الوظائف
        Route::prefix('jobs')->name('jobs.')->group(function () {
            Route::get('/', [App\Http\Controllers\Employer\JobController::class, 'index'])->name('index');
            Route::get('/drafts', [App\Http\Controllers\Employer\JobController::class, 'drafts'])->name('drafts');
            Route::get('/create', [App\Http\Controllers\Employer\JobController::class, 'create'])->name('create');
            Route::post('/', [App\Http\Controllers\Employer\JobController::class, 'store'])->name('store');
            Route::get('/{job}', [App\Http\Controllers\Employer\JobController::class, 'show'])->name('show');
            Route::get('/{job}/edit', [App\Http\Controllers\Employer\JobController::class, 'edit'])->name('edit');
            Route::put('/{job}', [App\Http\Controllers\Employer\JobController::class, 'update'])->name('update');
            Route::delete('/{job}', [App\Http\Controllers\Employer\JobController::class, 'destroy'])->name('destroy');
            Route::patch('/{job}/toggle-status', [App\Http\Controllers\Employer\JobController::class, 'toggleStatus'])->name('toggle-status');
            Route::post('/{job}/duplicate', [App\Http\Controllers\Employer\JobController::class, 'duplicate'])->name('duplicate');
            Route::patch('/{job}/publish', [App\Http\Controllers\Employer\JobController::class, 'publish'])->name('publish');
        });

        // Route للـ method القديم (للتوافق مع الروابط الموجودة)
        Route::get('/applications-old', [App\Http\Controllers\EmployerController::class, 'applications'])->name('applications');
        Route::get('/applications-export', [App\Http\Controllers\EmployerController::class, 'exportApplications'])->name('applications.export');

        // إدارة الطلبات
        Route::prefix('applications')->name('applications.')->group(function () {
            Route::get('/', [App\Http\Controllers\Employer\ApplicationController::class, 'index'])->name('index');
            Route::get('/stats', [App\Http\Controllers\Employer\ApplicationController::class, 'stats'])->name('stats');
            Route::get('/export', [App\Http\Controllers\Employer\ApplicationController::class, 'export'])->name('export');
            Route::post('/bulk-update', [App\Http\Controllers\Employer\ApplicationController::class, 'bulkUpdate'])->name('bulk-update');
            Route::get('/{application}/export', [App\Http\Controllers\Employer\ApplicationController::class, 'exportSingle'])->name('export-single');
            Route::post('/{application}/export', [App\Http\Controllers\Employer\ApplicationController::class, 'exportSingle'])->name('export-single');
            Route::get('/{application}', [App\Http\Controllers\Employer\ApplicationController::class, 'show'])->name('show');
            Route::patch('/{application}/status', [App\Http\Controllers\Employer\ApplicationController::class, 'updateStatus'])->name('update-status');
            Route::put('/{application}/status', [App\Http\Controllers\Employer\ApplicationController::class, 'updateStatus'])->name('update-status-put');
            Route::get('/{application}/status', [App\Http\Controllers\Employer\ApplicationController::class, 'showStatusForm'])->name('status-form');
            Route::post('/{application}/review', [App\Http\Controllers\Employer\ApplicationController::class, 'addReview'])->name('add-review');
        });



        // إدارة المرشحين المحفوظين
        Route::prefix('candidates')->name('candidates.')->group(function () {
            Route::get('/', [App\Http\Controllers\Employer\CandidateController::class, 'index'])->name('index');
            Route::get('/search', [App\Http\Controllers\Employer\CandidateController::class, 'search'])->name('search');
            Route::get('/{id}', [App\Http\Controllers\Employer\CandidateController::class, 'show'])->name('show');
            Route::post('/{id}/save', [App\Http\Controllers\Employer\CandidateController::class, 'save'])->name('save');
            Route::delete('/{id}/unsave', [App\Http\Controllers\Employer\CandidateController::class, 'unsave'])->name('unsave');
            Route::put('/{id}/notes', [App\Http\Controllers\Employer\CandidateController::class, 'updateNotes'])->name('update-notes');
        });

        // التقييمات والمراجعات
        Route::prefix('reviews')->name('reviews.')->group(function () {
            Route::get('/', [App\Http\Controllers\Employer\ReviewController::class, 'index'])->name('index');
            Route::get('/{review}', [App\Http\Controllers\Employer\ReviewController::class, 'show'])->name('show');
            Route::post('/{review}/respond', [App\Http\Controllers\Employer\ReviewController::class, 'respond'])->name('respond');
        });

        // الإشعارات
        Route::prefix('notifications')->name('notifications.')->group(function () {
            Route::get('/', [App\Http\Controllers\Employer\NotificationController::class, 'index'])->name('index');
            Route::post('/mark-all-read', [App\Http\Controllers\Employer\NotificationController::class, 'markAllAsRead'])->name('mark-all-read');
            Route::match(['GET', 'POST'], '/{notification}/mark-read', [App\Http\Controllers\Employer\NotificationController::class, 'markAsRead'])->name('mark-read');
            Route::delete('/{notification}', [App\Http\Controllers\Employer\NotificationController::class, 'destroy'])->name('destroy');
        });

        // الباقات والدفع
        Route::get('/packages', [App\Http\Controllers\Employer\DashboardController::class, 'packages'])->name('packages');
        Route::post('/packages/{package}/subscribe', [App\Http\Controllers\Employer\PaymentController::class, 'subscribe'])->name('packages.subscribe');

        // الفواتير
        Route::get('/invoices/{id}/download', [App\Http\Controllers\Employer\InvoiceController::class, 'download'])->name('invoices.download');

        // تصدير البيانات
        Route::get('/exports', [App\Http\Controllers\Employer\DashboardController::class, 'exports'])->name('exports');
        Route::post('/exports/applications', [App\Http\Controllers\Employer\ExportController::class, 'applications'])->name('exports.applications');

        // إنشاء بيانات تجريبية
        Route::post('/create-test-data', [App\Http\Controllers\Employer\DashboardController::class, 'createTestData'])->name('create-test-data');
        Route::post('/fix-registration-date', [App\Http\Controllers\Employer\DashboardController::class, 'fixRegistrationDate'])->name('fix-registration-date');
        Route::post('/reset-registration-date', [App\Http\Controllers\Employer\DashboardController::class, 'resetRegistrationDate'])->name('reset-registration-date');
        Route::post('/exports/jobs', [App\Http\Controllers\Employer\ExportController::class, 'jobs'])->name('exports.jobs');




    });

    // مسارات الإشعارات
    Route::middleware('auth')->prefix('notifications')->name('notifications.')->group(function () {
        Route::get('/', [App\Http\Controllers\NotificationController::class, 'index'])->name('index');
        Route::get('/unread', [App\Http\Controllers\NotificationController::class, 'getUnread'])->name('unread');
        Route::post('/mark-all-read', [App\Http\Controllers\NotificationController::class, 'markAllAsRead'])->name('mark-all-read');
        Route::match(['GET', 'POST'], '/{notification}/mark-read', [App\Http\Controllers\NotificationController::class, 'markAsRead'])->name('mark-read')->where('notification', '[0-9]+');
        Route::delete('/{notification}', [App\Http\Controllers\NotificationController::class, 'destroy'])->name('destroy');
        Route::delete('/clear-read', [App\Http\Controllers\NotificationController::class, 'clearRead'])->name('clear-read');

        // معالجة الإشعارات المفقودة
        Route::get('/{id}/mark-read', function ($id) {
            return redirect()->route('notifications.index')->with('warning', "الإشعار رقم {$id} غير موجود أو تم حذفه");
        })->where('id', '[0-9]+')->name('mark-read-fallback');

        // إعدادات الإشعارات
        Route::get('/settings', [App\Http\Controllers\NotificationSettingsController::class, 'index'])->name('settings');
        Route::put('/settings', [App\Http\Controllers\NotificationSettingsController::class, 'update'])->name('settings.update');
        Route::post('/settings/reset', [App\Http\Controllers\NotificationSettingsController::class, 'reset'])->name('settings.reset');
    });
});

// صفحة اختبار الإشعارات
Route::get('/notifications-test-page', function () {
    $totalNotifications = \App\Models\Notification::count();
    $unreadNotifications = \App\Models\Notification::where('is_read', false)->count();
    $activeUsers = \App\Models\User::whereNotNull('last_login_at')->count();
    $recentNotifications = \App\Models\Notification::with('user')->latest()->limit(10)->get();

    return view('notifications.test', compact('totalNotifications', 'unreadNotifications', 'activeUsers', 'recentNotifications'));
})->name('notifications.test.page');

// صفحة اختبار تشخيص الإشعارات
Route::get('/notifications-debug', function () {
    return view('notifications.test-debug');
})->name('notifications.debug');

// إنشاء إشعار تجريبي
Route::post('/notifications-test-create', function () {
    if (!auth()->check()) {
        return redirect()->back()->with('error', 'يجب تسجيل الدخول أولاً');
    }

    $notificationService = app(\App\Services\NotificationService::class);
    $notificationService->create(
        auth()->user(),
        request('type', 'test'),
        request('message', 'إشعار تجريبي'),
        null,
        ['created_via' => 'test_page']
    );

    return redirect()->back()->with('success', 'تم إنشاء الإشعار التجريبي بنجاح');
})->middleware('auth')->name('notifications.test.create');

// تشغيل seeder الإشعارات التجريبية
Route::get('/notifications-seed', function () {
    try {
        Artisan::call('db:seed', ['--class' => 'NotificationTestSeeder']);
        return redirect()->back()->with('success', 'تم إنشاء الإشعارات التجريبية بنجاح!');
    } catch (\Exception $e) {
        return redirect()->back()->with('error', 'حدث خطأ: ' . $e->getMessage());
    }
})->name('notifications.seed');

// اختبار رفع الملفات
Route::middleware('auth')->group(function () {
    Route::get('/test-upload', function () {
        return view('test-upload');
    })->name('test-upload');

    Route::post('/test-upload', function (Request $request) {
        $request->validate([
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'resume' => 'nullable|mimes:pdf,doc,docx|max:2048',
        ]);

        $user = Auth::user();
        $messages = [];

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            if ($user->avatar) {
                Storage::delete($user->avatar);
            }
            $avatarPath = $request->file('avatar')->store('avatars', 'public');
            $user->avatar = $avatarPath;
            $user->save();
            $messages[] = 'تم رفع الصورة الشخصية بنجاح';
        }

        // Handle resume upload
        if ($request->hasFile('resume')) {
            $jobSeeker = $user->jobSeeker;
            if (!$jobSeeker) {
                $jobSeeker = \App\Models\JobSeeker::create(['user_id' => $user->id]);
            }

            if ($jobSeeker->resume) {
                Storage::delete($jobSeeker->resume);
            }
            $resumePath = $request->file('resume')->store('resumes', 'public');
            $jobSeeker->resume = $resumePath;
            $jobSeeker->save();
            $messages[] = 'تم رفع السيرة الذاتية بنجاح';
        }

        $message = empty($messages) ? 'لم يتم رفع أي ملفات' : implode(' و ', $messages);
        return redirect()->back()->with('success', $message);
    })->name('test-upload.store');
});

// تشخيص مشكلة الإشعار
Route::get('/debug-notification/{id?}', function ($id = 253) {
    try {
        $notification = \App\Models\Notification::find($id);

        $debug = [
            'notification_id' => $id,
            'exists' => $notification ? true : false,
            'routes' => [],
            'user_info' => null,
        ];

        if ($notification) {
            $debug['notification'] = [
                'id' => $notification->id,
                'user_id' => $notification->user_id,
                'type' => $notification->type,
                'message' => substr($notification->message, 0, 100),
                'is_read' => $notification->is_read,
                'link' => $notification->link,
                'created_at' => $notification->created_at,
            ];

            if ($notification->user) {
                $debug['user_info'] = [
                    'name' => $notification->user->name,
                    'role' => $notification->user->role,
                    'email' => $notification->user->email,
                ];
            }
        }

        // اختبار الـ routes
        $routes = [
            'notifications.mark-read',
            'employer.notifications.mark-read',
            'admin.notifications.mark-read',
        ];

        foreach ($routes as $routeName) {
            try {
                $url = route($routeName, ['notification' => $id]);
                $debug['routes'][$routeName] = [
                    'status' => 'success',
                    'url' => $url
                ];
            } catch (Exception $e) {
                $debug['routes'][$routeName] = [
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        }

        return response()->json($debug, 200, [], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

    } catch (Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500, [], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }
})->name('debug.notification');

// صفحة تشخيص الإشعارات (واجهة مرئية)
Route::get('/debug-notification-page', function () {
    return view('debug.notification');
})->name('debug.notification.page');

// صفحة حالة النظام
Route::get('/system-status', function () {
    $stats = [
        'notifications' => [
            'total' => \App\Models\Notification::count(),
            'unread' => \App\Models\Notification::where('is_read', false)->count(),
            'today' => \App\Models\Notification::whereDate('created_at', today())->count(),
        ],
        'users' => [
            'total' => \App\Models\User::count(),
            'employers' => \App\Models\User::where('role', 'employer')->count(),
            'job_seekers' => \App\Models\User::where('role', 'job_seeker')->count(),
            'active' => \App\Models\User::whereNotNull('last_login_at')->count(),
        ],
        'system' => [
            'notification_service' => class_exists('\App\Services\NotificationService'),
            'helpers_loaded' => function_exists('getNotificationIcon'),
            'database_connected' => true,
        ]
    ];

    return response()->json([
        'status' => 'success',
        'message' => 'نظام الإشعارات يعمل بنجاح',
        'timestamp' => now()->toISOString(),
        'stats' => $stats
    ]);
})->name('system.status');

// اختبار صفحة إحصائيات الإشعارات
Route::get('/test-admin-notifications-stats', function () {
    $totalNotifications = \App\Models\Notification::count();
    $unreadNotifications = \App\Models\Notification::where('is_read', false)->count();
    $adminSentNotifications = \App\Models\Notification::whereJsonContains('data->test', true)->count();

    // إحصائيات حسب النوع
    $notificationsByType = \App\Models\Notification::selectRaw('type, COUNT(*) as count')
        ->groupBy('type')
        ->get()
        ->pluck('count', 'type');

    // إحصائيات حسب الشهر (آخر 6 أشهر)
    $monthlyStats = [];
    for ($i = 5; $i >= 0; $i--) {
        $month = now()->subMonths($i);
        $count = \App\Models\Notification::whereYear('created_at', $month->year)
            ->whereMonth('created_at', $month->month)
            ->count();
        $monthlyStats[$month->format('Y-m')] = $count;
    }

    return view('admin.notifications.statistics', compact(
        'totalNotifications',
        'unreadNotifications',
        'adminSentNotifications',
        'notificationsByType',
        'monthlyStats'
    ));
})->name('test.admin.notifications.stats');

// صفحة اختبار إحصائيات الإشعارات المبسطة
Route::get('/simple-notifications-stats', function () {
    $totalNotifications = \App\Models\Notification::count();
    $unreadNotifications = \App\Models\Notification::where('is_read', false)->count();
    $adminSentNotifications = \App\Models\Notification::whereJsonContains('data->test', true)->count();

    return view('admin.notifications.test-stats', compact(
        'totalNotifications',
        'unreadNotifications',
        'adminSentNotifications'
    ));
})->name('simple.notifications.stats');

// مسار اختبار الإشعارات API
Route::get('/test-notifications', function () {
    $notificationService = app(\App\Services\NotificationService::class);

    // الحصول على مستخدمين للاختبار
    $employer = \App\Models\User::where('role', 'employer')->first();
    $jobSeeker = \App\Models\User::where('role', 'job_seeker')->first();

    $results = [];

    if ($employer) {
        // إنشاء إشعار للموظف
        $notification = $notificationService->create(
            $employer,
            'system_announcement',
            'إشعار اختبار: مرحباً بك في نظام الإشعارات المحدث!',
            route('employer.dashboard'),
            ['test' => true, 'timestamp' => now()]
        );
        $results[] = "تم إنشاء إشعار للموظف: {$employer->name}";
    }

    if ($jobSeeker) {
        // إنشاء إشعار للباحث عن عمل
        $notification = $notificationService->create(
            $jobSeeker,
            'job_recommendation',
            'إشعار اختبار: لدينا وظائف جديدة قد تهمك!',
            route('jobs.index'),
            ['test' => true, 'timestamp' => now()]
        );
        $results[] = "تم إنشاء إشعار للباحث عن عمل: {$jobSeeker->name}";
    }

    // إحصائيات
    $totalNotifications = \App\Models\Notification::count();
    $unreadNotifications = \App\Models\Notification::where('is_read', false)->count();

    $results[] = "إجمالي الإشعارات: {$totalNotifications}";
    $results[] = "الإشعارات غير المقروءة: {$unreadNotifications}";

    return response()->json([
        'status' => 'success',
        'message' => 'تم اختبار نظام الإشعارات بنجاح',
        'results' => $results,
        'links' => [
            'employer_notifications' => route('employer.notifications.index'),
            'notification_settings' => route('notifications.settings'),
            'admin_notifications' => route('admin.notifications.index'),
        ]
    ]);
})->name('test.notifications');

// مسارات عامة للوظائف
Route::get('/jobs', [JobController::class, 'index'])->name('jobs.index');
Route::get('/jobs/{job}', [JobController::class, 'show'])->name('jobs.show');
Route::post('/jobs/{job}/save', [JobController::class, 'save'])->name('jobs.save')->middleware('auth');
Route::get('/search', [JobController::class, 'search'])->name('jobs.search');

// مسارات عامة للشركات
Route::get('/employers', [EmployerController::class, 'index'])->name('employers.index');
Route::get('/employers/{employer}', [EmployerController::class, 'show'])->name('employers.show');

// مسارات عامة للمرشحين
Route::get('/job-seekers', [JobSeekerController::class, 'index'])->name('job-seekers.index');
Route::get('/job-seekers/{jobSeeker}', [JobSeekerController::class, 'show'])->name('job-seekers.show');









// صفحة من نحن
Route::get('/about', function() {
    return view('about');
})->name('about');

// مسار لعرض الصور من storage
Route::get('/storage/{path}', function ($path) {
    $file = storage_path('app/public/' . $path);

    if (!file_exists($file)) {
        abort(404);
    }

    $mimeType = mime_content_type($file);

    return response()->file($file, [
        'Content-Type' => $mimeType,
        'Cache-Control' => 'public, max-age=31536000',
    ]);
})->where('path', '.*')->name('storage.file');

// صفحة اختبار الصور
Route::get('/test-images', function () {
    return view('test-images');
})->name('test.images');

// إنشاء جدول المرشحين المحفوظين
Route::get('/setup-candidates-table', function () {
    try {
        if (!\Illuminate\Support\Facades\Schema::hasTable('saved_candidates')) {
            \Illuminate\Support\Facades\Schema::create('saved_candidates', function (\Illuminate\Database\Schema\Blueprint $table) {
                $table->id();
                $table->foreignId('employer_id')->constrained('employers')->onDelete('cascade');
                $table->foreignId('job_seeker_id')->constrained('job_seekers')->onDelete('cascade');
                $table->text('notes')->nullable();
                $table->timestamps();
                $table->unique(['employer_id', 'job_seeker_id']);
            });
            return response()->json(['success' => true, 'message' => 'تم إنشاء الجدول بنجاح']);
        } else {
            return response()->json(['success' => true, 'message' => 'الجدول موجود بالفعل']);
        }
    } catch (\Exception $e) {
        return response()->json(['success' => false, 'message' => 'خطأ: ' . $e->getMessage()]);
    }
})->name('setup.candidates.table');

// صفحة الإعداد
Route::get('/setup', function () {
    return view('setup');
})->name('setup');

// فحص حالة قاعدة البيانات
Route::get('/database-status', function () {
    return view('database-status');
})->name('database.status');

// نظام الرسائل الداخلية
Route::middleware(['auth'])->prefix('messages')->name('messages.')->group(function () {
    Route::get('/', [App\Http\Controllers\MessageController::class, 'inbox'])->name('inbox');
    Route::get('/sent', [App\Http\Controllers\MessageController::class, 'sent'])->name('sent');
    Route::get('/create', [App\Http\Controllers\MessageController::class, 'create'])->name('create');
    Route::post('/', [App\Http\Controllers\MessageController::class, 'store'])->name('store');
    Route::get('/{message}', [App\Http\Controllers\MessageController::class, 'show'])->name('show');
    Route::get('/{message}/reply', [App\Http\Controllers\MessageController::class, 'reply'])->name('reply');
    Route::delete('/{message}', [App\Http\Controllers\MessageController::class, 'destroy'])->name('destroy');
    Route::patch('/{message}/toggle-read', [App\Http\Controllers\MessageController::class, 'toggleRead'])->name('toggle-read');
    Route::get('/api/unread-count', [App\Http\Controllers\MessageController::class, 'unreadCount'])->name('unread-count');
});















// تم نقل routes الوظائف إلى employer group أعلاه

// مسارات تقديم طلبات التوظيف (للباحثين عن عمل فقط)
Route::middleware(['auth', 'role:job_seeker'])->group(function () {
    Route::get('/applications/create/{job}', [App\Http\Controllers\ApplicationController::class, 'create'])->name('applications.create');
    Route::post('/applications/{job}', [App\Http\Controllers\ApplicationController::class, 'store'])->name('applications.store');
});

// مسارات عرض طلبات التوظيف (للباحثين عن عمل)
Route::middleware(['auth'])->group(function () {
    Route::get('/applications/{application}', [App\Http\Controllers\ApplicationController::class, 'show'])->name('applications.show');
    // تم نقل تحديث الحالة إلى employer routes لتجنب التعارض
});

// Test route (temporary)
Route::get('/test-status', function() {
    return view('test-status');
});



// التحقق من حالة الدفع قبل إنشاء وظائف جديدة
Route::middleware(['auth', 'role:employer', 'payment.status'])->group(function () {
    Route::get('/premium-jobs/create', [JobController::class, 'createPremium'])->name('jobs.create.premium');
    Route::post('/premium-jobs', [JobController::class, 'storePremium'])->name('jobs.store.premium');
});

// مسارات التقارير لصاحب العمل
Route::middleware(['auth', 'role:employer'])->group(function () {
    Route::get('/reports', [ReportController::class, 'employerDashboard'])->name('reports.dashboard');
    Route::get('/reports/job-statistics', [ReportController::class, 'jobStatistics'])->name('reports.job-statistics');
    Route::get('/reports/job-statistics/export', [ReportController::class, 'exportJobStatistics'])->name('reports.job-statistics.export');
    Route::get('/reports/applicant-analytics', [ReportController::class, 'applicantAnalytics'])->name('reports.applicant-analytics');
    Route::get('/reports/applicant-analytics/export', [ReportController::class, 'exportApplicantAnalytics'])->name('reports.applicant-analytics.export');
    Route::get('/jobs/{job}/export-applications', [ReportController::class, 'exportApplications'])->name('jobs.export-applications');
});

// مسارات التقارير للمدير
Route::middleware(['auth', 'role:admin', 'permission:view reports'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/reports', [ReportController::class, 'index'])->name('reports.index');
    Route::get('/reports/jobs', [ReportController::class, 'jobs'])->name('reports.jobs');
    Route::get('/reports/applications', [ReportController::class, 'applications'])->name('reports.applications');
    Route::get('/reports/financial', [ReportController::class, 'financial'])->name('reports.financial');

    // مسارات تصدير التقارير
    Route::get('/reports/export/pdf/{reportType}', [ReportController::class, 'exportPdf'])->name('reports.export.pdf');
    Route::get('/reports/export/excel/{reportType}', [ReportController::class, 'exportExcel'])->name('reports.export.excel');

    // مسارات التقارير المجدولة
    Route::get('/reports/scheduled', [ReportController::class, 'scheduledReports'])->name('reports.scheduled');
    Route::post('/reports/schedule', [ReportController::class, 'scheduleReport'])->name('reports.schedule');
    Route::delete('/reports/scheduled/{id}', [ReportController::class, 'deleteScheduledReport'])->name('reports.scheduled.delete');
});

// إنشاء حساب مدير (يمكن الوصول إليه بدون تسجيل دخول)
Route::get('/create-admin', [AdminController::class, 'createAdmin']);

// صفحة اختبار الإحصائيات
Route::get('/test-statistics', function () {
    return view('test-statistics');
})->name('test.statistics');

// صفحة تصحيح الإحصائيات
Route::get('/debug-statistics', function () {
    $debug = [];

    // البيانات الأساسية
    $debug['basic'] = [
        'users' => \App\Models\User::count(),
        'employers' => \App\Models\Employer::count(),
        'job_seekers' => \App\Models\JobSeeker::count(),
        'jobs' => \App\Models\Job::count(),
        'applications' => \App\Models\Application::count(),
    ];

    // أصحاب العمل
    $debug['employers_detail'] = \App\Models\Employer::with('user')->get()->map(function($employer) {
        return [
            'id' => $employer->id,
            'company_name' => $employer->company_name,
            'user_name' => $employer->user ? $employer->user->name : 'غير موجود',
            'jobs_count' => $employer->jobs()->count(),
            'applications_count' => \App\Models\Application::whereHas('job', function($q) use ($employer) {
                $q->where('employer_id', $employer->id);
            })->count(),
        ];
    });

    // الوظائف
    $debug['jobs_detail'] = \App\Models\Job::with('applications')->get()->map(function($job) {
        return [
            'id' => $job->id,
            'title' => $job->title,
            'employer_id' => $job->employer_id,
            'is_active' => $job->is_active,
            'applications_count' => $job->applications->count(),
        ];
    });

    return response()->json($debug, 200, [], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
})->name('debug.statistics');

// إنشاء بيانات تجريبية
Route::get('/create-sample-data', function () {
    try {
        // إنشاء فئات الوظائف
        $categories = ['تقنية المعلومات', 'التسويق', 'المبيعات', 'الموارد البشرية', 'المحاسبة'];
        foreach ($categories as $categoryName) {
            \App\Models\Category::firstOrCreate(['name' => $categoryName]);
        }

        // إنشاء مستخدم صاحب عمل
        $employerUser = \App\Models\User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'شركة الاختبار',
                'password' => \Illuminate\Support\Facades\Hash::make('password'),
                'role' => 'employer',
                'email_verified_at' => now(),
            ]
        );

        // إنشاء ملف صاحب العمل
        $employer = \App\Models\Employer::firstOrCreate(
            ['user_id' => $employerUser->id],
            [
                'company_name' => 'شركة الاختبار للتقنية',
                'company_description' => 'شركة رائدة في مجال التقنية',
                'industry' => 'تقنية المعلومات',
                'location' => 'الرياض',
                'company_size' => '50-100',
                'founded_year' => 2020,
            ]
        );

        // إنشاء مستخدمين باحثين عن عمل
        for ($i = 1; $i <= 3; $i++) {
            $jobSeekerUser = \App\Models\User::firstOrCreate(
                ['email' => "jobseeker{$i}@test.com"],
                [
                    'name' => "باحث عن عمل {$i}",
                    'password' => \Illuminate\Support\Facades\Hash::make('password'),
                    'role' => 'job_seeker',
                    'email_verified_at' => now(),
                ]
            );

            \App\Models\JobSeeker::firstOrCreate(
                ['user_id' => $jobSeekerUser->id],
                [
                    'job_title' => 'مطور برمجيات',
                    'location' => 'الرياض',
                    'experience' => 'خبرة ' . rand(1, 5) . ' سنوات',
                    'skills' => 'PHP, Laravel, JavaScript, MySQL',
                    'is_available' => true,
                ]
            );
        }

        // إنشاء وظائف
        $jobTitles = ['مطور ويب', 'مصمم جرافيك', 'مسوق رقمي', 'محاسب', 'مهندس برمجيات'];
        foreach ($jobTitles as $title) {
            $job = \App\Models\Job::firstOrCreate(
                ['title' => $title, 'employer_id' => $employer->id],
                [
                    'description' => "وصف تفصيلي للوظيفة: {$title}",
                    'requirements' => "متطلبات الوظيفة: {$title}",
                    'location' => 'الرياض',
                    'job_type' => 'full_time',
                    'salary_range' => '5000 - 15000 ريال',
                    'category_id' => \App\Models\Category::first()->id,
                    'is_active' => true,
                    'expires_at' => now()->addDays(30),
                    'posted_at' => now(),
                    'views' => rand(10, 100),
                ]
            );

            // إنشاء طلبات للوظيفة
            $jobSeekers = \App\Models\JobSeeker::all();
            foreach ($jobSeekers as $jobSeeker) {
                \App\Models\Application::firstOrCreate(
                    ['job_id' => $job->id, 'job_seeker_id' => $jobSeeker->id],
                    [
                        'cover_letter' => 'رسالة تغطية للتقديم على وظيفة ' . $title,
                        'status' => ['pending', 'reviewed', 'accepted', 'rejected'][rand(0, 3)],
                        'applied_at' => now()->subDays(rand(1, 10)),
                    ]
                );
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'تم إنشاء البيانات التجريبية بنجاح',
            'stats' => [
                'users' => \App\Models\User::count(),
                'employers' => \App\Models\Employer::count(),
                'jobs' => \App\Models\Job::count(),
                'applications' => \App\Models\Application::count(),
            ]
        ], 200, [], JSON_UNESCAPED_UNICODE);

    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'حدث خطأ: ' . $e->getMessage()
        ], 500, [], JSON_UNESCAPED_UNICODE);
    }
})->name('create.sample.data');

// صفحة أدوات الإدارة
Route::get('/admin-tools', function () {
    return view('admin-tools');
})->name('admin.tools');

// اختبار الإحصائيات بدون middleware
Route::get('/test-employer-stats', function () {
    // محاكاة صاحب عمل
    $employer = \App\Models\Employer::first();

    if (!$employer) {
        return response()->json(['error' => 'لا يوجد أصحاب عمل في النظام'], 404);
    }

    // تطبيق نفس الكود من DashboardController
    $jobStats = [
        'total' => \App\Models\Job::where('employer_id', $employer->id)->count(),
        'active' => \App\Models\Job::where('employer_id', $employer->id)->where('is_active', true)->count(),
        'expired' => \App\Models\Job::where('employer_id', $employer->id)->where('expires_at', '<', now())->count(),
        'draft' => \App\Models\Job::where('employer_id', $employer->id)->where('is_active', false)->count(),
    ];

    $applicationStats = [
        'total' => \App\Models\Application::whereHas('job', function($query) use ($employer) {
            $query->where('employer_id', $employer->id);
        })->count(),
        'pending' => \App\Models\Application::whereHas('job', function($query) use ($employer) {
            $query->where('employer_id', $employer->id);
        })->where('status', 'pending')->count(),
        'reviewed' => \App\Models\Application::whereHas('job', function($query) use ($employer) {
            $query->where('employer_id', $employer->id);
        })->where('status', 'reviewed')->count(),
        'accepted' => \App\Models\Application::whereHas('job', function($query) use ($employer) {
            $query->where('employer_id', $employer->id);
        })->where('status', 'accepted')->count(),
        'rejected' => \App\Models\Application::whereHas('job', function($query) use ($employer) {
            $query->where('employer_id', $employer->id);
        })->where('status', 'rejected')->count(),
    ];

    return response()->json([
        'employer' => [
            'id' => $employer->id,
            'company_name' => $employer->company_name,
        ],
        'job_stats' => $jobStats,
        'application_stats' => $applicationStats,
    ], 200, [], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
})->name('test.employer.stats');
