<?php

namespace App\Http\Controllers;

use App\Models\JobSeeker;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class JobSeekerController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:job_seeker'])->except(['index', 'show']);
    }

    public function profile()
    {
        $jobSeeker = Auth::user()->jobSeeker;
        return view('job-seekers.profile', compact('jobSeeker'));
    }

    public function edit()
    {
        $jobSeeker = Auth::user()->jobSeeker;
        return view('job-seekers.edit', compact('jobSeeker'));
    }

    public function update(Request $request)
    {
        $request->validate([
            'skills' => 'nullable|string',
            'experience' => 'nullable|string',
            'education' => 'nullable|string',
            'location' => 'nullable|string',
            'job_title' => 'nullable|string',
            'resume' => 'nullable|mimes:pdf,doc,docx|max:2048',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_available' => 'boolean',
        ]);

        $user = Auth::user();
        $jobSeeker = $user->jobSeeker;

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            // Delete old avatar if exists
            if ($user->avatar) {
                Storage::delete($user->avatar);
            }

            $avatarPath = $request->file('avatar')->store('avatars', 'public');
            $user->avatar = $avatarPath;
            $user->save();
        }

        // Handle resume upload
        if ($request->hasFile('resume')) {
            // Delete old resume if exists
            if ($jobSeeker->resume) {
                Storage::delete($jobSeeker->resume);
            }

            $resumePath = $request->file('resume')->store('resumes', 'public');
            $jobSeeker->resume = $resumePath;
        }

        // Update job seeker information
        $jobSeeker->update([
            'skills' => $request->skills,
            'experience' => $request->experience,
            'education' => $request->education,
            'location' => $request->location,
            'job_title' => $request->job_title,
            'is_available' => $request->has('is_available'),
        ]);

        return redirect()->route('job-seeker.profile')->with('success', 'تم تحديث الملف الشخصي بنجاح');
    }

    /**
     * Remove user avatar
     */
    public function removeAvatar()
    {
        $user = Auth::user();

        if ($user->avatar) {
            Storage::delete($user->avatar);
            $user->avatar = null;
            $user->save();

            return response()->json(['success' => true, 'message' => 'تم حذف الصورة الشخصية بنجاح']);
        }

        return response()->json(['success' => false, 'message' => 'لا توجد صورة شخصية لحذفها']);
    }

    public function applications()
    {
        $user = Auth::user();

        // التحقق من وجود ملف باحث عن عمل
        if (!$user->jobSeeker) {
            // إنشاء ملف باحث عن عمل تلقائياً إذا كان المستخدم من نوع job_seeker
            if ($user->role === 'job_seeker') {
                JobSeeker::create([
                    'user_id' => $user->id,
                    'job_title' => 'باحث عن عمل',
                    'location' => 'ليبيا',
                    'is_available' => true,
                ]);
                // إعادة تحميل العلاقة
                $user->load('jobSeeker');
            } else {
                return redirect()->route('home')->with('error', 'ليس لديك صلاحية للوصول إلى هذه الصفحة');
            }
        }

        $applications = $user->jobSeeker->applications()->with(['job.employer', 'job.category'])->latest()->paginate(10);

        // إحصائيات الطلبات
        $stats = [
            'total' => $user->jobSeeker->applications()->count(),
            'pending' => $user->jobSeeker->applications()->where('status', 'pending')->count(),
            'accepted' => $user->jobSeeker->applications()->where('status', 'accepted')->count(),
            'rejected' => $user->jobSeeker->applications()->where('status', 'rejected')->count(),
        ];

        return view('job-seekers.applications', compact('applications', 'stats'));
    }

    public function companyRatings()
    {
        $ratings = Auth::user()->jobSeeker->companyRatings()->with('employer.user')->latest()->paginate(10);
        return view('job-seekers.company-ratings', compact('ratings'));
    }

    /**
     * عرض قائمة المرشحين
     */
    public function index(Request $request)
    {
        $query = JobSeeker::with('user')
            ->whereHas('user', function($query) {
                $query->where('role', 'job_seeker');
            })
            ->where('is_available', true);

        // البحث بالاسم أو المسمى الوظيفي
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->whereHas('user', function($userQuery) use ($search) {
                    $userQuery->where('name', 'like', "%{$search}%");
                })->orWhere('job_title', 'like', "%{$search}%");
            });
        }

        // فلترة بالمهارات
        if ($request->filled('skills')) {
            $query->where('skills', 'like', "%{$request->skills}%");
        }

        // فلترة بالموقع
        if ($request->filled('location')) {
            $query->where('location', 'like', "%{$request->location}%");
        }

        $jobSeekers = $query->latest()->paginate(12);

        return view('job-seekers.index', compact('jobSeekers'));
    }

    /**
     * عرض ملف مرشح محدد
     */
    public function show(JobSeeker $jobSeeker)
    {
        // التأكد من أن المستخدم هو باحث عن عمل
        if ($jobSeeker->user->role !== 'job_seeker') {
            abort(404);
        }

        return view('job-seekers.show', compact('jobSeeker'));
    }
}