<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use App\Models\User;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AdminNotificationController extends Controller
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->middleware('auth');
        $this->middleware('role:admin');
        $this->notificationService = $notificationService;
    }

    /**
     * عرض إشعارات الإدارة
     */
    public function index()
    {
        $admin = Auth::user();
        $notifications = $admin->notifications()->latest()->paginate(15);
        $unreadCount = $this->notificationService->getUnreadCount($admin);
        
        return view('admin.notifications.index', compact('notifications', 'unreadCount'));
    }

    /**
     * صفحة إنشاء إشعار جديد
     */
    public function create()
    {
        $users = User::where('role', '!=', 'admin')->get();
        $employers = User::where('role', 'employer')->get();
        $jobSeekers = User::where('role', 'job_seeker')->get();
        
        return view('admin.notifications.create', compact('users', 'employers', 'jobSeekers'));
    }

    /**
     * إرسال إشعار جديد
     */
    public function store(Request $request)
    {
        $request->validate([
            'message' => 'required|string|max:500',
            'link' => 'nullable|url|max:255',
            'recipient_type' => 'required|in:all,employers,job_seekers,specific',
            'specific_users' => 'required_if:recipient_type,specific|array',
            'specific_users.*' => 'exists:users,id',
            'type' => 'required|in:system_announcement,urgent,info,warning'
        ]);

        $recipients = $this->getRecipients($request->recipient_type, $request->specific_users);
        $sentCount = 0;

        foreach ($recipients as $user) {
            $this->notificationService->create(
                $user,
                $request->type,
                $request->message,
                $request->link,
                [
                    'sent_by_admin' => Auth::id(),
                    'admin_name' => Auth::user()->name,
                    'sent_at' => now()->toDateTimeString()
                ]
            );
            $sentCount++;
        }

        return redirect()->route('admin.notifications.index')
            ->with('success', "تم إرسال الإشعار إلى {$sentCount} مستخدم بنجاح");
    }

    /**
     * تحديد إشعار كمقروء
     */
    public function markAsRead(Notification $notification)
    {
        if ($notification->user_id !== Auth::id()) {
            abort(403, 'غير مصرح لك بالوصول إلى هذا الإشعار');
        }
        
        $notification->markAsRead();
        
        if ($notification->link) {
            return redirect($notification->link);
        }
        
        return redirect()->back();
    }

    /**
     * تحديد جميع الإشعارات كمقروءة
     */
    public function markAllAsRead()
    {
        Auth::user()->notifications()->update(['is_read' => true]);
        return redirect()->back()->with('success', 'تم تحديد جميع الإشعارات كمقروءة');
    }

    /**
     * حذف إشعار
     */
    public function destroy(Notification $notification)
    {
        if ($notification->user_id !== Auth::id()) {
            abort(403, 'غير مصرح لك بحذف هذا الإشعار');
        }
        
        $notification->delete();
        
        return redirect()->back()->with('success', 'تم حذف الإشعار بنجاح');
    }

    /**
     * الحصول على الإشعارات غير المقروءة (API)
     */
    public function getUnread()
    {
        $user = Auth::user();
        $notifications = $this->notificationService->getUnreadNotifications($user);
        $count = $this->notificationService->getUnreadCount($user);
        
        return response()->json([
            'notifications' => $notifications,
            'count' => $count
        ]);
    }

    /**
     * صفحة إحصائيات الإشعارات
     */
    public function statistics()
    {
        $totalNotifications = Notification::count();
        $unreadNotifications = Notification::where('is_read', false)->count();
        $adminSentNotifications = Notification::whereJsonContains('data->sent_by_admin', Auth::id())->count();
        
        // إحصائيات حسب النوع
        $notificationsByType = Notification::selectRaw('type, COUNT(*) as count')
            ->groupBy('type')
            ->get()
            ->pluck('count', 'type');

        // إحصائيات حسب الشهر (آخر 6 أشهر)
        $monthlyStats = [];
        for ($i = 5; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $count = Notification::whereYear('created_at', $month->year)
                ->whereMonth('created_at', $month->month)
                ->count();
            $monthlyStats[$month->format('Y-m')] = $count;
        }

        return view('admin.notifications.statistics', compact(
            'totalNotifications',
            'unreadNotifications', 
            'adminSentNotifications',
            'notificationsByType',
            'monthlyStats'
        ));
    }

    /**
     * الحصول على المستلمين بناءً على النوع
     */
    private function getRecipients(string $type, ?array $specificUsers = null): \Illuminate\Database\Eloquent\Collection
    {
        switch ($type) {
            case 'all':
                return User::where('role', '!=', 'admin')->get();
            
            case 'employers':
                return User::where('role', 'employer')->get();
            
            case 'job_seekers':
                return User::where('role', 'job_seeker')->get();
            
            case 'specific':
                return User::whereIn('id', $specificUsers ?? [])->get();
            
            default:
                return collect();
        }
    }
}
