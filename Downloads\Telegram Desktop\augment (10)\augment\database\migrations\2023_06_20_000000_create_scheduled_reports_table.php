<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateScheduledReportsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('scheduled_reports', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('email');
            $table->string('report_type'); // jobs, applications, financial
            $table->string('frequency'); // daily, weekly, monthly
            $table->integer('day_of_week')->nullable(); // 0 (Sunday) to 6 (Saturday)
            $table->integer('day_of_month')->nullable(); // 1 to 31
            $table->json('filters')->nullable();
            $table->boolean('include_attachment')->default(true);
            $table->timestamp('last_sent_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('scheduled_reports');
    }
}
