<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('employers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('company_name')->nullable();
            $table->string('company_logo')->nullable(); // مثال: https://example.com/logo.png
            $table->text('company_description')->nullable();
            $table->string('industry')->nullable(); // مجال الصناعة
            $table->string('website')->nullable();
            $table->string('location')->nullable(); // الموقع
            $table->integer('company_size')->nullable(); // حجم الشركة (عدد الموظفين)
            $table->integer('founded_year')->nullable(); // سنة التأسيس
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('employers');
    }
};