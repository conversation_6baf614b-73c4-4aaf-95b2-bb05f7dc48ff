@extends('layouts.app')

@section('title', 'صندوق الوارد - Hire Me')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col lg:flex-row gap-6">
        
        <!-- القائمة الجانبية -->
        <div class="lg:w-1/4">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold mb-4">الرسائل</h3>
                
                <nav class="space-y-2">
                    <a href="{{ route('messages.inbox') }}" class="flex items-center gap-3 px-4 py-2 bg-blue-50 text-blue-700 rounded-lg">
                        <i class="fas fa-inbox"></i>
                        <span>صندوق الوارد</span>
                        @if($unreadCount > 0)
                            <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full">{{ $unreadCount }}</span>
                        @endif
                    </a>
                    
                    <a href="{{ route('messages.sent') }}" class="flex items-center gap-3 px-4 py-2 text-gray-700 hover:bg-gray-50 rounded-lg">
                        <i class="fas fa-paper-plane"></i>
                        <span>الرسائل المرسلة</span>
                    </a>
                    
                    <a href="{{ route('messages.create') }}" class="flex items-center gap-3 px-4 py-2 bg-green-600 text-white hover:bg-green-700 rounded-lg">
                        <i class="fas fa-plus"></i>
                        <span>رسالة جديدة</span>
                    </a>
                </nav>
            </div>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="lg:w-3/4">
            <div class="bg-white rounded-lg shadow-md">
                
                <!-- Header -->
                <div class="p-6 border-b border-gray-200">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">صندوق الوارد</h1>
                            <p class="text-gray-600">{{ $messages->total() }} رسالة</p>
                        </div>
                        
                        <!-- البحث -->
                        <form method="GET" class="flex gap-2">
                            <input type="text" name="search" value="{{ $search }}" 
                                   placeholder="البحث في الرسائل..." 
                                   class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                    </div>
                </div>

                <!-- قائمة الرسائل -->
                <div class="divide-y divide-gray-200">
                    @forelse($messages as $message)
                        <div class="p-6 hover:bg-gray-50 transition-colors {{ !$message->is_read ? 'bg-blue-50' : '' }}">
                            <div class="flex items-start gap-4">
                                
                                <!-- صورة المرسل -->
                                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold">
                                    {{ substr($message->sender->name, 0, 1) }}
                                </div>
                                
                                <!-- محتوى الرسالة -->
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center justify-between mb-2">
                                        <div class="flex items-center gap-2">
                                            <h3 class="font-semibold text-gray-900 {{ !$message->is_read ? 'font-bold' : '' }}">
                                                {{ $message->sender->name }}
                                            </h3>
                                            <span class="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded-full">
                                                {{ $message->sender->role === 'employer' ? 'صاحب عمل' : 'باحث عن عمل' }}
                                            </span>
                                            @if(!$message->is_read)
                                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                            @endif
                                        </div>
                                        <span class="text-sm text-gray-500">{{ $message->formatted_date }}</span>
                                    </div>
                                    
                                    <h4 class="font-medium text-gray-900 mb-1 {{ !$message->is_read ? 'font-semibold' : '' }}">
                                        {{ $message->subject }}
                                    </h4>
                                    
                                    <p class="text-gray-600 text-sm">{{ $message->excerpt }}</p>
                                </div>
                                
                                <!-- الإجراءات -->
                                <div class="flex items-center gap-2">
                                    <a href="{{ route('messages.show', $message) }}" 
                                       class="px-3 py-1 text-blue-600 hover:bg-blue-50 rounded-lg text-sm">
                                        قراءة
                                    </a>
                                    
                                    <form method="POST" action="{{ route('messages.toggle-read', $message) }}" class="inline">
                                        @csrf
                                        @method('PATCH')
                                        <button type="submit" class="px-3 py-1 text-gray-600 hover:bg-gray-50 rounded-lg text-sm">
                                            {{ $message->is_read ? 'غير مقروءة' : 'مقروءة' }}
                                        </button>
                                    </form>
                                    
                                    <form method="POST" action="{{ route('messages.destroy', $message) }}" class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" onclick="return confirm('هل أنت متأكد من حذف هذه الرسالة؟')"
                                                class="px-3 py-1 text-red-600 hover:bg-red-50 rounded-lg text-sm">
                                            حذف
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="p-12 text-center">
                            <i class="fas fa-inbox text-6xl text-gray-300 mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد رسائل</h3>
                            <p class="text-gray-600 mb-4">صندوق الوارد فارغ</p>
                            <a href="{{ route('messages.create') }}" class="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                                <i class="fas fa-plus"></i>
                                إنشاء رسالة جديدة
                            </a>
                        </div>
                    @endforelse
                </div>

                <!-- Pagination -->
                @if($messages->hasPages())
                    <div class="p-6 border-t border-gray-200">
                        {{ $messages->links() }}
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

@if(session('success'))
    <div class="fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
        {{ session('success') }}
    </div>
    <script>
        setTimeout(() => {
            document.querySelector('.fixed.top-4').remove();
        }, 3000);
    </script>
@endif
@endsection
