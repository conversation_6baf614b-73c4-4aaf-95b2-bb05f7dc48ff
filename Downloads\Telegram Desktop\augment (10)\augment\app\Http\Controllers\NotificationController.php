<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->middleware('auth');
        $this->notificationService = $notificationService;
    }

    /**
     * عرض جميع الإشعارات
     */
    public function index()
    {
        $user = Auth::user();
        $notifications = $user->notifications()->latest()->paginate(15);
        $unreadCount = $this->notificationService->getUnreadCount($user);

        // توجيه حسب نوع المستخدم
        if ($user->role === 'employer') {
            return view('employer.notifications.index', compact('notifications', 'unreadCount'));
        } elseif ($user->role === 'job_seeker') {
            return view('job-seekers.notifications', compact('notifications', 'unreadCount'));
        } else {
            return view('notifications.index', compact('notifications', 'unreadCount'));
        }
    }

    /**
     * تحديد إشعار كمقروء والانتقال إلى الرابط
     */
    public function markAsRead(Notification $notification)
    {
        try {
            // التحقق من أن الإشعار يخص المستخدم الحالي
            if ($notification->user_id !== Auth::id()) {
                abort(403, 'غير مصرح لك بالوصول إلى هذا الإشعار');
            }

            $notification->markAsRead();

            if ($notification->link) {
                return redirect($notification->link);
            }

            return redirect()->back()->with('success', 'تم تحديد الإشعار كمقروء');

        } catch (\Exception $e) {
            \Log::error('خطأ في تحديد الإشعار كمقروء: ' . $e->getMessage(), [
                'notification_id' => $notification->id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return redirect()->back()->with('error', 'حدث خطأ أثناء معالجة الإشعار');
        }
    }

    /**
     * تحديد جميع الإشعارات كمقروءة
     */
    public function markAllAsRead()
    {
        Auth::user()->notifications()->update(['is_read' => true]);
        return redirect()->back()->with('success', 'تم تحديد جميع الإشعارات كمقروءة');
    }

    /**
     * الحصول على الإشعارات غير المقروءة (API)
     */
    public function getUnread()
    {
        try {
            $user = Auth::user();

            // التحقق من وجود المستخدم
            if (!$user) {
                return response()->json([
                    'error' => 'المستخدم غير مسجل دخول',
                    'notifications' => [],
                    'count' => 0
                ], 401);
            }

            $notifications = $this->notificationService->getUnreadNotifications($user);
            $count = $this->notificationService->getUnreadCount($user);

            return response()->json([
                'notifications' => $notifications,
                'count' => $count
            ]);
        } catch (\Exception $e) {
            \Log::error('Error in getUnread notifications: ' . $e->getMessage());

            return response()->json([
                'error' => 'حدث خطأ في جلب الإشعارات',
                'notifications' => [],
                'count' => 0
            ], 500);
        }
    }

    /**
     * حذف إشعار
     */
    public function destroy(Notification $notification)
    {
        if ($notification->user_id !== Auth::id()) {
            abort(403, 'غير مصرح لك بحذف هذا الإشعار');
        }

        $notification->delete();

        return redirect()->back()->with('success', 'تم حذف الإشعار بنجاح');
    }

    /**
     * حذف جميع الإشعارات المقروءة
     */
    public function clearRead()
    {
        $deletedCount = Auth::user()->notifications()->where('is_read', true)->delete();

        return redirect()->back()->with('success', "تم حذف {$deletedCount} إشعار مقروء");
    }


}
