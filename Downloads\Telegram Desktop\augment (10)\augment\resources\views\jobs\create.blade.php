@extends('layouts.app')

@section('content')
<div class="p-6">
    <div class="mb-6">
        <h2 class="text-2xl font-bold">إضافة وظيفة جديدة</h2>
        <p class="text-gray-600 dark:text-gray-400 mt-1">قم بإضافة تفاصيل الوظيفة الجديدة</p>
    </div>

    <form action="{{ route('jobs.store') }}" method="POST" class="space-y-6">
        @csrf

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Job Details -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Basic Information -->
                <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                    <h3 class="text-lg font-bold mb-4">المعلومات الأساسية</h3>

                    <div class="space-y-4">
                        <div>
                            <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">عنوان الوظيفة <span class="text-red-600">*</span></label>
                            <input type="text" name="title" id="title" value="{{ old('title') }}" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base @error('title') border-red-500 @enderror" placeholder="مثال: مطور واجهات أمامية" required>
                            @error('title')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="category_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">التصنيف <span class="text-red-600">*</span></label>
                                <select name="category_id" id="category_id" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base @error('category_id') border-red-500 @enderror" required>
                                    <option value="">اختر التصنيف</option>
                                    @foreach(\App\Models\Category::all() as $category)
                                        <option value="{{ $category->id }}" {{ old('category_id') == $category->id ? 'selected' : '' }}>{{ $category->name }}</option>
                                    @endforeach
                                </select>
                                @error('category_id')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                            <div>
                                <label for="type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">نوع الوظيفة <span class="text-red-600">*</span></label>
                                <select name="type" id="type" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base @error('type') border-red-500 @enderror" required>
                                    <option value="">اختر نوع الوظيفة</option>
                                    <option value="full_time" {{ old('type') == 'full_time' ? 'selected' : '' }}>دوام كامل</option>
                                    <option value="part_time" {{ old('type') == 'part_time' ? 'selected' : '' }}>دوام جزئي</option>
                                    <option value="remote" {{ old('type') == 'remote' ? 'selected' : '' }}>عن بعد</option>
                                    <option value="freelance" {{ old('type') == 'freelance' ? 'selected' : '' }}>عمل حر</option>
                                </select>
                                @error('type')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div>
                            <label for="location" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الموقع <span class="text-red-600">*</span></label>
                            <input type="text" name="location" id="location" value="{{ old('location') }}" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base @error('location') border-red-500 @enderror" placeholder="مثال: طرابلس، ليبيا" required>
                            @error('location')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label for="salary_min" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الحد الأدنى للراتب</label>
                                <input type="number" name="salary_min" id="salary_min" value="{{ old('salary_min') }}" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base @error('salary_min') border-red-500 @enderror" placeholder="0">
                                @error('salary_min')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                            <div>
                                <label for="salary_max" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الحد الأعلى للراتب</label>
                                <input type="number" name="salary_max" id="salary_max" value="{{ old('salary_max') }}" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base @error('salary_max') border-red-500 @enderror" placeholder="0">
                                @error('salary_max')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                            <div>
                                <label for="salary_currency" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">العملة <span class="text-red-600">*</span></label>
                                <select name="salary_currency" id="salary_currency" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base @error('salary_currency') border-red-500 @enderror" required>
                                    <option value="">اختر العملة</option>
                                    <option value="LYD" {{ old('salary_currency') == 'LYD' ? 'selected' : '' }}>دينار ليبي (LYD)</option>
                                    <option value="USD" {{ old('salary_currency') == 'USD' ? 'selected' : '' }}>دولار أمريكي (USD)</option>
                                    <option value="EUR" {{ old('salary_currency') == 'EUR' ? 'selected' : '' }}>يورو (EUR)</option>
                                </select>
                                @error('salary_currency')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div>
                            <label for="deadline" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">تاريخ انتهاء التقديم</label>
                            <input type="date" name="deadline" id="deadline" value="{{ old('deadline') }}" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base @error('deadline') border-red-500 @enderror">
                            @error('deadline')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Job Description -->
                <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                    <h3 class="text-lg font-bold mb-4">وصف الوظيفة</h3>

                    <div class="space-y-4">
                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">وصف الوظيفة <span class="text-red-600">*</span></label>
                            <textarea name="description" id="description" rows="6" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base @error('description') border-red-500 @enderror" placeholder="اكتب وصفاً مفصلاً للوظيفة..." required>{{ old('description') }}</textarea>
                            @error('description')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="requirements" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">متطلبات الوظيفة <span class="text-red-600">*</span></label>
                            <textarea name="requirements" id="requirements" rows="6" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base @error('requirements') border-red-500 @enderror" placeholder="اكتب متطلبات الوظيفة..." required>{{ old('requirements') }}</textarea>
                            @error('requirements')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="benefits" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">مميزات الوظيفة</label>
                            <textarea name="benefits" id="benefits" rows="4" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base @error('benefits') border-red-500 @enderror" placeholder="اكتب مميزات الوظيفة...">{{ old('benefits') }}</textarea>
                            @error('benefits')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Job Status -->
                <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                    <h3 class="text-lg font-bold mb-4">حالة الوظيفة</h3>

                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input type="checkbox" name="is_featured" id="is_featured" value="1" {{ old('is_featured') ? 'checked' : '' }} class="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary">
                            <label for="is_featured" class="mr-2 text-sm font-medium text-gray-700 dark:text-gray-300">وظيفة مميزة (تظهر في قسم الوظائف المميزة)</label>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                    <button type="submit" class="w-full gradient-bg text-white py-3 px-4 rounded-lg hover:opacity-90 transition-opacity font-medium">
                        نشر الوظيفة
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

@endsection
