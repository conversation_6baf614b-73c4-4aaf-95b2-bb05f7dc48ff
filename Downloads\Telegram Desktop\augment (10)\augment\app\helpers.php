<?php

if (!function_exists('getNotificationIcon')) {
    /**
     * الحصول على أيقونة الإشعار حسب النوع
     */
    function getNotificationIcon(string $type): string
    {
        $icons = [
            'job_application' => 'file-alt',
            'application_update' => 'edit',
            'job_posted' => 'briefcase',
            'job_expired' => 'clock',
            'payment_success' => 'check-circle',
            'payment_failed' => 'times-circle',
            'profile_rating' => 'star',
            'company_rating' => 'building',
            'system_announcement' => 'bullhorn',
            'interview_scheduled' => 'calendar-alt',
            'job_recommendation' => 'lightbulb',
            'message' => 'envelope',
            'reminder' => 'bell',
            'warning' => 'exclamation-triangle',
            'info' => 'info-circle',
            'urgent' => 'exclamation',
        ];

        return $icons[$type] ?? 'bell';
    }
}

if (!function_exists('getNotificationColor')) {
    /**
     * الحصول على لون الإشعار حسب النوع
     */
    function getNotificationColor(string $type): string
    {
        $colors = [
            'job_application' => 'blue',
            'application_update' => 'yellow',
            'job_posted' => 'green',
            'job_expired' => 'red',
            'payment_success' => 'green',
            'payment_failed' => 'red',
            'profile_rating' => 'yellow',
            'company_rating' => 'purple',
            'system_announcement' => 'indigo',
            'interview_scheduled' => 'blue',
            'job_recommendation' => 'green',
            'message' => 'blue',
            'reminder' => 'orange',
            'warning' => 'red',
            'info' => 'blue',
            'urgent' => 'red',
        ];

        return $colors[$type] ?? 'gray';
    }
}

if (!function_exists('getNotificationTitle')) {
    /**
     * الحصول على عنوان الإشعار حسب النوع
     */
    function getNotificationTitle(string $type): string
    {
        $titles = [
            'job_application' => 'طلب توظيف جديد',
            'application_update' => 'تحديث طلب التوظيف',
            'job_posted' => 'تم نشر الوظيفة',
            'job_expired' => 'انتهت صلاحية الوظيفة',
            'payment_success' => 'تم الدفع بنجاح',
            'payment_failed' => 'فشل في الدفع',
            'profile_rating' => 'تقييم الملف الشخصي',
            'company_rating' => 'تقييم الشركة',
            'system_announcement' => 'إعلان النظام',
            'interview_scheduled' => 'تم جدولة مقابلة',
            'job_recommendation' => 'توصية وظيفة',
            'message' => 'رسالة جديدة',
            'reminder' => 'تذكير',
            'warning' => 'تحذير',
            'info' => 'معلومات',
            'urgent' => 'عاجل',
        ];

        return $titles[$type] ?? 'إشعار';
    }
}

if (!function_exists('formatNotificationTime')) {
    /**
     * تنسيق وقت الإشعار
     */
    function formatNotificationTime($timestamp): string
    {
        if (!$timestamp) {
            return 'غير محدد';
        }

        $carbon = \Carbon\Carbon::parse($timestamp);

        if ($carbon->isToday()) {
            return 'اليوم ' . $carbon->format('H:i');
        } elseif ($carbon->isYesterday()) {
            return 'أمس ' . $carbon->format('H:i');
        } elseif ($carbon->diffInDays() <= 7) {
            return $carbon->diffForHumans();
        } else {
            return $carbon->format('Y/m/d H:i');
        }
    }
}

if (!function_exists('getNotificationPriority')) {
    /**
     * الحصول على أولوية الإشعار
     */
    function getNotificationPriority(string $type): string
    {
        $priorities = [
            'urgent' => 'high',
            'payment_failed' => 'high',
            'job_expired' => 'high',
            'interview_scheduled' => 'medium',
            'job_application' => 'medium',
            'application_update' => 'medium',
            'system_announcement' => 'medium',
            'job_posted' => 'low',
            'job_recommendation' => 'low',
            'profile_rating' => 'low',
            'company_rating' => 'low',
            'payment_success' => 'low',
        ];

        return $priorities[$type] ?? 'low';
    }
}

if (!function_exists('shouldShowNotificationBadge')) {
    /**
     * تحديد ما إذا كان يجب عرض شارة الإشعار
     */
    function shouldShowNotificationBadge(string $type): bool
    {
        $badgeTypes = [
            'urgent',
            'payment_failed',
            'job_expired',
            'interview_scheduled',
            'job_application',
            'application_update',
        ];

        return in_array($type, $badgeTypes);
    }
}

if (!function_exists('getNotificationTypeInArabic')) {
    /**
     * الحصول على اسم نوع الإشعار باللغة العربية
     */
    function getNotificationTypeInArabic(string $type): string
    {
        $types = [
            'job_application' => 'طلب توظيف',
            'application_update' => 'تحديث طلب التوظيف',
            'job_posted' => 'وظيفة جديدة',
            'job_expired' => 'انتهاء صلاحية الوظيفة',
            'payment_success' => 'دفع ناجح',
            'payment_failed' => 'فشل في الدفع',
            'profile_rating' => 'تقييم الملف الشخصي',
            'company_rating' => 'تقييم الشركة',
            'system_announcement' => 'إعلان النظام',
            'interview_scheduled' => 'جدولة مقابلة',
            'job_recommendation' => 'توصية وظيفة',
            'message' => 'رسالة',
            'reminder' => 'تذكير',
            'warning' => 'تحذير',
            'info' => 'معلومات',
            'urgent' => 'عاجل',
        ];

        return $types[$type] ?? $type;
    }
}

if (!function_exists('getJobTypeInArabic')) {
    /**
     * الحصول على نوع الوظيفة باللغة العربية
     */
    function getJobTypeInArabic(string $type): string
    {
        $types = [
            'full_time' => 'دوام كامل',
            'part_time' => 'دوام جزئي',
            'remote' => 'عمل عن بُعد',
            'freelance' => 'عمل حر',
            'contract' => 'عقد مؤقت',
            'internship' => 'تدريب',
        ];

        return $types[$type] ?? $type;
    }
}

if (!function_exists('getApplicationStatusInArabic')) {
    /**
     * الحصول على حالة الطلب باللغة العربية
     */
    function getApplicationStatusInArabic(string $status): string
    {
        $statuses = [
            'pending' => 'قيد المراجعة',
            'reviewed' => 'تمت المراجعة',
            'accepted' => 'مقبول',
            'rejected' => 'مرفوض',
            'interview' => 'مقابلة',
            'hired' => 'تم التوظيف',
        ];

        return $statuses[$status] ?? $status;
    }
}

if (!function_exists('getCurrencyNameInArabic')) {
    /**
     * الحصول على اسم العملة باللغة العربية
     */
    function getCurrencyNameInArabic(string $currency): string
    {
        $currencies = [
            'LYD' => 'دينار ليبي',
            'USD' => 'دولار أمريكي',
            'EUR' => 'يورو',
            'GBP' => 'جنيه إسترليني',
            'SAR' => 'ريال سعودي',
            'AED' => 'درهم إماراتي',
            'QAR' => 'ريال قطري',
            'KWD' => 'دينار كويتي',
            'BHD' => 'دينار بحريني',
            'OMR' => 'ريال عماني',
            'JOD' => 'دينار أردني',
            'EGP' => 'جنيه مصري',
            'TND' => 'دينار تونسي',
            'MAD' => 'درهم مغربي',
            'DZD' => 'دينار جزائري',
            'TRY' => 'ليرة تركية',
            'CAD' => 'دولار كندي',
            'AUD' => 'دولار أسترالي',
            'CHF' => 'فرنك سويسري',
            'JPY' => 'ين ياباني',
        ];

        return $currencies[$currency] ?? $currency;
    }
}

if (!function_exists('getCurrencySymbol')) {
    /**
     * الحصول على رمز العملة
     */
    function getCurrencySymbol(string $currency): string
    {
        $symbols = [
            'LYD' => 'د.ل',
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'SAR' => 'ر.س',
            'AED' => 'د.إ',
            'QAR' => 'ر.ق',
            'KWD' => 'د.ك',
            'BHD' => 'د.ب',
            'OMR' => 'ر.ع',
            'JOD' => 'د.أ',
            'EGP' => 'ج.م',
            'TND' => 'د.ت',
            'MAD' => 'د.م',
            'DZD' => 'د.ج',
            'TRY' => '₺',
            'CAD' => 'C$',
            'AUD' => 'A$',
            'CHF' => 'CHF',
            'JPY' => '¥',
        ];

        return $symbols[$currency] ?? $currency;
    }
}

if (!function_exists('storageUrl')) {
    /**
     * إنشاء URL للملفات المحفوظة في storage
     */
    function storageUrl($path)
    {
        if (!$path) {
            return null;
        }

        // إذا كان المسار يبدأ بـ http أو https، أرجعه كما هو
        if (str_starts_with($path, 'http://') || str_starts_with($path, 'https://')) {
            return $path;
        }

        // إزالة 'public/' من بداية المسار إذا كانت موجودة
        $path = ltrim($path, '/');
        if (str_starts_with($path, 'public/')) {
            $path = substr($path, 7);
        }

        // إنشاء URL للملف
        return url('/storage/' . $path);
    }
}

if (!function_exists('defaultAvatar')) {
    /**
     * إنشاء avatar افتراضي باستخدام الأحرف الأولى من الاسم
     */
    function defaultAvatar($name = '', $size = 200)
    {
        if (empty($name)) {
            return 'https://ui-avatars.com/api/?name=User&background=3b82f6&color=ffffff&size=' . $size;
        }

        $initials = '';
        $words = explode(' ', $name);
        foreach ($words as $word) {
            if (!empty($word)) {
                $initials .= mb_substr($word, 0, 1);
            }
        }

        return 'https://ui-avatars.com/api/?name=' . urlencode($initials) . '&background=3b82f6&color=ffffff&size=' . $size;
    }
}

if (!function_exists('formatSalary')) {
    /**
     * تنسيق الراتب مع العملة
     */
    function formatSalary($min, $max, $currency = 'LYD')
    {
        if (!$min && !$max) {
            return 'غير محدد';
        }

        $currencySymbol = getCurrencySymbol($currency);

        if ($min && $max) {
            return number_format($min) . ' - ' . number_format($max) . ' ' . $currencySymbol;
        } elseif ($min) {
            return 'من ' . number_format($min) . ' ' . $currencySymbol;
        } else {
            return 'حتى ' . number_format($max) . ' ' . $currencySymbol;
        }
    }
}
