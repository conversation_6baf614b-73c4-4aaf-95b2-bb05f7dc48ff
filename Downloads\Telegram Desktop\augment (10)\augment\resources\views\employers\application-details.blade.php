@extends('layouts.app')

@section('title', 'تفاصيل طلب التوظيف - Hire Me')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
    <div class="container mx-auto px-4 py-8">
        <!-- Breadcrumb -->
        <nav class="flex mb-6" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3 rtl:space-x-reverse">
                <li class="inline-flex items-center">
                    <a href="{{ route('home') }}" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                        <i class="fas fa-home ml-2"></i>
                        الرئيسية
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                        <a href="{{ route('employer.applications.index') }}" class="text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">طلبات التوظيف</a>
                    </div>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">تفاصيل الطلب</span>
                    </div>
                </li>
            </ol>
        </nav>

        <div class="max-w-7xl mx-auto">
            <!-- Application Header -->
            <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">تقديم طلب للوظيفة</h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-2 flex flex-wrap items-center gap-4">
                        <span class="flex items-center gap-1">
                            <i class="fas fa-user text-blue-500"></i>
                            {{ $application->jobSeeker->user->name }}
                        </span>
                        <span class="flex items-center gap-1">
                            <i class="fas fa-briefcase text-green-500"></i>
                            {{ $application->job->title }}
                        </span>
                        <span class="flex items-center gap-1">
                            <i class="fas fa-calendar text-purple-500"></i>
                            {{ $application->created_at->format('Y/m/d') }}
                        </span>
                    </p>
                </div>
                <div class="mt-4 md:mt-0 flex gap-3">
                    <button onclick="printApplication()" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors flex items-center gap-2">
                        <i class="fas fa-print"></i>
                        <span>طباعة</span>
                    </button>
                    <button onclick="showExportModal()" class="px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl flex items-center gap-2">
                        <i class="fas fa-download"></i>
                        <span>تصدير</span>
                    </button>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Main Application Details -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Cover Letter -->
                    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                            <i class="fas fa-envelope text-blue-500"></i>
                            خطاب التقديم
                        </h2>
                        <div class="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                            <div class="flex items-start gap-4">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center flex-shrink-0">
                                    <i class="fas fa-quote-left text-white text-sm"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-line">{{ $application->cover_letter ?: 'لم يتم إرفاق خطاب تقديم مع هذا الطلب.' }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Applicant Information -->
                    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                        <div class="flex justify-between items-center mb-6">
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                                <i class="fas fa-user-circle text-green-500"></i>
                                معلومات المتقدم
                            </h2>
                            <a href="{{ route('job-seekers.show', $application->jobSeeker) }}" class="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-4 py-2 rounded-lg text-sm transition-all duration-200 shadow-lg hover:shadow-xl flex items-center gap-2">
                                <i class="fas fa-external-link-alt"></i>
                                <span>عرض الملف الكامل</span>
                            </a>
                        </div>

                        <div class="flex items-center mb-6 p-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg border border-green-200 dark:border-green-800">
                            <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center ml-4 flex-shrink-0">
                                @if($application->jobSeeker->user->avatar)
                                    <img src="{{ asset('storage/' . $application->jobSeeker->user->avatar) }}" alt="{{ $application->jobSeeker->user->name }}" class="w-14 h-14 rounded-full object-cover border-2 border-white">
                                @else
                                    <i class="fas fa-user text-2xl text-white"></i>
                                @endif
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-900 dark:text-white">{{ $application->jobSeeker->user->name }}</h3>
                                <p class="text-gray-600 dark:text-gray-400 flex items-center gap-1">
                                    <i class="fas fa-briefcase text-green-500"></i>
                                    {{ $application->jobSeeker->job_title ?: 'باحث عن عمل' }}
                                </p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                                <h3 class="text-sm font-medium text-gray-600 dark:text-gray-400 flex items-center gap-2 mb-2">
                                    <i class="fas fa-envelope text-blue-500"></i>
                                    البريد الإلكتروني
                                </h3>
                                <p class="text-lg font-medium text-gray-900 dark:text-white">{{ $application->jobSeeker->user->email }}</p>
                            </div>

                            <div class="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
                                <h3 class="text-sm font-medium text-gray-600 dark:text-gray-400 flex items-center gap-2 mb-2">
                                    <i class="fas fa-map-marker-alt text-purple-500"></i>
                                    الموقع
                                </h3>
                                <p class="text-lg font-medium text-gray-900 dark:text-white">{{ $application->jobSeeker->location ?: 'غير محدد' }}</p>
                            </div>

                            <div class="md:col-span-2 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                                <h3 class="text-sm font-medium text-gray-600 dark:text-gray-400 flex items-center gap-2 mb-3">
                                    <i class="fas fa-tools text-yellow-500"></i>
                                    المهارات
                                </h3>
                                <div class="flex flex-wrap gap-2">
                                    @if($application->jobSeeker->skills)
                                        @foreach(explode(',', $application->jobSeeker->skills) as $skill)
                                            <span class="px-3 py-1 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-full text-sm font-medium shadow-sm">{{ trim($skill) }}</span>
                                        @endforeach
                                    @else
                                        <p class="text-gray-500 dark:text-gray-400 italic">لم يتم تحديد مهارات</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Resume -->
                    @if($application->resume)
                    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                                <i class="fas fa-file-alt text-red-500"></i>
                                السيرة الذاتية (اختياري)
                            </h2>
                            <a href="{{ asset('storage/' . $application->resume) }}" target="_blank" class="bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700 text-white px-4 py-2 rounded-lg text-sm transition-all duration-200 shadow-lg hover:shadow-xl flex items-center gap-2">
                                <i class="fas fa-download"></i>
                                <span>تحميل السيرة الذاتية</span>
                            </a>
                        </div>

                        <div class="p-6 bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 rounded-lg border border-red-200 dark:border-red-800 flex items-center justify-center">
                            <div class="text-center">
                                <div class="w-16 h-16 bg-gradient-to-br from-red-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="far fa-file-pdf text-2xl text-white"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">ملف PDF متاح</h3>
                                <p class="text-gray-600 dark:text-gray-400 mb-4">اضغط للتحميل أو المعاينة</p>
                                <div class="flex justify-center gap-3">
                                    <a href="{{ asset('storage/' . $application->resume) }}" target="_blank" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm transition-colors flex items-center gap-2">
                                        <i class="fas fa-eye"></i>
                                        معاينة
                                    </a>
                                    <a href="{{ asset('storage/' . $application->resume) }}" download class="px-4 py-2 border border-red-600 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/30 rounded-lg text-sm transition-colors flex items-center gap-2">
                                        <i class="fas fa-download"></i>
                                        تحميل
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    @else
                    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                        <h2 class="text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-2 mb-4">
                            <i class="fas fa-file-alt text-gray-400"></i>
                            السيرة الذاتية (اختياري)
                        </h2>
                        <div class="p-6 bg-gray-50 dark:bg-gray-800 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 text-center">
                            <i class="fas fa-file-excel text-4xl text-gray-400 mb-3"></i>
                            <p class="text-gray-500 dark:text-gray-400">لم يتم إرفاق سيرة ذاتية مع هذا الطلب</p>
                        </div>
                    </div>
                    @endif

                    <!-- Employer Notes -->
                    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                        <h2 class="text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-2 mb-4">
                            <i class="fas fa-sticky-note text-orange-500"></i>
                            ملاحظات
                        </h2>
                        <form action="{{ route('employer.applications.update-status', $application) }}" method="POST">
                            @csrf
                            @method('PATCH')
                            <div class="mb-4">
                                <div class="relative">
                                    <textarea name="employer_notes" rows="4" class="w-full px-4 py-3 bg-gradient-to-r from-orange-50 to-yellow-50 dark:from-orange-900/20 dark:to-yellow-900/20 border border-orange-200 dark:border-orange-800 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 focus:outline-none transition-all duration-200" placeholder="أضف ملاحظاتك حول هذا المتقدم...">{{ $application->employer_notes }}</textarea>
                                    <div class="absolute top-3 left-3">
                                        <i class="fas fa-edit text-orange-400"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="flex justify-end">
                                <button type="submit" class="px-6 py-2 bg-gradient-to-r from-orange-600 to-yellow-600 hover:from-orange-700 hover:to-yellow-700 text-white rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl flex items-center gap-2">
                                    <i class="fas fa-save"></i>
                                    حفظ الملاحظات
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Application Status -->
                    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                            <i class="fas fa-tasks text-purple-500"></i>
                            حالة الطلب
                        </h3>
                        <div class="mb-6">
                            @if($application->status == 'pending')
                                <div class="p-4 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                                    <div class="flex items-center justify-between">
                                        <span class="text-yellow-700 dark:text-yellow-400 font-medium flex items-center gap-2">
                                            <i class="fas fa-clock text-yellow-500"></i>
                                            قيد المراجعة
                                        </span>
                                        <span class="px-3 py-1 text-xs font-medium text-yellow-700 dark:text-yellow-400 bg-yellow-200 dark:bg-yellow-900/50 rounded-full">قيد المراجعة</span>
                                    </div>
                                </div>
                            @elseif($application->status == 'reviewed')
                                <div class="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                                    <div class="flex items-center justify-between">
                                        <span class="text-blue-700 dark:text-blue-400 font-medium flex items-center gap-2">
                                            <i class="fas fa-check-circle text-blue-500"></i>
                                            تمت المراجعة
                                        </span>
                                        <span class="px-3 py-1 text-xs font-medium text-blue-700 dark:text-blue-400 bg-blue-200 dark:bg-blue-900/50 rounded-full">تمت المراجعة</span>
                                    </div>
                                </div>
                            @elseif($application->status == 'shortlisted')
                                <div class="p-4 bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-lg border border-indigo-200 dark:border-indigo-800">
                                    <div class="flex items-center justify-between">
                                        <span class="text-indigo-700 dark:text-indigo-400 font-medium flex items-center gap-2">
                                            <i class="fas fa-list-alt text-indigo-500"></i>
                                            القائمة المختصرة
                                        </span>
                                        <span class="px-3 py-1 text-xs font-medium text-indigo-700 dark:text-indigo-400 bg-indigo-200 dark:bg-indigo-900/50 rounded-full">القائمة المختصرة</span>
                                    </div>
                                </div>
                            @elseif($application->status == 'rejected')
                                <div class="p-4 bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 rounded-lg border border-red-200 dark:border-red-800">
                                    <div class="flex items-center justify-between">
                                        <span class="text-red-700 dark:text-red-400 font-medium flex items-center gap-2">
                                            <i class="fas fa-times-circle text-red-500"></i>
                                            مرفوض
                                        </span>
                                        <span class="px-3 py-1 text-xs font-medium text-red-700 dark:text-red-400 bg-red-200 dark:bg-red-900/50 rounded-full">مرفوض</span>
                                    </div>
                                </div>
                            @elseif($application->status == 'hired')
                                <div class="p-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg border border-green-200 dark:border-green-800">
                                    <div class="flex items-center justify-between">
                                        <span class="text-green-700 dark:text-green-400 font-medium flex items-center gap-2">
                                            <i class="fas fa-user-check text-green-500"></i>
                                            تم التعيين
                                        </span>
                                        <span class="px-3 py-1 text-xs font-medium text-green-700 dark:text-green-400 bg-green-200 dark:bg-green-900/50 rounded-full">تم التعيين</span>
                                    </div>
                                </div>
                            @endif
                        </div>

                        <form action="{{ route('employer.applications.update-status', $application) }}" method="POST" id="statusForm">
                            @csrf
                            @method('PATCH')
                            <input type="hidden" name="status" id="statusInput" value="{{ $application->status }}">

                            <div class="space-y-3">
                                <button type="button" class="w-full py-3 px-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800 rounded-lg text-blue-700 dark:text-blue-300 hover:from-blue-100 hover:to-indigo-100 dark:hover:from-blue-900/30 dark:hover:to-indigo-900/30 transition-all duration-200 text-right status-btn {{ $application->status == 'reviewed' ? 'ring-2 ring-blue-500 bg-gradient-to-r from-blue-100 to-indigo-100' : '' }}" data-status="reviewed">
                                    <i class="fas fa-check-circle ml-2 {{ $application->status == 'reviewed' ? 'text-blue-600' : 'text-blue-500' }}"></i>
                                    تمت المراجعة
                                </button>

                                <button type="button" class="w-full py-3 px-4 bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 border border-indigo-200 dark:border-indigo-800 rounded-lg text-indigo-700 dark:text-indigo-300 hover:from-indigo-100 hover:to-purple-100 dark:hover:from-indigo-900/30 dark:hover:to-purple-900/30 transition-all duration-200 text-right status-btn {{ $application->status == 'shortlisted' ? 'ring-2 ring-indigo-500 bg-gradient-to-r from-indigo-100 to-purple-100' : '' }}" data-status="shortlisted">
                                    <i class="fas fa-list-alt ml-2 {{ $application->status == 'shortlisted' ? 'text-indigo-600' : 'text-indigo-500' }}"></i>
                                    القائمة المختصرة
                                </button>

                                <button type="button" class="w-full py-3 px-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800 rounded-lg text-green-700 dark:text-green-300 hover:from-green-100 hover:to-emerald-100 dark:hover:from-green-900/30 dark:hover:to-emerald-900/30 transition-all duration-200 text-right status-btn {{ $application->status == 'hired' ? 'ring-2 ring-green-500 bg-gradient-to-r from-green-100 to-emerald-100' : '' }}" data-status="hired">
                                    <i class="fas fa-user-check ml-2 {{ $application->status == 'hired' ? 'text-green-600' : 'text-green-500' }}"></i>
                                    تم التعيين
                                </button>

                                <button type="button" class="w-full py-3 px-4 bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 border border-red-200 dark:border-red-800 rounded-lg text-red-700 dark:text-red-300 hover:from-red-100 hover:to-pink-100 dark:hover:from-red-900/30 dark:hover:to-pink-900/30 transition-all duration-200 text-right status-btn {{ $application->status == 'rejected' ? 'ring-2 ring-red-500 bg-gradient-to-r from-red-100 to-pink-100' : '' }}" data-status="rejected">
                                    <i class="fas fa-times-circle ml-2 {{ $application->status == 'rejected' ? 'text-red-600' : 'text-red-500' }}"></i>
                                    مرفوض
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Job Details -->
                    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                            <i class="fas fa-briefcase text-indigo-500"></i>
                            تفاصيل الوظيفة
                        </h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                                <span class="text-gray-600 dark:text-gray-400 flex items-center gap-2">
                                    <i class="fas fa-tag text-blue-500"></i>
                                    العنوان:
                                </span>
                                <span class="font-medium text-gray-900 dark:text-white">{{ $application->job->title }}</span>
                            </div>

                            <div class="flex items-center justify-between p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                                <span class="text-gray-600 dark:text-gray-400 flex items-center gap-2">
                                    <i class="fas fa-map-marker-alt text-purple-500"></i>
                                    الموقع:
                                </span>
                                <span class="font-medium text-gray-900 dark:text-white">{{ $application->job->location }}</span>
                            </div>

                            <div class="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                                <span class="text-gray-600 dark:text-gray-400 flex items-center gap-2">
                                    <i class="fas fa-clock text-green-500"></i>
                                    نوع الوظيفة:
                                </span>
                                <span class="font-medium text-gray-900 dark:text-white">
                                    @switch($application->job->job_type)
                                        @case('full_time')
                                            دوام كامل
                                            @break
                                        @case('part_time')
                                            دوام جزئي
                                            @break
                                        @case('remote')
                                            عن بعد
                                            @break
                                        @case('freelance')
                                            عمل حر
                                            @break
                                        @default
                                            {{ $application->job->job_type }}
                                    @endswitch
                                </span>
                            </div>

                            <div class="flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                                <span class="text-gray-600 dark:text-gray-400 flex items-center gap-2">
                                    <i class="fas fa-calendar-plus text-yellow-500"></i>
                                    تاريخ النشر:
                                </span>
                                <span class="font-medium text-gray-900 dark:text-white">{{ $application->job->created_at->format('Y/m/d') }}</span>
                            </div>

                            <div class="flex items-center justify-between p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                                <span class="text-gray-600 dark:text-gray-400 flex items-center gap-2">
                                    <i class="fas fa-users text-red-500"></i>
                                    عدد الطلبات:
                                </span>
                                <span class="font-medium text-gray-900 dark:text-white">{{ $application->job->applications->count() }}</span>
                            </div>
                        </div>

                        <div class="mt-6">
                            <a href="{{ route('employer.jobs.show', $application->job) }}" class="w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white py-3 px-4 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl flex items-center justify-center gap-2">
                                <i class="fas fa-eye"></i>
                                <span>عرض تفاصيل الوظيفة</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Modal -->
    <div id="exportModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-lg w-full mx-4" onclick="event.stopPropagation()">
            <!-- Header -->
            <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">تصدير طلبات التوظيف</h3>
                <button onclick="hideExportModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Body -->
            <div class="p-6">
                <div class="mb-6">
                    <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4">اختر صيغة التصدير</h4>
                    <div class="space-y-3">
                        <label class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors" onclick="selectOption('csv')">
                            <input type="radio" name="export_format" value="csv" checked class="hidden">
                            <div class="flex-1 text-right">
                                <div class="text-gray-900 dark:text-white font-medium">CSV (Excel) - جميع البيانات</div>
                            </div>
                            <div class="w-4 h-4 rounded-full border-2 border-red-600 bg-red-600 flex items-center justify-center mr-3" id="indicator-csv">
                                <div class="w-2 h-2 rounded-full bg-white"></div>
                            </div>
                        </label>

                        <label class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors" onclick="selectOption('csv_filtered')">
                            <input type="radio" name="export_format" value="csv_filtered" class="hidden">
                            <div class="flex-1 text-right">
                                <div class="text-gray-900 dark:text-white font-medium">CSV مع الفلاتر الحالية</div>
                            </div>
                            <div class="w-4 h-4 rounded-full border-2 border-gray-300 dark:border-gray-600 mr-3" id="indicator-csv_filtered"></div>
                        </label>

                        <label class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors" onclick="selectOption('pdf')">
                            <input type="radio" name="export_format" value="pdf" class="hidden">
                            <div class="flex-1 text-right">
                                <div class="text-gray-900 dark:text-white font-medium">PDF - تقرير مفصل</div>
                            </div>
                            <div class="w-4 h-4 rounded-full border-2 border-gray-300 dark:border-gray-600 mr-3" id="indicator-pdf"></div>
                        </label>
                    </div>
                </div>

                <!-- Info Box -->
                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                    <div class="flex items-start">
                        <i class="fas fa-info-circle text-blue-500 mt-1 ml-2"></i>
                        <div class="text-sm text-blue-700 dark:text-blue-300">
                            <div class="font-medium mb-2">سيتم تصدير:</div>
                            <ul class="space-y-1 text-xs">
                                <li>• أسماء المتقدمين وبياناتهم</li>
                                <li>• تفاصيل الوظائف المتقدم إليها</li>
                                <li>• حالات الطلبات وتواريخ التقديم</li>
                                <li>• التقييمات والملاحظات</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="flex gap-3 p-6 border-t border-gray-200 dark:border-gray-700">
                <button onclick="hideExportModal()" class="flex-1 px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors font-medium">
                    إلغاء
                </button>
                <button onclick="startExport()" class="flex-1 px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors flex items-center justify-center gap-2 font-medium">
                    <i class="fas fa-download"></i>
                    تصدير
                </button>
            </div>
        </div>
    </div>

    <!-- JavaScript for Interactive Features -->
    <script>
        function printApplication() {
            window.print();
        }

        function showExportModal() {
            console.log('Export button clicked!');
            document.getElementById('exportModal').classList.remove('hidden');
        }

        function hideExportModal() {
            document.getElementById('exportModal').classList.add('hidden');
        }

        // إغلاق modal عند النقر خارجه
        document.getElementById('exportModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideExportModal();
            }
        });

        // دالة لتحديد الخيار
        function selectOption(value) {
            // إلغاء تحديد جميع الخيارات
            document.querySelectorAll('input[name="export_format"]').forEach(radio => {
                radio.checked = false;
            });

            // تحديد الخيار المطلوب
            document.querySelector(`input[name="export_format"][value="${value}"]`).checked = true;

            // تحديث المؤشرات البصرية
            updateRadioButtons();
        }

        // تحديث الـ radio buttons بصرياً
        function updateRadioButtons() {
            const options = ['csv', 'csv_filtered', 'pdf'];

            options.forEach(option => {
                const radio = document.querySelector(`input[name="export_format"][value="${option}"]`);
                const indicator = document.getElementById(`indicator-${option}`);

                if (radio && indicator) {
                    if (radio.checked) {
                        indicator.className = 'w-4 h-4 rounded-full border-2 border-red-600 bg-red-600 flex items-center justify-center mr-3';
                        indicator.innerHTML = '<div class="w-2 h-2 rounded-full bg-white"></div>';
                    } else {
                        indicator.className = 'w-4 h-4 rounded-full border-2 border-gray-300 dark:border-gray-600 mr-3';
                        indicator.innerHTML = '';
                    }
                }
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            // تحديث أولي
            updateRadioButtons();
        });

        function startExport() {
            const selectedFormat = document.querySelector('input[name="export_format"]:checked').value;
            hideExportModal();

            console.log('Exporting as:', selectedFormat);

            let format = 'csv';
            if (selectedFormat === 'pdf') {
                format = 'pdf';
            } else if (selectedFormat === 'csv_filtered') {
                format = 'csv';
            }

            // استخدام window.open للتحميل المباشر
            const url = '{{ route("employer.applications.export-single", $application->id) }}?format=' + format;
            console.log('Export URL:', url);

            window.open(url, '_blank');
        }

        function exportAsPDF() {
            hideExportModal();
            console.log('Exporting as PDF...');

            // استخدام window.open للتحميل المباشر
            const url = '{{ route("employer.applications.export-single", $application->id) }}?format=pdf';
            console.log('Export URL:', url);

            window.open(url, '_blank');
        }

        function exportAsCSV() {
            hideExportModal();
            console.log('Exporting as CSV...');

            // استخدام window.open للتحميل المباشر
            const url = '{{ route("employer.applications.export-single", $application->id) }}?format=csv';
            console.log('Export URL:', url);

            window.open(url, '_blank');
        }

        // Status buttons
        const statusButtons = document.querySelectorAll('.status-btn');
        const statusInput = document.getElementById('statusInput');
        const statusForm = document.getElementById('statusForm');

        statusButtons.forEach(button => {
            button.addEventListener('click', () => {
                const status = button.getAttribute('data-status');

                // Confirm status change
                const statusNames = {
                    'reviewed': 'تمت المراجعة',
                    'shortlisted': 'القائمة المختصرة',
                    'hired': 'تم التعيين',
                    'rejected': 'مرفوض'
                };

                if (confirm(`هل أنت متأكد من تغيير حالة الطلب إلى "${statusNames[status]}"؟`)) {
                    statusInput.value = status;
                    statusForm.submit();
                }
            });
        });

        // Add animation classes
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.bg-white, .bg-dark-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.classList.add('animate-fade-in');
            });
        });
    </script>

    <style>
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fade-in {
            animation: fadeIn 0.6s ease-out forwards;
        }

        @media print {
            .no-print {
                display: none !important;
            }

            body {
                background: white !important;
            }

            .bg-gradient-to-br,
            .bg-gradient-to-r {
                background: white !important;
            }
        }
    </style>
</div>
@endsection


