@extends('layouts.app')

@section('title', 'الملف الشخصي لصاحب العمل')

@section('content')
<div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div class="p-6 bg-white border-b border-gray-200">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-800">الملف الشخصي للشركة</h2>
                <a href="{{ route('employer.profile.edit') }}" class="bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700">
                    تعديل الملف الشخصي
                </a>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="md:col-span-1">
                    <div class="bg-gray-100 p-4 rounded-lg">
                        <div class="text-center mb-4">
                            @if($employer->company_logo)
                                <img src="{{ Storage::url($employer->company_logo) }}" alt="{{ $employer->company_name }}" class="w-32 h-32 mx-auto rounded-full object-cover">
                            @else
                                <div class="w-32 h-32 mx-auto bg-indigo-100 rounded-full flex items-center justify-center text-indigo-500">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                    </svg>
                                </div>
                            @endif
                            <h3 class="mt-2 text-xl font-semibold text-gray-800">{{ $employer->company_name ?? 'اسم الشركة غير محدد' }}</h3>
                            <p class="text-gray-600">{{ Auth::user()->email }}</p>
                        </div>

                        <div class="border-t border-gray-200 pt-4">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-gray-600">الموقع الإلكتروني:</span>
                                @if($employer->website)
                                    <a href="{{ $employer->website }}" target="_blank" class="text-indigo-600 hover:text-indigo-800">
                                        {{ $employer->website }}
                                    </a>
                                @else
                                    <span class="text-gray-500">غير محدد</span>
                                @endif
                            </div>

                            <div class="flex justify-between items-center mb-2">
                                <span class="text-gray-600">الموقع:</span>
                                <span>{{ $employer->location ?? 'غير محدد' }}</span>
                            </div>

                            <div class="flex justify-between items-center mb-2">
                                <span class="text-gray-600">الصناعة:</span>
                                <span>{{ $employer->industry ?? 'غير محدد' }}</span>
                            </div>

                            <div class="flex justify-between items-center mb-2">
                                <span class="text-gray-600">حجم الشركة:</span>
                                <span>{{ $employer->company_size ?? 'غير محدد' }}</span>
                            </div>

                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">سنة التأسيس:</span>
                                <span>{{ $employer->founded_year ?? 'غير محدد' }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="md:col-span-2">
                    <div class="bg-white p-4 rounded-lg shadow">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">وصف الشركة</h3>
                        <p class="text-gray-700">
                            {{ $employer->company_description ?? 'لم يتم إضافة وصف للشركة بعد.' }}
                        </p>
                    </div>

                    <div class="mt-6 bg-white p-4 rounded-lg shadow">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-800">الوظائف المنشورة</h3>
                            <a href="{{ route('employer.jobs.index') }}" class="text-indigo-600 hover:text-indigo-800">
                                عرض الكل
                            </a>
                        </div>

                        @if($employer->jobs->count() > 0)
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                العنوان
                                            </th>
                                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                الموقع
                                            </th>
                                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                النوع
                                            </th>
                                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                تاريخ النشر
                                            </th>
                                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                التقديمات
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        @foreach($employer->jobs->take(5) as $job)
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <a href="{{ route('jobs.show', $job) }}" class="text-indigo-600 hover:text-indigo-900">
                                                        {{ $job->title }}
                                                    </a>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    {{ $job->location }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    {{ $job->job_type }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    {{ $job->posted_at->format('Y-m-d') }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    {{ $job->applications->count() }}
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <p class="text-gray-500">لم تقم بنشر أي وظائف بعد.</p>
                            <div class="mt-4">
                                <a href="{{ route('jobs.create') }}" class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:border-indigo-900 focus:ring ring-indigo-300 disabled:opacity-25 transition ease-in-out duration-150">
                                    إنشاء وظيفة جديدة
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
