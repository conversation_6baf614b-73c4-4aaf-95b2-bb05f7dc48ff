@php
use App\Services\NotificationService;

$notificationService = app(NotificationService::class);
$unreadNotifications = $notificationService->getUnreadNotifications(auth()->user(), 5);
$unreadCount = $notificationService->getUnreadCount(auth()->user());
@endphp

<div class="relative" x-data="{ open: false }">
    <!-- Notification Button -->
    <button @click="open = !open"
            @keydown.enter="open = !open"
            @keydown.space.prevent="open = !open"
            :aria-expanded="open"
            aria-haspopup="true"
            aria-label="الإشعارات"
            class="relative p-3 rounded-xl bg-gradient-to-br from-amber-100 to-orange-200 dark:from-amber-800 dark:to-orange-900 hover:shadow-lg transition-all duration-300 group focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2 hover:scale-105">
        <i class="fas fa-bell text-amber-600 dark:text-amber-400 group-hover:scale-110 transition-transform"></i>

        @if($unreadCount > 0)
        <span class="notification-badge absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-br from-red-500 to-red-600 text-white text-xs rounded-full flex items-center justify-center notification-pulse shadow-lg animate-bounce">
            {{ $unreadCount > 99 ? '99+' : $unreadCount }}
        </span>
        @endif
    </button>

    <!-- Dropdown -->
    <div x-show="open"
         x-cloak
         @click.away="open = false"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 scale-95 translate-y-2"
         x-transition:enter-end="opacity-100 scale-100 translate-y-0"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 scale-100 translate-y-0"
         x-transition:leave-end="opacity-0 scale-95 translate-y-2"
         role="menu"
         aria-label="قائمة الإشعارات"
         class="absolute left-0 mt-2 w-96 bg-white/95 dark:bg-gray-800/95 backdrop-blur-lg rounded-xl shadow-2xl border border-gray-200/50 dark:border-gray-700/50 z-[9999] max-h-96 overflow-hidden notification-dropdown">

        <!-- Header -->
        <div class="px-4 py-3 border-b border-gray-200/50 dark:border-gray-700/50 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900/50 dark:to-gray-800/50">
            <div class="flex items-center justify-between">
                <h3 class="text-sm font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                    <i class="fas fa-bell text-amber-500"></i>
                    الإشعارات
                </h3>
                @if($unreadCount > 0)
                <form action="{{ route('notifications.mark-all-read') }}" method="POST" class="inline">
                    @csrf
                    <button type="submit" class="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 px-2 py-1 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-colors">
                        تحديد الكل كمقروء
                    </button>
                </form>
                @endif
            </div>
        </div>

        <!-- Notifications List -->
        <div class="max-h-80 overflow-y-auto">
            @forelse($unreadNotifications as $notification)
            <div class="px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700/50 border-b border-gray-100 dark:border-gray-700 last:border-b-0 notification-item">
                <div class="flex items-start gap-3">
                    <!-- Icon -->
                    <div class="flex-shrink-0 mt-1">
                        <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                            <i class="fas fa-{{ getNotificationIcon($notification->type) }} text-blue-600 dark:text-blue-400 text-xs"></i>
                        </div>
                    </div>

                    <!-- Content -->
                    <div class="flex-1 min-w-0">
                        <p class="text-sm text-gray-900 dark:text-white font-medium line-clamp-2">
                            {{ $notification->message }}
                        </p>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            {{ $notification->created_at->diffForHumans() }}
                        </p>
                    </div>

                    <!-- Action -->
                    @if($notification->link)
                    <div class="flex-shrink-0">
                        <a href="{{ route('notifications.mark-read', $notification) }}" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                            <i class="fas fa-external-link-alt text-xs"></i>
                        </a>
                    </div>
                    @endif
                </div>
            </div>
            @empty
            <div class="px-4 py-8 text-center">
                <div class="w-12 h-12 mx-auto mb-3 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                    <i class="fas fa-bell-slash text-gray-400"></i>
                </div>
                <p class="text-sm text-gray-500 dark:text-gray-400">لا توجد إشعارات جديدة</p>
            </div>
            @endforelse
        </div>

        <!-- Footer -->
        @if($unreadNotifications->count() > 0)
        <div class="px-4 py-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900/50">
            <a href="{{ route('notifications.index') }}" class="block text-center text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium">
                عرض جميع الإشعارات
            </a>
        </div>
        @endif
    </div>
</div>



<style>
/* تحسينات للنوافذ المنبثقة */
[x-cloak] {
    display: none !important;
}

.notification-pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.notification-dropdown {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    will-change: transform, opacity;
}

.notification-item {
    transition: all 0.2s ease-in-out;
}

.notification-item:hover {
    transform: translateX(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .notification-dropdown {
        position: fixed !important;
        top: 60px !important;
        left: 10px !important;
        right: 10px !important;
        width: auto !important;
        max-width: none !important;
    }

    .notification-item:hover {
        transform: none;
        box-shadow: none;
    }
}

/* تحسين إمكانية الوصول */
.notification-dropdown:focus-within {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
