<?php

require_once 'vendor/autoload.php';

// تحميل Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Notification;
use Illuminate\Support\Facades\DB;

try {
    echo "=== تشخيص مشكلة الإشعار 253 ===\n\n";
    
    // التحقق من الاتصال بقاعدة البيانات
    DB::connection()->getPdo();
    echo "✅ الاتصال بقاعدة البيانات: نجح\n";
    
    // البحث عن الإشعار 253
    $notification = Notification::find(253);
    
    if ($notification) {
        echo "✅ الإشعار 253: موجود\n";
        echo "   - المستخدم: {$notification->user_id}\n";
        echo "   - النوع: {$notification->type}\n";
        echo "   - الرسالة: " . substr($notification->message, 0, 100) . "...\n";
        echo "   - مقروء: " . ($notification->is_read ? 'نعم' : 'لا') . "\n";
        echo "   - الرابط: {$notification->link}\n";
        echo "   - تاريخ الإنشاء: {$notification->created_at}\n";
        
        // التحقق من المستخدم
        if ($notification->user) {
            echo "   - اسم المستخدم: {$notification->user->name}\n";
            echo "   - دور المستخدم: {$notification->user->role}\n";
        } else {
            echo "   ❌ المستخدم غير موجود!\n";
        }
    } else {
        echo "❌ الإشعار 253: غير موجود\n";
        
        // البحث عن آخر الإشعارات
        $latestNotifications = Notification::latest()->limit(5)->get();
        echo "\n--- آخر 5 إشعارات ---\n";
        foreach ($latestNotifications as $notif) {
            echo "ID: {$notif->id}, المستخدم: {$notif->user_id}, النوع: {$notif->type}\n";
        }
    }
    
    // إحصائيات عامة
    $totalNotifications = Notification::count();
    $unreadNotifications = Notification::where('is_read', false)->count();
    
    echo "\n--- إحصائيات ---\n";
    echo "إجمالي الإشعارات: {$totalNotifications}\n";
    echo "الإشعارات غير المقروءة: {$unreadNotifications}\n";
    
    // التحقق من الـ routes
    echo "\n--- فحص الـ Routes ---\n";
    $routes = [
        'notifications.mark-read' => 'notifications/{notification}/mark-read',
        'employer.notifications.mark-read' => 'employer/notifications/{notification}/mark-read',
        'admin.notifications.mark-read' => 'admin/notifications/{notification}/mark-read',
    ];
    
    foreach ($routes as $name => $pattern) {
        try {
            $url = route($name, ['notification' => 253]);
            echo "✅ {$name}: {$url}\n";
        } catch (Exception $e) {
            echo "❌ {$name}: خطأ - {$e->getMessage()}\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== انتهى التشخيص ===\n";
