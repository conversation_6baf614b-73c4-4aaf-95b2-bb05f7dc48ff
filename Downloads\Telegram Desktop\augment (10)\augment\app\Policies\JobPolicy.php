<?php

namespace App\Policies;

use App\Models\Job;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class JobPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Job $job): bool
    {
        // يمكن لأي شخص عرض الوظائف النشطة
        if ($job->is_active) {
            return true;
        }

        // يمكن لصاحب العمل عرض وظائفه حتى لو كانت غير نشطة
        if ($user->role === 'employer' && $user->employer && $user->employer->id === $job->employer_id) {
            return true;
        }

        // يمكن للمدير عرض جميع الوظائف
        if ($user->role === 'admin') {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->role === 'employer' || $user->role === 'admin';
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Job $job): bool
    {
        // يمكن لصاحب العمل تعديل وظائفه فقط
        if ($user->role === 'employer' && $user->employer && $user->employer->id === $job->employer_id) {
            return true;
        }

        // يمكن للمدير تعديل جميع الوظائف
        if ($user->role === 'admin') {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Job $job): bool
    {
        // يمكن لصاحب العمل حذف وظائفه فقط
        if ($user->role === 'employer' && $user->employer && $user->employer->id === $job->employer_id) {
            return true;
        }

        // يمكن للمدير حذف جميع الوظائف
        if ($user->role === 'admin') {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Job $job): bool
    {
        return $this->delete($user, $job);
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Job $job): bool
    {
        return $user->role === 'admin';
    }
}
