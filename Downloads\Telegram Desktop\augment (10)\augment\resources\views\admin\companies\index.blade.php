@extends('layouts.admin')

@section('title', 'الشركات - Hire Me')
@section('header_title', 'الشركات')

@section('content')
<div class="p-6">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
            <h2 class="text-2xl font-bold">قائمة الشركات</h2>
            <p class="text-gray-600 dark:text-gray-400 mt-1">إدارة الشركات المسجلة في المنصة</p>
        </div>
        <div class="mt-4 md:mt-0 flex gap-3">
            <button class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors flex items-center gap-2">
                <i class="fas fa-filter"></i>
                <span>تصفية</span>
            </button>
            <button class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors flex items-center gap-2">
                <i class="fas fa-download"></i>
                <span>تصدير</span>
            </button>
            <button class="px-4 py-2 gradient-bg text-white rounded-lg hover:opacity-90 transition-colors flex items-center gap-2">
                <i class="fas fa-plus"></i>
                <span>إضافة شركة</span>
            </button>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6 mb-6">
        <div class="flex flex-col md:flex-row gap-4">
            <div class="flex-1">
                <div class="relative">
                    <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" name="search" id="search" class="block w-full pr-10 p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" placeholder="البحث عن شركة...">
                </div>
            </div>
            <div class="w-full md:w-48">
                <select id="industryFilter" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                    <option value="">المجال</option>
                    <option value="tech">تقنية المعلومات</option>
                    <option value="healthcare">الرعاية الصحية</option>
                    <option value="education">التعليم</option>
                    <option value="finance">المالية والمصرفية</option>
                    <option value="retail">التجزئة</option>
                </select>
            </div>
            <div class="w-full md:w-48">
                <select id="subscriptionFilter" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                    <option value="">نوع الاشتراك</option>
                    <option value="free">مجاني</option>
                    <option value="basic">أساسي</option>
                    <option value="standard">قياسي</option>
                    <option value="premium">متميز</option>
                </select>
            </div>
            <div class="w-full md:w-48">
                <select id="statusFilter" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                    <option value="">الحالة</option>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                    <option value="pending">قيد المراجعة</option>
                    <option value="suspended">معلق</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Companies Table -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm overflow-hidden">
        <div class="overflow-x-auto">
            <table class="w-full text-right text-sm">
                <thead class="bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-300">
                    <tr>
                        <th class="px-6 py-4">الشركة</th>
                        <th class="px-6 py-4">المجال</th>
                        <th class="px-6 py-4">البريد الإلكتروني</th>
                        <th class="px-6 py-4">نوع الاشتراك</th>
                        <th class="px-6 py-4">الوظائف النشطة</th>
                        <th class="px-6 py-4">الحالة</th>
                        <th class="px-6 py-4">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse($companies ?? [] as $company)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">
                                <div class="flex items-center gap-3">
                                    <div class="w-10 h-10 bg-{{ $company->color ?? 'blue' }}-100 dark:bg-{{ $company->color ?? 'blue' }}-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-building text-{{ $company->color ?? 'blue' }}-600 dark:text-{{ $company->color ?? 'blue' }}-400"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium">{{ $company->name }}</p>
                                        <p class="text-gray-500 dark:text-gray-400 text-xs">{{ $company->location }}</p>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">{{ $company->industry }}</td>
                            <td class="px-6 py-4">{{ $company->email }}</td>
                            <td class="px-6 py-4">
                                @if($company->subscription_type == 'premium')
                                    <span class="px-2.5 py-0.5 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 rounded-full text-xs">متميز</span>
                                @elseif($company->subscription_type == 'standard')
                                    <span class="px-2.5 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 rounded-full text-xs">قياسي</span>
                                @elseif($company->subscription_type == 'basic')
                                    <span class="px-2.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">أساسي</span>
                                @else
                                    <span class="px-2.5 py-0.5 bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400 rounded-full text-xs">مجاني</span>
                                @endif
                            </td>
                            <td class="px-6 py-4">{{ $company->active_jobs }}</td>
                            <td class="px-6 py-4">
                                @if($company->status == 'active')
                                    <span class="px-2.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">نشط</span>
                                @elseif($company->status == 'inactive')
                                    <span class="px-2.5 py-0.5 bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400 rounded-full text-xs">غير نشط</span>
                                @elseif($company->status == 'pending')
                                    <span class="px-2.5 py-0.5 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400 rounded-full text-xs">قيد المراجعة</span>
                                @elseif($company->status == 'suspended')
                                    <span class="px-2.5 py-0.5 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 rounded-full text-xs">معلق</span>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                                    <a href="{{ route('admin.companies.edit', $company->id) }}" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{{ route('admin.companies.show', $company->id) }}" class="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button type="button" class="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 delete-company" data-company-id="{{ $company->id }}">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <!-- Sample company data for display -->
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">
                                <div class="flex items-center gap-3">
                                    <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-building text-blue-600 dark:text-blue-400"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium">شركة التقنية المتطورة</p>
                                        <p class="text-gray-500 dark:text-gray-400 text-xs">الرياض، المملكة العربية السعودية</p>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">تقنية المعلومات</td>
                            <td class="px-6 py-4"><EMAIL></td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 rounded-full text-xs">متميز</span>
                            </td>
                            <td class="px-6 py-4">12</td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">نشط</span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                                    <a href="{{ route('admin.companies.edit', 1) }}" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{{ route('admin.companies.show', 1) }}" class="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button type="button" class="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 delete-company" data-company-id="1">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">
                                <div class="flex items-center gap-3">
                                    <div class="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-building text-green-600 dark:text-green-400"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium">مستشفى الصحة الوطني</p>
                                        <p class="text-gray-500 dark:text-gray-400 text-xs">جدة، المملكة العربية السعودية</p>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">الرعاية الصحية</td>
                            <td class="px-6 py-4"><EMAIL></td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 rounded-full text-xs">قياسي</span>
                            </td>
                            <td class="px-6 py-4">8</td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">نشط</span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                                    <a href="{{ route('admin.companies.edit', 2) }}" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{{ route('admin.companies.show', 2) }}" class="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button type="button" class="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 delete-company" data-company-id="2">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">
                                <div class="flex items-center gap-3">
                                    <div class="w-10 h-10 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-building text-yellow-600 dark:text-yellow-400"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium">البنك التجاري الوطني</p>
                                        <p class="text-gray-500 dark:text-gray-400 text-xs">الدمام، المملكة العربية السعودية</p>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">المالية والمصرفية</td>
                            <td class="px-6 py-4"><EMAIL></td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 rounded-full text-xs">متميز</span>
                            </td>
                            <td class="px-6 py-4">6</td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">نشط</span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                                    <a href="{{ route('admin.companies.edit', 3) }}" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{{ route('admin.companies.show', 3) }}" class="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button type="button" class="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 delete-company" data-company-id="3">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">
                                <div class="flex items-center gap-3">
                                    <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-building text-purple-600 dark:text-purple-400"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium">جامعة المستقبل</p>
                                        <p class="text-gray-500 dark:text-gray-400 text-xs">الرياض، المملكة العربية السعودية</p>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">التعليم</td>
                            <td class="px-6 py-4"><EMAIL></td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">أساسي</span>
                            </td>
                            <td class="px-6 py-4">4</td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">نشط</span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                                    <a href="{{ route('admin.companies.edit', 4) }}" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{{ route('admin.companies.show', 4) }}" class="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button type="button" class="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 delete-company" data-company-id="4">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">
                                <div class="flex items-center gap-3">
                                    <div class="w-10 h-10 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-building text-red-600 dark:text-red-400"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium">متاجر العاصمة</p>
                                        <p class="text-gray-500 dark:text-gray-400 text-xs">جدة، المملكة العربية السعودية</p>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">التجزئة</td>
                            <td class="px-6 py-4"><EMAIL></td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5 bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400 rounded-full text-xs">مجاني</span>
                            </td>
                            <td class="px-6 py-4">2</td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400 rounded-full text-xs">قيد المراجعة</span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                                    <a href="{{ route('admin.companies.edit', 5) }}" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{{ route('admin.companies.show', 5) }}" class="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button type="button" class="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 delete-company" data-company-id="5">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <div class="px-6 py-4 flex items-center justify-between border-t border-gray-200 dark:border-gray-700">
            <div class="text-sm text-gray-700 dark:text-gray-300">
                عرض <span class="font-medium">1</span> إلى <span class="font-medium">5</span> من <span class="font-medium">{{ $totalCompanies ?? '42' }}</span> النتائج
            </div>
            <div class="flex items-center space-x-2 rtl:space-x-reverse">
                <a href="#" class="px-3 py-1 text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">السابق</a>
                <a href="#" class="px-3 py-1 text-white bg-primary border border-primary rounded-md">1</a>
                <a href="#" class="px-3 py-1 text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">2</a>
                <a href="#" class="px-3 py-1 text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">3</a>
                <a href="#" class="px-3 py-1 text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">التالي</a>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
        </div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white dark:bg-dark-card rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white dark:bg-dark-card px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/30 sm:mx-0 sm:h-10 sm:w-10">
                        <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400"></i>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:mr-4 sm:text-right">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                            حذف الشركة
                        </h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                هل أنت متأكد من رغبتك في حذف هذه الشركة؟ جميع البيانات المرتبطة بها بما في ذلك الوظائف والطلبات ستفقد ولا يمكن التراجع عن هذا الإجراء.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="confirmDelete" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                    حذف
                </button>
                <button type="button" id="cancelDelete" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    إلغاء
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Delete company confirmation
    const deleteButtons = document.querySelectorAll('.delete-company');
    const deleteModal = document.getElementById('deleteModal');
    const confirmDelete = document.getElementById('confirmDelete');
    const cancelDelete = document.getElementById('cancelDelete');
    let companyIdToDelete = null;

    // Show delete modal
    deleteButtons.forEach(button => {
        button.addEventListener('click', () => {
            companyIdToDelete = button.getAttribute('data-company-id');
            deleteModal.classList.remove('hidden');
        });
    });

    // Cancel delete
    cancelDelete.addEventListener('click', () => {
        deleteModal.classList.add('hidden');
        companyIdToDelete = null;
    });

    // Confirm delete
    confirmDelete.addEventListener('click', () => {
        if (companyIdToDelete) {
            // Here you would normally send an AJAX request to delete the company
            // For demo purposes, we'll just hide the modal
            console.log(`Deleting company with ID: ${companyIdToDelete}`);
            deleteModal.classList.add('hidden');
            companyIdToDelete = null;
        }
    });

    // Close modal when clicking outside
    deleteModal.addEventListener('click', (e) => {
        if (e.target === deleteModal) {
            deleteModal.classList.add('hidden');
            companyIdToDelete = null;
        }
    });

    // Filter companies
    const searchInput = document.getElementById('search');
    const industryFilter = document.getElementById('industryFilter');
    const subscriptionFilter = document.getElementById('subscriptionFilter');
    const statusFilter = document.getElementById('statusFilter');

    // Add event listeners for filters (in a real app, these would trigger AJAX requests or form submissions)
    [searchInput, industryFilter, subscriptionFilter, statusFilter].forEach(element => {
        element.addEventListener('change', () => {
            console.log('Filtering companies with:', {
                search: searchInput.value,
                industry: industryFilter.value,
                subscription: subscriptionFilter.value,
                status: statusFilter.value
            });
        });
    });

    // Search as you type
    searchInput.addEventListener('input', () => {
        console.log('Searching for:', searchInput.value);
    });
</script>
@endsection