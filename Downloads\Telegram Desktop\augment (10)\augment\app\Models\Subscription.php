<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Subscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'employer_id',
        'package_type',
        'price',
        'job_limit',
        'jobs_posted_this_month',
        'starts_at',
        'ends_at',
        'is_active',
        'features',
        'last_reset_at',
    ];

    protected $casts = [
        'starts_at' => 'date',
        'ends_at' => 'date',
        'is_active' => 'boolean',
        'features' => 'array',
        'last_reset_at' => 'datetime',
        'price' => 'decimal:2',
    ];

    /**
     * Get the employer that owns the subscription.
     */
    public function employer(): BelongsTo
    {
        return $this->belongsTo(Employer::class);
    }

    /**
     * Check if subscription is expired.
     */
    public function isExpired(): bool
    {
        return $this->ends_at < now()->toDateString();
    }

    /**
     * Check if subscription is active and not expired.
     */
    public function isActiveAndValid(): bool
    {
        return $this->is_active && !$this->isExpired();
    }

    /**
     * Get remaining jobs for this month.
     */
    public function getRemainingJobs(): int
    {
        // إعادة تعيين العداد إذا بدأ شهر جديد
        $this->resetMonthlyCounterIfNeeded();

        if ($this->package_type === 'enterprise') {
            return 999999; // عدد كبير للعرض (بدلاً من PHP_INT_MAX)
        }

        // حساب الوظائف الفعلية المنشورة هذا الشهر
        $actualJobsPosted = \App\Models\Job::where('employer_id', $this->employer_id)
            ->where('is_active', true)
            ->whereYear('posted_at', now()->year)
            ->whereMonth('posted_at', now()->month)
            ->count();

        // تحديث العداد إذا كان مختلفاً
        if ($actualJobsPosted !== $this->jobs_posted_this_month) {
            $this->update(['jobs_posted_this_month' => $actualJobsPosted]);
        }

        return max(0, $this->job_limit - $actualJobsPosted);
    }

    /**
     * Check if can post more jobs.
     */
    public function canPostJob(): bool
    {
        if (!$this->isActiveAndValid()) {
            return false;
        }

        return $this->getRemainingJobs() > 0;
    }

    /**
     * Increment jobs posted counter.
     */
    public function incrementJobsPosted(): void
    {
        $this->resetMonthlyCounterIfNeeded();

        // حساب العدد الفعلي من قاعدة البيانات
        $actualJobsPosted = \App\Models\Job::where('employer_id', $this->employer_id)
            ->where('is_active', true)
            ->whereYear('posted_at', now()->year)
            ->whereMonth('posted_at', now()->month)
            ->count();

        $this->update(['jobs_posted_this_month' => $actualJobsPosted]);
    }

    /**
     * Reset monthly counter if needed.
     */
    public function resetMonthlyCounterIfNeeded(): void
    {
        $lastReset = $this->last_reset_at ? Carbon::parse($this->last_reset_at) : null;
        $now = now();

        // إذا لم يتم إعادة التعيين من قبل أو إذا مر شهر كامل
        if (!$lastReset || $lastReset->month !== $now->month || $lastReset->year !== $now->year) {
            $this->update([
                'jobs_posted_this_month' => 0,
                'last_reset_at' => $now,
            ]);
        }
    }

    /**
     * Get package details.
     */
    public function getPackageDetails(): array
    {
        $packages = [
            'basic' => [
                'name' => 'الباقة المجانية',
                'price' => 0,
                'job_limit' => 3,
                'features' => [
                    'نشر 3 وظائف شهرياً',
                    'عرض الطلبات الأساسي',
                    'دعم فني محدود'
                ],
                'color' => 'gray'
            ],
            'premium' => [
                'name' => 'الباقة المميزة',
                'price' => 299,
                'job_limit' => 15,
                'features' => [
                    'نشر 15 وظيفة شهرياً',
                    'إبراز الوظائف في النتائج',
                    'تحليلات مفصلة',
                    'دعم فني أولوية',
                    'تصدير البيانات'
                ],
                'color' => 'purple'
            ],
            'enterprise' => [
                'name' => 'باقة الشركات',
                'price' => 599,
                'job_limit' => -1, // غير محدود
                'features' => [
                    'وظائف غير محدودة',
                    'صفحة شركة مخصصة',
                    'مدير حساب مخصص',
                    'تكامل API',
                    'تقارير مخصصة',
                    'دعم فني 24/7'
                ],
                'color' => 'yellow'
            ]
        ];

        return $packages[$this->package_type] ?? $packages['basic'];
    }

    /**
     * Create default subscription for employer.
     */
    public static function createDefault(Employer $employer): self
    {
        return self::create([
            'employer_id' => $employer->id,
            'package_type' => 'basic',
            'price' => 0,
            'job_limit' => 3,
            'jobs_posted_this_month' => 0,
            'starts_at' => now()->toDateString(),
            'ends_at' => now()->addYear()->toDateString(), // الباقة المجانية لمدة سنة
            'is_active' => true,
            'features' => [
                'نشر 3 وظائف شهرياً',
                'عرض الطلبات الأساسي',
                'دعم فني محدود'
            ],
            'last_reset_at' => now(),
        ]);
    }

    /**
     * Scope for active subscriptions.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where('ends_at', '>=', now()->toDateString());
    }

    /**
     * Scope for specific package type.
     */
    public function scopePackageType($query, $type)
    {
        return $query->where('package_type', $type);
    }
}
