@extends('layouts.admin')

@section('title', 'تعديل الوظيفة - Hire Me')
@section('header_title', 'تعديل الوظيفة')

@section('content')
<div class="p-6">
    <div class="mb-6">
        <h2 class="text-2xl font-bold">تعديل الوظيفة</h2>
        <p class="text-gray-600 dark:text-gray-400 mt-1">قم بتعديل تفاصيل الوظيفة</p>
    </div>

    <form action="{{ route('admin.jobs.update', $job->id) }}" method="POST" class="space-y-6">
        @csrf
        @method('PUT')

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Job Details -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Basic Information -->
                <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                    <h3 class="text-lg font-bold mb-4">المعلومات الأساسية</h3>

                    <div class="space-y-4">
                        <div>
                            <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">عنوان الوظيفة <span class="text-red-600">*</span></label>
                            <input type="text" name="title" id="title" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" placeholder="مثال: مطور واجهات أمامية" value="{{ $job->title }}" required>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="employer_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الشركة <span class="text-red-600">*</span></label>
                                <select name="employer_id" id="employer_id" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" required>
                                    <option value="">اختر الشركة</option>
                                    @foreach($employers as $employer)
                                        <option value="{{ $employer->id }}" {{ $job->employer_id == $employer->id ? 'selected' : '' }}>{{ $employer->company_name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div>
                                <label for="category_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">التصنيف <span class="text-red-600">*</span></label>
                                <select name="category_id" id="category_id" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" required>
                                    <option value="">اختر التصنيف</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}" {{ $job->category_id == $category->id ? 'selected' : '' }}>{{ $category->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="job_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">نوع الوظيفة <span class="text-red-600">*</span></label>
                                <select name="job_type" id="job_type" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" required>
                                    <option value="">اختر نوع الوظيفة</option>
                                    @foreach($jobTypes as $value => $label)
                                        <option value="{{ $value }}" {{ $job->job_type == $value ? 'selected' : '' }}>{{ $label }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div>
                                <label for="location" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الموقع <span class="text-red-600">*</span></label>
                                <input type="text" name="location" id="location" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" placeholder="مثال: طرابلس، ليبيا" value="{{ $job->location }}" required>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="salary_range" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">نطاق الراتب</label>
                                <input type="text" name="salary_range" id="salary_range" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" placeholder="مثال: 1500 - 2250" value="{{ $job->salary_range }}">
                            </div>
                            <div>
                                <label for="salary_currency" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">العملة <span class="text-red-600">*</span></label>
                                <select name="salary_currency" id="salary_currency" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" required>
                                    <option value="">اختر العملة</option>
                                    <option value="LYD" {{ $job->salary_currency == 'LYD' ? 'selected' : '' }}>دينار ليبي (LYD)</option>
                                    <option value="USD" {{ $job->salary_currency == 'USD' ? 'selected' : '' }}>دولار أمريكي (USD)</option>
                                    <option value="EUR" {{ $job->salary_currency == 'EUR' ? 'selected' : '' }}>يورو (EUR)</option>
                                </select>
                            </div>
                        </div>

                        <div>
                            <label for="expires_at" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">تاريخ انتهاء التقديم <span class="text-red-600">*</span></label>
                            <input type="date" name="expires_at" id="expires_at" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" value="{{ $job->expires_at ? $job->expires_at->format('Y-m-d') : '' }}" required>
                        </div>
                    </div>
                </div>

                <!-- Job Description -->
                <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                    <h3 class="text-lg font-bold mb-4">وصف الوظيفة</h3>

                    <div class="space-y-4">
                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">وصف الوظيفة <span class="text-red-600">*</span></label>
                            <textarea name="description" id="description" rows="6" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" placeholder="وصف مفصل للوظيفة ومسؤولياتها" required>{{ $job->description }}</textarea>
                        </div>

                        <div>
                            <label for="requirements" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">المتطلبات <span class="text-red-600">*</span></label>
                            <textarea name="requirements" id="requirements" rows="6" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" placeholder="المهارات والمؤهلات المطلوبة" required>{{ $job->requirements }}</textarea>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">نصيحة: استخدم علامة النقطة "-" لبداية كل سطر جديد لعرض قائمة منسقة</p>
                        </div>

                        <div>
                            <label for="benefits" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">المميزات والفوائد</label>
                            <textarea name="benefits" id="benefits" rows="4" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" placeholder="المميزات التي تقدمها الشركة للموظف">{{ $job->benefits }}</textarea>
                        </div>
                    </div>
                </div>

                <!-- Skills & Qualifications -->
                <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                    <h3 class="text-lg font-bold mb-4">المهارات والمؤهلات</h3>

                    <div class="space-y-4">
                        <div>
                            <label for="skills" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">المهارات المطلوبة</label>
                            <input type="text" name="skills" id="skills" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" placeholder="مثال: React، Vue.js، JavaScript" value="{{ $job->skills }}">
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">افصل بين المهارات بفاصلة</p>
                        </div>

                        <div>
                            <label for="education" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">المؤهل العلمي</label>
                            <select name="education" id="education" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                                <option value="">اختر المؤهل العلمي</option>
                                <option value="high_school" {{ $job->education == 'high_school' ? 'selected' : '' }}>ثانوية عامة</option>
                                <option value="diploma" {{ $job->education == 'diploma' ? 'selected' : '' }}>دبلوم</option>
                                <option value="bachelor" {{ $job->education == 'bachelor' ? 'selected' : '' }}>بكالوريوس</option>
                                <option value="master" {{ $job->education == 'master' ? 'selected' : '' }}>ماجستير</option>
                                <option value="phd" {{ $job->education == 'phd' ? 'selected' : '' }}>دكتوراه</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Job Status & Visibility -->
                <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                    <h3 class="text-lg font-bold mb-4">الحالة والظهور</h3>

                    <div class="space-y-4">
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">حالة الوظيفة</label>
                            <select name="status" id="status" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                                <option value="draft" {{ ($job->status ?? '') == 'draft' ? 'selected' : '' }}>مسودة</option>
                                <option value="active" {{ ($job->status ?? 'active') == 'active' ? 'selected' : '' }}>نشط</option>
                                <option value="inactive" {{ ($job->status ?? '') == 'inactive' ? 'selected' : '' }}>غير نشط</option>
                            </select>
                        </div>

                        <div class="flex items-center">
                            <input id="featured" name="featured" type="checkbox" class="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary" {{ ($job->featured ?? false) ? 'checked' : '' }}>
                            <label for="featured" class="mr-2 text-sm font-medium text-gray-700 dark:text-gray-300">وظيفة مميزة</label>
                        </div>

                        <div class="flex items-center">
                            <input id="urgent" name="urgent" type="checkbox" class="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary" {{ ($job->urgent ?? false) ? 'checked' : '' }}>
                            <label for="urgent" class="mr-2 text-sm font-medium text-gray-700 dark:text-gray-300">وظيفة عاجلة</label>
                        </div>
                    </div>
                </div>

                <!-- Job Icon & Color -->
                <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                    <h3 class="text-lg font-bold mb-4">الأيقونة واللون</h3>

                    <div class="space-y-4">
                        <div>
                            <label for="icon" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">أيقونة الوظيفة</label>
                            <select name="icon" id="icon" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                                <option value="code" {{ ($job->icon ?? 'code') == 'code' ? 'selected' : '' }}>برمجة</option>
                                <option value="paint-brush" {{ ($job->icon ?? '') == 'paint-brush' ? 'selected' : '' }}>تصميم</option>
                                <option value="chart-bar" {{ ($job->icon ?? '') == 'chart-bar' ? 'selected' : '' }}>تسويق</option>
                                <option value="mobile-alt" {{ ($job->icon ?? '') == 'mobile-alt' ? 'selected' : '' }}>تطبيقات جوال</option>
                                <option value="database" {{ ($job->icon ?? '') == 'database' ? 'selected' : '' }}>قواعد بيانات</option>
                                <option value="cogs" {{ ($job->icon ?? '') == 'cogs' ? 'selected' : '' }}>هندسة</option>
                                <option value="user-tie" {{ ($job->icon ?? '') == 'user-tie' ? 'selected' : '' }}>إدارة</option>
                                <option value="bullhorn" {{ ($job->icon ?? '') == 'bullhorn' ? 'selected' : '' }}>تسويق</option>
                                <option value="headset" {{ ($job->icon ?? '') == 'headset' ? 'selected' : '' }}>دعم فني</option>
                                <option value="briefcase" {{ ($job->icon ?? '') == 'briefcase' ? 'selected' : '' }}>أخرى</option>
                            </select>
                        </div>

                        <div>
                            <label for="color" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">لون الوظيفة</label>
                            <select name="color" id="color" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                                <option value="primary" {{ ($job->color ?? 'primary') == 'primary' ? 'selected' : '' }}>أساسي (بنفسجي)</option>
                                <option value="blue" {{ ($job->color ?? '') == 'blue' ? 'selected' : '' }}>أزرق</option>
                                <option value="green" {{ ($job->color ?? '') == 'green' ? 'selected' : '' }}>أخضر</option>
                                <option value="red" {{ ($job->color ?? '') == 'red' ? 'selected' : '' }}>أحمر</option>
                                <option value="yellow" {{ ($job->color ?? '') == 'yellow' ? 'selected' : '' }}>أصفر</option>
                                <option value="purple" {{ ($job->color ?? '') == 'purple' ? 'selected' : '' }}>بنفسجي</option>
                                <option value="pink" {{ ($job->color ?? '') == 'pink' ? 'selected' : '' }}>وردي</option>
                                <option value="indigo" {{ ($job->color ?? '') == 'indigo' ? 'selected' : '' }}>نيلي</option>
                            </select>
                        </div>

                        <!-- Color Preview -->
                        <div class="mt-4">
                            <div class="flex justify-center">
                                <div id="colorPreview" class="w-16 h-16 rounded-lg flex items-center justify-center text-white gradient-bg">
                                    <i id="iconPreview" class="fas fa-code text-2xl"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Application Settings -->
                <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                    <h3 class="text-lg font-bold mb-4">إعدادات التقديم</h3>

                    <div class="space-y-4">
                        <div>
                            <label for="application_method" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">طريقة التقديم</label>
                            <select name="application_method" id="application_method" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                                <option value="internal" {{ ($job->application_method ?? 'internal') == 'internal' ? 'selected' : '' }}>من خلال المنصة</option>
                                <option value="external" {{ ($job->application_method ?? '') == 'external' ? 'selected' : '' }}>رابط خارجي</option>
                                <option value="email" {{ ($job->application_method ?? '') == 'email' ? 'selected' : '' }}>البريد الإلكتروني</option>
                            </select>
                        </div>

                        <div id="external_url_container" class="{{ ($job->application_method ?? 'internal') == 'external' ? '' : 'hidden' }}">
                            <label for="external_url" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">رابط التقديم الخارجي</label>
                            <input type="url" name="external_url" id="external_url" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" placeholder="https://example.com/careers" value="{{ $job->external_url ?? '' }}">
                        </div>

                        <div id="email_container" class="{{ ($job->application_method ?? 'internal') == 'email' ? '' : 'hidden' }}">
                            <label for="application_email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">البريد الإلكتروني للتقديم</label>
                            <input type="email" name="application_email" id="application_email" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" placeholder="<EMAIL>" value="{{ $job->application_email ?? '' }}">
                        </div>
                    </div>
                </div>

                <!-- Job Statistics -->
                <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                    <h3 class="text-lg font-bold mb-4">إحصائيات الوظيفة</h3>

                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600 dark:text-gray-400">عدد المشاهدات:</span>
                            <span class="font-medium">{{ $job->views }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600 dark:text-gray-400">عدد الطلبات:</span>
                            <span class="font-medium">{{ $job->applications->count() }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600 dark:text-gray-400">تاريخ النشر:</span>
                            <span class="font-medium">{{ $job->posted_at ? $job->posted_at->format('d/m/Y') : 'غير محدد' }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600 dark:text-gray-400">آخر تحديث:</span>
                            <span class="font-medium">{{ $job->updated_at ? $job->updated_at->format('d/m/Y') : 'غير محدد' }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Submit Buttons -->
        <div class="flex justify-end space-x-4 rtl:space-x-reverse">
            <a href="{{ route('admin.jobs.index') }}" class="px-6 py-2.5 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                إلغاء
            </a>
            <button type="submit" name="save_draft" value="1" class="px-6 py-2.5 border border-primary text-primary bg-white dark:bg-gray-800 rounded-lg hover:bg-primary/10 transition-colors">
                حفظ كمسودة
            </button>
            <button type="submit" class="px-6 py-2.5 gradient-bg text-white rounded-lg hover:opacity-90 transition-colors">
                تحديث الوظيفة
            </button>
        </div>
    </form>
</div>
@endsection

@section('scripts')
<script>
    // Toggle application method fields
    const applicationMethod = document.getElementById('application_method');
    const externalUrlContainer = document.getElementById('external_url_container');
    const emailContainer = document.getElementById('email_container');

    applicationMethod.addEventListener('change', function() {
        if (this.value === 'external') {
            externalUrlContainer.classList.remove('hidden');
            emailContainer.classList.add('hidden');
        } else if (this.value === 'email') {
            externalUrlContainer.classList.add('hidden');
            emailContainer.classList.remove('hidden');
        } else {
            externalUrlContainer.classList.add('hidden');
            emailContainer.classList.add('hidden');
        }
    });

    // Color and Icon Preview
    const iconSelect = document.getElementById('icon');
    const colorSelect = document.getElementById('color');
    const iconPreview = document.getElementById('iconPreview');
    const colorPreview = document.getElementById('colorPreview');

    // Update icon immediately on page load
    iconPreview.className = `fas fa-${iconSelect.value} text-2xl`;

    // Update color immediately on page load
    colorPreview.className = 'w-16 h-16 rounded-lg flex items-center justify-center text-white';
    if (colorSelect.value === 'primary') {
        colorPreview.classList.add('gradient-bg');
    } else {
        colorPreview.classList.add(`bg-${colorSelect.value}-500`);
    }

    // Update icon when changed
    iconSelect.addEventListener('change', function() {
        iconPreview.className = `fas fa-${this.value} text-2xl`;
    });

    // Update color when changed
    colorSelect.addEventListener('change', function() {
        // Remove all color classes
        colorPreview.className = 'w-16 h-16 rounded-lg flex items-center justify-center text-white';

        // Add the selected color class
        if (this.value === 'primary') {
            colorPreview.classList.add('gradient-bg');
        } else {
            colorPreview.classList.add(`bg-${this.value}-500`);
        }
    });
</script>
@endsection