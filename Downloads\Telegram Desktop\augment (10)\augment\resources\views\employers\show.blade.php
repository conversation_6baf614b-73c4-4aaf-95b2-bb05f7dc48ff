@extends('layouts.app')

@section('title', $employer->company_name)

@section('content')
<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 bg-white border-b border-gray-200">
                <div class="flex flex-col md:flex-row">
                    <div class="md:w-1/3 p-4">
                        <div class="bg-gray-50 p-6 rounded-lg shadow-sm">
                            <div class="text-center mb-6">
                                @if($employer->company_logo)
                                    <img src="{{ Storage::url($employer->company_logo) }}" alt="{{ $employer->company_name }}" class="w-32 h-32 object-cover rounded-full mx-auto">
                                @else
                                    <div class="w-32 h-32 bg-indigo-100 rounded-full flex items-center justify-center text-indigo-500 mx-auto">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                        </svg>
                                    </div>
                                @endif
                                <h1 class="text-2xl font-bold mt-4">{{ $employer->company_name }}</h1>
                                @if($employer->industry)
                                    <p class="text-gray-600">{{ $employer->industry }}</p>
                                @endif
                            </div>
                            
                            <div class="border-t border-gray-200 pt-4">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-gray-600">الموقع:</span>
                                    <span>{{ $employer->location ?? 'غير محدد' }}</span>
                                </div>
                                
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-gray-600">حجم الشركة:</span>
                                    <span>{{ $employer->company_size ? $employer->company_size . ' موظف' : 'غير محدد' }}</span>
                                </div>
                                
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-gray-600">سنة التأسيس:</span>
                                    <span>{{ $employer->founded_year ?? 'غير محدد' }}</span>
                                </div>
                                
                                @if($employer->website)
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">الموقع الإلكتروني:</span>
                                        <a href="{{ $employer->website }}" target="_blank" class="text-indigo-600 hover:text-indigo-800">
                                            زيارة الموقع
                                        </a>
                                    </div>
                                @endif
                            </div>
                            
                            <div class="mt-6">
                                <div class="flex items-center mb-2">
                                    <div class="flex-1">
                                        <div class="flex items-center">
                                            <span class="text-yellow-400">
                                                @for($i = 1; $i <= 5; $i++)
                                                    @if($i <= $employer->companyRatings->avg('rating'))
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                        </svg>
                                                    @else
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                                                        </svg>
                                                    @endif
                                                @endfor
                                            </span>
                                            <span class="ml-2 text-gray-600">
                                                {{ number_format($employer->companyRatings->avg('rating'), 1) }} ({{ $employer->companyRatings->count() }} تقييم)
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="md:w-2/3 p-4">
                        <div class="mb-8">
                            <h2 class="text-xl font-semibold mb-4">عن الشركة</h2>
                            <div class="bg-white p-6 rounded-lg shadow-sm">
                                @if($employer->company_description)
                                    <p class="text-gray-700 whitespace-pre-line">{{ $employer->company_description }}</p>
                                @else
                                    <p class="text-gray-500">لا يوجد وصف للشركة.</p>
                                @endif
                            </div>
                        </div>
                        
                        <div>
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-xl font-semibold">الوظائف المتاحة</h2>
                                <a href="{{ route('jobs.search') }}?employer_id={{ $employer->id }}" class="text-indigo-600 hover:text-indigo-800">
                                    عرض كل الوظائف
                                </a>
                            </div>
                            
                            @php
                                $activeJobs = $employer->jobs()->where('expires_at', '>', now())->latest()->take(3)->get();
                            @endphp
                            
                            @if($activeJobs->count() > 0)
                                <div class="space-y-4">
                                    @foreach($activeJobs as $job)
                                        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-300">
                                            <div class="flex justify-between">
                                                <div>
                                                    <h3 class="text-lg font-medium text-gray-900">
                                                        <a href="{{ route('jobs.show', $job) }}" class="hover:text-indigo-600">
                                                            {{ $job->title }}
                                                        </a>
                                                    </h3>
                                                    <div class="mt-2 flex items-center text-sm text-gray-500">
                                                        @if($job->location)
                                                            <div class="flex items-center mr-4">
                                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                                                </svg>
                                                                {{ $job->location }}
                                                            </div>
                                                        @endif
                                                        
                                                        <div class="flex items-center mr-4">
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                                            </svg>
                                                            {{ $job->job_type }}
                                                        </div>
                                                        
                                                        @if($job->salary_range)
                                                            <div class="flex items-center">
                                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                                </svg>
                                                                {{ $job->salary_range }}
                                                            </div>
                                                        @endif
                                                    </div>
                                                </div>
                                                
                                                <div class="text-right">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                        نُشرت {{ $job->posted_at->diffForHumans() }}
                                                    </span>
                                                    <p class="mt-2 text-sm text-gray-500">
                                                        تنتهي {{ $job->expires_at->format('Y/m/d') }}
                                                    </p>
                                                </div>
                                            </div>
                                            
                                            <div class="mt-4">
                                                <a href="{{ route('jobs.show', $job) }}" class="text-indigo-600 hover:text-indigo-800 font-medium">
                                                    عرض التفاصيل والتقديم
                                                </a>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200 text-center">
                                    <p class="text-gray-500">لا توجد وظائف متاحة حالياً من هذه الشركة.</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
