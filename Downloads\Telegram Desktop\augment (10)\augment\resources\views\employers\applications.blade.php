@extends('layouts.app')

@section('title', 'طلبات التوظيف')

@section('content')
<div class="container mx-auto px-4 py-12">
    <div class="max-w-7xl mx-auto">
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold">طلبات التوظيف</h1>
            <div class="flex items-center gap-3">
                <a href="{{ route('employer.jobs.index') }}" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors flex items-center gap-2">
                    <i class="fas fa-briefcase"></i>
                    <span>الوظائف</span>
                </a>
                <button onclick="showExportModal()" class="px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl flex items-center gap-2">
                    <i class="fas fa-download"></i>
                    <span>تصدير</span>
                </button>
                <button id="filterButton" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors flex items-center gap-2">
                    <i class="fas fa-filter"></i>
                    <span>تصفية</span>
                </button>
            </div>
        </div>

        <p class="text-gray-600 dark:text-gray-400 mb-6">إدارة ومراجعة طلبات التوظيف المقدمة</p>

        <!-- Filter Panel (Hidden by default) -->
        <div id="filterPanel" class="bg-white dark:bg-dark-card rounded-xl shadow-md p-6 mb-6 hidden">
            <form action="{{ route('employer.applications') }}" method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">بحث</label>
                    <input type="text" name="search" id="search" value="{{ request('search') }}" placeholder="اسم المتقدم أو المهارات..." class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white">
                </div>

                <div>
                    <label for="job" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الوظيفة</label>
                    <select name="job" id="job" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white">
                        <option value="">كل الوظائف</option>
                        @foreach($allJobs ?? [] as $job)
                            <option value="{{ $job->id }}" {{ request('job') == $job->id ? 'selected' : '' }}>{{ $job->title }}</option>
                        @endforeach
                    </select>
                </div>

                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الحالة</label>
                    <select name="status" id="status" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white">
                        <option value="">كل الحالات</option>
                        <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>قيد المراجعة</option>
                        <option value="reviewed" {{ request('status') == 'reviewed' ? 'selected' : '' }}>تمت المراجعة</option>
                        <option value="shortlisted" {{ request('status') == 'shortlisted' ? 'selected' : '' }}>القائمة المختصرة</option>
                        <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>مرفوض</option>
                        <option value="hired" {{ request('status') == 'hired' ? 'selected' : '' }}>تم التعيين</option>
                    </select>
                </div>

                <div>
                    <label for="date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">تاريخ التقديم</label>
                    <select name="date" id="date" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white">
                        <option value="">كل الأوقات</option>
                        <option value="today">اليوم</option>
                        <option value="week">هذا الأسبوع</option>
                        <option value="month">هذا الشهر</option>
                    </select>
                </div>

                <div class="md:col-span-4 flex justify-end gap-3">
                    <button type="submit" class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors">
                        <i class="fas fa-search mr-2"></i>
                        تطبيق الفلتر
                    </button>
                    <a href="{{ route('employer.applications') }}" class="px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                        <i class="fas fa-times mr-2"></i>
                        إعادة تعيين
                    </a>
                </div>
            </form>
        </div>

        <!-- Applications List -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-md overflow-hidden">
            <!-- Bulk Actions Bar -->
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-4">
                        <span class="text-sm text-gray-600 dark:text-gray-400">
                            إجمالي الطلبات: {{ $jobs->sum(function($job) { return $job->applications->count(); }) }}
                        </span>
                        <span id="selectedCount" class="text-sm text-blue-600 dark:text-blue-400 hidden">
                            <i class="fas fa-check-square mr-1"></i>
                            <span id="selectedNumber">0</span> محدد
                        </span>
                    </div>

                    <div class="flex items-center gap-2">
                        <button onclick="selectAll()"
                                class="text-xs px-3 py-1 bg-blue-100 text-blue-600 rounded-lg hover:bg-blue-200 transition-colors">
                            تحديد الكل
                        </button>
                        <button onclick="bulkAction('accept')"
                                class="text-xs px-3 py-1 bg-green-100 text-green-600 rounded-lg hover:bg-green-200 transition-colors hidden bulk-action-btn"
                                disabled>
                            <i class="fas fa-check mr-1"></i>
                            قبول المحدد
                        </button>
                        <button onclick="bulkAction('reject')"
                                class="text-xs px-3 py-1 bg-red-100 text-red-600 rounded-lg hover:bg-red-200 transition-colors hidden bulk-action-btn"
                                disabled>
                            <i class="fas fa-times mr-1"></i>
                            رفض المحدد
                        </button>
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="w-full text-right text-sm">
                    <thead class="bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-300">
                        <tr>
                            <th class="px-6 py-4">
                                <input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()"
                                       class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </th>
                            <th class="px-6 py-4">المرشح</th>
                            <th class="px-6 py-4">الوظيفة</th>
                            <th class="px-6 py-4">تاريخ التقديم</th>
                            <th class="px-6 py-4">التقييم</th>
                            <th class="px-6 py-4">الحالة</th>
                            <th class="px-6 py-4">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                        @php $hasApplications = false; @endphp

                        @foreach($jobs as $job)
                            @foreach($job->applications as $application)
                                @php $hasApplications = true; @endphp
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors" data-application-id="{{ $application->id }}">
                                    <td class="px-6 py-4">
                                        <input type="checkbox" class="application-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                               value="{{ $application->id }}" onchange="updateBulkActionButtons()">
                                    </td>
                                    <td class="px-6 py-4 font-medium">
                                        <div class="flex items-center gap-3">
                                            <div class="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                                                @if($application->jobSeeker->user->avatar)
                                                    <img src="{{ asset('storage/' . $application->jobSeeker->user->avatar) }}" alt="{{ $application->jobSeeker->user->name }}" class="w-8 h-8 rounded-full object-cover">
                                                @else
                                                    <i class="fas fa-user text-primary"></i>
                                                @endif
                                            </div>
                                            <div>
                                                <div class="font-medium">{{ $application->jobSeeker->user->name }}</div>
                                                <div class="text-gray-500 dark:text-gray-400 text-xs">{{ $application->jobSeeker->job_title ?? 'باحث عن عمل' }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="font-medium">{{ $job->title }}</div>
                                        <div class="text-gray-500 dark:text-gray-400 text-xs">{{ $job->location }}</div>
                                    </td>
                                    <td class="px-6 py-4 text-gray-500 dark:text-gray-400">
                                        {{ $application->created_at->format('Y/m/d') }}
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            @for($i = 1; $i <= 5; $i++)
                                                <i class="fas fa-star {{ $i <= ($application->rating ?? 4) ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600' }} text-sm"></i>
                                            @endfor
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        @if($application->status == 'pending')
                                            <span class="px-2.5 py-1 text-xs font-medium text-yellow-700 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/30 rounded-full">قيد المراجعة</span>
                                        @elseif($application->status == 'reviewed')
                                            <span class="px-2.5 py-1 text-xs font-medium text-blue-700 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30 rounded-full">تمت المراجعة</span>
                                        @elseif($application->status == 'shortlisted')
                                            <span class="px-2.5 py-1 text-xs font-medium text-indigo-700 dark:text-indigo-400 bg-indigo-100 dark:bg-indigo-900/30 rounded-full">القائمة المختصرة</span>
                                        @elseif($application->status == 'rejected')
                                            <span class="px-2.5 py-1 text-xs font-medium text-red-700 dark:text-red-400 bg-red-100 dark:bg-red-900/30 rounded-full">مرفوض</span>
                                        @elseif($application->status == 'hired')
                                            <span class="px-2.5 py-1 text-xs font-medium text-green-700 dark:text-green-400 bg-green-100 dark:bg-green-900/30 rounded-full">تم التعيين</span>
                                        @endif
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="flex items-center gap-2">
                                            <!-- عرض التفاصيل -->
                                            <a href="{{ route('employer.applications.show', $application) }}"
                                               class="inline-flex items-center justify-center w-8 h-8 rounded-lg bg-blue-100 text-blue-600 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:hover:bg-blue-800 transition-colors"
                                               title="عرض التفاصيل">
                                                <i class="fas fa-eye text-sm"></i>
                                            </a>

                                            @if($application->status == 'pending')
                                                <!-- قبول الطلب -->
                                                <button type="button"
                                                        onclick="updateStatus({{ $application->id }}, 'accepted')"
                                                        class="inline-flex items-center justify-center w-8 h-8 rounded-lg bg-green-100 text-green-600 hover:bg-green-200 dark:bg-green-900 dark:text-green-300 dark:hover:bg-green-800 transition-colors"
                                                        title="قبول الطلب">
                                                    <i class="fas fa-check text-sm"></i>
                                                </button>

                                                <!-- رفض الطلب -->
                                                <button type="button"
                                                        onclick="updateStatus({{ $application->id }}, 'rejected')"
                                                        class="inline-flex items-center justify-center w-8 h-8 rounded-lg bg-red-100 text-red-600 hover:bg-red-200 dark:bg-red-900 dark:text-red-300 dark:hover:bg-red-800 transition-colors"
                                                        title="رفض الطلب">
                                                    <i class="fas fa-times text-sm"></i>
                                                </button>
                                            @endif

                                            <!-- المزيد من الخيارات -->
                                            <div class="relative status-dropdown" data-application-id="{{ $application->id }}">
                                                <button type="button"
                                                        class="inline-flex items-center justify-center w-8 h-8 rounded-lg bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 transition-colors status-dropdown-btn"
                                                        title="المزيد من الخيارات">
                                                    <i class="fas fa-ellipsis-v text-sm"></i>
                                                </button>
                                                <div class="absolute left-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg z-10 hidden status-dropdown-menu">
                                                    <div class="py-1">
                                                        <button type="button" onclick="updateStatus({{ $application->id }}, 'reviewed')" class="w-full text-right px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                                            <i class="fas fa-eye mr-2"></i>
                                                            تمت المراجعة
                                                        </button>
                                                        <button type="button" onclick="updateStatus({{ $application->id }}, 'shortlisted')" class="w-full text-right px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                                            <i class="fas fa-list mr-2"></i>
                                                            القائمة المختصرة
                                                        </button>
                                                        <button type="button" onclick="updateStatus({{ $application->id }}, 'hired')" class="w-full text-right px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                                            <i class="fas fa-user-check mr-2"></i>
                                                            تم التعيين
                                                        </button>
                                                        <hr class="my-1">
                                                        <button type="button" onclick="updateStatus({{ $application->id }}, 'rejected')" class="w-full text-right px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20">
                                                            <i class="fas fa-times mr-2"></i>
                                                            رفض الطلب
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        @endforeach

                        @if(!$hasApplications)
                            <tr>
                                <td colspan="6" class="px-6 py-12 text-center">
                                    <div class="flex flex-col items-center justify-center">
                                        <i class="fas fa-file-alt text-5xl text-gray-300 dark:text-gray-700 mb-4"></i>
                                        <h3 class="text-xl font-bold text-gray-500 dark:text-gray-400">لا توجد طلبات توظيف</h3>
                                        <p class="text-gray-500 dark:text-gray-500 mt-2">لم يتقدم أحد للوظائف المنشورة بعد</p>
                                    </div>
                                </td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="px-6 py-4 flex items-center justify-between border-t border-gray-200 dark:border-gray-700">
                {{ $jobs->links() }}
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Toggle filter panel
    const filterButton = document.getElementById('filterButton');
    const filterPanel = document.getElementById('filterPanel');

    filterButton.addEventListener('click', () => {
        filterPanel.classList.toggle('hidden');
    });

    // Status dropdown toggle
    const statusDropdowns = document.querySelectorAll('.status-dropdown');

    statusDropdowns.forEach(dropdown => {
        const btn = dropdown.querySelector('.status-dropdown-btn');
        const menu = dropdown.querySelector('.status-dropdown-menu');

        btn.addEventListener('click', (e) => {
            e.stopPropagation();
            menu.classList.toggle('hidden');

            // Close other dropdowns
            statusDropdowns.forEach(otherDropdown => {
                if (otherDropdown !== dropdown) {
                    otherDropdown.querySelector('.status-dropdown-menu').classList.add('hidden');
                }
            });
        });
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', () => {
        statusDropdowns.forEach(dropdown => {
            dropdown.querySelector('.status-dropdown-menu').classList.add('hidden');
        });
    });

    // Export Modal Functions
    function showExportModal() {
        console.log('Export button clicked!');
        document.getElementById('exportModal').classList.remove('hidden');
    }

    function hideExportModal() {
        document.getElementById('exportModal').classList.add('hidden');
    }

    // إغلاق modal عند النقر خارجه
    document.getElementById('exportModal').addEventListener('click', function(e) {
        if (e.target === this) {
            hideExportModal();
        }
    });

    function startExport() {
        const selectedFormat = document.querySelector('input[name="export_format"]:checked').value;
        hideExportModal();

        console.log('Exporting as:', selectedFormat);

        let format = 'csv';
        if (selectedFormat === 'pdf') {
            format = 'pdf';
        } else if (selectedFormat === 'csv_filtered') {
            format = 'csv';
        }

        // الحصول على الفلاتر الحالية
        const urlParams = new URLSearchParams(window.location.search);
        const exportUrl = new URL('{{ route("employer.applications.export") }}', window.location.origin);

        // إضافة الفلاتر الحالية إلى رابط التصدير
        urlParams.forEach((value, key) => {
            exportUrl.searchParams.append(key, value);
        });

        // إضافة نوع التصدير
        exportUrl.searchParams.set('format', format);

        console.log('Export URL:', exportUrl.toString());
        window.open(exportUrl.toString(), '_blank');
    }

    // دالة لتحديد الخيار
    function selectOption(value) {
        // إلغاء تحديد جميع الخيارات
        document.querySelectorAll('input[name="export_format"]').forEach(radio => {
            radio.checked = false;
        });

        // تحديد الخيار المطلوب
        document.querySelector(`input[name="export_format"][value="${value}"]`).checked = true;

        // تحديث المؤشرات البصرية
        updateRadioButtons();
    }

    // تحديث الـ radio buttons بصرياً
    function updateRadioButtons() {
        const options = ['csv', 'csv_filtered', 'pdf'];

        options.forEach(option => {
            const radio = document.querySelector(`input[name="export_format"][value="${option}"]`);
            const indicator = document.getElementById(`indicator-${option}`);

            if (radio && indicator) {
                if (radio.checked) {
                    indicator.className = 'w-4 h-4 rounded-full border-2 border-red-600 bg-red-600 flex items-center justify-center mr-3';
                    indicator.innerHTML = '<div class="w-2 h-2 rounded-full bg-white"></div>';
                } else {
                    indicator.className = 'w-4 h-4 rounded-full border-2 border-gray-300 dark:border-gray-600 mr-3';
                    indicator.innerHTML = '';
                }
            }
        });
    }

    document.addEventListener('DOMContentLoaded', function() {
        // تحديث أولي
        updateRadioButtons();
    });
</script>

<!-- Export Modal -->
<div id="exportModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-lg w-full mx-4" onclick="event.stopPropagation()">
        <!-- Header -->
        <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">تصدير طلبات التوظيف</h3>
            <button onclick="hideExportModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <!-- Body -->
        <div class="p-6">
            <div class="mb-6">
                <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4">اختر صيغة التصدير</h4>
                <div class="space-y-3">
                    <label class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors" onclick="selectOption('csv')">
                        <input type="radio" name="export_format" value="csv" checked class="hidden">
                        <div class="flex-1 text-right">
                            <div class="text-gray-900 dark:text-white font-medium">CSV (Excel) - جميع البيانات</div>
                        </div>
                        <div class="w-4 h-4 rounded-full border-2 border-red-600 bg-red-600 flex items-center justify-center mr-3" id="indicator-csv">
                            <div class="w-2 h-2 rounded-full bg-white"></div>
                        </div>
                    </label>

                    <label class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors" onclick="selectOption('csv_filtered')">
                        <input type="radio" name="export_format" value="csv_filtered" class="hidden">
                        <div class="flex-1 text-right">
                            <div class="text-gray-900 dark:text-white font-medium">CSV مع الفلاتر الحالية</div>
                        </div>
                        <div class="w-4 h-4 rounded-full border-2 border-gray-300 dark:border-gray-600 mr-3" id="indicator-csv_filtered"></div>
                    </label>

                    <label class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors" onclick="selectOption('pdf')">
                        <input type="radio" name="export_format" value="pdf" class="hidden">
                        <div class="flex-1 text-right">
                            <div class="text-gray-900 dark:text-white font-medium">PDF - تقرير مفصل</div>
                        </div>
                        <div class="w-4 h-4 rounded-full border-2 border-gray-300 dark:border-gray-600 mr-3" id="indicator-pdf"></div>
                    </label>
                </div>
            </div>

            <!-- Info Box -->
            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <div class="flex items-start">
                    <i class="fas fa-info-circle text-blue-500 mt-1 ml-2"></i>
                    <div class="text-sm text-blue-700 dark:text-blue-300">
                        <div class="font-medium mb-2">سيتم تصدير:</div>
                        <ul class="space-y-1 text-xs">
                            <li>• أسماء المتقدمين وبياناتهم</li>
                            <li>• تفاصيل الوظائف المتقدم إليها</li>
                            <li>• حالات الطلبات وتواريخ التقديم</li>
                            <li>• التقييمات والملاحظات</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="flex gap-3 p-6 border-t border-gray-200 dark:border-gray-700">
            <button onclick="hideExportModal()" class="flex-1 px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors font-medium">
                إلغاء
            </button>
            <button onclick="startExport()" class="flex-1 px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors flex items-center justify-center gap-2 font-medium">
                <i class="fas fa-download"></i>
                تصدير
            </button>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
// Toggle filter panel
function toggleFilters() {
    const panel = document.getElementById('filterPanel');
    panel.classList.toggle('hidden');

    if (!panel.classList.contains('hidden')) {
        panel.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
}

// Show export modal
function showExportModal() {
    document.getElementById('exportModal').classList.remove('hidden');
}

// Hide export modal
function hideExportModal() {
    document.getElementById('exportModal').classList.add('hidden');
}

// Select export option
function selectOption(format) {
    // Remove all selected indicators
    document.querySelectorAll('[id^="indicator-"]').forEach(indicator => {
        indicator.classList.remove('bg-green-500', 'border-green-500');
        indicator.classList.add('border-gray-300', 'dark:border-gray-600');
    });

    // Add selected indicator
    const indicator = document.getElementById(`indicator-${format}`);
    if (indicator) {
        indicator.classList.remove('border-gray-300', 'dark:border-gray-600');
        indicator.classList.add('bg-green-500', 'border-green-500');
    }

    // Set radio button
    const radio = document.querySelector(`input[value="${format}"]`);
    if (radio) {
        radio.checked = true;
    }
}

// Start export
function startExport() {
    const selectedFormat = document.querySelector('input[name="export_format"]:checked');

    if (!selectedFormat) {
        showNotification('يرجى اختيار نوع التصدير', 'warning');
        return;
    }

    const format = selectedFormat.value;
    const currentUrl = new URL(window.location);
    const exportUrl = new URL('{{ route("employer.applications.export") }}', window.location.origin);

    // Copy current filters to export URL
    currentUrl.searchParams.forEach((value, key) => {
        exportUrl.searchParams.append(key, value);
    });

    // Add format parameter
    exportUrl.searchParams.set('format', format);

    // Create temporary link and click it
    const link = document.createElement('a');
    link.href = exportUrl.toString();
    link.download = `applications_${new Date().toISOString().split('T')[0]}.${format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Hide modal
    hideExportModal();

    // Show success message
    showNotification('تم تصدير البيانات بنجاح', 'success');
}

// Update application status
function updateStatus(applicationId, status) {
    const statusText = {
        'pending': 'قيد المراجعة',
        'reviewed': 'تمت المراجعة',
        'accepted': 'مقبول',
        'rejected': 'مرفوض',
        'hired': 'تم التعيين'
    };

    if (confirm(`هل أنت متأكد من تغيير حالة الطلب إلى "${statusText[status]}"؟`)) {
        showLoadingOverlay();

        fetch(`{{ url('/employer/applications') }}/${applicationId}/status`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ status: status })
        })
        .then(response => response.json())
        .then(data => {
            hideLoadingOverlay();
            if (data.success) {
                showNotification('تم تحديث حالة الطلب بنجاح', 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification('حدث خطأ أثناء تحديث الحالة', 'error');
            }
        })
        .catch(error => {
            hideLoadingOverlay();
            console.error('Error:', error);
            showNotification('حدث خطأ أثناء تحديث الحالة', 'error');
        });
    }
}

// Show loading overlay
function showLoadingOverlay() {
    let overlay = document.getElementById('loadingOverlay');
    if (!overlay) {
        overlay = document.createElement('div');
        overlay.id = 'loadingOverlay';
        overlay.className = 'fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50 flex items-center justify-center';
        overlay.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-2xl">
                <div class="flex items-center gap-3">
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                    <span class="text-gray-900 dark:text-white font-medium">جاري التحديث...</span>
                </div>
            </div>
        `;
        document.body.appendChild(overlay);
    }
    overlay.classList.remove('hidden');
}

// Hide loading overlay
function hideLoadingOverlay() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.classList.add('hidden');
    }
}

// Show notification function
function showNotification(message, type = 'info') {
    // Remove existing notifications
    document.querySelectorAll('.notification-toast').forEach(n => n.remove());

    const notification = document.createElement('div');
    notification.className = `notification-toast ${type} fixed top-5 right-5 z-50 p-4 rounded-lg shadow-lg`;
    notification.style.cssText = `
        background: ${type === 'success' ? 'linear-gradient(135deg, #10b981 0%, #059669 100%)' :
                     type === 'error' ? 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)' :
                     type === 'warning' ? 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)' :
                     'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)'};
        color: white;
        animation: slideInRight 0.3s ease-out;
    `;

    notification.innerHTML = `
        <div class="flex items-center gap-3">
            <div class="flex-shrink-0">
                ${type === 'success' ? '<i class="fas fa-check-circle"></i>' :
                  type === 'error' ? '<i class="fas fa-exclamation-circle"></i>' :
                  type === 'warning' ? '<i class="fas fa-exclamation-triangle"></i>' :
                  '<i class="fas fa-info-circle"></i>'}
            </div>
            <div class="flex-1">
                <p class="text-sm font-medium">${message}</p>
            </div>
            <button onclick="this.parentElement.parentElement.remove()" class="text-white hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Select all checkboxes
function selectAll() {
    const checkboxes = document.querySelectorAll('.application-checkbox');
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });

    if (selectAllCheckbox) {
        selectAllCheckbox.checked = true;
    }

    updateBulkActionButtons();
}

// Toggle select all
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const checkboxes = document.querySelectorAll('.application-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateBulkActionButtons();
}

// Update bulk action buttons visibility
function updateBulkActionButtons() {
    const selectedCheckboxes = document.querySelectorAll('.application-checkbox:checked');
    const bulkButtons = document.querySelectorAll('.bulk-action-btn');
    const selectedCount = document.getElementById('selectedCount');
    const selectedNumber = document.getElementById('selectedNumber');

    if (selectedCheckboxes.length > 0) {
        bulkButtons.forEach(button => {
            button.classList.remove('hidden');
            button.disabled = false;
        });

        // Show selected count
        if (selectedCount && selectedNumber) {
            selectedCount.classList.remove('hidden');
            selectedNumber.textContent = selectedCheckboxes.length;
        }
    } else {
        bulkButtons.forEach(button => {
            button.classList.add('hidden');
            button.disabled = true;
        });

        // Hide selected count
        if (selectedCount) {
            selectedCount.classList.add('hidden');
        }
    }
}

// Bulk action
function bulkAction(action) {
    const selectedCheckboxes = document.querySelectorAll('.application-checkbox:checked');
    const selectedIds = Array.from(selectedCheckboxes).map(cb => cb.value);

    if (selectedIds.length === 0) {
        showNotification('يرجى تحديد طلب واحد على الأقل', 'warning');
        return;
    }

    const actionText = action === 'accept' ? 'قبول' : 'رفض';
    const confirmMessage = `هل أنت متأكد من ${actionText} ${selectedIds.length} طلب؟`;

    if (confirm(confirmMessage)) {
        showLoadingOverlay();

        // Process each application individually since we don't have bulk endpoint
        let completed = 0;
        let errors = 0;

        selectedIds.forEach(applicationId => {
            fetch(`{{ url('/employer/applications') }}/${applicationId}/status`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    status: action === 'accept' ? 'accepted' : 'rejected'
                })
            })
            .then(response => response.json())
            .then(data => {
                completed++;
                if (!data.success) errors++;

                if (completed === selectedIds.length) {
                    hideLoadingOverlay();
                    if (errors === 0) {
                        showNotification(`تم ${actionText} ${selectedIds.length} طلب بنجاح`, 'success');
                    } else {
                        showNotification(`تم ${actionText} ${completed - errors} طلب، فشل في ${errors} طلب`, 'warning');
                    }
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                }
            })
            .catch(error => {
                completed++;
                errors++;
                console.error('Error:', error);

                if (completed === selectedIds.length) {
                    hideLoadingOverlay();
                    showNotification(`حدث خطأ أثناء ${actionText} الطلبات`, 'error');
                }
            });
        });
    }
}

// Toggle status dropdown
function toggleStatusDropdown(applicationId) {
    const dropdown = document.querySelector(`[data-application-id="${applicationId}"] .status-dropdown-menu`);
    if (dropdown) {
        dropdown.classList.toggle('hidden');
    }

    // Close other dropdowns
    document.querySelectorAll('.status-dropdown-menu').forEach(menu => {
        if (menu !== dropdown) {
            menu.classList.add('hidden');
        }
    });
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Filter button event
    const filterButton = document.getElementById('filterButton');
    if (filterButton) {
        filterButton.addEventListener('click', toggleFilters);
    }

    // Status dropdown buttons
    document.querySelectorAll('.status-dropdown-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.stopPropagation();
            const applicationId = this.closest('.status-dropdown').dataset.applicationId;
            toggleStatusDropdown(applicationId);
        });
    });

    // Add event listeners to checkboxes
    document.querySelectorAll('.application-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActionButtons);
    });

    // Initialize bulk action buttons state
    updateBulkActionButtons();

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.status-dropdown')) {
            document.querySelectorAll('.status-dropdown-menu').forEach(menu => {
                menu.classList.add('hidden');
            });
        }
    });

    // Close export modal when clicking outside
    const exportModal = document.getElementById('exportModal');
    if (exportModal) {
        exportModal.addEventListener('click', function(e) {
            if (e.target === this) {
                hideExportModal();
            }
        });
    }

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + F for filter toggle
        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            toggleFilters();
        }

        // Escape to close modals
        if (e.key === 'Escape') {
            const filterPanel = document.getElementById('filterPanel');
            const exportModal = document.getElementById('exportModal');

            if (filterPanel && !filterPanel.classList.contains('hidden')) {
                toggleFilters();
            }

            if (exportModal && !exportModal.classList.contains('hidden')) {
                hideExportModal();
            }
        }
    });
});
</script>

<style>
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.notification-toast {
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* تحسينات الأزرار */
.bulk-action-btn {
    animation: fadeIn 0.3s ease-out;
}

.bulk-action-btn.hidden {
    animation: fadeOut 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.8);
    }
}

/* تحسينات الجدول */
.hover\:bg-gray-50:hover {
    background: linear-gradient(90deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 51, 234, 0.05) 100%);
}

/* تحسينات أزرار الإجراءات */
.inline-flex {
    transition: all 0.2s ease;
}

.inline-flex:hover {
    transform: scale(1.1);
}

.inline-flex:active {
    transform: scale(0.95);
}
</style>
@endpush
