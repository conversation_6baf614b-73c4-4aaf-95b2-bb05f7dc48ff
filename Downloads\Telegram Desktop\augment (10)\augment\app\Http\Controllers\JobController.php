<?php

namespace App\Http\Controllers;

use App\Models\Job;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class JobController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:employer'])->except(['index', 'show', 'search']);
    }

    public function index(Request $request)
    {
        $query = Job::with(['employer.user', 'category'])
            ->active(); // استخدام scope active

        // البحث بالكلمة المفتاحية (دعم keyword و search)
        $searchTerm = $request->filled('keyword') ? $request->keyword : $request->search;
        if ($searchTerm) {
            $query->where(function($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%")
                  ->orWhere('requirements', 'like', "%{$searchTerm}%")
                  ->orWhereHas('employer', function($employerQuery) use ($searchTerm) {
                      $employerQuery->where('company_name', 'like', "%{$searchTerm}%");
                  });
            });
        }

        // فلترة بالموقع
        if ($request->filled('location')) {
            $query->inLocation($request->location); // استخدام scope
        }

        // فلترة بالتصنيف
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        // فلترة بنوع الوظيفة
        if ($request->filled('job_type')) {
            $query->ofType($request->job_type); // استخدام scope
        }

        $jobs = $query->latest('posted_at')->paginate(12);

        // الوظائف المميزة للعرض في الأعلى
        $featuredJobs = Job::with(['employer.user', 'category'])
            ->active()
            ->where('is_featured', true)
            ->latest('posted_at')
            ->take(6)
            ->get();

        return view('jobs.index', compact('jobs', 'featuredJobs'));
    }

    public function create()
    {
        return view('jobs.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'location' => 'required|string',
            'type' => 'required|string',
            'category_id' => 'required|exists:categories,id',
            'salary_min' => 'nullable|numeric',
            'salary_max' => 'nullable|numeric',
            'salary_currency' => 'required|string|in:LYD,USD,EUR',
            'requirements' => 'nullable|string',
            'benefits' => 'nullable|string',
            'deadline' => 'nullable|date|after:today',
            'is_featured' => 'boolean',
        ]);

        $job = new Job();
        $job->employer_id = Auth::user()->employer->id;
        $job->title = $request->title;
        $job->description = $request->description;
        $job->location = $request->location;
        $job->type = $request->type;
        $job->category_id = $request->category_id;
        $job->salary_min = $request->salary_min;
        $job->salary_max = $request->salary_max;
        $job->salary_currency = $request->salary_currency;
        $job->requirements = $request->requirements;
        $job->benefits = $request->benefits;
        $job->deadline = $request->deadline;
        $job->is_featured = $request->is_featured ?? false;
        $job->save();

        return redirect()->route('employer.jobs.index')->with('success', 'Job posted successfully');
    }

    public function show(Job $job)
    {
        // تحميل العلاقات
        $job->load(['employer.user', 'category', 'applications.jobSeeker.user']);

        // زيادة عدد المشاهدات
        $job->increment('views');

        // الوظائف المشابهة
        $relatedJobs = Job::with(['employer.user', 'category'])
            ->active()
            ->where('category_id', $job->category_id)
            ->where('id', '!=', $job->id)
            ->latest('posted_at')
            ->take(3)
            ->get();

        return view('jobs.show', compact('job', 'relatedJobs'));
    }

    public function edit(Job $job)
    {
        $this->authorize('update', $job);
        return view('jobs.edit', compact('job'));
    }

    public function update(Request $request, Job $job)
    {
        $this->authorize('update', $job);

        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'location' => 'required|string',
            'type' => 'required|string',
            'category_id' => 'required|exists:categories,id',
            'salary_min' => 'nullable|numeric',
            'salary_max' => 'nullable|numeric',
            'salary_currency' => 'required|string|in:LYD,USD,EUR',
            'requirements' => 'nullable|string',
            'benefits' => 'nullable|string',
            'deadline' => 'nullable|date',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
        ]);

        $job->title = $request->title;
        $job->description = $request->description;
        $job->location = $request->location;
        $job->type = $request->type;
        $job->category_id = $request->category_id;
        $job->salary_min = $request->salary_min;
        $job->salary_max = $request->salary_max;
        $job->salary_currency = $request->salary_currency;
        $job->requirements = $request->requirements;
        $job->benefits = $request->benefits;
        $job->deadline = $request->deadline;
        $job->is_active = $request->is_active ?? false;
        $job->is_featured = $request->is_featured ?? false;
        $job->save();

        return redirect()->route('employer.jobs.index')->with('success', 'Job updated successfully');
    }

    public function destroy(Job $job)
    {
        $this->authorize('delete', $job);
        $job->delete();

        return redirect()->route('employer.jobs.index')->with('success', 'Job deleted successfully');
    }

    public function search(Request $request)
    {
        $query = Job::with(['employer.user', 'category'])
            ->where('is_active', true)
            ->where('expires_at', '>', now());

        // البحث بالكلمات المفتاحية
        if ($request->filled('keyword')) {
            $keyword = $request->keyword;
            $query->where(function($q) use ($keyword) {
                $q->where('title', 'like', '%' . $keyword . '%')
                  ->orWhere('description', 'like', '%' . $keyword . '%')
                  ->orWhere('requirements', 'like', '%' . $keyword . '%')
                  ->orWhereHas('employer', function($employerQuery) use ($keyword) {
                      $employerQuery->where('company_name', 'like', '%' . $keyword . '%');
                  });
            });
        }

        // فلترة بالموقع
        if ($request->filled('location')) {
            $query->where('location', 'like', '%' . $request->location . '%');
        }

        // فلترة بالتصنيف
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        // فلترة بنوع الوظيفة
        if ($request->filled('type')) {
            $query->where('job_type', $request->type);
        }

        // فلترة بالراتب الأدنى
        if ($request->filled('salary_min')) {
            $query->where('salary_min', '>=', $request->salary_min);
        }

        // ترتيب النتائج
        $sortBy = $request->get('sort', 'latest');
        switch ($sortBy) {
            case 'oldest':
                $query->oldest('posted_at');
                break;
            case 'title':
                $query->orderBy('title');
                break;
            case 'company':
                $query->join('employers', 'jobs.employer_id', '=', 'employers.id')
                      ->orderBy('employers.company_name');
                break;
            default:
                $query->latest('posted_at');
        }

        $jobs = $query->paginate(12);

        // إحصائيات البحث
        $totalJobs = $query->count();
        $searchTerm = $request->keyword;

        // التصنيفات للفلترة
        $categories = \App\Models\Category::all();

        return view('jobs.search', compact('jobs', 'totalJobs', 'searchTerm', 'categories'));
    }

    /**
     * حفظ الوظيفة في المفضلة
     */
    public function save(Job $job)
    {
        try {
            $user = auth()->user();

            // التحقق من أن المستخدم باحث عن عمل
            if ($user->role !== 'job_seeker') {
                return response()->json([
                    'success' => false,
                    'message' => 'هذه الميزة متاحة للباحثين عن عمل فقط'
                ], 403);
            }

            // التحقق من وجود JobSeeker profile
            $jobSeeker = $user->jobSeeker;
            if (!$jobSeeker) {
                return response()->json([
                    'success' => false,
                    'message' => 'يجب إكمال الملف الشخصي أولاً'
                ], 400);
            }

            // التحقق من أن الوظيفة غير محفوظة مسبقاً
            $exists = \DB::table('saved_jobs')
                ->where('job_seeker_id', $jobSeeker->id)
                ->where('job_id', $job->id)
                ->exists();

            if ($exists) {
                return response()->json([
                    'success' => false,
                    'message' => 'الوظيفة محفوظة مسبقاً'
                ]);
            }

            // حفظ الوظيفة
            \DB::table('saved_jobs')->insert([
                'job_seeker_id' => $jobSeeker->id,
                'job_id' => $job->id,
                'created_at' => now(),
                'updated_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم حفظ الوظيفة بنجاح'
            ]);

        } catch (\Exception $e) {
            \Log::error('خطأ في حفظ الوظيفة: ' . $e->getMessage(), [
                'job_id' => $job->id,
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حفظ الوظيفة'
            ], 500);
        }
    }
}
