<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حالة قاعدة البيانات - Hire Me</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-2xl mx-auto bg-white rounded-lg shadow p-6">
        <h1 class="text-2xl font-bold text-center mb-6">📊 حالة قاعدة البيانات</h1>
        
        <div class="space-y-4">
            @php
                $tables = [
                    'users' => 'جدول المستخدمين',
                    'employers' => 'جدول أصحاب العمل', 
                    'job_seekers' => 'جدول الباحثين عن عمل',
                    'jobs' => 'جدول الوظائف',
                    'applications' => 'جدول طلبات التوظيف',
                    'saved_candidates' => 'جدول المرشحين المحفوظين'
                ];
                
                $tableStatus = [];
                foreach ($tables as $table => $name) {
                    try {
                        $tableStatus[$table] = Schema::hasTable($table);
                    } catch (\Exception $e) {
                        $tableStatus[$table] = false;
                    }
                }
            @endphp
            
            @foreach($tables as $table => $name)
                <div class="flex items-center justify-between p-4 border rounded-lg {{ $tableStatus[$table] ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200' }}">
                    <span class="font-medium">{{ $name }}</span>
                    @if($tableStatus[$table])
                        <span class="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">
                            <i class="fas fa-check"></i> موجود
                        </span>
                    @else
                        <span class="px-3 py-1 bg-red-100 text-red-800 text-sm rounded-full">
                            <i class="fas fa-times"></i> غير موجود
                        </span>
                    @endif
                </div>
            @endforeach
        </div>
        
        @if($tableStatus['saved_candidates'])
            <div class="mt-6 p-4 bg-green-100 border border-green-300 rounded-lg">
                <h3 class="font-semibold text-green-800 mb-2">🎉 ممتاز!</h3>
                <p class="text-green-700">جدول المرشحين المحفوظين موجود. زر الحفظ سيعمل الآن بشكل مثالي!</p>
            </div>
        @else
            <div class="mt-6 p-4 bg-red-100 border border-red-300 rounded-lg">
                <h3 class="font-semibold text-red-800 mb-2">⚠️ تحذير!</h3>
                <p class="text-red-700">جدول المرشحين المحفوظين غير موجود. قد لا يعمل زر الحفظ.</p>
            </div>
        @endif
        
        <div class="mt-6 flex gap-4 justify-center">
            <a href="/employer/candidates/search" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                صفحة البحث
            </a>
            <a href="/employer/candidates" class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700">
                المرشحين المحفوظين
            </a>
        </div>
    </div>
</body>
</html>
