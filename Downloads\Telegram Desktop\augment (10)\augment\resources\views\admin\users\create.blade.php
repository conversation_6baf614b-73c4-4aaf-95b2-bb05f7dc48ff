@extends('layouts.admin')

@section('title', 'إضافة مستخدم جديد - Hire Me')
@section('header_title', 'إضافة مستخدم جديد')

@section('content')
<div class="p-6">
    <div class="mb-6">
        <div class="flex items-center gap-4">
            <a href="{{ route('admin.users.index') }}" class="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white">
                <i class="fas fa-arrow-right text-xl"></i>
            </a>
            <div>
                <h2 class="text-2xl font-bold">إضافة مستخدم جديد</h2>
                <p class="text-gray-600 dark:text-gray-400 mt-1">إنشاء حساب مستخدم جديد في النظام</p>
            </div>
        </div>
    </div>

    <!-- Error Messages -->
    @if($errors->any())
        <div class="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
            <div class="flex items-center mb-2">
                <i class="fas fa-exclamation-circle ml-2"></i>
                <span class="font-medium">يرجى تصحيح الأخطاء التالية:</span>
            </div>
            <ul class="list-disc list-inside">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Form -->
        <div class="lg:col-span-2">
            <form action="{{ route('admin.users.store') }}" method="POST" class="space-y-6">
                @csrf
                
                <!-- Basic Information -->
                <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                    <h3 class="text-lg font-bold mb-4">المعلومات الأساسية</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الاسم الكامل <span class="text-red-600">*</span></label>
                            <input type="text" name="name" id="name" value="{{ old('name') }}" required
                                   class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary">
                        </div>
                        
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">البريد الإلكتروني <span class="text-red-600">*</span></label>
                            <input type="email" name="email" id="email" value="{{ old('email') }}" required
                                   class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary">
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">كلمة المرور <span class="text-red-600">*</span></label>
                                <input type="password" name="password" id="password" required
                                       class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary">
                            </div>
                            
                            <div>
                                <label for="password_confirmation" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">تأكيد كلمة المرور <span class="text-red-600">*</span></label>
                                <input type="password" name="password_confirmation" id="password_confirmation" required
                                       class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary">
                            </div>
                        </div>
                        
                        <div>
                            <label for="role" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الدور <span class="text-red-600">*</span></label>
                            <select name="role" id="role" required
                                    class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary">
                                <option value="">اختر الدور</option>
                                <option value="admin" {{ old('role') === 'admin' ? 'selected' : '' }}>مدير</option>
                                <option value="employer" {{ old('role') === 'employer' ? 'selected' : '' }}>صاحب عمل</option>
                                <option value="candidate" {{ old('role') === 'candidate' ? 'selected' : '' }}>مرشح</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- Contact Information -->
                <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                    <h3 class="text-lg font-bold mb-4">معلومات الاتصال</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">رقم الهاتف</label>
                            <input type="tel" name="phone" id="phone" value="{{ old('phone') }}"
                                   class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary"
                                   placeholder="+966 50 123 4567">
                        </div>
                        
                        <div>
                            <label for="location" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الموقع</label>
                            <input type="text" name="location" id="location" value="{{ old('location') }}"
                                   class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary"
                                   placeholder="طرابلس، ليبيا">
                        </div>
                    </div>
                </div>
                
                <!-- Account Settings -->
                <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                    <h3 class="text-lg font-bold mb-4">إعدادات الحساب</h3>
                    
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input type="checkbox" name="verified" id="verified" value="1" {{ old('verified') ? 'checked' : '' }}
                                   class="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary">
                            <label for="verified" class="mr-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                                تفعيل الحساب فوراً (تخطي تأكيد البريد الإلكتروني)
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- Submit Buttons -->
                <div class="flex justify-end space-x-4 rtl:space-x-reverse">
                    <a href="{{ route('admin.users.index') }}" class="px-6 py-2.5 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                        إلغاء
                    </a>
                    <button type="submit" class="px-6 py-2.5 btn-gradient text-white rounded-lg hover:opacity-90 transition-colors">
                        إنشاء المستخدم
                    </button>
                </div>
            </form>
        </div>
        
        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Guidelines -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">إرشادات إنشاء المستخدم</h3>
                
                <div class="space-y-4 text-sm">
                    <div class="flex items-start gap-3">
                        <i class="fas fa-info-circle text-blue-500 mt-0.5"></i>
                        <div>
                            <p class="font-medium text-gray-900 dark:text-white">كلمة المرور</p>
                            <p class="text-gray-600 dark:text-gray-400">يجب أن تكون 8 أحرف على الأقل وتحتوي على أحرف وأرقام</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start gap-3">
                        <i class="fas fa-user-shield text-green-500 mt-0.5"></i>
                        <div>
                            <p class="font-medium text-gray-900 dark:text-white">الأدوار</p>
                            <p class="text-gray-600 dark:text-gray-400">المدير: صلاحيات كاملة، صاحب العمل: إدارة الوظائف، المرشح: التقديم للوظائف</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start gap-3">
                        <i class="fas fa-envelope-check text-purple-500 mt-0.5"></i>
                        <div>
                            <p class="font-medium text-gray-900 dark:text-white">تفعيل الحساب</p>
                            <p class="text-gray-600 dark:text-gray-400">يمكنك تفعيل الحساب فوراً أو ترك المستخدم يفعله عبر البريد الإلكتروني</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Stats -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">إحصائيات سريعة</h3>
                
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600 dark:text-gray-400">إجمالي المستخدمين</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">{{ \App\Models\User::count() }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600 dark:text-gray-400">المديرون</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">{{ \App\Models\User::where('role', 'admin')->count() }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600 dark:text-gray-400">أصحاب العمل</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">{{ \App\Models\User::where('role', 'employer')->count() }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600 dark:text-gray-400">المرشحون</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">{{ \App\Models\User::where('role', 'candidate')->count() }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
