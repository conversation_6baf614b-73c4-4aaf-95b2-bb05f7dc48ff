@extends('layouts.admin')

@section('title', 'إدارة المراجعات والتقييمات - Hire Me')

@section('content')
<!-- Reviews Content -->
<div class="p-6">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">إدارة المراجعات والتقييمات</h2>
        <div class="flex gap-3">
            <button class="btn-primary">
                <i class="fas fa-filter ml-2"></i>
                تصفية
            </button>
            <button class="btn-secondary">
                <i class="fas fa-download ml-2"></i>
                تصدير
            </button>
        </div>
    </div>

    <!-- Filters -->
    <form action="{{ route('admin.reviews.index') }}" method="GET" class="mb-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">نوع المراجعة</label>
                    <select name="type" class="form-select">
                        <option value="">جميع الأنواع</option>
                        <option value="company" {{ request('type') == 'company' ? 'selected' : '' }}>مراجعات الشركات</option>
                        <option value="candidate" {{ request('type') == 'candidate' ? 'selected' : '' }}>مراجعات المرشحين</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">التقييم</label>
                    <select name="rating" class="form-select">
                        <option value="">جميع التقييمات</option>
                        <option value="5" {{ request('rating') == '5' ? 'selected' : '' }}>5 نجوم</option>
                        <option value="4" {{ request('rating') == '4' ? 'selected' : '' }}>4 نجوم</option>
                        <option value="3" {{ request('rating') == '3' ? 'selected' : '' }}>3 نجوم</option>
                        <option value="2" {{ request('rating') == '2' ? 'selected' : '' }}>2 نجوم</option>
                        <option value="1" {{ request('rating') == '1' ? 'selected' : '' }}>1 نجمة</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>موافق عليها</option>
                        <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>قيد المراجعة</option>
                        <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>مرفوضة</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">تاريخ النشر</label>
                    <select name="date" class="form-select">
                        <option value="">جميع الفترات</option>
                        <option value="today" {{ request('date') == 'today' ? 'selected' : '' }}>اليوم</option>
                        <option value="week" {{ request('date') == 'week' ? 'selected' : '' }}>هذا الأسبوع</option>
                        <option value="month" {{ request('date') == 'month' ? 'selected' : '' }}>هذا الشهر</option>
                        <option value="year" {{ request('date') == 'year' ? 'selected' : '' }}>هذا العام</option>
                    </select>
                </div>
            </div>
            <div class="mt-4 flex justify-end">
                <button type="submit" class="btn-primary">
                    <i class="fas fa-filter ml-2"></i>
                    تطبيق الفلتر
                </button>
                <a href="{{ route('admin.reviews.index') }}" class="btn-secondary mr-2">
                    <i class="fas fa-times ml-2"></i>
                    إعادة ضبط
                </a>
            </div>
        </div>
    </form>

    <!-- Reviews Table -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        المراجع
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        النوع
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        التقييم
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        المحتوى
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        الحالة
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        تاريخ النشر
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        الإجراءات
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                @forelse($reviews as $review)
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10">
                                @if($review->type == 'company')
                                    <img class="h-10 w-10 rounded-full" src="{{ $review->jobSeeker->user->avatar ?? 'https://ui-avatars.com/api/?name=' . urlencode($review->jobSeeker->user->name) . '&background=random' }}" alt="">
                                @else
                                    <img class="h-10 w-10 rounded-full" src="{{ $review->employer->user->avatar ?? 'https://ui-avatars.com/api/?name=' . urlencode($review->employer->user->name) . '&background=random' }}" alt="">
                                @endif
                            </div>
                            <div class="mr-4">
                                <div class="text-sm font-medium text-gray-900 dark:text-white">
                                    @if($review->type == 'company')
                                        {{ $review->jobSeeker->user->name }}
                                    @else
                                        {{ $review->employer->user->name }}
                                    @endif
                                </div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    @if($review->type == 'company')
                                        {{ $review->jobSeeker->user->email }}
                                    @else
                                        {{ $review->employer->user->email }}
                                    @endif
                                </div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        @if($review->type == 'company')
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                                شركة
                            </span>
                        @else
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100">
                                مرشح
                            </span>
                        @endif
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex text-yellow-400">
                            @for($i = 1; $i <= 5; $i++)
                                @if($i <= $review->rating)
                                    <i class="fas fa-star"></i>
                                @else
                                    <i class="far fa-star"></i>
                                @endif
                            @endfor
                        </div>
                    </td>
                    <td class="px-6 py-4">
                        <div class="text-sm text-gray-900 dark:text-white truncate max-w-xs">{{ $review->comment }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        @if(isset($review->status))
                            @if($review->status == 'approved')
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                                    موافق عليها
                                </span>
                            @elseif($review->status == 'rejected')
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100">
                                    مرفوضة
                                </span>
                            @else
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100">
                                    قيد المراجعة
                                </span>
                            @endif
                        @else
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100">
                                قيد المراجعة
                            </span>
                        @endif
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {{ $review->created_at->diffForHumans() }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-left text-sm font-medium">
                        <a href="{{ route('admin.reviews.show', $review->id) }}" class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 ml-2">
                            <i class="fas fa-eye"></i>
                        </a>
                        <form action="{{ route('admin.reviews.update-status', $review->id) }}" method="POST" class="inline">
                            @csrf
                            <input type="hidden" name="status" value="approved">
                            <input type="hidden" name="type" value="{{ $review->type }}">
                            <button type="submit" class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 ml-2">
                                <i class="fas fa-check"></i>
                            </button>
                        </form>
                        <form action="{{ route('admin.reviews.update-status', $review->id) }}" method="POST" class="inline">
                            @csrf
                            <input type="hidden" name="status" value="rejected">
                            <input type="hidden" name="type" value="{{ $review->type }}">
                            <button type="submit" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                                <i class="fas fa-times"></i>
                            </button>
                        </form>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="7" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                        لا توجد مراجعات متاحة
                    </td>
                </tr>
                @endforelse
            </tbody>
        </table>

        <!-- Pagination -->
        <div class="bg-white dark:bg-gray-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6">
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700 dark:text-gray-300">
                        عرض
                        <span class="font-medium">{{ ($currentPage - 1) * $perPage + 1 }}</span>
                        إلى
                        <span class="font-medium">{{ min($currentPage * $perPage, $total) }}</span>
                        من أصل
                        <span class="font-medium">{{ $total }}</span>
                        نتيجة
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        @if($currentPage > 1)
                            <a href="{{ route('admin.reviews.index', array_merge(request()->except('page'), ['page' => $currentPage - 1])) }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600">
                                <span class="sr-only">السابق</span>
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        @endif

                        @for($i = 1; $i <= $lastPage; $i++)
                            <a href="{{ route('admin.reviews.index', array_merge(request()->except('page'), ['page' => $i])) }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 {{ $i == $currentPage ? 'bg-primary text-white' : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600' }} text-sm font-medium">
                                {{ $i }}
                            </a>
                        @endfor

                        @if($currentPage < $lastPage)
                            <a href="{{ route('admin.reviews.index', array_merge(request()->except('page'), ['page' => $currentPage + 1])) }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600">
                                <span class="sr-only">التالي</span>
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        @endif
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
