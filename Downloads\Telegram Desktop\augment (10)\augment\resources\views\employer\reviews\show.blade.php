@extends('layouts.employer')

@section('title', 'تفاصيل التقييم')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
            <div class="mb-4 sm:mb-0">
                <div class="flex items-center gap-3 mb-2">
                    <div class="w-10 h-10 rounded-xl bg-gradient-to-br from-yellow-400 to-orange-600 flex items-center justify-center text-white shadow-lg">
                        <i class="fas fa-star text-lg"></i>
                    </div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">تفاصيل التقييم</h1>
                </div>
                <!-- Breadcrumb -->
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="inline-flex items-center space-x-1 md:space-x-3">
                        <li class="inline-flex items-center">
                            <a href="{{ route('employer.dashboard') }}" class="text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400">
                                لوحة التحكم
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                                <a href="{{ route('employer.reviews.index') }}" class="text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400">
                                    التقييمات
                                </a>
                            </div>
                        </li>
                        <li aria-current="page">
                            <div class="flex items-center">
                                <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                                <span class="text-gray-500 dark:text-gray-400">تفاصيل التقييم</span>
                            </div>
                        </li>
                    </ol>
                </nav>
            </div>
            <div class="flex gap-3">
                <a href="{{ route('employer.reviews.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right ml-2"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>

            <div class="row">
                <!-- معلومات التقييم -->
                <div class="col-lg-8">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">معلومات التقييم</h6>
                        </div>
                        <div class="card-body">
                            <!-- معلومات المقيم -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <h6 class="text-gray-800 mb-2">المقيم</h6>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-lg me-3">
                                            <div class="avatar-title bg-primary rounded-circle">
                                                {{ substr($review->reviewer->name, 0, 2) }}
                                            </div>
                                        </div>
                                        <div>
                                            <h5 class="mb-1">{{ $review->reviewer->name }}</h5>
                                            <p class="text-muted mb-0">{{ $review->reviewer->email }}</p>
                                            @if($review->reviewer->role === 'job_seeker')
                                                <small class="text-info">باحث عن عمل</small>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-gray-800 mb-2">تاريخ التقييم</h6>
                                    <p class="mb-1">{{ $review->created_at->format('Y-m-d H:i') }}</p>
                                    <small class="text-muted">{{ $review->created_at->diffForHumans() }}</small>
                                </div>
                            </div>

                            <!-- التقييم والتعليق -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <h6 class="text-gray-800 mb-2">التقييم</h6>
                                    <div class="rating-display">
                                        @for($i = 1; $i <= 5; $i++)
                                            <i class="fas fa-star fa-lg {{ $i <= $review->rating ? 'text-warning' : 'text-muted' }}"></i>
                                        @endfor
                                        <span class="ms-2 h5 mb-0">{{ $review->rating }}/5</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-gray-800 mb-2">الحالة</h6>
                                    <span class="badge bg-{{ $review->status_color }} fs-6 px-3 py-2">
                                        {{ $review->status_text }}
                                    </span>
                                </div>
                            </div>

                            <!-- التعليق -->
                            <div class="mb-4">
                                <h6 class="text-gray-800 mb-2">التعليق</h6>
                                <div class="bg-light p-3 rounded">
                                    <p class="mb-0">{{ $review->comment ?: 'لا يوجد تعليق' }}</p>
                                </div>
                            </div>

                            <!-- رد صاحب العمل -->
                            @if($review->employer_response)
                                <div class="mb-4">
                                    <h6 class="text-gray-800 mb-2">ردك على التقييم</h6>
                                    <div class="bg-primary bg-opacity-10 p-3 rounded border-start border-primary border-3">
                                        <p class="mb-1">{{ $review->employer_response }}</p>
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            {{ $review->responded_at->format('Y-m-d H:i') }}
                                        </small>
                                    </div>
                                </div>
                            @elseif($review->status === 'approved')
                                <!-- نموذج الرد -->
                                <div class="mb-4">
                                    <h6 class="text-gray-800 mb-2">الرد على التقييم</h6>
                                    <form action="{{ route('employer.reviews.respond', $review) }}" method="POST">
                                        @csrf
                                        <div class="form-group mb-3">
                                            <textarea name="response" class="form-control" rows="4"
                                                      placeholder="اكتب ردك على هذا التقييم..." required></textarea>
                                        </div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-reply me-1"></i>
                                            إرسال الرد
                                        </button>
                                    </form>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="col-lg-4">
                    <!-- حالة التقييم -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">حالة التقييم</h6>
                        </div>
                        <div class="card-body">
                            <div class="text-center">
                                <div class="status-icon mb-3">
                                    @if($review->status === 'approved')
                                        <i class="fas fa-check-circle fa-3x text-success"></i>
                                    @elseif($review->status === 'pending')
                                        <i class="fas fa-clock fa-3x text-warning"></i>
                                    @else
                                        <i class="fas fa-times-circle fa-3x text-danger"></i>
                                    @endif
                                </div>
                                <h5 class="text-{{ $review->status_color }}">{{ $review->status_text }}</h5>

                                @if($review->status === 'approved' && $review->approved_at)
                                    <p class="text-muted mb-0">
                                        تم الاعتماد في: {{ $review->approved_at->format('Y-m-d H:i') }}
                                    </p>
                                    @if($review->approvedBy)
                                        <small class="text-muted">
                                            بواسطة: {{ $review->approvedBy->name }}
                                        </small>
                                    @endif
                                @elseif($review->status === 'pending')
                                    <p class="text-muted mb-0">
                                        في انتظار مراجعة الإدارة
                                    </p>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- ملاحظات الإدارة -->
                    @if($review->admin_notes)
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">ملاحظات الإدارة</h6>
                            </div>
                            <div class="card-body">
                                <p class="mb-0">{{ $review->admin_notes }}</p>
                            </div>
                        </div>
                    @endif

                    <!-- إحصائيات سريعة -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">إحصائيات سريعة</h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <h4 class="text-primary mb-1">{{ $review->rating }}</h4>
                                        <small class="text-muted">التقييم</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h4 class="text-info mb-1">{{ $review->type === 'company' ? 'شركة' : 'ملف شخصي' }}</h4>
                                    <small class="text-muted">نوع التقييم</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 60px;
    height: 60px;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
    font-size: 1.2rem;
}

.rating-display .fas {
    margin-right: 2px;
}

.status-icon {
    margin-bottom: 1rem;
}

.border-start {
    border-left-width: 3px !important;
}
</style>
@endsection
