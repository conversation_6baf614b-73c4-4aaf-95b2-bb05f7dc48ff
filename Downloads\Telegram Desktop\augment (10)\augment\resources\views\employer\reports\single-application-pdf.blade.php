<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل طلب التوظيف - {{ $application->jobSeeker && $application->jobSeeker->user ? $application->jobSeeker->user->name : '<PERSON><PERSON><PERSON> محدد' }}</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #3b82f6;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #1f2937;
            margin: 0;
            font-size: 24px;
        }
        .header p {
            color: #6b7280;
            margin: 10px 0 0 0;
        }
        .section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background-color: #f9fafb;
        }
        .section h3 {
            color: #1f2937;
            margin: 0 0 15px 0;
            font-size: 18px;
            border-bottom: 2px solid #3b82f6;
            padding-bottom: 5px;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .info-row:last-child {
            border-bottom: none;
        }
        .info-label {
            font-weight: bold;
            color: #374151;
            min-width: 150px;
        }
        .info-value {
            color: #6b7280;
            flex: 1;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
        }
        .status-pending {
            background-color: #fef3c7;
            color: #92400e;
        }
        .status-reviewed {
            background-color: #dbeafe;
            color: #1e40af;
        }
        .status-accepted {
            background-color: #d1fae5;
            color: #065f46;
        }
        .status-rejected {
            background-color: #fee2e2;
            color: #991b1b;
        }
        .status-hired {
            background-color: #ecfdf5;
            color: #047857;
        }
        .cover-letter {
            background-color: #f3f4f6;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
            margin-top: 10px;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
            font-size: 12px;
        }
        @media print {
            body {
                background-color: white;
            }
            .container {
                box-shadow: none;
                padding: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>تفاصيل طلب التوظيف</h1>
            <p>رقم الطلب: {{ $application->id }}</p>
            <p>تاريخ التقرير: {{ $reportDate }}</p>
        </div>

        <!-- معلومات المتقدم -->
        <div class="section">
            <h3>معلومات المتقدم</h3>
            <div class="info-row">
                <span class="info-label">الاسم:</span>
                <span class="info-value">{{ $application->jobSeeker && $application->jobSeeker->user ? $application->jobSeeker->user->name : 'غير محدد' }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">البريد الإلكتروني:</span>
                <span class="info-value">{{ $application->jobSeeker && $application->jobSeeker->user ? $application->jobSeeker->user->email : 'غير محدد' }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">رقم الهاتف:</span>
                <span class="info-value">{{ $application->jobSeeker && $application->jobSeeker->user ? ($application->jobSeeker->user->phone ?? 'غير محدد') : 'غير محدد' }}</span>
            </div>
            @if($application->jobSeeker->job_title)
            <div class="info-row">
                <span class="info-label">المسمى الوظيفي:</span>
                <span class="info-value">{{ $application->jobSeeker->job_title }}</span>
            </div>
            @endif
            @if($application->jobSeeker->location)
            <div class="info-row">
                <span class="info-label">الموقع:</span>
                <span class="info-value">{{ $application->jobSeeker->location }}</span>
            </div>
            @endif
        </div>

        <!-- معلومات الوظيفة -->
        <div class="section">
            <h3>معلومات الوظيفة</h3>
            <div class="info-row">
                <span class="info-label">عنوان الوظيفة:</span>
                <span class="info-value">{{ $application->job->title }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">موقع الوظيفة:</span>
                <span class="info-value">{{ $application->job->location ?? 'غير محدد' }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">نوع الوظيفة:</span>
                <span class="info-value">{{ getJobTypeInArabic($application->job->job_type) }}</span>
            </div>
            @if($application->job->salary_range)
            <div class="info-row">
                <span class="info-label">نطاق الراتب:</span>
                <span class="info-value">{{ $application->job->salary_range }}</span>
            </div>
            @endif
        </div>

        <!-- معلومات الطلب -->
        <div class="section">
            <h3>معلومات الطلب</h3>
            <div class="info-row">
                <span class="info-label">الحالة:</span>
                <span class="info-value">
                    <span class="status-badge status-{{ $application->status }}">
                        {{ getApplicationStatusInArabic($application->status) }}
                    </span>
                </span>
            </div>
            <div class="info-row">
                <span class="info-label">تاريخ التقديم:</span>
                <span class="info-value">{{ $application->created_at->format('Y-m-d H:i') }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">تاريخ آخر تحديث:</span>
                <span class="info-value">{{ $application->updated_at->format('Y-m-d H:i') }}</span>
            </div>
            @if($application->employer_rating)
            <div class="info-row">
                <span class="info-label">التقييم:</span>
                <span class="info-value">{{ $application->employer_rating }}/5</span>
            </div>
            @endif
        </div>

        <!-- رسالة التقديم -->
        @if($application->cover_letter)
        <div class="section">
            <h3>رسالة التقديم</h3>
            <div class="cover-letter">{{ strip_tags($application->cover_letter) }}</div>
        </div>
        @endif

        <!-- ملاحظات صاحب العمل -->
        @if($application->employer_notes)
        <div class="section">
            <h3>ملاحظات صاحب العمل</h3>
            <div class="cover-letter">{{ strip_tags($application->employer_notes) }}</div>
        </div>
        @endif

        <!-- Footer -->
        <div class="footer">
            <p>تم إنشاء هذا التقرير بواسطة نظام إدارة التوظيف</p>
            <p>{{ $employer->company_name ?? 'الشركة' }} - {{ $reportDate }}</p>
        </div>
    </div>
</body>
</html>
