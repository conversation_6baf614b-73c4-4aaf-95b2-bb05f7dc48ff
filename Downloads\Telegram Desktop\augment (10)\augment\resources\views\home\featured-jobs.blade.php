<!-- Featured Jobs Section -->
<section class="py-16 bg-gray-50 dark:bg-gray-900">
    <div class="p-6">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-star text-yellow-500 mr-3"></i>
                    الوظائف المميزة
                </h2>
                <p class="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                    اكتشف أفضل الفرص الوظيفية المتاحة من الشركات الرائدة
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @forelse($featuredJobs as $job)
                <!-- Job Card -->
                <div class="bg-white dark:bg-dark-card rounded-2xl shadow-sm hover:shadow-xl hover:scale-105 transition-all duration-300 p-6 border border-gray-100 dark:border-gray-700 group">
                    <!-- Header -->
                    <div class="flex justify-between items-start mb-4">
                        <div class="w-16 h-16 gradient-bg rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-briefcase text-2xl text-white"></i>
                        </div>
                        <div class="flex flex-col items-end gap-2">
                            <span class="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded-full">
                                <i class="fas fa-clock mr-1"></i>
                                {{ $job->posted_at->diffForHumans() }}
                            </span>
                            <span class="text-xs font-medium px-3 py-1 rounded-full
                                @if($job->job_type == 'full_time') bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400
                                @elseif($job->job_type == 'part_time') bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400
                                @elseif($job->job_type == 'remote') bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400
                                @else bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400
                                @endif">
                                @switch($job->job_type)
                                    @case('full_time')
                                        دوام كامل
                                        @break
                                    @case('part_time')
                                        دوام جزئي
                                        @break
                                    @case('remote')
                                        عن بعد
                                        @break
                                    @case('freelance')
                                        عمل حر
                                        @break
                                    @default
                                        {{ $job->job_type }}
                                @endswitch
                            </span>
                        </div>
                    </div>

                    <!-- Job Title -->
                    <h3 class="text-xl font-bold mb-2 group-hover:text-primary transition-colors duration-300">
                        <a href="{{ route('jobs.show', $job) }}" class="hover:text-primary transition">
                            {{ $job->title }}
                        </a>
                    </h3>

                    <!-- Company -->
                    <div class="flex items-center mb-4">
                        <i class="fas fa-building text-primary mr-2"></i>
                        <h4 class="text-primary font-medium">
                            @if($job->employer)
                                <a href="{{ route('employers.show', $job->employer) }}" class="hover:underline">
                                    {{ $job->employer->company_name }}
                                </a>
                            @else
                                <span>شركة غير محددة</span>
                            @endif
                        </h4>
                    </div>

                    <!-- Tags -->
                    <div class="flex flex-wrap gap-2 mb-4">
                        @if($job->category)
                            <span class="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium">
                                <i class="fas fa-tag mr-1"></i>
                                {{ $job->category->name }}
                            </span>
                        @endif

                        @if($job->location)
                            <span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 rounded-full text-sm">
                                <i class="fas fa-map-marker-alt mr-1"></i>
                                {{ $job->location }}
                            </span>
                        @endif
                    </div>

                    <!-- Job Description Preview -->
                    <p class="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2">
                        {{ Str::limit($job->description, 100) }}
                    </p>

                    <!-- Footer -->
                    <div class="flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-700">
                        <div class="flex items-center gap-2">
                            @if($job->salary_min || $job->salary_max)
                                <div class="flex items-center text-primary font-bold">
                                    <i class="fas fa-money-bill-wave mr-1"></i>
                                    {{ $job->salary_range }}
                                </div>
                            @else
                                <span class="text-gray-500 dark:text-gray-400 text-sm">راتب قابل للتفاوض</span>
                            @endif
                        </div>
                        <a href="{{ route('jobs.show', $job) }}" class="gradient-bg text-white px-4 py-2 rounded-lg hover:opacity-90 hover:scale-105 transition-all duration-300 text-sm font-medium shadow-lg">
                            <i class="fas fa-eye mr-1"></i>
                            عرض التفاصيل
                        </a>
                    </div>
                </div>
                @empty
                <div class="col-span-full">
                    <div class="text-center py-16">
                        <div class="w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-briefcase text-3xl text-gray-400"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">لا توجد وظائف مميزة</h3>
                        <p class="text-gray-600 dark:text-gray-400 mb-6">لم يتم العثور على وظائف مميزة في الوقت الحالي</p>
                        <a href="{{ route('jobs.index') }}" class="gradient-bg text-white px-6 py-3 rounded-lg hover:opacity-90 transition-opacity">
                            تصفح جميع الوظائف
                        </a>
                    </div>
                </div>
                @endforelse
            </div>

            <!-- View All Jobs Button -->
            <div class="text-center mt-12">
                <a href="{{ route('jobs.index') }}" class="inline-flex items-center px-8 py-4 bg-white dark:bg-dark-card text-primary border-2 border-primary rounded-xl hover:bg-primary hover:text-white transition-all duration-300 font-semibold shadow-lg hover:shadow-xl">
                    <i class="fas fa-briefcase mr-2"></i>
                    عرض جميع الوظائف
                    <i class="fas fa-arrow-left mr-2"></i>
                </a>
            </div>
        </div>
    </div>
</section>
