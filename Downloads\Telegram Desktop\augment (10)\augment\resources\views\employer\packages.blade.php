@extends('layouts.employer')

@section('title', 'الباقات والدفع - Hire Me')
@section('header_title', 'الباقات والدفع')

@section('content')
<div class="p-8 space-y-8">
    <!-- Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">الباقات والدفع</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">اختر الباقة المناسبة لاحتياجات شركتك</p>
        </div>
    </div>

    @if(session('warning'))
        <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded-xl mb-6">
            <div class="flex items-center gap-2">
                <i class="fas fa-exclamation-triangle"></i>
                <span>{{ session('warning') }}</span>
            </div>
            @if(session('show_upgrade'))
                <div class="mt-3">
                    <a href="#packages" class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg text-sm transition-colors">
                        ترقية الباقة الآن
                    </a>
                </div>
            @endif
        </div>
    @endif

    <!-- Current Package -->
    <div class="glass-card rounded-xl p-6 border-2 border-blue-200 dark:border-blue-800">
        <div class="flex items-center gap-4 mb-4">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                <i class="fas fa-crown text-white"></i>
            </div>
            <div>
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">الباقة الحالية</h2>
                <p class="text-blue-600 dark:text-blue-400 font-medium">
                    {{ $currentSubscription->getPackageDetails()['name'] ?? 'غير محددة' }}
                </p>
            </div>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="text-center">
                <p class="text-2xl font-bold text-gray-900 dark:text-white">
                    @if($currentSubscription->package_type === 'enterprise')
                        <span class="text-yellow-600 dark:text-yellow-400">∞</span>
                    @else
                        {{ $jobsLeft }}
                    @endif
                </p>
                <p class="text-sm text-gray-600 dark:text-gray-400">وظائف متبقية</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $jobsPostedThisMonth }}</p>
                <p class="text-sm text-gray-600 dark:text-gray-400">وظائف منشورة هذا الشهر</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-bold text-gray-900 dark:text-white">
                    {{ $daysLeft }}
                </p>
                <p class="text-sm text-gray-600 dark:text-gray-400">يوم متبقي</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-bold text-gray-900 dark:text-white">
                    @if($currentSubscription->price == 0)
                        مجاني
                    @else
                        {{ $currentSubscription->price }} دينار ليبي
                    @endif
                </p>
                <p class="text-sm text-gray-600 dark:text-gray-400">السعر الحالي</p>
            </div>
        </div>

        <!-- Progress Bar -->
        @if($currentSubscription->package_type !== 'enterprise')
            <div class="mt-6">
                <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
                    <span>الاستخدام الشهري</span>
                    <span>{{ $jobsPostedThisMonth }} / {{ $currentSubscription->job_limit }}</span>
                </div>
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    @php
                        $percentage = $currentSubscription->job_limit > 0 ? min(100, ($jobsPostedThisMonth / $currentSubscription->job_limit) * 100) : 0;
                        $colorClass = $percentage >= 90 ? 'from-red-500 to-red-600' : ($percentage >= 70 ? 'from-yellow-500 to-orange-600' : 'from-blue-500 to-purple-600');
                    @endphp
                    <div class="bg-gradient-to-r {{ $colorClass }} h-2 rounded-full transition-all duration-300"
                         style="width: {{ $percentage }}%"></div>
                </div>
                <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                    <span>{{ $percentage >= 90 ? 'اقتراب من النهاية' : ($percentage >= 70 ? 'استخدام مرتفع' : 'استخدام طبيعي') }}</span>
                    <span>{{ number_format($percentage, 1) }}%</span>
                </div>
            </div>
        @else
            <div class="mt-6 text-center">
                <div class="inline-flex items-center px-4 py-2 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 rounded-full text-sm font-medium">
                    <i class="fas fa-crown mr-2"></i>
                    وظائف غير محدودة - لا توجد قيود
                </div>
            </div>
        @endif
    </div>

    <!-- Available Packages -->
    <div id="packages" class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        @foreach($packages as $package)
        <div class="glass-card rounded-xl overflow-hidden {{ $package['is_current'] ? 'ring-2 ring-blue-500' : '' }} {{ $package['id'] == 'premium' ? 'transform scale-105 shadow-2xl' : '' }}">
            @if($package['id'] == 'premium')
            <div class="bg-gradient-to-r from-purple-600 to-pink-600 text-white text-center py-2 text-sm font-medium">
                الأكثر شعبية
            </div>
            @endif

            <div class="p-6">
                <!-- Package Header -->
                <div class="text-center mb-6">
                    <div class="w-16 h-16 bg-gradient-to-br {{ $package['id'] == 'basic' ? 'from-gray-500 to-gray-600' : ($package['id'] == 'premium' ? 'from-purple-500 to-purple-600' : 'from-yellow-500 to-yellow-600') }} rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-{{ $package['id'] == 'basic' ? 'user' : ($package['id'] == 'premium' ? 'star' : 'crown') }} text-white text-xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white">{{ $package['name'] }}</h3>
                    <div class="mt-2">
                        @if($package['price'] == 0)
                            <span class="text-3xl font-bold text-green-600 dark:text-green-400">مجاني</span>
                        @else
                            <span class="text-3xl font-bold text-gray-900 dark:text-white">{{ $package['price'] }}</span>
                            <span class="text-gray-600 dark:text-gray-400">دينار ليبي/شهر</span>
                        @endif

                        <!-- Job Limit Display -->
                        <div class="mt-2">
                            @if($package['job_limit'] == -1)
                                <span class="text-sm text-yellow-600 dark:text-yellow-400 font-medium">وظائف غير محدودة</span>
                            @else
                                <span class="text-sm text-blue-600 dark:text-blue-400 font-medium">{{ $package['job_limit'] }} وظائف شهرياً</span>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Features -->
                <div class="space-y-3 mb-6">
                    @foreach($package['features'] as $feature)
                    <div class="flex items-center gap-3">
                        <div class="w-5 h-5 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                            <i class="fas fa-check text-green-600 dark:text-green-400 text-xs"></i>
                        </div>
                        <span class="text-gray-700 dark:text-gray-300 text-sm">{{ $feature }}</span>
                    </div>
                    @endforeach
                </div>

                <!-- Action Button -->
                <div class="text-center">
                    @if($package['is_current'])
                        <button disabled class="w-full px-6 py-3 bg-gray-300 text-gray-500 rounded-xl cursor-not-allowed">
                            الباقة الحالية
                        </button>
                    @else
                        <form action="{{ route('employer.packages.subscribe', $package['id']) }}" method="POST">
                            @csrf
                            <button type="submit" class="w-full px-6 py-3 bg-gradient-to-r {{ $package['id'] == 'premium' ? 'from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700' : ($package['id'] == 'enterprise' ? 'from-yellow-600 to-orange-600 hover:from-yellow-700 hover:to-orange-700' : 'from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700') }} text-white rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                                @if($package['price'] == 0)
                                    الرجوع للباقة المجانية
                                @else
                                    ترقية الآن - {{ $package['price'] }} دينار ليبي
                                @endif
                            </button>
                        </form>
                    @endif
                </div>
            </div>
        </div>
        @endforeach
    </div>

    <!-- Payment History -->
    <div class="glass-card rounded-xl p-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
            <i class="fas fa-history text-green-600"></i>
            سجل المدفوعات
        </h2>

        <div class="overflow-x-auto">
            <table class="w-full">
                <thead>
                    <tr class="border-b border-gray-200 dark:border-gray-700">
                        <th class="text-right py-3 px-4 font-medium text-gray-900 dark:text-white">التاريخ</th>
                        <th class="text-right py-3 px-4 font-medium text-gray-900 dark:text-white">الباقة</th>
                        <th class="text-right py-3 px-4 font-medium text-gray-900 dark:text-white">المبلغ</th>
                        <th class="text-right py-3 px-4 font-medium text-gray-900 dark:text-white">الحالة</th>
                        <th class="text-right py-3 px-4 font-medium text-gray-900 dark:text-white">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    <!-- Sample Data -->
                    <tr>
                        <td class="py-3 px-4 text-gray-700 dark:text-gray-300">2024-01-15</td>
                        <td class="py-3 px-4 text-gray-700 dark:text-gray-300">الباقة المميزة</td>
                        <td class="py-3 px-4 text-gray-700 dark:text-gray-300">45 دينار</td>
                        <td class="py-3 px-4">
                            <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 rounded-full">
                                مكتملة
                            </span>
                        </td>
                        <td class="py-3 px-4">
                            <div class="relative inline-block text-left">
                                <button type="button" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm px-4 py-2 bg-gray-100 rounded-lg" onclick="document.getElementById('invoice-download-menu-1').classList.toggle('hidden')">
                                    <i class="fas fa-download mr-1"></i>
                                    تحميل الفاتورة
                                    <i class="fas fa-chevron-down ml-1"></i>
                                </button>
                                <div id="invoice-download-menu-1" class="hidden absolute left-0 mt-2 w-40 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-10">
                                    <a href="{{ route('employer.invoices.download', ['id' => 1, 'format' => 'pdf']) }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">تحميل PDF</a>
                                    <a href="{{ route('employer.invoices.download', ['id' => 1, 'format' => 'excel']) }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">تحميل Excel</a>
                                </div>
                            </div>
                            <script>
                                document.addEventListener('click', function(e) {
                                    var menu = document.getElementById('invoice-download-menu-1');
                                    if (!e.target.closest('.relative.inline-block')) {
                                        menu.classList.add('hidden');
                                    }
                                });
                            </script>
                        </td>
                    </tr>
                    <tr>
                        <td class="py-3 px-4 text-gray-700 dark:text-gray-300">2023-12-15</td>
                        <td class="py-3 px-4 text-gray-700 dark:text-gray-300">الباقة الأساسية</td>
                        <td class="py-3 px-4 text-gray-700 dark:text-gray-300">0 دينار</td>
                        <td class="py-3 px-4">
                            <span class="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400 rounded-full">
                                مجانية
                            </span>
                        </td>
                        <td class="py-3 px-4">
                            <span class="text-gray-400 text-sm">-</span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- FAQ -->
    <div class="glass-card rounded-xl p-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
            <i class="fas fa-question-circle text-purple-600"></i>
            الأسئلة الشائعة
        </h2>

        <div class="space-y-4">
            <div class="border border-gray-200 dark:border-gray-700 rounded-lg">
                <button class="w-full text-right px-4 py-3 font-medium text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors" onclick="toggleFaq(1)">
                    كيف يمكنني ترقية باقتي؟
                    <i class="fas fa-chevron-down float-left mt-1"></i>
                </button>
                <div id="faq-1" class="hidden px-4 pb-3 text-gray-600 dark:text-gray-400">
                    يمكنك ترقية باقتك في أي وقت بالنقر على زر "ترقية الآن" في الباقة المرغوبة. سيتم تطبيق التغيير فوراً.
                </div>
            </div>

            <div class="border border-gray-200 dark:border-gray-700 rounded-lg">
                <button class="w-full text-right px-4 py-3 font-medium text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors" onclick="toggleFaq(2)">
                    هل يمكنني إلغاء اشتراكي في أي وقت؟
                    <i class="fas fa-chevron-down float-left mt-1"></i>
                </button>
                <div id="faq-2" class="hidden px-4 pb-3 text-gray-600 dark:text-gray-400">
                    نعم، يمكنك إلغاء اشتراكك في أي وقت. ستستمر في الاستفادة من مزايا الباقة حتى نهاية فترة الاشتراك.
                </div>
            </div>

            <div class="border border-gray-200 dark:border-gray-700 rounded-lg">
                <button class="w-full text-right px-4 py-3 font-medium text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors" onclick="toggleFaq(3)">
                    ما هي طرق الدفع المتاحة؟
                    <i class="fas fa-chevron-down float-left mt-1"></i>
                </button>
                <div id="faq-3" class="hidden px-4 pb-3 text-gray-600 dark:text-gray-400">
                    نقبل جميع بطاقات الائتمان الرئيسية، التحويل البنكي، وخدمات الدفع الإلكتروني مثل مدى وأبل باي.
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleFaq(id) {
    const element = document.getElementById(`faq-${id}`);
    element.classList.toggle('hidden');
}
</script>
@endsection

@push('styles')
<style>
.package-card {
    transition: all 0.3s ease-in-out;
}

.package-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}
</style>
@endpush
