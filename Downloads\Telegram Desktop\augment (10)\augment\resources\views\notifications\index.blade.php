@extends('layouts.app')

@section('title', 'الإشعارات')

@section('content')
<!-- Hero Section -->
<div class="gradient-bg py-16">
    <div class="p-6">
        <div class="max-w-7xl mx-auto text-center">
            <div class="animate-fade-in">
                <h1 class="text-4xl md:text-5xl font-bold text-white mb-6">
                    <i class="fas fa-bell mr-3"></i>
                    الإشعارات
                </h1>
                <p class="text-xl text-white/90 max-w-3xl mx-auto mb-8">
                    تابع جميع التحديثات والإشعارات المهمة في مكان واحد
                </p>
                
                <!-- Quick Stats -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto">
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                        <div class="text-2xl font-bold text-white">{{ $notifications->total() }}</div>
                        <div class="text-white/80 text-sm">إجمالي الإشعارات</div>
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                        <div class="text-2xl font-bold text-white">{{ $unreadCount }}</div>
                        <div class="text-white/80 text-sm">غير مقروءة</div>
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                        <div class="text-2xl font-bold text-white">{{ $notifications->total() - $unreadCount }}</div>
                        <div class="text-white/80 text-sm">مقروءة</div>
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                        <div class="text-2xl font-bold text-white">{{ $notifications->where('created_at', '>=', now()->subDay())->count() }}</div>
                        <div class="text-white/80 text-sm">اليوم</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="py-16 bg-gray-50 dark:bg-gray-900">
    <div class="p-6">
        <div class="max-w-7xl mx-auto">
            
            <!-- Action Bar -->
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
                <div class="mb-4 sm:mb-0">
                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white">إدارة الإشعارات</h2>
                    <p class="text-gray-600 dark:text-gray-400">تصفح وإدارة جميع إشعاراتك</p>
                </div>
                
                <div class="flex flex-col sm:flex-row gap-3">
                    @if($unreadCount > 0)
                        <form action="{{ route('notifications.mark-all-read') }}" method="POST" class="inline">
                            @csrf
                            <button type="submit" class="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 shadow-lg hover:shadow-xl">
                                <i class="fas fa-check-double mr-2"></i>
                                تحديد الكل كمقروء
                            </button>
                        </form>
                    @endif
                    
                    <button onclick="toggleFilters()" class="bg-white dark:bg-dark-card text-gray-700 dark:text-gray-300 px-6 py-3 rounded-lg font-medium border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                        <i class="fas fa-filter mr-2"></i>
                        فلترة
                    </button>
                    
                    <a href="{{ route('notifications.settings') }}" class="bg-primary hover:bg-primary-dark text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 shadow-lg hover:shadow-xl">
                        <i class="fas fa-cog mr-2"></i>
                        الإعدادات
                    </a>
                </div>
            </div>

            <!-- Filter Panel -->
            <div id="filterPanel" class="hidden mb-8">
                <div class="bg-white dark:bg-dark-card rounded-2xl shadow-sm p-6 border border-gray-100 dark:border-gray-700">
                    <form method="GET" action="{{ route('notifications.index') }}" class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البحث</label>
                                <input type="text" id="search" name="search" value="{{ request('search') }}" 
                                       placeholder="البحث في الإشعارات..."
                                       class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                            </div>
                            
                            <div>
                                <label for="type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع الإشعار</label>
                                <select id="type" name="type" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                                    <option value="">جميع الأنواع</option>
                                    <option value="job_application" {{ request('type') == 'job_application' ? 'selected' : '' }}>طلبات التوظيف</option>
                                    <option value="application_update" {{ request('type') == 'application_update' ? 'selected' : '' }}>تحديثات الطلبات</option>
                                    <option value="job_posted" {{ request('type') == 'job_posted' ? 'selected' : '' }}>وظائف جديدة</option>
                                    <option value="system_announcement" {{ request('type') == 'system_announcement' ? 'selected' : '' }}>إعلانات النظام</option>
                                </select>
                            </div>
                            
                            <div>
                                <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الحالة</label>
                                <select id="status" name="is_read" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                                    <option value="">جميع الإشعارات</option>
                                    <option value="0" {{ request('is_read') === '0' ? 'selected' : '' }}>غير مقروءة</option>
                                    <option value="1" {{ request('is_read') === '1' ? 'selected' : '' }}>مقروءة</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="flex gap-3">
                            <button type="submit" class="bg-primary hover:bg-primary-dark text-white px-6 py-2 rounded-lg font-medium transition-colors">
                                <i class="fas fa-search mr-2"></i>
                                بحث
                            </button>
                            <a href="{{ route('notifications.index') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                                <i class="fas fa-times mr-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Notifications List -->
            <div class="bg-white dark:bg-dark-card rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                        <i class="fas fa-list mr-2"></i>
                        جميع الإشعارات
                    </h3>
                </div>
                
                <div class="p-6">
                    @if($notifications->count() > 0)
                        <div class="space-y-4">
                            @foreach($notifications as $notification)
                                <div class="notification-item {{ !$notification->is_read ? 'unread' : 'read' }} p-4 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300 {{ !$notification->is_read ? 'bg-blue-50 dark:bg-blue-900/10 border-blue-200 dark:border-blue-800' : 'bg-white dark:bg-gray-800' }}">
                                    <div class="flex items-start gap-4">
                                        <!-- Icon -->
                                        <div class="flex-shrink-0">
                                            <div class="w-12 h-12 rounded-full {{ !$notification->is_read ? 'bg-blue-100 dark:bg-blue-900/30' : 'bg-gray-100 dark:bg-gray-700' }} flex items-center justify-center">
                                                <i class="fas fa-{{ getNotificationIcon($notification->type) }} {{ !$notification->is_read ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500' }}"></i>
                                            </div>
                                        </div>
                                        
                                        <!-- Content -->
                                        <div class="flex-1 min-w-0">
                                            <div class="flex items-start justify-between">
                                                <div class="flex-1">
                                                    <p class="text-sm font-medium text-gray-900 dark:text-white mb-1">
                                                        {{ getNotificationTitle($notification->type) }}
                                                    </p>
                                                    <p class="text-gray-700 dark:text-gray-300 mb-2">
                                                        {{ $notification->message }}
                                                    </p>
                                                    <div class="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                                                        <span>
                                                            <i class="fas fa-clock mr-1"></i>
                                                            {{ $notification->created_at->diffForHumans() }}
                                                        </span>
                                                        @if($notification->type)
                                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ getNotificationBadgeClass($notification->type) }}">
                                                                {{ getNotificationTypeLabel($notification->type) }}
                                                            </span>
                                                        @endif
                                                    </div>
                                                </div>
                                                
                                                <!-- Actions -->
                                                <div class="flex items-center gap-2 ml-4">
                                                    @if(!$notification->is_read)
                                                        <span class="w-3 h-3 bg-blue-500 rounded-full"></span>
                                                    @endif
                                                    
                                                    @if($notification->link)
                                                        <a href="{{ route('notifications.mark-read', $notification) }}" 
                                                           class="text-primary hover:text-primary-dark transition-colors">
                                                            <i class="fas fa-external-link-alt"></i>
                                                        </a>
                                                    @endif
                                                    
                                                    <form action="{{ route('notifications.destroy', $notification) }}" method="POST" class="inline">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="text-red-500 hover:text-red-700 transition-colors" 
                                                                onclick="return confirm('هل أنت متأكد من حذف هذا الإشعار؟')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        
                        <!-- Pagination -->
                        <div class="mt-8 flex justify-center">
                            {{ $notifications->appends(request()->query())->links() }}
                        </div>
                    @else
                        <div class="text-center py-12">
                            <div class="w-24 h-24 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-bell-slash text-4xl text-gray-400"></i>
                            </div>
                            <h3 class="text-xl font-medium text-gray-900 dark:text-white mb-2">لا توجد إشعارات</h3>
                            <p class="text-gray-500 dark:text-gray-400 mb-6">ستظهر الإشعارات هنا عند وصولها</p>
                            <a href="{{ route('home') }}" class="bg-primary hover:bg-primary-dark text-white px-6 py-3 rounded-lg font-medium transition-colors">
                                <i class="fas fa-home mr-2"></i>
                                العودة للرئيسية
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleFilters() {
    const panel = document.getElementById('filterPanel');
    panel.classList.toggle('hidden');
}

// Auto-refresh notifications every 30 seconds
setInterval(function() {
    fetch('/notifications/unread')
        .then(response => response.json())
        .then(data => {
            // Update notification badge if exists
            const badge = document.querySelector('.notification-badge');
            if (badge && data.count !== undefined) {
                badge.textContent = data.count;
                badge.style.display = data.count > 0 ? 'flex' : 'none';
            }
        })
        .catch(error => console.log('Auto-refresh failed:', error));
}, 30000);
</script>

@php
function getNotificationIcon($type) {
    $icons = [
        'job_application' => 'briefcase',
        'application_update' => 'sync-alt',
        'job_posted' => 'plus-circle',
        'job_expired' => 'clock',
        'payment_success' => 'check-circle',
        'payment_failed' => 'times-circle',
        'profile_rating' => 'star',
        'company_rating' => 'building',
        'system_announcement' => 'bullhorn',
        'interview_scheduled' => 'calendar-alt',
        'job_recommendation' => 'lightbulb',
        'default' => 'bell'
    ];
    
    return $icons[$type] ?? $icons['default'];
}

function getNotificationTitle($type) {
    $titles = [
        'job_application' => 'طلب توظيف جديد',
        'application_update' => 'تحديث طلب التوظيف',
        'job_posted' => 'وظيفة جديدة',
        'job_expired' => 'انتهاء صلاحية وظيفة',
        'payment_success' => 'دفع ناجح',
        'payment_failed' => 'فشل في الدفع',
        'profile_rating' => 'تقييم الملف الشخصي',
        'company_rating' => 'تقييم الشركة',
        'system_announcement' => 'إعلان النظام',
        'interview_scheduled' => 'جدولة مقابلة',
        'job_recommendation' => 'توصية وظيفة',
        'default' => 'إشعار'
    ];
    
    return $titles[$type] ?? $titles['default'];
}

function getNotificationTypeLabel($type) {
    $labels = [
        'job_application' => 'طلب توظيف',
        'application_update' => 'تحديث طلب',
        'job_posted' => 'وظيفة جديدة',
        'job_expired' => 'وظيفة منتهية',
        'payment_success' => 'دفع',
        'payment_failed' => 'دفع',
        'profile_rating' => 'تقييم',
        'company_rating' => 'تقييم',
        'system_announcement' => 'إعلان',
        'interview_scheduled' => 'مقابلة',
        'job_recommendation' => 'توصية',
        'default' => 'عام'
    ];
    
    return $labels[$type] ?? $labels['default'];
}

function getNotificationBadgeClass($type) {
    $classes = [
        'job_application' => 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
        'application_update' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300',
        'job_posted' => 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
        'job_expired' => 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300',
        'payment_success' => 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
        'payment_failed' => 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300',
        'profile_rating' => 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',
        'company_rating' => 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',
        'system_announcement' => 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300',
        'interview_scheduled' => 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300',
        'job_recommendation' => 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900/30 dark:text-cyan-300',
        'default' => 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
    ];
    
    return $classes[$type] ?? $classes['default'];
}
@endphp
@endsection
