@extends('layouts.admin')

@section('title', 'المرشحون المحفوظون - Hire Me')
@section('header_title', 'المرشحون المحفوظون')

@section('content')
<div class="p-6">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
            <h2 class="text-2xl font-bold">المرشحون المحفوظون</h2>
            <p class="text-gray-600 dark:text-gray-400 mt-1">قائمة المرشحين الذين تم حفظهم للرجوع إليهم لاحقًا</p>
        </div>
        <div class="mt-4 md:mt-0 flex gap-3">
            <button class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors flex items-center gap-2">
                <i class="fas fa-filter"></i>
                <span>تصفية</span>
            </button>
            <button class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors flex items-center gap-2">
                <i class="fas fa-download"></i>
                <span>تصدير</span>
            </button>
            <button class="px-4 py-2 gradient-bg text-white rounded-lg hover:opacity-90 transition-colors flex items-center gap-2">
                <i class="fas fa-plus"></i>
                <span>إضافة مرشح</span>
            </button>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6 mb-6">
        <div class="flex flex-col md:flex-row gap-4">
            <div class="flex-1">
                <div class="relative">
                    <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" name="search" id="search" class="block w-full pr-10 p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" placeholder="بحث عن اسم أو مهارة أو منصب...">
                </div>
            </div>
            <div class="w-full md:w-48">
                <select id="skillFilter" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                    <option value="">المهارات</option>
                    <option value="frontend">تطوير واجهات</option>
                    <option value="backend">تطوير خلفيات</option>
                    <option value="design">تصميم</option>
                    <option value="marketing">تسويق</option>
                    <option value="management">إدارة</option>
                </select>
            </div>
            <div class="w-full md:w-48">
                <select id="ratingFilter" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                    <option value="">التقييم</option>
                    <option value="5">5 نجوم</option>
                    <option value="4">4 نجوم وأعلى</option>
                    <option value="3">3 نجوم وأعلى</option>
                    <option value="2">2 نجوم وأعلى</option>
                    <option value="1">1 نجمة وأعلى</option>
                </select>
            </div>
            <div class="w-full md:w-48">
                <select id="tagFilter" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                    <option value="">الوسم</option>
                    <option value="potential">مرشح محتمل</option>
                    <option value="interview">للمقابلة</option>
                    <option value="contacted">تم التواصل</option>
                    <option value="rejected">مرفوض</option>
                    <option value="future">للوظائف المستقبلية</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Candidates Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
        @forelse($candidates ?? [] as $candidate)
            <!-- Candidate Card -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center gap-3">
                            <div class="w-14 h-14 bg-{{ $candidate->color ?? 'blue' }}-100 dark:bg-{{ $candidate->color ?? 'blue' }}-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-user text-{{ $candidate->color ?? 'blue' }}-600 dark:text-{{ $candidate->color ?? 'blue' }}-400 text-xl"></i>
                            </div>
                            <div>
                                <h3 class="font-bold text-lg">{{ $candidate->name }}</h3>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">{{ $candidate->title }}</p>
                            </div>
                        </div>
                        <div>
                            <div class="flex items-center gap-1">
                                @for($i = 1; $i <= 5; $i++)
                                    @if($i <= $candidate->rating)
                                        <i class="fas fa-star text-yellow-400 text-xs"></i>
                                    @else
                                        <i class="far fa-star text-gray-300 dark:text-gray-600 text-xs"></i>
                                    @endif
                                @endfor
                            </div>
                        </div>
                    </div>
                    
                    <!-- Tags -->
                    <div class="flex flex-wrap gap-2 mb-4">
                        @foreach($candidate->tags ?? [] as $tag)
                            <span class="px-2 py-1 bg-{{ $tag->color }}-100 dark:bg-{{ $tag->color }}-900/30 text-{{ $tag->color }}-700 dark:text-{{ $tag->color }}-400 rounded-full text-xs">{{ $tag->name }}</span>
                        @endforeach
                    </div>
                    
                    <!-- Skills -->
                    <div class="mb-4">
                        <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">المهارات:</p>
                        <div class="flex flex-wrap gap-1">
                            @foreach($candidate->skills ?? [] as $skill)
                                <span class="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs">{{ $skill }}</span>
                            @endforeach
                        </div>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-gray-500 dark:text-gray-400 text-sm">تم الحفظ: {{ $candidate->saved_at }}</span>
                        <a href="{{ route('admin.candidates.show', $candidate->id) }}" class="text-primary hover:underline text-sm">عرض الملف</a>
                    </div>
                </div>
            </div>
        @empty
            <!-- Sample candidate cards for display -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center gap-3">
                            <div class="w-14 h-14 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-user text-blue-600 dark:text-blue-400 text-xl"></i>
                            </div>
                            <div>
                                <h3 class="font-bold text-lg">أحمد محمد</h3>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">مطور واجهات أمامية</p>
                            </div>
                        </div>
                        <div>
                            <div class="flex items-center gap-1">
                                <i class="fas fa-star text-yellow-400 text-xs"></i>
                                <i class="fas fa-star text-yellow-400 text-xs"></i>
                                <i class="fas fa-star text-yellow-400 text-xs"></i>
                                <i class="fas fa-star text-yellow-400 text-xs"></i>
                                <i class="far fa-star text-gray-300 dark:text-gray-600 text-xs"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Tags -->
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="px-2 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 rounded-full text-xs">للمقابلة</span>
                        <span class="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">مرشح محتمل</span>
                    </div>
                    
                    <!-- Skills -->
                    <div class="mb-4">
                        <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">المهارات:</p>
                        <div class="flex flex-wrap gap-1">
                            <span class="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs">HTML</span>
                            <span class="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs">CSS</span>
                            <span class="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs">JavaScript</span>
                            <span class="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs">React</span>
                        </div>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-gray-500 dark:text-gray-400 text-sm">تم الحفظ: منذ 3 أيام</span>
                        <a href="{{ route('admin.candidates.show', 1) }}" class="text-primary hover:underline text-sm">عرض الملف</a>
                    </div>
                </div>
            </div>
            
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center gap-3">
                            <div class="w-14 h-14 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-user text-green-600 dark:text-green-400 text-xl"></i>
                            </div>
                            <div>
                                <h3 class="font-bold text-lg">سارة عبدالله</h3>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">مصممة واجهات المستخدم</p>
                            </div>
                        </div>
                        <div>
                            <div class="flex items-center gap-1">
                                <i class="fas fa-star text-yellow-400 text-xs"></i>
                                <i class="fas fa-star text-yellow-400 text-xs"></i>
                                <i class="fas fa-star text-yellow-400 text-xs"></i>
                                <i class="fas fa-star text-yellow-400 text-xs"></i>
                                <i class="fas fa-star text-yellow-400 text-xs"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Tags -->
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 rounded-full text-xs">تم التواصل</span>
                        <span class="px-2 py-1 bg-indigo-100 dark:bg-indigo-900/30 text-indigo-700 dark:text-indigo-400 rounded-full text-xs">للوظائف المستقبلية</span>
                    </div>
                    
                    <!-- Skills -->
                    <div class="mb-4">
                        <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">المهارات:</p>
                        <div class="flex flex-wrap gap-1">
                            <span class="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs">Figma</span>
                            <span class="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs">Adobe XD</span>
                            <span class="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs">UI/UX Design</span>
                            <span class="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs">Sketch</span>
                        </div>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-gray-500 dark:text-gray-400 text-sm">تم الحفظ: منذ أسبوع</span>
                        <a href="{{ route('admin.candidates.show', 2) }}" class="text-primary hover:underline text-sm">عرض الملف</a>
                    </div>
                </div>
            </div>
            
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center gap-3">
                            <div class="w-14 h-14 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-user text-purple-600 dark:text-purple-400 text-xl"></i>
                            </div>
                            <div>
                                <h3 class="font-bold text-lg">محمد خالد</h3>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">مطور تطبيقات الجوال</p>
                            </div>
                        </div>
                        <div>
                            <div class="flex items-center gap-1">
                                <i class="fas fa-star text-yellow-400 text-xs"></i>
                                <i class="fas fa-star text-yellow-400 text-xs"></i>
                                <i class="fas fa-star text-yellow-400 text-xs"></i>
                                <i class="fas fa-star text-yellow-400 text-xs"></i>
                                <i class="far fa-star text-gray-300 dark:text-gray-600 text-xs"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Tags -->
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400 rounded-full text-xs">للمقابلة</span>
                    </div>
                    
                    <!-- Skills -->
                    <div class="mb-4">
                        <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">المهارات:</p>
                        <div class="flex flex-wrap gap-1">
                            <span class="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs">Flutter</span>
                            <span class="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs">React Native</span>
                            <span class="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs">Swift</span>
                            <span class="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs">Kotlin</span>
                        </div>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-gray-500 dark:text-gray-400 text-sm">تم الحفظ: منذ 5 أيام</span>
                        <a href="{{ route('admin.candidates.show', 3) }}" class="text-primary hover:underline text-sm">عرض الملف</a>
                    </div>
                </div>
            </div>
            
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center gap-3">
                            <div class="w-14 h-14 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-user text-red-600 dark:text-red-400 text-xl"></i>
                            </div>
                            <div>
                                <h3 class="font-bold text-lg">نورة سعيد</h3>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">مديرة تسويق</p>
                            </div>
                        </div>
                        <div>
                            <div class="flex items-center gap-1">
                                <i class="fas fa-star text-yellow-400 text-xs"></i>
                                <i class="fas fa-star text-yellow-400 text-xs"></i>
                                <i class="fas fa-star text-yellow-400 text-xs"></i>
                                <i class="far fa-star text-gray-300 dark:text-gray-600 text-xs"></i>
                                <i class="far fa-star text-gray-300 dark:text-gray-600 text-xs"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Tags -->
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">مرشح محتمل</span>
                        <span class="px-2 py-1 bg-indigo-100 dark:bg-indigo-900/30 text-indigo-700 dark:text-indigo-400 rounded-full text-xs">للوظائف المستقبلية</span>
                    </div>
                    
                    <!-- Skills -->
                    <div class="mb-4">
                        <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">المهارات:</p>
                        <div class="flex flex-wrap gap-1">
                            <span class="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs">التسويق الرقمي</span>
                            <span class="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs">إدارة الحملات</span>
                            <span class="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs">التسويق عبر وسائل التواصل</span>
                        </div>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-gray-500 dark:text-gray-400 text-sm">تم الحفظ: منذ أسبوعين</span>
                        <a href="{{ route('admin.candidates.show', 4) }}" class="text-primary hover:underline text-sm">عرض الملف</a>
                    </div>
                </div>
            </div>
            
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center gap-3">
                            <div class="w-14 h-14 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-user text-yellow-600 dark:text-yellow-400 text-xl"></i>
                            </div>
                            <div>
                                <h3 class="font-bold text-lg">خالد العتيبي</h3>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">مدير مشاريع</p>
                            </div>
                        </div>
                        <div>
                            <div class="flex items-center gap-1">
                                <i class="fas fa-star text-yellow-400 text-xs"></i>
                                <i class="fas fa-star text-yellow-400 text-xs"></i>
                                <i class="fas fa-star text-yellow-400 text-xs"></i>
                                <i class="fas fa-star text-yellow-400 text-xs"></i>
                                <i class="far fa-star text-gray-300 dark:text-gray-600 text-xs"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Tags -->
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 rounded-full text-xs">تم التواصل</span>
                    </div>
                    
                    <!-- Skills -->
                    <div class="mb-4">
                        <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">المهارات:</p>
                        <div class="flex flex-wrap gap-1">
                            <span class="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs">إدارة المشاريع</span>
                            <span class="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs">Agile</span>
                            <span class="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs">Scrum</span>
                            <span class="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs">MS Project</span>
                        </div>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-gray-500 dark:text-gray-400 text-sm">تم الحفظ: منذ 10 أيام</span>
                        <a href="{{ route('admin.candidates.show', 5) }}" class="text-primary hover:underline text-sm">عرض الملف</a>
                    </div>
                </div>
            </div>
            
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center gap-3">
                            <div class="w-14 h-14 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-user text-indigo-600 dark:text-indigo-400 text-xl"></i>
                            </div>
                            <div>
                                <h3 class="font-bold text-lg">ليلى علي</h3>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">محلل بيانات</p>
                            </div>
                        </div>
                        <div>
                            <div class="flex items-center gap-1">
                                <i class="fas fa-star text-yellow-400 text-xs"></i>
                                <i class="fas fa-star text-yellow-400 text-xs"></i>
                                <i class="fas fa-star text-yellow-400 text-xs"></i>
                                <i class="fas fa-star text-yellow-400 text-xs"></i>
                                <i class="fas fa-star text-yellow-400 text-xs"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Tags -->
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="px-2 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 rounded-full text-xs">للمقابلة</span>
                        <span class="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">مرشح محتمل</span>
                    </div>
                    
                    <!-- Skills -->
                    <div class="mb-4">
                        <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">المهارات:</p>
                        <div class="flex flex-wrap gap-1">
                            <span class="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs">Python</span>
                            <span class="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs">R</span>
                            <span class="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs">SQL</span>
                            <span class="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs">Power BI</span>
                        </div>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-gray-500 dark:text-gray-400 text-sm">تم الحفظ: منذ 3 أيام</span>
                        <a href="{{ route('admin.candidates.show', 6) }}" class="text-primary hover:underline text-sm">عرض الملف</a>
                    </div>
                </div>
            </div>
        @endforelse
    </div>
    
    <!-- Pagination -->
    <div class="flex items-center justify-between">
        <div class="text-sm text-gray-700 dark:text-gray-300">
            عرض <span class="font-medium">1</span> إلى <span class="font-medium">6</span> من <span class="font-medium">{{ $totalCandidates ?? '28' }}</span> النتائج
        </div>
        <div class="flex items-center space-x-2 rtl:space-x-reverse">
            <a href="#" class="px-3 py-1 text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">السابق</a>
            <a href="#" class="px-3 py-1 text-white bg-primary border border-primary rounded-md">1</a>
            <a href="#" class="px-3 py-1 text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">2</a>
            <a href="#" class="px-3 py-1 text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">3</a>
            <a href="#" class="px-3 py-1 text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">التالي</a>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Filter candidates
    const searchInput = document.getElementById('search');
    const skillFilter = document.getElementById('skillFilter');
    const ratingFilter = document.getElementById('ratingFilter');
    const tagFilter = document.getElementById('tagFilter');
    
    // Add event listeners for filters (in a real app, these would trigger AJAX requests or form submissions)
    [searchInput, skillFilter, ratingFilter, tagFilter].forEach(element => {
        element.addEventListener('change', () => {
            console.log('Filtering candidates with:', {
                search: searchInput.value,
                skill: skillFilter.value,
                rating: ratingFilter.value,
                tag: tagFilter.value
            });
        });
    });
    
    // Search as you type
    searchInput.addEventListener('input', () => {
        console.log('Searching for:', searchInput.value);
    });
</script>
@endsection