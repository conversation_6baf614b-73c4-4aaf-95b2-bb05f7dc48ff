# إعداد تسجيل الدخول الاجتماعي

هذا الدليل يوضح كيفية إعداد تسجيل الدخول باستخدام Google و LinkedIn في تطبيق Hire Me.

## المتطلبات

1. <PERSON><PERSON><PERSON><PERSON> Google Developer Console
2. حساب LinkedIn Developer
3. Laravel Socialite مثبت (موجود بالفعل)

## إعداد Google OAuth

### 1. إنشاء مشروع في Google Cloud Console

1. اذهب إلى [Google Cloud Console](https://console.cloud.google.com/)
2. أنشئ مشروع جديد أو اختر مشروع موجود
3. فعّل Google+ API أو People API

### 2. إنشاء OAuth 2.0 Credentials

1. اذهب إلى "APIs & Services" > "Credentials"
2. انقر على "Create Credentials" > "OAuth 2.0 Client IDs"
3. اختر "Web application"
4. أضف Authorized redirect URIs:
   - للتطوير: `http://localhost:8000/auth/google/callback`
   - للإنتاج: `https://yourdomain.com/auth/google/callback`

### 3. إضافة المتغيرات إلى .env

```env
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_REDIRECT_URI=http://localhost:8000/auth/google/callback
```

## إعداد LinkedIn OAuth

### 1. إنشاء تطبيق في LinkedIn Developer

1. اذهب إلى [LinkedIn Developer Portal](https://www.linkedin.com/developers/)
2. انقر على "Create App"
3. املأ المعلومات المطلوبة
4. اختر "Sign In with LinkedIn" في Products

### 2. إعداد OAuth 2.0 settings

1. في صفحة التطبيق، اذهب إلى "Auth" tab
2. أضف Authorized redirect URLs:
   - للتطوير: `http://localhost:8000/auth/linkedin/callback`
   - للإنتاج: `https://yourdomain.com/auth/linkedin/callback`

### 3. إضافة المتغيرات إلى .env

```env
LINKEDIN_CLIENT_ID=your_linkedin_client_id
LINKEDIN_CLIENT_SECRET=your_linkedin_client_secret
LINKEDIN_REDIRECT_URI=http://localhost:8000/auth/linkedin/callback
```

## الصلاحيات المطلوبة

### Google
- `email`
- `profile`

### LinkedIn
- `r_liteprofile` (للحصول على المعلومات الأساسية)
- `r_emailaddress` (للحصول على البريد الإلكتروني)

## اختبار الإعداد

1. تأكد من أن جميع المتغيرات موجودة في ملف `.env`
2. شغّل الخادم: `php artisan serve`
3. اذهب إلى صفحة تسجيل الدخول
4. جرب تسجيل الدخول باستخدام Google أو LinkedIn

## حل المشاكل الشائعة

### خطأ "Invalid redirect URI"
- تأكد من أن الـ redirect URI في إعدادات التطبيق يطابق الـ URL في ملف `.env`
- تأكد من عدم وجود مسافات إضافية في الـ URLs

### خطأ "Client ID not found"
- تأكد من صحة GOOGLE_CLIENT_ID أو LINKEDIN_CLIENT_ID
- تأكد من أن التطبيق مفعّل في وحدة التحكم

### خطأ "Scope not authorized"
- تأكد من أن الصلاحيات المطلوبة مفعّلة في إعدادات التطبيق
- لـ LinkedIn: تأكد من أن "Sign In with LinkedIn" مفعّل

### خطأ "Email not provided"
- لـ LinkedIn: تأكد من أن `r_emailaddress` مضاف إلى الصلاحيات
- لـ Google: تأكد من أن `email` scope مضاف

## ملاحظات أمنية

1. لا تشارك Client Secret مع أي شخص
2. استخدم HTTPS في الإنتاج
3. قم بتحديث redirect URIs عند تغيير النطاق
4. راجع صلاحيات التطبيق بانتظام

## الدعم

إذا واجهت مشاكل في الإعداد:
1. تحقق من logs Laravel: `storage/logs/laravel.log`
2. تحقق من وحدة تحكم المتصفح للأخطاء JavaScript
3. تأكد من أن Socialite محدث إلى أحدث إصدار
