<?php $__env->startSection('title', 'الإعدادات العامة - Hire Me'); ?>
<?php $__env->startSection('header_title', 'الإعدادات العامة'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <div class="mb-6">
        <h2 class="text-2xl font-bold">الإعدادات العامة</h2>
        <p class="text-gray-600 dark:text-gray-400 mt-1">إدارة إعدادات النظام وتفضيلات الحساب</p>
    </div>

    <!-- Success Message -->
    <?php if(session('success')): ?>
        <div class="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg">
            <div class="flex items-center">
                <i class="fas fa-check-circle ml-2"></i>
                <?php echo e(session('success')); ?>

            </div>
        </div>
    <?php endif; ?>

    <!-- Error Messages -->
    <?php if($errors->any()): ?>
        <div class="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
            <div class="flex items-center mb-2">
                <i class="fas fa-exclamation-circle ml-2"></i>
                <span class="font-medium">يرجى تصحيح الأخطاء التالية:</span>
            </div>
            <ul class="list-disc list-inside">
                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li><?php echo e($error); ?></li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
        </div>
    <?php endif; ?>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Settings Form -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Company Information -->
            <form action="<?php echo e(route('admin.settings.general.update')); ?>" method="POST" class="space-y-6" id="settingsForm">
                <?php echo csrf_field(); ?>

                <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                    <h3 class="text-lg font-bold mb-4">معلومات الشركة</h3>

                    <div class="space-y-4">
                        <div>
                            <label for="company_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">اسم الشركة <span class="text-red-600">*</span></label>
                            <input type="text" name="company_name" id="company_name" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" value="<?php echo e($settings->company_name ?? 'شركة التقنية المتطورة'); ?>" required>
                        </div>

                        <div>
                            <label for="company_tagline" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">شعار الشركة</label>
                            <input type="text" name="company_tagline" id="company_tagline" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" value="<?php echo e($settings->company_tagline ?? 'حلول تقنية متطورة لعالم متغير'); ?>">
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="industry" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">المجال <span class="text-red-600">*</span></label>
                                <select name="industry" id="industry" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" required>
                                    <option value="">اختر المجال</option>
                                    <option value="tech" <?php echo e(($settings->industry ?? 'tech') == 'tech' ? 'selected' : ''); ?>>تقنية المعلومات</option>
                                    <option value="healthcare" <?php echo e(($settings->industry ?? '') == 'healthcare' ? 'selected' : ''); ?>>الرعاية الصحية</option>
                                    <option value="education" <?php echo e(($settings->industry ?? '') == 'education' ? 'selected' : ''); ?>>التعليم</option>
                                    <option value="finance" <?php echo e(($settings->industry ?? '') == 'finance' ? 'selected' : ''); ?>>المالية والمصرفية</option>
                                    <option value="retail" <?php echo e(($settings->industry ?? '') == 'retail' ? 'selected' : ''); ?>>التجزئة</option>
                                    <option value="manufacturing" <?php echo e(($settings->industry ?? '') == 'manufacturing' ? 'selected' : ''); ?>>الصناعة</option>
                                    <option value="consulting" <?php echo e(($settings->industry ?? '') == 'consulting' ? 'selected' : ''); ?>>الاستشارات</option>
                                    <option value="media" <?php echo e(($settings->industry ?? '') == 'media' ? 'selected' : ''); ?>>الإعلام والاتصالات</option>
                                    <option value="other" <?php echo e(($settings->industry ?? '') == 'other' ? 'selected' : ''); ?>>أخرى</option>
                                </select>
                            </div>

                            <div>
                                <label for="company_size" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">حجم الشركة</label>
                                <select name="company_size" id="company_size" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                                    <option value="">اختر حجم الشركة</option>
                                    <option value="1-10" <?php echo e(($settings->company_size ?? '51-200') == '1-10' ? 'selected' : ''); ?>>1-10 موظفين</option>
                                    <option value="11-50" <?php echo e(($settings->company_size ?? '') == '11-50' ? 'selected' : ''); ?>>11-50 موظف</option>
                                    <option value="51-200" <?php echo e(($settings->company_size ?? '51-200') == '51-200' ? 'selected' : ''); ?>>51-200 موظف</option>
                                    <option value="201-500" <?php echo e(($settings->company_size ?? '') == '201-500' ? 'selected' : ''); ?>>201-500 موظف</option>
                                    <option value="501-1000" <?php echo e(($settings->company_size ?? '') == '501-1000' ? 'selected' : ''); ?>>501-1000 موظف</option>
                                    <option value="1000+" <?php echo e(($settings->company_size ?? '') == '1000+' ? 'selected' : ''); ?>>أكثر من 1000 موظف</option>
                                </select>
                            </div>
                        </div>

                        <div>
                            <label for="about_company" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">نبذة عن الشركة</label>
                            <textarea name="about_company" id="about_company" rows="4" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base"><?php echo e($settings->about_company ?? 'شركة رائدة في مجال تقنية المعلومات، متخصصة في تطوير البرمجيات وتقديم الحلول التقنية المبتكرة للمؤسسات والشركات. تأسست الشركة في عام 2010 ومنذ ذلك الحين نمت لتصبح من الشركات الرائدة في المنطقة.'); ?></textarea>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                    <h3 class="text-lg font-bold mb-4">معلومات الاتصال</h3>

                    <div class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">البريد الإلكتروني <span class="text-red-600">*</span></label>
                                <input type="email" name="email" id="email" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" value="<?php echo e($settings->email ?? '<EMAIL>'); ?>" required>
                            </div>

                            <div>
                                <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">رقم الهاتف</label>
                                <input type="tel" name="phone" id="phone" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" value="<?php echo e($settings->phone ?? '+966 11 234 5678'); ?>">
                            </div>
                        </div>

                        <div>
                            <label for="address" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">العنوان</label>
                            <input type="text" name="address" id="address" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" value="<?php echo e($settings->address ?? 'شارع الجمهورية، حي الأندلس، طرابلس، ليبيا'); ?>">
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label for="city" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">المدينة</label>
                                <input type="text" name="city" id="city" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" value="<?php echo e($settings->city ?? 'طرابلس'); ?>">
                            </div>

                            <div>
                                <label for="country" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">البلد</label>
                                <input type="text" name="country" id="country" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" value="<?php echo e($settings->country ?? 'ليبيا'); ?>">
                            </div>

                            <div>
                                <label for="postal_code" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الرمز البريدي</label>
                                <input type="text" name="postal_code" id="postal_code" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" value="<?php echo e($settings->postal_code ?? '12345'); ?>">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Social Media Links -->
                <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                    <h3 class="text-lg font-bold mb-4">روابط التواصل الاجتماعي</h3>

                    <div class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="website" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الموقع الإلكتروني</label>
                                <input type="url" name="website" id="website" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" value="<?php echo e($settings->website ?? 'https://www.techcompany.com'); ?>" placeholder="https://example.com">
                            </div>

                            <div>
                                <label for="linkedin" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">لينكد إن</label>
                                <input type="url" name="linkedin" id="linkedin" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" value="<?php echo e($settings->linkedin ?? 'https://www.linkedin.com/company/techcompany'); ?>" placeholder="https://www.linkedin.com/company/yourcompany">
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="facebook" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">فيسبوك</label>
                                <input type="url" name="facebook" id="facebook" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" value="<?php echo e($settings->facebook ?? 'https://www.facebook.com/techcompany'); ?>" placeholder="https://www.facebook.com/yourcompany">
                            </div>

                            <div>
                                <label for="twitter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">تويتر</label>
                                <input type="url" name="twitter" id="twitter" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" value="<?php echo e($settings->twitter ?? 'https://twitter.com/techcompany'); ?>" placeholder="https://twitter.com/yourcompany">
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="instagram" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">انستغرام</label>
                                <input type="url" name="instagram" id="instagram" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" value="<?php echo e($settings->instagram ?? ''); ?>" placeholder="https://www.instagram.com/yourcompany">
                            </div>

                            <div>
                                <label for="youtube" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">يوتيوب</label>
                                <input type="url" name="youtube" id="youtube" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" value="<?php echo e($settings->youtube ?? ''); ?>" placeholder="https://www.youtube.com/c/yourcompany">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Preferences -->
                <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                    <h3 class="text-lg font-bold mb-4">تفضيلات النظام</h3>

                    <div class="space-y-4">
                        <div>
                            <label for="default_language" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">اللغة الافتراضية</label>
                            <select name="default_language" id="default_language" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                                <option value="ar" <?php echo e(($settings->default_language ?? 'ar') == 'ar' ? 'selected' : ''); ?>>العربية</option>
                                <option value="en" <?php echo e(($settings->default_language ?? '') == 'en' ? 'selected' : ''); ?>>English</option>
                            </select>
                        </div>

                        <div>
                            <label for="timezone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">المنطقة الزمنية</label>
                            <select name="timezone" id="timezone" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                                <option value="Africa/Tripoli" <?php echo e(($settings->timezone ?? 'Africa/Tripoli') == 'Africa/Tripoli' ? 'selected' : ''); ?>>طرابلس (GMT+2)</option>
                                <option value="Africa/Cairo" <?php echo e(($settings->timezone ?? '') == 'Africa/Cairo' ? 'selected' : ''); ?>>القاهرة (GMT+2)</option>
                                <option value="Africa/Tunis" <?php echo e(($settings->timezone ?? '') == 'Africa/Tunis' ? 'selected' : ''); ?>>تونس (GMT+1)</option>
                                <option value="Asia/Qatar" <?php echo e(($settings->timezone ?? '') == 'Asia/Qatar' ? 'selected' : ''); ?>>قطر (GMT+3)</option>
                                <option value="Africa/Cairo" <?php echo e(($settings->timezone ?? '') == 'Africa/Cairo' ? 'selected' : ''); ?>>القاهرة (GMT+2)</option>
                                <option value="UTC" <?php echo e(($settings->timezone ?? '') == 'UTC' ? 'selected' : ''); ?>>التوقيت العالمي (UTC)</option>
                            </select>
                        </div>

                        <div class="flex items-center pt-2">
                            <input type="checkbox" name="email_notifications" id="email_notifications" class="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary" <?php echo e(($settings->email_notifications ?? true) ? 'checked' : ''); ?>>
                            <label for="email_notifications" class="mr-2 text-sm font-medium text-gray-700 dark:text-gray-300">تفعيل إشعارات البريد الإلكتروني</label>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" name="application_notifications" id="application_notifications" class="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary" <?php echo e(($settings->application_notifications ?? true) ? 'checked' : ''); ?>>
                            <label for="application_notifications" class="mr-2 text-sm font-medium text-gray-700 dark:text-gray-300">تلقي إشعارات عند استلام طلبات جديدة</label>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" name="message_notifications" id="message_notifications" class="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary" <?php echo e(($settings->message_notifications ?? true) ? 'checked' : ''); ?>>
                            <label for="message_notifications" class="mr-2 text-sm font-medium text-gray-700 dark:text-gray-300">تلقي إشعارات عند استلام رسائل جديدة</label>
                        </div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="flex justify-end space-x-4 rtl:space-x-reverse">
                    <button type="button" class="px-6 py-2.5 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                        إلغاء
                    </button>
                    <button type="submit" class="px-6 py-2.5 gradient-bg text-white rounded-lg hover:opacity-90 transition-colors">
                        حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Company Logo -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">شعار الشركة</h3>

                <div class="text-center mb-4">
                    <div class="w-32 h-32 bg-gray-200 dark:bg-gray-700 rounded-lg mx-auto mb-4 flex items-center justify-center overflow-hidden">
                        <?php if($settings->logo ?? false): ?>
                            <img src="<?php echo e($settings->logo); ?>" alt="Company Logo" class="max-w-full max-h-full">
                        <?php else: ?>
                            <i class="fas fa-building text-gray-400 text-4xl"></i>
                        <?php endif; ?>
                    </div>
                    <p class="text-sm text-gray-500 dark:text-gray-400">الشعار المستخدم في لوحة التحكم والمراسلات</p>
                </div>

                <div class="space-y-3">
                    <button type="button" class="w-full px-4 py-2 gradient-bg text-white rounded-lg hover:opacity-90 transition-colors">
                        تغيير الشعار
                    </button>

                    <?php if($settings->logo ?? false): ?>
                        <button type="button" class="w-full px-4 py-2 border border-red-300 dark:border-red-800 text-red-600 dark:text-red-400 bg-white dark:bg-gray-800 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/10 transition-colors">
                            حذف الشعار
                        </button>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Account Settings -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">إعدادات الحساب</h3>

                <div class="space-y-4">
                    <a href="<?php echo e(route('admin.settings.users')); ?>" class="flex items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                        <div class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-users text-primary"></i>
                        </div>
                        <div class="mr-3">
                            <p class="font-medium">إدارة المستخدمين</p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">إدارة حسابات مستخدمي النظام</p>
                        </div>
                    </a>

                    <a href="<?php echo e(route('admin.settings.payment')); ?>" class="flex items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                        <div class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-credit-card text-primary"></i>
                        </div>
                        <div class="mr-3">
                            <p class="font-medium">إعدادات الدفع والاشتراكات</p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">تعديل باقات الاشتراك وطرق الدفع</p>
                        </div>
                    </a>

                    <a href="#" class="flex items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                        <div class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-bell text-primary"></i>
                        </div>
                        <div class="mr-3">
                            <p class="font-medium">إعدادات الإشعارات</p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">تخصيص إشعارات البريد الإلكتروني</p>
                        </div>
                    </a>

                    <a href="#" class="flex items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                        <div class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-shield-alt text-primary"></i>
                        </div>
                        <div class="mr-3">
                            <p class="font-medium">الأمان والخصوصية</p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">إدارة إعدادات الأمان والخصوصية</p>
                        </div>
                    </a>
                </div>
            </div>

            <!-- Help & Support -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">المساعدة والدعم</h3>

                <div class="space-y-4">
                    <a href="#" class="flex items-center gap-3 text-primary hover:text-primary/80 transition-colors">
                        <i class="fas fa-question-circle"></i>
                        <span>مركز المساعدة</span>
                    </a>

                    <a href="#" class="flex items-center gap-3 text-primary hover:text-primary/80 transition-colors">
                        <i class="fas fa-book"></i>
                        <span>دليل المستخدم</span>
                    </a>

                    <a href="#" class="flex items-center gap-3 text-primary hover:text-primary/80 transition-colors">
                        <i class="fas fa-envelope"></i>
                        <span>اتصل بالدعم</span>
                    </a>
                </div>

                <div class="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <p class="text-sm text-gray-500 dark:text-gray-400">هل تواجه مشكلة؟ فريق الدعم الفني متاح على مدار الساعة.</p>
                    <button class="w-full mt-2 px-4 py-2 border border-primary text-primary bg-white dark:bg-gray-800 rounded-lg hover:bg-primary/10 transition-colors flex items-center gap-2 justify-center">
                        <i class="fas fa-headset"></i>
                        <span>طلب مساعدة</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>


<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('settingsForm');
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalBtnText = submitBtn.innerHTML;

        // Form submission handling
        form.addEventListener('submit', function(e) {
            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري الحفظ...';

            // Allow form to submit normally
            // The loading state will be reset when page reloads
        });

        // Auto-save draft functionality (optional)
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('change', function() {
                // Save to localStorage as draft
                const formData = new FormData(form);
                const data = {};
                for (let [key, value] of formData.entries()) {
                    data[key] = value;
                }
                localStorage.setItem('settings_draft', JSON.stringify(data));
            });
        });

        // Load draft on page load
        const draft = localStorage.getItem('settings_draft');
        if (draft) {
            try {
                const data = JSON.parse(draft);
                Object.keys(data).forEach(key => {
                    const input = form.querySelector(`[name="${key}"]`);
                    if (input && input.value === '') {
                        if (input.type === 'checkbox') {
                            input.checked = data[key] === 'on';
                        } else {
                            input.value = data[key];
                        }
                    }
                });
            } catch (e) {
                console.log('Error loading draft:', e);
            }
        }

        // Clear draft on successful submission
        if (document.querySelector('.bg-green-100')) {
            localStorage.removeItem('settings_draft');
        }
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\Telegram Desktop\augment (10)\augment\resources\views/admin/settings/general.blade.php ENDPATH**/ ?>