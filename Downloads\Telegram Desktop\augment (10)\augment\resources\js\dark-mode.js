// Check for dark mode preference
document.addEventListener('DOMContentLoaded', function() {
    // On page load or when changing themes, best to add inline in `head` to avoid FOUC
    if (localStorage.theme === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        document.documentElement.classList.add('dark');
    } else {
        document.documentElement.classList.remove('dark');
    }

    // Initialize Alpine.js dark mode state
    window.darkMode = localStorage.theme === 'dark' || 
                      (!('theme' in localStorage) && 
                       window.matchMedia('(prefers-color-scheme: dark)').matches);

    // Toggle dark mode
    document.getElementById('dark-mode-toggle')?.addEventListener('click', function() {
        if (document.documentElement.classList.contains('dark')) {
            document.documentElement.classList.remove('dark');
            localStorage.theme = 'light';
            window.darkMode = false;
        } else {
            document.documentElement.classList.add('dark');
            localStorage.theme = 'dark';
            window.darkMode = true;
        }
    });
});