<!-- Categories Section -->
<section class="py-16 bg-white dark:bg-gray-900">
    <div class="p-6">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-th-large text-primary mr-3"></i>
                    تصفح حسب الفئات
                </h2>
                <p class="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                    استكشف الوظائف المتاحة حسب تصنيفها المهني واختر المجال الذي يناسب مهاراتك واهتماماتك
                </p>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                @php
                    $icons = [
                        'laptop-code' => 'blue',
                        'hand-holding-dollar' => 'green',
                        'bullhorn' => 'purple',
                        'user-doctor' => 'red',
                        'graduation-cap' => 'yellow',
                        'building' => 'indigo',
                        'palette' => 'pink',
                        'gavel' => 'teal'
                    ];
                    $iconIndex = 0;
                @endphp

                @forelse($categories as $category)
                <!-- Category Card -->
                <a href="{{ route('jobs.search', ['category' => $category->id]) }}" class="block group">
                    <div class="bg-white dark:bg-dark-card rounded-2xl shadow-sm hover:shadow-xl transition-all duration-300 p-6 text-center border border-gray-100 dark:border-gray-700 hover:scale-105 hover:border-primary group">
                        @php
                            $iconName = array_keys($icons)[$iconIndex % count($icons)];
                            $iconColor = $icons[$iconName];
                            $iconIndex++;
                        @endphp

                        <div class="relative mb-4">
                            <div class="w-20 h-20 gradient-bg rounded-2xl flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300">
                                @if($category->icon)
                                    <i class="fas fa-{{ $category->icon }} text-3xl text-white"></i>
                                @else
                                    <i class="fas fa-{{ $iconName }} text-3xl text-white"></i>
                                @endif
                            </div>
                            <div class="absolute -top-2 -right-2 w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white text-sm font-bold shadow-lg">
                                {{ $category->jobs_count }}
                            </div>
                        </div>

                        <h3 class="text-lg font-bold mb-2 text-gray-900 dark:text-white group-hover:text-primary transition-colors duration-300">
                            {{ $category->name }}
                        </h3>
                        <p class="text-gray-600 dark:text-gray-400 text-sm">
                            {{ $category->jobs_count }} وظيفة متاحة
                        </p>
                    </div>
                </a>
            @empty
            <!-- Default Categories if none exist in database -->
            <a href="{{ route('jobs.search', ['keyword' => 'تقنية المعلومات']) }}" class="block">
                <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm hover:shadow-md transition p-6 text-center hover:bg-primary hover:text-white dark:hover:bg-primary group">
                    <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-white/20 transition">
                        <i class="fas fa-laptop-code text-2xl text-blue-600 dark:text-blue-400 group-hover:text-white transition"></i>
                    </div>

                    <h3 class="text-xl font-bold mb-2">تقنية المعلومات</h3>
                    <p class="text-gray-600 dark:text-gray-400 group-hover:text-white/80 transition">+1200 وظيفة</p>
                </div>
            </a>

            <a href="{{ route('jobs.search', ['keyword' => 'التمويل والمحاسبة']) }}" class="block">
                <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm hover:shadow-md transition p-6 text-center hover:bg-primary hover:text-white dark:hover:bg-primary group">
                    <div class="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-white/20 transition">
                        <i class="fas fa-hand-holding-dollar text-2xl text-green-600 dark:text-green-400 group-hover:text-white transition"></i>
                    </div>

                    <h3 class="text-xl font-bold mb-2">التمويل والمحاسبة</h3>
                    <p class="text-gray-600 dark:text-gray-400 group-hover:text-white/80 transition">+850 وظيفة</p>
                </div>
            </a>

            <a href="{{ route('jobs.search', ['keyword' => 'التسويق والمبيعات']) }}" class="block">
                <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm hover:shadow-md transition p-6 text-center hover:bg-primary hover:text-white dark:hover:bg-primary group">
                    <div class="w-16 h-16 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-white/20 transition">
                        <i class="fas fa-bullhorn text-2xl text-purple-600 dark:text-purple-400 group-hover:text-white transition"></i>
                    </div>

                    <h3 class="text-xl font-bold mb-2">التسويق والمبيعات</h3>
                    <p class="text-gray-600 dark:text-gray-400 group-hover:text-white/80 transition">+950 وظيفة</p>
                </div>
            </a>

            <a href="{{ route('jobs.search', ['keyword' => 'الرعاية الصحية']) }}" class="block">
                <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm hover:shadow-md transition p-6 text-center hover:bg-primary hover:text-white dark:hover:bg-primary group">
                    <div class="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-white/20 transition">
                        <i class="fas fa-user-doctor text-2xl text-red-600 dark:text-red-400 group-hover:text-white transition"></i>
                    </div>

                    <h3 class="text-xl font-bold mb-2">الرعاية الصحية</h3>
                    <p class="text-gray-600 dark:text-gray-400 group-hover:text-white/80 transition">+750 وظيفة</p>
                </div>
            </a>
            @endforelse
        </div>
    </div>
</section>