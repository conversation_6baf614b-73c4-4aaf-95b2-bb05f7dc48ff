<?php

namespace App\Http\Controllers\Employer;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        // Allow both employer and admin to access this
        $this->middleware(function ($request, $next) {
            $user = auth()->user();
            if (!$user || !in_array($user->role, ['employer', 'admin'])) {
                abort(403, 'Unauthorized');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of users.
     */
    public function index(Request $request)
    {
        $query = User::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Filter by role
        if ($request->filled('role')) {
            $query->where('role', $request->role);
        }

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'verified') {
                $query->whereNotNull('email_verified_at');
            } elseif ($request->status === 'unverified') {
                $query->whereNull('email_verified_at');
            }
        }

        $users = $query->latest()->paginate(15);

        return view('employer.users.index', compact('users'));
    }

    /**
     * Export users data
     */
    public function export(Request $request)
    {
        try {
            $format = $request->input('format', 'csv');

            $query = User::query();

            // Apply same filters as index
            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%");
                });
            }

            if ($request->filled('role')) {
                $query->where('role', $request->role);
            }

            if ($request->filled('status')) {
                if ($request->status === 'verified') {
                    $query->whereNotNull('email_verified_at');
                } elseif ($request->status === 'unverified') {
                    $query->whereNull('email_verified_at');
                }
            }

            $users = $query->orderBy('created_at', 'desc')->get();

            if ($format === 'pdf') {
                return $this->exportUsersPDF($users);
            }

            return $this->exportUsersCSV($users);

        } catch (\Exception $e) {
            // Return a simple error response for debugging
            return response('Export failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Export users as CSV
     */
    private function exportUsersCSV($users)
    {
        $filename = 'users_' . date('Y-m-d_H-i-s') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($users) {
            $file = fopen('php://output', 'w');

            // إضافة BOM للدعم العربي
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            // معلومات التقرير
            fputcsv($file, ['تقرير المستخدمين']);
            fputcsv($file, ['تاريخ التصدير', date('Y-m-d H:i:s')]);
            fputcsv($file, ['عدد المستخدمين', count($users)]);
            fputcsv($file, []);

            // العناوين
            fputcsv($file, [
                'الاسم',
                'البريد الإلكتروني',
                'رقم الهاتف',
                'الدور',
                'الموقع',
                'حالة التفعيل',
                'تاريخ الإنضمام',
                'آخر تسجيل دخول'
            ]);

            // البيانات
            foreach ($users as $user) {
                fputcsv($file, [
                    $user->name,
                    $user->email,
                    $user->phone ?? 'غير محدد',
                    $this->getRoleInArabic($user->role),
                    $user->location ?? 'غير محدد',
                    $user->email_verified_at ? 'مفعل' : 'غير مفعل',
                    $user->created_at->format('Y-m-d H:i'),
                    $user->last_login_at ? $user->last_login_at->format('Y-m-d H:i') : 'لم يسجل دخول'
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export users as PDF
     */
    private function exportUsersPDF($users)
    {
        $stats = [
            'total_users' => $users->count(),
            'verified_users' => $users->whereNotNull('email_verified_at')->count(),
            'admins' => $users->where('role', 'admin')->count(),
            'employers' => $users->where('role', 'employer')->count(),
            'candidates' => $users->where('role', 'candidate')->count(),
            'recent_users' => $users->where('created_at', '>=', now()->subDays(7))->count(),
        ];

        $data = [
            'users' => $users,
            'totalUsers' => $users->count(),
            'stats' => $stats,
            'reportDate' => now()->format('Y-m-d H:i:s'),
            'roleStats' => [
                'admins' => $users->where('role', 'admin')->count(),
                'employers' => $users->where('role', 'employer')->count(),
                'candidates' => $users->where('role', 'candidate')->count(),
            ],
            'verificationStats' => [
                'verified' => $users->whereNotNull('email_verified_at')->count(),
                'unverified' => $users->whereNull('email_verified_at')->count(),
            ]
        ];

        // إنشاء HTML للتقرير
        $html = view('employer.reports.users-pdf', $data)->render();

        return response($html, 200, [
            'Content-Type' => 'text/html; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="users_report_' . date('Y-m-d_H-i-s') . '.html"'
        ]);
    }

    /**
     * Get role name in Arabic
     */
    private function getRoleInArabic($role)
    {
        $roles = [
            'admin' => 'مدير',
            'employer' => 'صاحب عمل',
            'candidate' => 'باحث عن عمل',
            'job_seeker' => 'باحث عن عمل',
        ];

        return $roles[$role] ?? $role;
    }

    /**
     * Show the form for creating a new user.
     */
    public function create()
    {
        return view('employer.users.create');
    }

    /**
     * Store a newly created user.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|in:admin,employer,job_seeker',
            'phone' => 'nullable|string|max:20',
            'location' => 'nullable|string|max:255',
            'verified' => 'boolean',
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->role,
            'phone' => $request->phone,
            'location' => $request->location,
            'email_verified_at' => $request->has('verified') ? now() : null,
        ]);

        return redirect()->route('employer.users.index')
            ->with('success', 'تم إنشاء المستخدم بنجاح');
    }

    /**
     * Display the specified user.
     */
    public function show(User $user)
    {
        return view('employer.users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified user.
     */
    public function edit(User $user)
    {
        return view('employer.users.edit', compact('user'));
    }

    /**
     * Update the specified user.
     */
    public function update(Request $request, User $user)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'phone' => 'nullable|string|max:20',
            'location' => 'nullable|string|max:255',
            'role' => 'required|in:admin,employer,job_seeker',
        ]);

        $user->update([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'location' => $request->location,
            'role' => $request->role,
        ]);

        return redirect()->route('admin.users.index')
            ->with('success', 'تم تحديث المستخدم بنجاح');
    }

    /**
     * Remove the specified user.
     */
    public function destroy(User $user)
    {
        // Prevent deleting the current user
        if ($user->id === auth()->id()) {
            return redirect()->route('admin.users.index')
                ->with('error', 'لا يمكنك حذف حسابك الخاص');
        }

        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', 'تم حذف المستخدم بنجاح');
    }

    /**
     * Toggle user verification status.
     */
    public function toggleVerification(User $user)
    {
        $user->update([
            'email_verified_at' => $user->email_verified_at ? null : now()
        ]);

        $status = $user->email_verified_at ? 'تم تفعيل' : 'تم إلغاء تفعيل';

        return redirect()->back()
            ->with('success', "{$status} المستخدم بنجاح");
    }
}
