@extends('layouts.admin')

@section('title', 'إرسال إشعار جديد - Hire Me')
@section('header_title', 'إرسال إشعار جديد')

@section('content')
<div class="p-8">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="flex items-center gap-4 mb-8">
            <a href="{{ route('admin.notifications.index') }}" class="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors">
                <i class="fas fa-arrow-right text-gray-600 dark:text-gray-400"></i>
            </a>
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">إرسال إشعار جديد</h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">إرسال إشعار مخصص للمستخدمين</p>
            </div>
        </div>

        <!-- Form -->
        <form action="{{ route('admin.notifications.store') }}" method="POST" class="space-y-6">
            @csrf
            
            <div class="glass-card rounded-xl p-8">
                <!-- Message -->
                <div class="mb-6">
                    <label for="message" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <i class="fas fa-comment-alt ml-2"></i>
                        نص الإشعار
                    </label>
                    <textarea 
                        id="message" 
                        name="message" 
                        rows="4" 
                        class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white resize-none"
                        placeholder="اكتب نص الإشعار هنا..."
                        required
                    >{{ old('message') }}</textarea>
                    @error('message')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Link (Optional) -->
                <div class="mb-6">
                    <label for="link" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <i class="fas fa-link ml-2"></i>
                        رابط الإشعار (اختياري)
                    </label>
                    <input 
                        type="url" 
                        id="link" 
                        name="link" 
                        value="{{ old('link') }}"
                        class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                        placeholder="https://example.com"
                    >
                    @error('link')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Notification Type -->
                <div class="mb-6">
                    <label for="type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <i class="fas fa-tag ml-2"></i>
                        نوع الإشعار
                    </label>
                    <select 
                        id="type" 
                        name="type" 
                        class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                        required
                    >
                        <option value="">اختر نوع الإشعار</option>
                        <option value="system_announcement" {{ old('type') == 'system_announcement' ? 'selected' : '' }}>إعلان النظام</option>
                        <option value="urgent" {{ old('type') == 'urgent' ? 'selected' : '' }}>عاجل</option>
                        <option value="info" {{ old('type') == 'info' ? 'selected' : '' }}>معلومات</option>
                        <option value="warning" {{ old('type') == 'warning' ? 'selected' : '' }}>تحذير</option>
                    </select>
                    @error('type')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Recipients -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
                        <i class="fas fa-users ml-2"></i>
                        المستلمون
                    </label>
                    
                    <div class="space-y-4">
                        <!-- All Users -->
                        <label class="flex items-center p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer">
                            <input 
                                type="radio" 
                                name="recipient_type" 
                                value="all" 
                                class="text-blue-600 focus:ring-blue-500"
                                {{ old('recipient_type') == 'all' ? 'checked' : '' }}
                            >
                            <div class="mr-3">
                                <div class="flex items-center gap-2">
                                    <i class="fas fa-globe text-blue-600"></i>
                                    <span class="font-medium text-gray-900 dark:text-white">جميع المستخدمين</span>
                                </div>
                                <p class="text-sm text-gray-500 dark:text-gray-400">إرسال للجميع (أصحاب العمل والباحثين عن عمل)</p>
                            </div>
                        </label>

                        <!-- Employers Only -->
                        <label class="flex items-center p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer">
                            <input 
                                type="radio" 
                                name="recipient_type" 
                                value="employers" 
                                class="text-blue-600 focus:ring-blue-500"
                                {{ old('recipient_type') == 'employers' ? 'checked' : '' }}
                            >
                            <div class="mr-3">
                                <div class="flex items-center gap-2">
                                    <i class="fas fa-building text-purple-600"></i>
                                    <span class="font-medium text-gray-900 dark:text-white">أصحاب العمل فقط</span>
                                    <span class="bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 px-2 py-1 rounded text-xs">{{ $employers->count() }} مستخدم</span>
                                </div>
                                <p class="text-sm text-gray-500 dark:text-gray-400">إرسال لأصحاب العمل والشركات فقط</p>
                            </div>
                        </label>

                        <!-- Job Seekers Only -->
                        <label class="flex items-center p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer">
                            <input 
                                type="radio" 
                                name="recipient_type" 
                                value="job_seekers" 
                                class="text-blue-600 focus:ring-blue-500"
                                {{ old('recipient_type') == 'job_seekers' ? 'checked' : '' }}
                            >
                            <div class="mr-3">
                                <div class="flex items-center gap-2">
                                    <i class="fas fa-user-tie text-green-600"></i>
                                    <span class="font-medium text-gray-900 dark:text-white">الباحثين عن عمل فقط</span>
                                    <span class="bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 px-2 py-1 rounded text-xs">{{ $jobSeekers->count() }} مستخدم</span>
                                </div>
                                <p class="text-sm text-gray-500 dark:text-gray-400">إرسال للباحثين عن عمل فقط</p>
                            </div>
                        </label>

                        <!-- Specific Users -->
                        <label class="flex items-start p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer">
                            <input 
                                type="radio" 
                                name="recipient_type" 
                                value="specific" 
                                class="text-blue-600 focus:ring-blue-500 mt-1"
                                {{ old('recipient_type') == 'specific' ? 'checked' : '' }}
                            >
                            <div class="mr-3 flex-1">
                                <div class="flex items-center gap-2 mb-3">
                                    <i class="fas fa-user-check text-orange-600"></i>
                                    <span class="font-medium text-gray-900 dark:text-white">مستخدمين محددين</span>
                                </div>
                                <p class="text-sm text-gray-500 dark:text-gray-400 mb-3">اختر مستخدمين محددين لإرسال الإشعار إليهم</p>
                                
                                <div id="specific-users" class="max-h-48 overflow-y-auto border border-gray-200 dark:border-gray-700 rounded-lg p-3 space-y-2">
                                    @foreach($users as $user)
                                    <label class="flex items-center gap-3 p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded cursor-pointer">
                                        <input 
                                            type="checkbox" 
                                            name="specific_users[]" 
                                            value="{{ $user->id }}"
                                            class="text-blue-600 focus:ring-blue-500"
                                            {{ in_array($user->id, old('specific_users', [])) ? 'checked' : '' }}
                                        >
                                        <div class="flex items-center gap-2">
                                            <div class="w-8 h-8 rounded-full {{ $user->role == 'employer' ? 'bg-purple-100 dark:bg-purple-900/30' : 'bg-green-100 dark:bg-green-900/30' }} flex items-center justify-center">
                                                <i class="fas fa-{{ $user->role == 'employer' ? 'building' : 'user' }} text-xs {{ $user->role == 'employer' ? 'text-purple-600 dark:text-purple-400' : 'text-green-600 dark:text-green-400' }}"></i>
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $user->name }}</p>
                                                <p class="text-xs text-gray-500 dark:text-gray-400">{{ $user->email }} • {{ $user->role == 'employer' ? 'صاحب عمل' : 'باحث عن عمل' }}</p>
                                            </div>
                                        </div>
                                    </label>
                                    @endforeach
                                </div>
                            </div>
                        </label>
                    </div>
                    
                    @error('recipient_type')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                    @error('specific_users')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Submit Buttons -->
                <div class="flex items-center gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <button 
                        type="submit" 
                        class="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-300 flex items-center gap-2 shadow-lg"
                    >
                        <i class="fas fa-paper-plane"></i>
                        إرسال الإشعار
                    </button>
                    
                    <a 
                        href="{{ route('admin.notifications.index') }}" 
                        class="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors flex items-center gap-2"
                    >
                        <i class="fas fa-times"></i>
                        إلغاء
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@section('scripts')
<script>
// إظهار/إخفاء قائمة المستخدمين المحددين
document.addEventListener('DOMContentLoaded', function() {
    const recipientRadios = document.querySelectorAll('input[name="recipient_type"]');
    const specificUsersDiv = document.getElementById('specific-users');
    
    function toggleSpecificUsers() {
        const specificRadio = document.querySelector('input[name="recipient_type"][value="specific"]');
        if (specificRadio.checked) {
            specificUsersDiv.style.display = 'block';
        } else {
            specificUsersDiv.style.display = 'none';
            // إلغاء تحديد جميع المستخدمين المحددين
            const checkboxes = specificUsersDiv.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => checkbox.checked = false);
        }
    }
    
    recipientRadios.forEach(radio => {
        radio.addEventListener('change', toggleSpecificUsers);
    });
    
    // تشغيل الدالة عند تحميل الصفحة
    toggleSpecificUsers();
});
</script>
@endsection
