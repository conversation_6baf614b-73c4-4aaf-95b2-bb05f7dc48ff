@extends('layouts.employer')

@section('title', 'تفاصيل طلب التوظيف')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-file-alt me-2"></i>
                تفاصيل طلب التوظيف
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('employer.dashboard') }}">لوحة التحكم</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('employer.applications.index') }}">طلبات التوظيف</a></li>
                    <li class="breadcrumb-item active">تفاصيل الطلب</li>
                </ol>
            </nav>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('employer.applications.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للقائمة
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Application Details -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">معلومات المتقدم</h6>
                    <div>
                        @switch($application->status)
                            @case('pending')
                                <span class="badge bg-warning">قيد المراجعة</span>
                                @break
                            @case('reviewed')
                                <span class="badge bg-info">تمت المراجعة</span>
                                @break
                            @case('accepted')
                                <span class="badge bg-success">مقبول</span>
                                @break
                            @case('rejected')
                                <span class="badge bg-danger">مرفوض</span>
                                @break
                        @endswitch
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 text-center mb-4">
                            @if($application->jobSeeker && $application->jobSeeker->user)
                                @if($application->jobSeeker->user->avatar)
                                    <img src="{{ Storage::url($application->jobSeeker->user->avatar) }}"
                                         alt="{{ $application->jobSeeker->user->name }}"
                                         class="rounded-circle mb-3" width="120" height="120">
                                @else
                                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3"
                                         style="width: 120px; height: 120px;">
                                        <span class="text-white fw-bold fs-1">
                                            {{ substr($application->jobSeeker->user->name, 0, 1) }}
                                        </span>
                                    </div>
                                @endif
                                <h5 class="fw-bold">{{ $application->jobSeeker->user->name }}</h5>
                                <p class="text-muted">{{ $application->jobSeeker->user->email }}</p>
                            @else
                                <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3"
                                     style="width: 120px; height: 120px;">
                                    <span class="text-white fw-bold fs-1">؟</span>
                                </div>
                                <h5 class="fw-bold text-muted">مستخدم غير معروف</h5>
                                <p class="text-muted">بيانات غير متوفرة</p>
                            @endif
                        </div>
                        <div class="col-md-8">
                            <h6 class="fw-bold mb-3">معلومات شخصية</h6>
                            @if($application->jobSeeker && $application->jobSeeker->user)
                                <div class="row mb-3">
                                    <div class="col-sm-4"><strong>الاسم:</strong></div>
                                    <div class="col-sm-8">{{ $application->jobSeeker->user->name }}</div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-sm-4"><strong>البريد الإلكتروني:</strong></div>
                                    <div class="col-sm-8">{{ $application->jobSeeker->user->email }}</div>
                                </div>
                                @if($application->jobSeeker->user->phone)
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>رقم الهاتف:</strong></div>
                                        <div class="col-sm-8">{{ $application->jobSeeker->user->phone }}</div>
                                    </div>
                                @endif
                                @if($application->jobSeeker->location)
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>الموقع:</strong></div>
                                        <div class="col-sm-8">{{ $application->jobSeeker->location }}</div>
                                    </div>
                                @endif
                                @if($application->jobSeeker->experience)
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>الخبرة:</strong></div>
                                        <div class="col-sm-8">{{ $application->jobSeeker->experience }}</div>
                                    </div>
                                @endif
                            @else
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    بيانات المتقدم غير متوفرة
                                </div>
                            @endif
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>تاريخ التقديم:</strong></div>
                                <div class="col-sm-8">{{ $application->created_at->format('Y-m-d H:i') }}</div>
                            </div>
                        </div>
                    </div>

                    @if($application->cover_letter)
                        <hr>
                        <h6 class="fw-bold mb-3">رسالة التقديم</h6>
                        <div class="bg-light p-3 rounded">
                            {{ $application->cover_letter }}
                        </div>
                    @endif

                    @if($application->jobSeeker && $application->jobSeeker->skills)
                        <hr>
                        <h6 class="fw-bold mb-3">المهارات</h6>
                        <div class="d-flex flex-wrap gap-2">
                            @foreach(explode(',', $application->jobSeeker->skills) as $skill)
                                <span class="badge bg-secondary">{{ trim($skill) }}</span>
                            @endforeach
                        </div>
                    @endif

                    @if($application->resume_path)
                        <hr>
                        <h6 class="fw-bold mb-3">السيرة الذاتية</h6>
                        <a href="{{ Storage::url($application->resume_path) }}" target="_blank" class="btn btn-outline-primary">
                            <i class="fas fa-download me-2"></i>تحميل السيرة الذاتية
                        </a>
                    @endif
                </div>
            </div>
        </div>

        <!-- Job Details & Actions -->
        <div class="col-lg-4">
            <!-- Job Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">معلومات الوظيفة</h6>
                </div>
                <div class="card-body">
                    <h6 class="fw-bold">{{ $application->job->title }}</h6>
                    <p class="text-muted mb-2">
                        <i class="fas fa-map-marker-alt me-1"></i>{{ $application->job->location }}
                    </p>
                    <p class="text-muted mb-2">
                        <i class="fas fa-briefcase me-1"></i>{{ $application->job->job_type }}
                    </p>
                    @if($application->job->salary_range)
                        <p class="text-muted mb-2">
                            <i class="fas fa-money-bill-wave me-1"></i>{{ $application->job->salary_range }}
                        </p>
                    @endif
                    <a href="{{ route('employer.jobs.show', $application->job) }}" class="btn btn-outline-primary btn-sm">
                        عرض تفاصيل الوظيفة
                    </a>
                </div>
            </div>

            <!-- Actions -->
            @if($application->status == 'pending')
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">الإجراءات</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-success" onclick="updateStatus('accepted')">
                                <i class="fas fa-check me-2"></i>قبول الطلب
                            </button>
                            <button type="button" class="btn btn-danger" onclick="updateStatus('rejected')">
                                <i class="fas fa-times me-2"></i>رفض الطلب
                            </button>
                            <button type="button" class="btn btn-info" onclick="updateStatus('reviewed')">
                                <i class="fas fa-eye me-2"></i>تمت المراجعة
                            </button>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Application Timeline -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">تاريخ الطلب</h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">تم تقديم الطلب</h6>
                                <p class="text-muted small mb-0">{{ $application->created_at->format('Y-m-d H:i') }}</p>
                            </div>
                        </div>
                        @if($application->updated_at != $application->created_at)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-info"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">آخر تحديث</h6>
                                    <p class="text-muted small mb-0">{{ $application->updated_at->format('Y-m-d H:i') }}</p>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updateStatus(status) {
    const statusText = {
        'accepted': 'قبول',
        'rejected': 'رفض',
        'reviewed': 'مراجعة'
    };

    if (confirm(`هل أنت متأكد من ${statusText[status]} هذا الطلب؟`)) {
        fetch(`{{ route('employer.applications.update-status', $application) }}`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ status: status })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء تحديث الحالة');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء تحديث الحالة');
        });
    }
}
</script>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e3e6f0;
}
</style>
@endsection
