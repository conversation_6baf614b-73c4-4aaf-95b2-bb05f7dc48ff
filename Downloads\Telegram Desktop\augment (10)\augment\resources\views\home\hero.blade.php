<!-- Hero Section -->
<div class="relative gradient-bg overflow-hidden">
    <div class="absolute inset-0 bg-black opacity-10"></div>
    <div class="relative p-6">
        <div class="max-w-7xl mx-auto">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center py-16 md:py-24">
                <div class="text-center lg:text-right animate-fade-in">
                    <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight mb-6">
                        ابحث عن وظيفتك المثالية أو اعثر على أفضل المواهب
                    </h1>
                    <p class="text-xl md:text-2xl text-white/90 mb-8 leading-relaxed">
                        منصة متكاملة تجمع بين الباحثين عن عمل وأصحاب العمل في مكان واحد
                    </p>
                    <div class="flex flex-col sm:flex-row justify-center lg:justify-start gap-4">
                        <a href="{{ route('jobs.index') }}" class="px-8 py-4 bg-white text-primary font-semibold rounded-xl shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300">
                            <i class="fas fa-search mr-2"></i>
                            تصفح الوظائف
                        </a>
                        @guest
                        <a href="{{ route('register') }}" class="px-8 py-4 bg-white/20 backdrop-blur-sm text-white font-semibold rounded-xl border border-white/30 hover:bg-white/30 hover:scale-105 transition-all duration-300">
                            <i class="fas fa-user-plus mr-2"></i>
                            انضم إلينا مجاناً
                        </a>
                        @else
                        @if(auth()->user()->isJobSeeker())
                            <a href="{{ route('job-seeker.profile') }}" class="px-8 py-4 bg-white/20 backdrop-blur-sm text-white font-semibold rounded-xl border border-white/30 hover:bg-white/30 hover:scale-105 transition-all duration-300">
                                <i class="fas fa-user mr-2"></i>
                                الملف الشخصي
                            </a>
                        @elseif(auth()->user()->isEmployer())
                            <a href="{{ route('employer.dashboard') }}" class="px-8 py-4 bg-white/20 backdrop-blur-sm text-white font-semibold rounded-xl border border-white/30 hover:bg-white/30 hover:scale-105 transition-all duration-300">
                                <i class="fas fa-building mr-2"></i>
                                لوحة التحكم
                            </a>
                        @elseif(auth()->user()->role === 'admin')
                            <a href="{{ route('admin.dashboard') }}" class="px-8 py-4 bg-white/20 backdrop-blur-sm text-white font-semibold rounded-xl border border-white/30 hover:bg-white/30 hover:scale-105 transition-all duration-300">
                                <i class="fas fa-chart-line mr-2"></i>
                                لوحة الإدارة
                            </a>
                        @endif
                        @endguest
                    </div>
                </div>
                <div class="hidden lg:block animate-fade-in" style="animation-delay: 0.3s">
                    <div class="relative">
                        <div class="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 rounded-3xl blur-3xl opacity-30 animate-pulse"></div>
                        <img src="https://images.unsplash.com/photo-1573497620053-ea5300f94f21?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80"
                             alt="Job Search"
                             class="relative rounded-3xl shadow-2xl hover:scale-105 transition-transform duration-500">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Box -->
    <div class="relative -mt-16 z-10">
        <div class="max-w-5xl mx-auto px-6">
            <div class="bg-white dark:bg-dark-card rounded-2xl shadow-2xl p-6 md:p-8 backdrop-blur-sm border border-gray-100 dark:border-gray-700">
                <div class="text-center mb-6">
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">ابحث عن وظيفتك المثالية</h3>
                    <p class="text-gray-600 dark:text-gray-400">اكتشف آلاف الفرص الوظيفية المتاحة</p>
                </div>
                <form action="{{ route('jobs.search') }}" method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div class="md:col-span-2 relative">
                        <label for="keyword" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-search mr-2 text-primary"></i>
                            الكلمات المفتاحية
                        </label>
                        <div class="relative">
                            <input type="text" name="keyword" id="keyword"
                                   placeholder="مثال: مطور ويب، مصمم جرافيك، محاسب، Laravel"
                                   class="block w-full p-3 pr-12 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base"
                                   autocomplete="off">
                            <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                        </div>

                        <!-- Search Suggestions -->
                        <div id="searchSuggestions" class="absolute top-full left-0 right-0 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg mt-1 z-50 hidden">
                            <div class="p-2">
                                <div class="text-xs text-gray-500 dark:text-gray-400 mb-2 px-2">اقتراحات شائعة:</div>
                                <div class="space-y-1">
                                    <button type="button" onclick="selectSuggestion('مطور ويب')" class="w-full text-right px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-sm">مطور ويب</button>
                                    <button type="button" onclick="selectSuggestion('مصمم جرافيك')" class="w-full text-right px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-sm">مصمم جرافيك</button>
                                    <button type="button" onclick="selectSuggestion('محاسب مالي')" class="w-full text-right px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-sm">محاسب مالي</button>
                                    <button type="button" onclick="selectSuggestion('مسوق رقمي')" class="w-full text-right px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-sm">مسوق رقمي</button>
                                    <button type="button" onclick="selectSuggestion('Laravel')" class="w-full text-right px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-sm">Laravel</button>
                                    <button type="button" onclick="selectSuggestion('PHP')" class="w-full text-right px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-sm">PHP</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <label for="location" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-map-marker-alt mr-2 text-primary"></i>
                            الموقع
                        </label>
                        <input type="text" name="location" id="location"
                               placeholder="المدينة أو المنطقة"
                               class="block w-full p-3 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                    </div>
                    <div class="flex items-end">
                        <button type="submit" class="w-full gradient-bg text-white font-semibold py-3 px-6 rounded-lg hover:opacity-90 hover:scale-105 transition-all duration-300 text-base shadow-lg">
                            <i class="fas fa-search mr-2"></i>
                            ابحث الآن
                        </button>
                    </div>
                </form>

                <!-- Quick Stats -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-primary">{{ \App\Models\Job::count() }}+</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">وظيفة متاحة</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-primary">{{ \App\Models\Employer::count() }}+</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">شركة</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-primary">{{ \App\Models\User::where('role', 'job_seeker')->count() }}+</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">باحث عن عمل</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-primary">{{ \App\Models\Application::count() }}+</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">طلب توظيف</div>
                    </div>
                </div>
            </div>

            <!-- رابط البيانات التجريبية للمطورين -->
            <div class="text-center mt-8">
                <a href="{{ route('test-data') }}" class="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-sm text-white text-sm font-medium rounded-lg border border-white/20 hover:bg-white/20 transition-all duration-300">
                    <i class="fas fa-database mr-2"></i>
                    إنشاء بيانات تجريبية (للمطورين)
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Search suggestions functionality
document.addEventListener('DOMContentLoaded', function() {
    const keywordInput = document.getElementById('keyword');
    const suggestions = document.getElementById('searchSuggestions');

    if (keywordInput && suggestions) {
        // Show suggestions on focus
        keywordInput.addEventListener('focus', function() {
            if (this.value.length === 0) {
                suggestions.classList.remove('hidden');
            }
        });

        // Hide suggestions on blur (with delay for clicks)
        keywordInput.addEventListener('blur', function() {
            setTimeout(() => {
                suggestions.classList.add('hidden');
            }, 200);
        });

        // Hide suggestions when typing
        keywordInput.addEventListener('input', function() {
            if (this.value.length > 0) {
                suggestions.classList.add('hidden');
            } else {
                suggestions.classList.remove('hidden');
            }
        });
    }
});

// Select suggestion function
function selectSuggestion(text) {
    const keywordInput = document.getElementById('keyword');
    const suggestions = document.getElementById('searchSuggestions');

    if (keywordInput) {
        keywordInput.value = text;
        keywordInput.focus();
    }

    if (suggestions) {
        suggestions.classList.add('hidden');
    }
}

// Close suggestions when clicking outside
document.addEventListener('click', function(event) {
    const suggestions = document.getElementById('searchSuggestions');
    const keywordInput = document.getElementById('keyword');

    if (suggestions && keywordInput &&
        !suggestions.contains(event.target) &&
        !keywordInput.contains(event.target)) {
        suggestions.classList.add('hidden');
    }
});
</script>
