@extends('layouts.admin')

@section('title', 'إدارة قاعدة البيانات - Hire Me')
@section('header_title', 'إدارة قاعدة البيانات')

@section('content')
<div class="p-6">
    <div class="mb-6">
        <h2 class="text-2xl font-bold">إدارة قاعدة البيانات</h2>
        <p class="text-gray-600 dark:text-gray-400 mt-1">مراقبة وإدارة قاعدة البيانات والنسخ الاحتياطية</p>
    </div>

    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg">
            <div class="flex items-center">
                <i class="fas fa-check-circle ml-2"></i>
                {{ session('success') }}
            </div>
        </div>
    @endif

    @if(session('error'))
        <div class="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
            <div class="flex items-center">
                <i class="fas fa-exclamation-circle ml-2"></i>
                {{ session('error') }}
            </div>
        </div>
    @endif

    <!-- Database Overview -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- Database Stats -->
        <div class="lg:col-span-2">
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">معلومات قاعدة البيانات</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">اسم قاعدة البيانات</span>
                            <span class="text-sm text-gray-900 dark:text-white font-mono">{{ $stats['database_name'] ?? 'غير محدد' }}</span>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">عدد الجداول</span>
                            <span class="text-sm text-gray-900 dark:text-white">
                                @if(isset($stats['error']))
                                    <span class="text-red-500">خطأ</span>
                                @else
                                    {{ number_format($stats['total_tables'] ?? 0) }}
                                @endif
                            </span>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">حجم قاعدة البيانات</span>
                            <span class="text-sm text-gray-900 dark:text-white">
                                @if(isset($stats['error']))
                                    <span class="text-red-500">غير متاح</span>
                                @else
                                    {{ number_format($stats['database_size'] ?? 0, 2) }} MB
                                @endif
                            </span>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">حالة الاتصال</span>
                            <span class="text-sm {{ isset($stats['error']) ? 'text-red-600' : 'text-green-600' }}">
                                <i class="fas {{ isset($stats['error']) ? 'fa-times-circle' : 'fa-check-circle' }} ml-1"></i>
                                {{ $stats['connection_status'] ?? 'غير معروف' }}
                            </span>
                        </div>

                        @if(isset($stats['table_counts']) && !empty($stats['table_counts']))
                            @foreach($stats['table_counts'] as $table => $count)
                                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        @if($table === 'users') المستخدمون
                                        @elseif($table === 'jobs') الوظائف
                                        @elseif($table === 'applications') الطلبات
                                        @elseif($table === 'notifications') الإشعارات
                                        @else {{ ucfirst($table) }}
                                        @endif
                                    </span>
                                    <span class="text-sm text-gray-900 dark:text-white">{{ number_format($count) }} سجل</span>
                                </div>
                            @endforeach
                        @else
                            <div class="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg text-center">
                                <span class="text-sm text-gray-500 dark:text-gray-400">لا توجد بيانات متاحة</span>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="space-y-6">
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">إجراءات سريعة</h3>

                <div class="space-y-3">
                    <a href="{{ route('admin.database.backup') }}" class="w-full flex items-center gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors">
                        <i class="fas fa-download"></i>
                        <span>النسخ الاحتياطية</span>
                    </a>

                    <a href="{{ route('admin.database.migrations') }}" class="w-full flex items-center gap-3 p-3 bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors">
                        <i class="fas fa-code-branch"></i>
                        <span>إدارة الترحيلات</span>
                    </a>

                    <button onclick="refreshStats()" class="w-full flex items-center gap-3 p-3 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors">
                        <i class="fas fa-sync-alt"></i>
                        <span>تحديث الإحصائيات</span>
                    </button>
                </div>
            </div>

            <!-- System Info -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">معلومات النظام</h3>

                <div class="space-y-3 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">إصدار PHP</span>
                        <span class="text-gray-900 dark:text-white">{{ PHP_VERSION }}</span>
                    </div>

                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">إصدار Laravel</span>
                        <span class="text-gray-900 dark:text-white">{{ app()->version() }}</span>
                    </div>

                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">البيئة</span>
                        <span class="text-gray-900 dark:text-white">{{ app()->environment() }}</span>
                    </div>

                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">وضع التطوير</span>
                        <span class="text-gray-900 dark:text-white">{{ config('app.debug') ? 'مفعل' : 'معطل' }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tables Information -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-bold">جداول قاعدة البيانات</h3>
        </div>

        @if($tables->isNotEmpty())

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-800">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">اسم الجدول</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">عدد السجلات</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الحجم (MB)</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-dark-card divide-y divide-gray-200 dark:divide-gray-700">
                        @foreach($tables as $table)
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <i class="fas fa-table text-gray-400 ml-2"></i>
                                        <span class="text-sm font-medium text-gray-900 dark:text-white font-mono">{{ $table['name'] }}</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                    {{ number_format($table['rows']) }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                    @if($table['size'] > 0)
                                        {{ number_format($table['size'], 2) }}
                                    @else
                                        <span class="text-gray-400">0.00</span>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="px-6 py-12 text-center text-gray-500 dark:text-gray-400">
                <i class="fas fa-table text-4xl mb-4"></i>
                <p class="text-lg font-medium mb-2">لا توجد جداول</p>
                <p>لم يتم العثور على أي جداول في قاعدة البيانات</p>
            </div>
        @endif
    </div>
</div>

@section('scripts')
<script>
    function refreshStats() {
        // Show loading state
        const button = event.target.closest('button');
        const icon = button.querySelector('i');
        const originalClass = icon.className;

        icon.className = 'fas fa-spinner fa-spin';
        button.disabled = true;

        // Reload page after a short delay
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    }
</script>
@endsection
@endsection
