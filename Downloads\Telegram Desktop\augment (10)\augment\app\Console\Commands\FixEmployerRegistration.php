<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use App\Models\Employer;
use App\Models\User;

class FixEmployerRegistration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:employer-registration';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'إصلاح مشكلة تسجيل أصحاب العمل - جعل company_name nullable وإصلاح البيانات الموجودة';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('بدء إصلاح مشكلة تسجيل أصحاب العمل...');

        try {
            // التحقق من وجود جدول employers
            if (!Schema::hasTable('employers')) {
                $this->error('جدول employers غير موجود!');
                return 1;
            }

            // التحقق من وجود العمود company_name
            if (!Schema::hasColumn('employers', 'company_name')) {
                $this->error('العمود company_name غير موجود في جدول employers!');
                return 1;
            }

            $this->info('تعديل جدول employers لجعل company_name nullable...');
            
            // تعديل العمود ليصبح nullable
            Schema::table('employers', function ($table) {
                $table->string('company_name')->nullable()->change();
            });

            $this->info('✅ تم تعديل جدول employers بنجاح!');

            // البحث عن سجلات employers بدون company_name
            $employersWithoutCompanyName = Employer::whereNull('company_name')
                ->orWhere('company_name', '')
                ->with('user')
                ->get();

            if ($employersWithoutCompanyName->count() > 0) {
                $this->info("تم العثور على {$employersWithoutCompanyName->count()} سجل employer بدون company_name. جاري الإصلاح...");

                $bar = $this->output->createProgressBar($employersWithoutCompanyName->count());
                $bar->start();

                foreach ($employersWithoutCompanyName as $employer) {
                    if ($employer->user) {
                        $companyName = $employer->user->name . ' - شركة';
                        $employer->update(['company_name' => $companyName]);
                        $this->line("\n✅ تم إصلاح Employer ID: {$employer->id} - Company Name: {$companyName}");
                    } else {
                        $this->line("\n⚠️  تحذير: Employer ID: {$employer->id} لا يحتوي على user مرتبط");
                    }
                    $bar->advance();
                }

                $bar->finish();
                $this->newLine();
            } else {
                $this->info('✅ جميع سجلات employers تحتوي على company_name صحيح.');
            }

            $this->info('🎉 تم الانتهاء من إصلاح جميع المشاكل بنجاح!');
            
            return 0;

        } catch (\Exception $e) {
            $this->error('❌ حدث خطأ: ' . $e->getMessage());
            return 1;
        }
    }
}
