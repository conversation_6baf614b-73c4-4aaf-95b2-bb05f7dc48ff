<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\Notification;
use Illuminate\Support\Facades\Auth;

class CheckNotificationExists
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $notificationId = $request->route('notification');
        
        // إذا كان الـ parameter عبارة عن رقم
        if (is_numeric($notificationId)) {
            $notification = Notification::find($notificationId);
            
            if (!$notification) {
                return redirect()->route('notifications.index')
                    ->with('warning', "الإشعار رقم {$notificationId} غير موجود أو تم حذفه");
            }
            
            // التحقق من أن الإشعار يخص المستخدم الحالي
            if ($notification->user_id !== Auth::id()) {
                return redirect()->route('notifications.index')
                    ->with('error', 'غير مصرح لك بالوصول إلى هذا الإشعار');
            }
            
            // إضافة الإشعار إلى الـ request للاستخدام في الكنترولر
            $request->merge(['notification_model' => $notification]);
        }
        
        return $next($request);
    }
}
