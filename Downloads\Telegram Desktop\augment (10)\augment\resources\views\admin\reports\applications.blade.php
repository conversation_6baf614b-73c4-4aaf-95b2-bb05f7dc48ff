@extends('layouts.admin')

@section('title', 'تقارير الطلبات - Hire Me')
@section('header_title', 'تقارير الطلبات')

@section('content')
<div class="p-6">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
            <h2 class="text-2xl font-bold">تقارير الطلبات</h2>
            <p class="text-gray-600 dark:text-gray-400 mt-1">تحليل بيانات طلبات التوظيف وتتبع المرشحين</p>
        </div>
        <div class="mt-4 md:mt-0 flex gap-3">
            <button id="printReportBtn" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors flex items-center gap-2">
                <i class="fas fa-print"></i>
                <span>طباعة</span>
            </button>
            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors flex items-center gap-2">
                    <i class="fas fa-download"></i>
                    <span>تصدير</span>
                    <i class="fas fa-chevron-down text-xs"></i>
                </button>
                <div x-show="open" @click.away="open = false" class="absolute left-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg z-10">
                    <a href="{{ route('admin.reports.export.pdf', 'applications') }}?start_date={{ $filters->start_date ?? '' }}&end_date={{ $filters->end_date ?? '' }}&status={{ $filters->status ?? '' }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="far fa-file-pdf ml-2 text-red-500"></i>
                        تصدير PDF
                    </a>
                    <a href="{{ route('admin.reports.export.excel', 'applications') }}?start_date={{ $filters->start_date ?? '' }}&end_date={{ $filters->end_date ?? '' }}&status={{ $filters->status ?? '' }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="far fa-file-excel ml-2 text-green-500"></i>
                        تصدير Excel
                    </a>
                </div>
            </div>
            <button id="sharingOptionsBtn" class="px-4 py-2 gradient-bg text-white rounded-lg hover:opacity-90 transition-colors flex items-center gap-2">
                <i class="fas fa-share-alt"></i>
                <span>مشاركة</span>
            </button>
        </div>
    </div>

    <!-- Date Range and Filters -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6 mb-6">
        <div class="flex flex-col md:flex-row gap-4">
            <div class="flex-1 grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">من تاريخ</label>
                    <input type="date" id="start_date" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" value="{{ $filters->start_date ?? '2023-01-01' }}">
                </div>
                <div>
                    <label for="end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">إلى تاريخ</label>
                    <input type="date" id="end_date" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" value="{{ $filters->end_date ?? date('Y-m-d') }}">
                </div>
            </div>
            <div class="w-full md:w-48">
                <label for="job_filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الوظيفة</label>
                <select id="job_filter" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                    <option value="">جميع الوظائف</option>
                    <option value="1" {{ ($filters->job_id ?? '') == '1' ? 'selected' : '' }}>مطور واجهات أمامية</option>
                    <option value="2" {{ ($filters->job_id ?? '') == '2' ? 'selected' : '' }}>مطور خلفيات</option>
                    <option value="3" {{ ($filters->job_id ?? '') == '3' ? 'selected' : '' }}>مصمم واجهات المستخدم</option>
                    <option value="4" {{ ($filters->job_id ?? '') == '4' ? 'selected' : '' }}>مطور تطبيقات الجوال</option>
                    <option value="5" {{ ($filters->job_id ?? '') == '5' ? 'selected' : '' }}>مسؤول تسويق رقمي</option>
                </select>
            </div>
            <div class="w-full md:w-48">
                <label for="status_filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الحالة</label>
                <select id="status_filter" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                    <option value="">جميع الحالات</option>
                    <option value="new" {{ ($filters->status ?? '') == 'new' ? 'selected' : '' }}>جديد</option>
                    <option value="reviewing" {{ ($filters->status ?? '') == 'reviewing' ? 'selected' : '' }}>قيد المراجعة</option>
                    <option value="shortlisted" {{ ($filters->status ?? '') == 'shortlisted' ? 'selected' : '' }}>بالقائمة المختصرة</option>
                    <option value="interview" {{ ($filters->status ?? '') == 'interview' ? 'selected' : '' }}>مقابلة</option>
                    <option value="hired" {{ ($filters->status ?? '') == 'hired' ? 'selected' : '' }}>تم التعيين</option>
                    <option value="rejected" {{ ($filters->status ?? '') == 'rejected' ? 'selected' : '' }}>مرفوض</option>
                </select>
            </div>
            <div class="flex items-end">
                <button id="applyFiltersBtn" class="px-4 py-2.5 gradient-bg text-white rounded-lg hover:opacity-90 transition-colors w-full md:w-auto">
                    تطبيق
                </button>
            </div>
        </div>
    </div>

    <!-- Dashboard Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <!-- Total Applications Card -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <div class="flex items-center justify-between mb-4">
                <div class="w-14 h-14 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                    <i class="fas fa-file-alt text-blue-600 dark:text-blue-400 text-xl"></i>
                </div>
                <div class="text-left">
                    <p class="text-gray-500 dark:text-gray-400 text-sm">إجمالي الطلبات</p>
                    <p class="text-3xl font-bold">{{ $stats->total_applications ?? '374' }}</p>
                </div>
            </div>
            <div class="flex items-center justify-between text-sm">
                <div class="flex items-center gap-1 {{ ($stats->applications_change ?? 22) >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                    <i class="fas fa-{{ ($stats->applications_change ?? 22) >= 0 ? 'arrow-up' : 'arrow-down' }}"></i>
                    <span>{{ abs($stats->applications_change ?? 22) }}%</span>
                </div>
                <p class="text-gray-500 dark:text-gray-400">مقارنة بالفترة السابقة</p>
            </div>
        </div>

        <!-- Shortlisted Card -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <div class="flex items-center justify-between mb-4">
                <div class="w-14 h-14 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                    <i class="fas fa-user-check text-green-600 dark:text-green-400 text-xl"></i>
                </div>
                <div class="text-left">
                    <p class="text-gray-500 dark:text-gray-400 text-sm">بالقائمة المختصرة</p>
                    <p class="text-3xl font-bold">{{ $stats->shortlisted ?? '85' }}</p>
                </div>
            </div>
            <div class="flex items-center justify-between text-sm">
                <div class="flex items-center gap-1 {{ ($stats->shortlisted_change ?? 15) >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                    <i class="fas fa-{{ ($stats->shortlisted_change ?? 15) >= 0 ? 'arrow-up' : 'arrow-down' }}"></i>
                    <span>{{ abs($stats->shortlisted_change ?? 15) }}%</span>
                </div>
                <p class="text-gray-500 dark:text-gray-400">مقارنة بالفترة السابقة</p>
            </div>
        </div>

        <!-- Hiring Rate Card -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <div class="flex items-center justify-between mb-4">
                <div class="w-14 h-14 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
                    <i class="fas fa-percentage text-purple-600 dark:text-purple-400 text-xl"></i>
                </div>
                <div class="text-left">
                    <p class="text-gray-500 dark:text-gray-400 text-sm">معدل التعيين</p>
                    <p class="text-3xl font-bold">{{ $stats->hiring_rate ?? '12' }}%</p>
                </div>
            </div>
            <div class="flex items-center justify-between text-sm">
                <div class="flex items-center gap-1 {{ ($stats->hiring_rate_change ?? 3) >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                    <i class="fas fa-{{ ($stats->hiring_rate_change ?? 3) >= 0 ? 'arrow-up' : 'arrow-down' }}"></i>
                    <span>{{ abs($stats->hiring_rate_change ?? 3) }}%</span>
                </div>
                <p class="text-gray-500 dark:text-gray-400">مقارنة بالفترة السابقة</p>
            </div>
        </div>

        <!-- Time to Hire Card -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <div class="flex items-center justify-between mb-4">
                <div class="w-14 h-14 rounded-full bg-yellow-100 dark:bg-yellow-900/30 flex items-center justify-center">
                    <i class="fas fa-clock text-yellow-600 dark:text-yellow-400 text-xl"></i>
                </div>
                <div class="text-left">
                    <p class="text-gray-500 dark:text-gray-400 text-sm">متوسط مدة التعيين</p>
                    <p class="text-3xl font-bold">{{ $stats->time_to_hire ?? '18' }} <span class="text-lg">يوم</span></p>
                </div>
            </div>
            <div class="flex items-center justify-between text-sm">
                <div class="flex items-center gap-1 {{ ($stats->time_to_hire_change ?? -5) >= 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400' }}">
                    <i class="fas fa-{{ ($stats->time_to_hire_change ?? -5) >= 0 ? 'arrow-up' : 'arrow-down' }}"></i>
                    <span>{{ abs($stats->time_to_hire_change ?? 5) }}%</span>
                </div>
                <p class="text-gray-500 dark:text-gray-400">مقارنة بالفترة السابقة</p>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Applications by Status -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-bold mb-4">الطلبات حسب الحالة</h3>
            <div id="statusChart" class="h-72"></div>
        </div>

        <!-- Applications Timeline -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-bold mb-4">الطلبات بمرور الوقت</h3>
            <div id="timelineChart" class="h-72"></div>
        </div>
    </div>

    <!-- Additional Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <!-- Applications by Source -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-bold mb-4">الطلبات حسب المصدر</h3>
            <div id="sourceChart" class="h-64"></div>
        </div>

        <!-- Applications by Job Department -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-bold mb-4">الطلبات حسب قسم الوظيفة</h3>
            <div id="departmentChart" class="h-64"></div>
        </div>

        <!-- Applications by Experience Level -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-bold mb-4">الطلبات حسب مستوى الخبرة</h3>
            <div id="experienceChart" class="h-64"></div>
        </div>
    </div>

    <!-- Application Funnel -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6 mb-6">
        <h3 class="text-lg font-bold mb-4">مسار الطلبات (Funnel)</h3>
        <div id="funnelChart" class="h-96"></div>
    </div>

    <!-- Application Status Table -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm overflow-hidden mb-6">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-bold">ملخص حالات الطلبات</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="w-full text-right text-sm">
                <thead class="bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-300">
                    <tr>
                        <th class="px-6 py-3">عنوان الوظيفة</th>
                        <th class="px-6 py-3">إجمالي الطلبات</th>
                        <th class="px-6 py-3">جديد</th>
                        <th class="px-6 py-3">قيد المراجعة</th>
                        <th class="px-6 py-3">بالقائمة المختصرة</th>
                        <th class="px-6 py-3">مقابلة</th>
                        <th class="px-6 py-3">تم التعيين</th>
                        <th class="px-6 py-3">مرفوض</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse($application_status ?? [] as $job)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">{{ $job->title }}</td>
                            <td class="px-6 py-4">{{ $job->total }}</td>
                            <td class="px-6 py-4">{{ $job->new }}</td>
                            <td class="px-6 py-4">{{ $job->reviewing }}</td>
                            <td class="px-6 py-4">{{ $job->shortlisted }}</td>
                            <td class="px-6 py-4">{{ $job->interview }}</td>
                            <td class="px-6 py-4">{{ $job->hired }}</td>
                            <td class="px-6 py-4">{{ $job->rejected }}</td>
                        </tr>
                    @empty
                        <!-- Sample Application Status Data -->
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">مطور واجهات أمامية</td>
                            <td class="px-6 py-4">28</td>
                            <td class="px-6 py-4">3</td>
                            <td class="px-6 py-4">5</td>
                            <td class="px-6 py-4">8</td>
                            <td class="px-6 py-4">6</td>
                            <td class="px-6 py-4">2</td>
                            <td class="px-6 py-4">4</td>
                        </tr>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">مطور خلفيات</td>
                            <td class="px-6 py-4">22</td>
                            <td class="px-6 py-4">2</td>
                            <td class="px-6 py-4">4</td>
                            <td class="px-6 py-4">6</td>
                            <td class="px-6 py-4">5</td>
                            <td class="px-6 py-4">1</td>
                            <td class="px-6 py-4">4</td>
                        </tr>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">مصمم واجهات المستخدم</td>
                            <td class="px-6 py-4">19</td>
                            <td class="px-6 py-4">0</td>
                            <td class="px-6 py-4">3</td>
                            <td class="px-6 py-4">5</td>
                            <td class="px-6 py-4">4</td>
                            <td class="px-6 py-4">2</td>
                            <td class="px-6 py-4">5</td>
                        </tr>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">مطور تطبيقات الجوال</td>
                            <td class="px-6 py-4">16</td>
                            <td class="px-6 py-4">0</td>
                            <td class="px-6 py-4">0</td>
                            <td class="px-6 py-4">4</td>
                            <td class="px-6 py-4">6</td>
                            <td class="px-6 py-4">1</td>
                            <td class="px-6 py-4">5</td>
                        </tr>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">مسؤول تسويق رقمي</td>
                            <td class="px-6 py-4">12</td>
                            <td class="px-6 py-4">0</td>
                            <td class="px-6 py-4">0</td>
                            <td class="px-6 py-4">3</td>
                            <td class="px-6 py-4">4</td>
                            <td class="px-6 py-4">1</td>
                            <td class="px-6 py-4">4</td>
                        </tr>
                    @endforelse
                </tbody>
                <tfoot class="bg-gray-50 dark:bg-gray-800 font-medium text-gray-900 dark:text-white">
                    <tr>
                        <td class="px-6 py-3">الإجمالي</td>
                        <td class="px-6 py-3">97</td>
                        <td class="px-6 py-3">5</td>
                        <td class="px-6 py-3">12</td>
                        <td class="px-6 py-3">26</td>
                        <td class="px-6 py-3">25</td>
                        <td class="px-6 py-3">7</td>
                        <td class="px-6 py-3">22</td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>

    <!-- Key Insights Card -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
        <h3 class="text-lg font-bold mb-4">أهم الرؤى والتوصيات</h3>

        <div class="space-y-4">
            <div class="flex items-start gap-3">
                <div class="w-10 h-10 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-chart-line text-green-600 dark:text-green-400"></i>
                </div>
                <div>
                    <h4 class="font-medium">ارتفاع معدل الطلبات</h4>
                    <p class="text-gray-600 dark:text-gray-400">شهدت الطلبات زيادة بنسبة 22% مقارنة بالفترة السابقة، مما يدل على نجاح حملات التوظيف وجاذبية الوظائف المعروضة.</p>
                </div>
            </div>

            <div class="flex items-start gap-3">
                <div class="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-hourglass-half text-blue-600 dark:text-blue-400"></i>
                </div>
                <div>
                    <h4 class="font-medium">تحسن وقت التعيين</h4>
                    <p class="text-gray-600 dark:text-gray-400">انخفض متوسط وقت التعيين بنسبة 5% ليصل إلى 18 يوم، مما يشير إلى تحسن في كفاءة عملية التوظيف.</p>
                </div>
            </div>

            <div class="flex items-start gap-3">
                <div class="w-10 h-10 rounded-full bg-yellow-100 dark:bg-yellow-900/30 flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-yellow-600 dark:text-yellow-400"></i>
                </div>
                <div>
                    <h4 class="font-medium">اختناق في مرحلة المقابلات</h4>
                    <p class="text-gray-600 dark:text-gray-400">25% من الطلبات متوقفة في مرحلة المقابلة، مما قد يشير إلى الحاجة إلى تحسين عملية جدولة المقابلات أو زيادة عدد المقابلين.</p>
                </div>
            </div>

            <div class="flex items-start gap-3">
                <div class="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-bullseye text-purple-600 dark:text-purple-400"></i>
                </div>
                <div>
                    <h4 class="font-medium">توصيات لتحسين الأداء</h4>
                    <p class="text-gray-600 dark:text-gray-400">1. تسريع عملية المراجعة الأولية للطلبات الجديدة.<br>2. تقليل نسبة الرفض من خلال تحسين معايير الاختيار الأولية.<br>3. توسيع قنوات التوظيف لزيادة التنوع في مصادر الطلبات.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sharing Options Modal -->
<div id="sharingModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
        </div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white dark:bg-dark-card rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white dark:bg-dark-card px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold">مشاركة التقرير</h3>
                    <button type="button" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300" id="closeModal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="space-y-4">
                    <div>
                        <label for="share_email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">مشاركة عبر البريد الإلكتروني</label>
                        <input type="email" name="share_email" id="share_email" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" placeholder="أدخل البريد الإلكتروني">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">مشاركة مباشرة</label>
                        <div class="flex gap-2">
                            <button class="w-12 h-12 bg-blue-600 hover:bg-blue-700 transition-colors text-white rounded-full flex items-center justify-center">
                                <i class="fab fa-linkedin-in"></i>
                            </button>
                            <button class="w-12 h-12 bg-sky-500 hover:bg-sky-600 transition-colors text-white rounded-full flex items-center justify-center">
                                <i class="fab fa-twitter"></i>
                            </button>
                            <button class="w-12 h-12 bg-green-600 hover:bg-green-700 transition-colors text-white rounded-full flex items-center justify-center">
                                <i class="fab fa-whatsapp"></i>
                            </button>
                            <button class="w-12 h-12 bg-primary hover:opacity-90 transition-colors text-white rounded-full flex items-center justify-center">
                                <i class="fas fa-envelope"></i>
                            </button>
                        </div>
                    </div>

                    <div>
                        <label for="share_link" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">رابط مباشر للتقرير</label>
                        <div class="relative">
                            <input type="text" name="share_link" id="share_link" class="block w-full pr-10 p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" value="https://hireme.com/admin/reports/applications?id=27593" readonly>
                            <button id="copyLink" class="absolute left-2.5 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400 hover:text-primary transition-colors">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>

                    <div>
                        <label for="schedule_report" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">جدولة التقرير</label>
                        <div class="grid grid-cols-2 gap-4">
                            <select id="schedule_frequency" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                                <option value="weekly">أسبوعي</option>
                                <option value="biweekly">كل أسبوعين</option>
                                <option value="monthly">شهري</option>
                            </select>
                            <input type="email" id="schedule_email" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" placeholder="البريد الإلكتروني">
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="confirmShare" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 gradient-bg text-base font-medium text-white hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm">
                    مشاركة
                </button>
                <button type="button" id="scheduleReport" class="w-full inline-flex justify-center rounded-md border border-primary text-primary shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium hover:bg-primary/10 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm">
                    جدولة
                </button>
                <button type="button" id="cancelModal" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    إلغاء
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<script>
    // Initialize Charts
    document.addEventListener('DOMContentLoaded', function () {
        // Detect dark mode
        const isDarkMode = document.documentElement.classList.contains('dark');
        const textColor = isDarkMode ? '#d1d5db' : '#374151';
        const gridColor = isDarkMode ? '#374151' : '#e5e7eb';

        // Status Chart (Bar Chart)
        const statusChartOptions = {
            series: [{
                name: 'الطلبات',
                data: [97, 5, 12, 26, 25, 7, 22]
            }],
            chart: {
                type: 'bar',
                height: 300,
                fontFamily: 'inherit',
                toolbar: {
                    show: false
                }
            },
            colors: ['#5D5CDE'],
            plotOptions: {
                bar: {
                    borderRadius: 4,
                    horizontal: true,
                }
            },
            dataLabels: {
                enabled: true,
                formatter: function (val) {
                    return val;
                },
                style: {
                    fontSize: '12px',
                    colors: ['#fff']
                }
            },
            xaxis: {
                categories: ['الإجمالي', 'جديد', 'قيد المراجعة', 'بالقائمة المختصرة', 'مقابلة', 'تم التعيين', 'مرفوض'],
                labels: {
                    style: {
                        colors: textColor
                    }
                }
            },
            yaxis: {
                labels: {
                    style: {
                        colors: textColor
                    }
                }
            },
            grid: {
                borderColor: gridColor,
            }
        };
        const statusChart = new ApexCharts(document.querySelector('#statusChart'), statusChartOptions);
        statusChart.render();

        // Timeline Chart (Line Chart)
        const timelineChartOptions = {
            series: [{
                name: 'الطلبات',
                data: [45, 52, 38, 47, 65, 74, 53]
            }],
            chart: {
                height: 300,
                type: 'line',
                toolbar: {
                    show: false
                },
                fontFamily: 'inherit',
            },
            colors: ['#5D5CDE'],
            dataLabels: {
                enabled: false
            },
            stroke: {
                width: 3,
                curve: 'smooth'
            },
            grid: {
                borderColor: gridColor,
                row: {
                    colors: [isDarkMode ? 'transparent' : '#f8fafc', 'transparent'],
                    opacity: 0.5
                },
            },
            markers: {
                size: 4
            },
            xaxis: {
                categories: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو'],
                labels: {
                    style: {
                        colors: textColor
                    }
                }
            },
            yaxis: {
                labels: {
                    style: {
                        colors: textColor
                    }
                }
            },
            legend: {
                position: 'top',
                horizontalAlign: 'left',
                offsetY: -10,
                labels: {
                    colors: textColor
                }
            }
        };
        const timelineChart = new ApexCharts(document.querySelector('#timelineChart'), timelineChartOptions);
        timelineChart.render();

        // Source Chart (Donut Chart)
        const sourceChartOptions = {
            series: [45, 25, 15, 10, 5],
            chart: {
                type: 'donut',
                height: 250,
                fontFamily: 'inherit',
            },
            labels: ['موقع الشركة', 'لينكد إن', 'تطبيقات التوظيف', 'الإحالات', 'أخرى'],
            colors: ['#5D5CDE', '#0077B5', '#10B981', '#F59E0B', '#9CA3AF'],
            legend: {
                position: 'bottom',
                offsetY: 0,
                labels: {
                    colors: textColor
                }
            },
            plotOptions: {
                pie: {
                    donut: {
                        size: '65%'
                    }
                }
            },
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        height: 250
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }],
            tooltip: {
                y: {
                    formatter: function(val) {
                        return val + '%'
                    }
                }
            }
        };
        const sourceChart = new ApexCharts(document.querySelector('#sourceChart'), sourceChartOptions);
        sourceChart.render();

        // Department Chart (Donut Chart)
        const departmentChartOptions = {
            series: [42, 28, 16, 8, 6],
            chart: {
                type: 'donut',
                height: 250,
                fontFamily: 'inherit',
            },
            labels: ['تطوير البرمجيات', 'التصميم', 'التسويق', 'الموارد البشرية', 'أخرى'],
            colors: ['#5D5CDE', '#10B981', '#F59E0B', '#EC4899', '#6366F1'],
            legend: {
                position: 'bottom',
                offsetY: 0,
                labels: {
                    colors: textColor
                }
            },
            plotOptions: {
                pie: {
                    donut: {
                        size: '65%'
                    }
                }
            },
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        height: 250
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }],
            tooltip: {
                y: {
                    formatter: function(val) {
                        return val + '%'
                    }
                }
            }
        };
        const departmentChart = new ApexCharts(document.querySelector('#departmentChart'), departmentChartOptions);
        departmentChart.render();

        // Experience Chart (Donut Chart)
        const experienceChartOptions = {
            series: [15, 25, 35, 20, 5],
            chart: {
                type: 'donut',
                height: 250,
                fontFamily: 'inherit',
            },
            labels: ['مبتدئ (0-1 سنة)', 'جونيور (1-3 سنوات)', 'متوسط (3-5 سنوات)', 'سينيور (5-8 سنوات)', 'خبير (8+ سنوات)'],
            colors: ['#06B6D4', '#5D5CDE', '#10B981', '#F59E0B', '#EC4899'],
            legend: {
                position: 'bottom',
                offsetY: 0,
                labels: {
                    colors: textColor
                }
            },
            plotOptions: {
                pie: {
                    donut: {
                        size: '65%'
                    }
                }
            },
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        height: 250
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }],
            tooltip: {
                y: {
                    formatter: function(val) {
                        return val + '%'
                    }
                }
            }
        };
        const experienceChart = new ApexCharts(document.querySelector('#experienceChart'), experienceChartOptions);
        experienceChart.render();

        // Funnel Chart
        const funnelChartOptions = {
            series: [
                {
                    name: 'الطلبات',
                    data: [
                        { x: 'إجمالي الطلبات', y: 97 },
                        { x: 'تمت المراجعة', y: 92 },
                        { x: 'بالقائمة المختصرة', y: 51 },
                        { x: 'تمت المقابلة', y: 32 },
                        { x: 'عرض وظيفي', y: 11 },
                        { x: 'تم التعيين', y: 7 }
                    ]
                }
            ],
            chart: {
                type: 'bar',
                height: 380,
                fontFamily: 'inherit',
                toolbar: {
                    show: false
                }
            },
            plotOptions: {
                bar: {
                    horizontal: true,
                    distributed: true,
                    barHeight: '80%',
                    dataLabels: {
                        position: 'bottom'
                    }
                }
            },
            colors: ['#34d399', '#3b82f6', '#6366f1', '#a855f7', '#ec4899', '#5D5CDE'],
            dataLabels: {
                enabled: true,
                textAnchor: 'start',
                style: {
                    colors: ['#fff']
                },
                formatter: function (val, opt) {
                    const prevValue = opt.w.globals.series[opt.seriesIndex][opt.dataPointIndex - 1];
                    const conversionRate = prevValue ? Math.round((val / prevValue) * 100) : 100;
                    return opt.w.globals.labels[opt.dataPointIndex] + ": " + val + ` (${conversionRate}%)`;
                },
                offsetX: 0,
                dropShadow: {
                    enabled: false
                }
            },
            stroke: {
                width: 1,
                colors: ['#fff']
            },
            grid: {
                borderColor: gridColor,
            },
            xaxis: {
                categories: ['إجمالي الطلبات', 'تمت المراجعة', 'بالقائمة المختصرة', 'تمت المقابلة', 'عرض وظيفي', 'تم التعيين'],
                labels: {
                    style: {
                        colors: textColor
                    }
                }
            },
            yaxis: {
                labels: {
                    style: {
                        colors: textColor
                    }
                }
            },
            tooltip: {
                theme: isDarkMode ? 'dark' : 'light',
                y: {
                    formatter: function (val) {
                        return val + " طلب";
                    }
                }
            }
        };
        const funnelChart = new ApexCharts(document.querySelector('#funnelChart'), funnelChartOptions);
        funnelChart.render();
    });

    // Sharing Modal
    const sharingOptionsBtn = document.getElementById('sharingOptionsBtn');
    const sharingModal = document.getElementById('sharingModal');
    const closeModal = document.getElementById('closeModal');
    const cancelModal = document.getElementById('cancelModal');
    const copyLink = document.getElementById('copyLink');
    const confirmShare = document.getElementById('confirmShare');
    const scheduleReport = document.getElementById('scheduleReport');

    // Show sharing modal
    sharingOptionsBtn.addEventListener('click', () => {
        sharingModal.classList.remove('hidden');
    });

    // Hide modal
    closeModal.addEventListener('click', () => {
        sharingModal.classList.add('hidden');
    });

    cancelModal.addEventListener('click', () => {
        sharingModal.classList.add('hidden');
    });

    // Copy link
    copyLink.addEventListener('click', () => {
        const linkInput = document.getElementById('share_link');
        linkInput.select();
        document.execCommand('copy');

        // Show copied feedback
        copyLink.innerHTML = '<i class="fas fa-check"></i>';
        setTimeout(() => {
            copyLink.innerHTML = '<i class="fas fa-copy"></i>';
        }, 2000);
    });

    // Share report
    confirmShare.addEventListener('click', () => {
        const emailInput = document.getElementById('share_email');
        if (emailInput.value) {
            // Here you would normally send an AJAX request to share the report
            console.log(`Sharing report with: ${emailInput.value}`);

            // Close modal
            sharingModal.classList.add('hidden');

            // Show success message
            alert('تم مشاركة التقرير بنجاح!');
        } else {
            emailInput.focus();
        }
    });

    // Schedule report
    scheduleReport.addEventListener('click', () => {
        const frequency = document.getElementById('schedule_frequency').value;
        const email = document.getElementById('schedule_email').value;

        if (email) {
            // Here you would normally send an AJAX request to schedule the report
            console.log(`Scheduling report: ${frequency} to ${email}`);

            // Close modal
            sharingModal.classList.add('hidden');

            // Show success message
            alert(`تم جدولة إرسال التقرير ${frequency === 'weekly' ? 'أسبوعياً' : frequency === 'biweekly' ? 'كل أسبوعين' : 'شهرياً'} إلى ${email}`);
        } else {
            document.getElementById('schedule_email').focus();
        }
    });

    // Close modal when clicking outside
    sharingModal.addEventListener('click', (e) => {
        if (e.target === sharingModal) {
            sharingModal.classList.add('hidden');
        }
    });

    // Print report
    const printReportBtn = document.getElementById('printReportBtn');

    printReportBtn.addEventListener('click', () => {
        window.print();
    });

    // Export report
    const exportReportBtn = document.getElementById('exportReportBtn');

    exportReportBtn.addEventListener('click', () => {
        // In a real app, this would trigger a download
        alert('جاري تصدير التقرير بصيغة PDF...');
    });

    // Apply filters
    const applyFiltersBtn = document.getElementById('applyFiltersBtn');

    applyFiltersBtn.addEventListener('click', () => {
        const startDate = document.getElementById('start_date').value;
        const endDate = document.getElementById('end_date').value;
        const job = document.getElementById('job_filter').value;
        const status = document.getElementById('status_filter').value;

        // Here you would normally send an AJAX request to update the report
        console.log('Applying filters:', { startDate, endDate, job, status });

        // For demo purposes, show a loading message
        alert('جاري تحديث التقرير...');
    });
</script>
@endsection