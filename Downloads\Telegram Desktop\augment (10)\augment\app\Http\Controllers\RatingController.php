<?php

namespace App\Http\Controllers;

use App\Models\CompanyRating;
use App\Models\Employer;
use App\Models\JobSeeker;
use App\Models\ProfileRating;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RatingController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }
    
    public function rateCompany(Request $request, Employer $employer)
    {
        $this->middleware('role:job_seeker');
        
        $request->validate([
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'nullable|string|max:500',
        ]);
        
        // Check if already rated
        $exists = CompanyRating::where('job_seeker_id', Auth::user()->jobSeeker->id)
                              ->where('employer_id', $employer->id)
                              ->first();
                              
        if ($exists) {
            $exists->rating = $request->rating;
            $exists->comment = $request->comment;
            $exists->save();
            $message = 'Rating updated successfully';
        } else {
            $rating = new CompanyRating();
            $rating->job_seeker_id = Auth::user()->jobSeeker->id;
            $rating->employer_id = $employer->id;
            $rating->rating = $request->rating;
            $rating->comment = $request->comment;
            $rating->save();
            $message = 'Rating submitted successfully';
        }
        
        return redirect()->back()->with('success', $message);
    }
    
    public function rateProfile(Request $request, JobSeeker $jobSeeker)
    {
        $this->middleware('role:employer');
        
        $request->validate([
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'nullable|string|max:500',
        ]);
        
        // Check if already rated
        $exists = ProfileRating::where('employer_id', Auth::user()->employer->id)
                              ->where('job_seeker_id', $jobSeeker->id)
                              ->first();
                              
        if ($exists) {
            $exists->rating = $request->rating;
            $exists->comment = $request->comment;
            $exists->save();
            $message = 'Rating updated successfully';
        } else {
            $rating = new ProfileRating();
            $rating->employer_id = Auth::user()->employer->id;
            $rating->job_seeker_id = $jobSeeker->id;
            $rating->rating = $request->rating;
            $rating->comment = $request->comment;
            $rating->save();
            $message = 'Rating submitted successfully';
        }
        
        return redirect()->back()->with('success', $message);
    }
}