<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // التحقق من وجود العمود قبل إضافته
        if (!Schema::hasColumn('jobs', 'views')) {
            Schema::table('jobs', function (Blueprint $table) {
                $table->integer('views')->default(0)->after('expires_at');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // التحقق من وجود العمود قبل حذفه
        if (Schema::hasColumn('jobs', 'views')) {
            Schema::table('jobs', function (Blueprint $table) {
                $table->dropColumn('views');
            });
        }
    }
};
