@extends('layouts.employer')

@section('title', 'الملف الشخصي - Hire Me')
@section('header_title', 'الملف الشخصي')

@section('content')
<div class="p-8 space-y-8">
    <!-- Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">الملف الشخصي</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">إدارة معلومات الشركة والملف الشخصي</p>
        </div>
    </div>

    <!-- Profile Form -->
    <form action="{{ route('employer.profile.update') }}" method="POST" class="space-y-8">
        @csrf
        @method('PUT')

        <!-- Company Information -->
        <div class="glass-card rounded-xl p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
                <i class="fas fa-building text-blue-600"></i>
                معلومات الشركة
            </h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="md:col-span-2">
                    <label for="company_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        اسم الشركة *
                    </label>
                    <input type="text" id="company_name" name="company_name" value="{{ old('company_name', $employer->company_name) }}" required
                           class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    @error('company_name')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div class="md:col-span-2">
                    <label for="company_description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        وصف الشركة
                    </label>
                    <textarea id="company_description" name="company_description" rows="4"
                              class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">{{ old('company_description', $employer->company_description) }}</textarea>
                    @error('company_description')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="company_website" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        موقع الشركة الإلكتروني
                    </label>
                    <input type="url" id="company_website" name="company_website" value="{{ old('company_website', $employer->company_website) }}"
                           class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    @error('company_website')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="company_size" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        حجم الشركة
                    </label>
                    <select id="company_size" name="company_size"
                            class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                        <option value="">اختر حجم الشركة</option>
                        <option value="1-10" {{ old('company_size', $employer->company_size) == '1-10' ? 'selected' : '' }}>1-10 موظفين</option>
                        <option value="11-50" {{ old('company_size', $employer->company_size) == '11-50' ? 'selected' : '' }}>11-50 موظف</option>
                        <option value="51-200" {{ old('company_size', $employer->company_size) == '51-200' ? 'selected' : '' }}>51-200 موظف</option>
                        <option value="201-500" {{ old('company_size', $employer->company_size) == '201-500' ? 'selected' : '' }}>201-500 موظف</option>
                        <option value="500+" {{ old('company_size', $employer->company_size) == '500+' ? 'selected' : '' }}>أكثر من 500 موظف</option>
                    </select>
                    @error('company_size')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="industry" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        القطاع
                    </label>
                    <input type="text" id="industry" name="industry" value="{{ old('industry', $employer->industry) }}"
                           class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    @error('industry')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="location" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        الموقع
                    </label>
                    <input type="text" id="location" name="location" value="{{ old('location', $employer->location) }}"
                           class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    @error('location')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="glass-card rounded-xl p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
                <i class="fas fa-user text-green-600"></i>
                معلومات الاتصال
            </h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        اسم المسؤول *
                    </label>
                    <input type="text" id="name" name="name" value="{{ old('name', $employer->name) }}" required
                           class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    @error('name')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        البريد الإلكتروني *
                    </label>
                    <input type="email" id="email" name="email" value="{{ old('email', $employer->email) }}" required
                           class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    @error('email')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        رقم الهاتف
                    </label>
                    <input type="tel" id="phone" name="phone" value="{{ old('phone', $employer->phone) }}"
                           class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    @error('phone')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="flex justify-end">
            <button type="submit" class="px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 flex items-center gap-2 shadow-lg">
                <i class="fas fa-save"></i>
                حفظ التغييرات
            </button>
        </div>
    </form>

    <!-- Account Statistics -->
    <div class="glass-card rounded-xl p-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
            <i class="fas fa-chart-bar text-purple-600"></i>
            إحصائيات الحساب
        </h2>

        @php
            $stats = $employer->getStatistics();
        @endphp

        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-briefcase text-white text-xl"></i>
                </div>
                <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $stats['total_jobs'] }}</p>
                <p class="text-sm text-gray-600 dark:text-gray-400">وظيفة منشورة</p>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-users text-white text-xl"></i>
                </div>
                <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $stats['total_applications'] }}</p>
                <p class="text-sm text-gray-600 dark:text-gray-400">طلب توظيف</p>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-eye text-white text-xl"></i>
                </div>
                <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ number_format($stats['total_views']) }}</p>
                <p class="text-sm text-gray-600 dark:text-gray-400">مشاهدة</p>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-calendar text-white text-xl"></i>
                </div>
                <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $stats['days_since_registration'] }}</p>
                <p class="text-sm text-gray-600 dark:text-gray-400">يوم منذ التسجيل</p>
            </div>
        </div>
    </div>


</div>
@endsection
