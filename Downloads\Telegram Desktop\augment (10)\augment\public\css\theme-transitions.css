/**
 * تأثيرات الانتقال للثيم - Hire Me
 * تحسينات بصرية لتبديل الوضع الليلي/النهاري
 */

/* انتقال سلس للألوان الأساسية */
* {
    transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تأثيرات خاصة لأزرار الثيم */
#darkModeToggle,
[data-theme-toggle] {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

#darkModeToggle:hover,
[data-theme-toggle]:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

#darkModeToggle:active,
[data-theme-toggle]:active {
    transform: translateY(0) scale(0.95);
}

/* تأثير الموجة عند النقر */
#darkModeToggle::before,
[data-theme-toggle]::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
    z-index: 0;
}

#darkModeToggle.ripple::before,
[data-theme-toggle].ripple::before {
    width: 300px;
    height: 300px;
}

/* تأثيرات الأيقونات */
#darkModeToggle i,
[data-theme-toggle] i {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 1;
}

#darkModeToggle:hover i,
[data-theme-toggle]:hover i {
    transform: rotate(15deg) scale(1.1);
}

/* تأثير النبض للأيقونة الشمس */
.dark #darkModeToggle .fa-sun,
.dark [data-theme-toggle] .fa-sun {
    animation: sunPulse 2s ease-in-out infinite;
}

@keyframes sunPulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

/* تأثير التلاشي للأيقونة القمر */
#darkModeToggle .fa-moon,
[data-theme-toggle] .fa-moon {
    animation: moonGlow 3s ease-in-out infinite;
}

@keyframes moonGlow {
    0%, 100% {
        opacity: 1;
        filter: drop-shadow(0 0 5px rgba(59, 130, 246, 0.3));
    }
    50% {
        opacity: 0.9;
        filter: drop-shadow(0 0 10px rgba(59, 130, 246, 0.5));
    }
}

/* تأثيرات الانتقال للبطاقات */
.glass-card,
.stat-card,
.action-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تأثيرات الانتقال للنصوص */
.text-gradient {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تأثيرات الانتقال للخلفيات */
body {
    transition: background-color 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تأثير الانتقال للقوائم الجانبية */
.sidebar,
#sidebar {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تأثيرات الانتقال للأزرار */
button,
.btn {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تأثيرات الانتقال للروابط */
a {
    transition: color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تأثيرات الانتقال للحدود */
.border,
[class*="border-"] {
    transition: border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تأثيرات الانتقال للظلال */
.shadow,
[class*="shadow-"] {
    transition: box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تحسينات للأداء */
.theme-transition {
    will-change: background-color, color, border-color;
}

/* تأثير خاص عند تغيير الثيم */
.theme-changing {
    animation: themeChange 0.5s ease-in-out;
}

@keyframes themeChange {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.95;
    }
    100% {
        opacity: 1;
    }
}

/* تأثيرات للعناصر التفاعلية */
.interactive-element {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.interactive-element:hover {
    transform: translateY(-1px);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    * {
        transition-duration: 0.2s;
    }
    
    #darkModeToggle:hover,
    [data-theme-toggle]:hover {
        transform: none;
    }
}

/* تحسينات للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
    * {
        transition: none !important;
        animation: none !important;
    }
}

/* تأثيرات إضافية للوضع الليلي */
.dark {
    color-scheme: dark;
}

html.dark {
    color-scheme: dark;
}
