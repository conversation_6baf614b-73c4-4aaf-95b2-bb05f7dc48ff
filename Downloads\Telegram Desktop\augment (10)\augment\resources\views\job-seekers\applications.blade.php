@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">طلبات التوظيف</h1>
            <p class="text-gray-600 dark:text-gray-400">تتبع حالة طلبات التوظيف التي قدمتها</p>
        </div>

        <!-- Statistics Cards -->
        @if($applications->count() > 0)
            @php
                $totalApplications = $applications->total();
                $pendingCount = $applications->where('status', 'pending')->count();
                $acceptedCount = $applications->where('status', 'accepted')->count();
                $rejectedCount = $applications->where('status', 'rejected')->count();
            @endphp
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white dark:bg-dark-card rounded-xl shadow-md p-6">
                    <div class="flex items-center">
                        <div class="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                            <i class="fas fa-paper-plane text-blue-600 dark:text-blue-400 text-xl"></i>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm text-gray-600 dark:text-gray-400">إجمالي الطلبات</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $totalApplications }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-dark-card rounded-xl shadow-md p-6">
                    <div class="flex items-center">
                        <div class="p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg">
                            <i class="fas fa-clock text-yellow-600 dark:text-yellow-400 text-xl"></i>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm text-gray-600 dark:text-gray-400">قيد المراجعة</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $pendingCount }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-dark-card rounded-xl shadow-md p-6">
                    <div class="flex items-center">
                        <div class="p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
                            <i class="fas fa-check-circle text-green-600 dark:text-green-400 text-xl"></i>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm text-gray-600 dark:text-gray-400">مقبولة</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $acceptedCount }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-dark-card rounded-xl shadow-md p-6">
                    <div class="flex items-center">
                        <div class="p-3 bg-red-100 dark:bg-red-900/30 rounded-lg">
                            <i class="fas fa-times-circle text-red-600 dark:text-red-400 text-xl"></i>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm text-gray-600 dark:text-gray-400">مرفوضة</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $rejectedCount }}</p>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <!-- Applications List -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-md overflow-hidden">
            @if($applications->count() > 0)
                <!-- Filter Tabs -->
                <div class="border-b border-gray-200 dark:border-gray-700">
                    <nav class="flex space-x-8 px-6" aria-label="Tabs">
                        <button onclick="filterApplications('all')" class="tab-button border-b-2 border-primary text-primary py-4 px-1 text-sm font-medium" data-filter="all">
                            جميع الطلبات
                        </button>
                        <button onclick="filterApplications('pending')" class="tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-4 px-1 text-sm font-medium" data-filter="pending">
                            قيد المراجعة
                        </button>
                        <button onclick="filterApplications('accepted')" class="tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-4 px-1 text-sm font-medium" data-filter="accepted">
                            مقبولة
                        </button>
                        <button onclick="filterApplications('rejected')" class="tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-4 px-1 text-sm font-medium" data-filter="rejected">
                            مرفوضة
                        </button>
                    </nav>
                </div>

                <!-- Applications Cards -->
                <div class="p-6">
                    <div class="space-y-4">
                        @foreach($applications as $application)
                            <div class="application-card border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:shadow-md transition-shadow" data-status="{{ $application->status }}">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center mb-2">
                                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                                <a href="{{ route('jobs.show', $application->job) }}" class="hover:text-primary transition-colors">
                                                    {{ $application->job->title }}
                                                </a>
                                            </h3>
                                            <div class="mr-3">
                                                @if($application->status == 'pending')
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300">
                                                        <i class="fas fa-clock ml-1"></i>
                                                        قيد المراجعة
                                                    </span>
                                                @elseif($application->status == 'accepted')
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                                                        <i class="fas fa-check-circle ml-1"></i>
                                                        مقبول
                                                    </span>
                                                @elseif($application->status == 'rejected')
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300">
                                                        <i class="fas fa-times-circle ml-1"></i>
                                                        مرفوض
                                                    </span>
                                                @endif
                                            </div>
                                        </div>

                                        <div class="flex items-center text-sm text-gray-600 dark:text-gray-400 mb-3">
                                            <i class="fas fa-building ml-2"></i>
                                            <span>{{ $application->job->employer->company_name ?? 'غير محدد' }}</span>
                                            <span class="mx-2">•</span>
                                            <i class="fas fa-map-marker-alt ml-2"></i>
                                            <span>{{ $application->job->location }}</span>
                                            <span class="mx-2">•</span>
                                            <i class="fas fa-calendar ml-2"></i>
                                            <span>تم التقديم في {{ $application->created_at->format('d/m/Y') }}</span>
                                        </div>

                                        @if($application->job->job_type)
                                            <div class="flex items-center mb-3">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                                    {{ $application->job->job_type }}
                                                </span>
                                            </div>
                                        @endif

                                        @if($application->cover_letter)
                                            <div class="mt-3">
                                                <p class="text-sm text-gray-600 dark:text-gray-400">
                                                    <strong>رسالة التقديم:</strong>
                                                    {{ Str::limit($application->cover_letter, 150) }}
                                                </p>
                                            </div>
                                        @endif
                                    </div>

                                    <div class="flex flex-col space-y-2 mr-4">
                                        <a href="{{ route('jobs.show', $application->job) }}"
                                           class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                            <i class="fas fa-eye ml-2"></i>
                                            عرض الوظيفة
                                        </a>

                                        @if($application->status == 'accepted')
                                            <span class="inline-flex items-center px-3 py-2 rounded-md text-sm font-medium text-green-700 bg-green-50 dark:bg-green-900/30 dark:text-green-300">
                                                <i class="fas fa-handshake ml-2"></i>
                                                تهانينا!
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>

                <!-- Pagination -->
                <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                    {{ $applications->links() }}
                </div>
            @else
                <div class="text-center py-16">
                    <div class="mx-auto w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-6">
                        <i class="fas fa-paper-plane text-3xl text-gray-400 dark:text-gray-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">لا توجد طلبات توظيف</h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-8 max-w-md mx-auto">
                        لم تقم بتقديم أي طلبات توظيف بعد. ابدأ في البحث عن الوظائف المناسبة لك وقدم طلباتك.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="{{ route('jobs.index') }}"
                           class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary hover:bg-primary-dark transition-colors">
                            <i class="fas fa-search ml-2"></i>
                            تصفح الوظائف المتاحة
                        </a>
                        <a href="{{ route('job-seeker.profile.edit') }}"
                           class="inline-flex items-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-base font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                            <i class="fas fa-user-edit ml-2"></i>
                            تحسين الملف الشخصي
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

<script>
function filterApplications(status) {
    // إزالة الفئة النشطة من جميع التبويبات
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('border-primary', 'text-primary');
        button.classList.add('border-transparent', 'text-gray-500');
    });

    // إضافة الفئة النشطة للتبويب المحدد
    const activeButton = document.querySelector(`[data-filter="${status}"]`);
    if (activeButton) {
        activeButton.classList.remove('border-transparent', 'text-gray-500');
        activeButton.classList.add('border-primary', 'text-primary');
    }

    // إظهار/إخفاء البطاقات حسب الحالة
    document.querySelectorAll('.application-card').forEach(card => {
        if (status === 'all' || card.dataset.status === status) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

// تفعيل التبويب الأول عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    filterApplications('all');
});
</script>
@endsection
