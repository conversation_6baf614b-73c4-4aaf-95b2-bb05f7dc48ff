@extends('layouts.app')

@section('title', $job->title . ' - Hire Me')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
    <div class="container mx-auto px-4 py-8">
        <!-- Breadcrumb -->
        <nav class="flex mb-6" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3 rtl:space-x-reverse">
                <li class="inline-flex items-center">
                    <a href="{{ route('home') }}" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                        <i class="fas fa-home ml-2"></i>
                        الرئيسية
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                        <a href="{{ route('jobs.index') }}" class="text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">الوظائف</a>
                    </div>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ $job->title }}</span>
                    </div>
                </li>
            </ol>
        </nav>

        <div class="max-w-7xl mx-auto">
            <!-- Job Header -->
            <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ $job->title }}</h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-2 flex flex-wrap items-center gap-4">
                        @if($job->employer)
                            <span class="flex items-center gap-1">
                                <i class="fas fa-building text-blue-500"></i>
                                {{ $job->employer->company_name }}
                            </span>
                        @endif
                        <span class="flex items-center gap-1">
                            <i class="fas fa-map-marker-alt text-red-500"></i>
                            {{ $job->location }}
                        </span>
                        <span class="flex items-center gap-1">
                            <i class="fas fa-clock text-green-500"></i>
                            @switch($job->job_type)
                                @case('full_time')
                                    دوام كامل
                                    @break
                                @case('part_time')
                                    دوام جزئي
                                    @break
                                @case('remote')
                                    عن بعد
                                    @break
                                @case('freelance')
                                    عمل حر
                                    @break
                                @default
                                    {{ $job->job_type }}
                            @endswitch
                        </span>
                    </p>
                </div>
                <div class="mt-4 md:mt-0 flex gap-3">
                    <button onclick="shareJob()" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors flex items-center gap-2">
                        <i class="fas fa-share-alt"></i>
                        <span>مشاركة</span>
                    </button>
                    <button onclick="saveJob()" class="px-4 py-2 border border-blue-300 dark:border-blue-600 rounded-lg text-blue-700 dark:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-colors flex items-center gap-2">
                        <i class="fas fa-bookmark"></i>
                        <span>حفظ</span>
                    </button>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Main Job Details -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Job Information -->
                    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                        <div class="mb-6 flex items-center gap-4">
                            <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white flex-shrink-0">
                                <i class="fas fa-briefcase text-2xl"></i>
                            </div>
                            <div>
                                <h2 class="text-xl font-bold text-gray-900 dark:text-white">{{ $job->title }}</h2>
                                <div class="flex flex-wrap items-center gap-3 mt-1 text-gray-600 dark:text-gray-400 text-sm">
                                    <span class="flex items-center gap-1">
                                        <i class="fas fa-map-marker-alt"></i>
                                        {{ $job->location }}
                                    </span>
                                    <span class="flex items-center gap-1">
                                        <i class="fas fa-clock"></i>
                                        @switch($job->job_type)
                                            @case('full_time')
                                                دوام كامل
                                                @break
                                            @case('part_time')
                                                دوام جزئي
                                                @break
                                            @case('remote')
                                                عن بعد
                                                @break
                                            @case('freelance')
                                                عمل حر
                                                @break
                                            @default
                                                {{ $job->job_type }}
                                        @endswitch
                                    </span>
                                    <span class="flex items-center gap-1">
                                        <i class="fas fa-calendar-alt"></i>
                                        نُشرت {{ $job->created_at->diffForHumans() }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Status & Tags -->
                        <div class="flex flex-wrap gap-2 mb-6">
                            <span class="px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-sm">نشط</span>

                            @if($job->category)
                                <span class="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 rounded-full text-sm">
                                    {{ $job->category->name }}
                                </span>
                            @endif

                            <span class="px-3 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 rounded-full text-sm">
                                {{ $job->views ?? 0 }} مشاهدة
                            </span>

                            @if($job->salary_min || $job->salary_max)
                                <span class="px-3 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400 rounded-full text-sm">
                                    <i class="fas fa-money-bill-wave ml-1"></i>
                                    {{ $job->salary_range }}
                                </span>
                            @endif
                        </div>
                    </div>
                
                    <!-- Job Description -->
                    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                            <i class="fas fa-file-alt text-blue-500"></i>
                            وصف الوظيفة
                        </h3>
                        <div class="prose dark:prose-invert max-w-none text-gray-700 dark:text-gray-300">
                            {!! $job->description !!}
                        </div>
                    </div>

                    <!-- Job Requirements -->
                    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                            <i class="fas fa-list-check text-green-500"></i>
                            المتطلبات
                        </h3>
                        <div class="prose dark:prose-invert max-w-none text-gray-700 dark:text-gray-300">
                            {!! $job->requirements !!}
                        </div>
                    </div>

                    <!-- Job Benefits (if available) -->
                    @if($job->benefits)
                        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                                <i class="fas fa-gift text-purple-500"></i>
                                المزايا والفوائد
                            </h3>
                            <div class="prose dark:prose-invert max-w-none text-gray-700 dark:text-gray-300">
                                {!! $job->benefits !!}
                            </div>
                        </div>
                    @endif
                
                    <!-- Apply Button (for authenticated users) -->
                    @auth
                        @if(auth()->user()->role == 'job_seeker')
                            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-800 p-6 text-center">
                                <div class="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full mx-auto mb-4">
                                    <i class="fas fa-paper-plane text-white text-2xl"></i>
                                </div>
                                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">هل أنت مهتم بهذه الوظيفة؟</h3>
                                <p class="text-gray-600 dark:text-gray-400 mb-6">قم بالتقديم الآن وأظهر مهاراتك للشركة</p>

                                @php
                                    $hasApplied = $job->applications->where('job_seeker_id', auth()->user()->jobSeeker->id)->count() > 0;
                                @endphp
                            
                                @if($hasApplied)
                                    <div class="bg-green-100 dark:bg-green-900/30 border border-green-300 dark:border-green-700 rounded-lg p-4 mb-4">
                                        <div class="flex items-center gap-2 text-green-700 dark:text-green-400">
                                            <i class="fas fa-check-circle"></i>
                                            <span class="font-medium">لقد تقدمت بالفعل لهذه الوظيفة</span>
                                        </div>
                                        <p class="text-green-600 dark:text-green-500 text-sm mt-1">
                                            سيتم مراجعة طلبك من قبل الشركة قريباً
                                        </p>
                                    </div>
                                @else
                                    <a href="{{ route('applications.create', $job) }}"
                                       class="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-8 py-3 rounded-lg transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                                        <i class="fas fa-paper-plane"></i>
                                        تقدم للوظيفة
                                    </a>
                                @endif
                            </div>
                        @endif
                    @else
                        <div class="bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 rounded-xl border border-amber-200 dark:border-amber-800 p-6 text-center">
                            <div class="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-amber-500 to-orange-600 rounded-full mx-auto mb-4">
                                <i class="fas fa-user-plus text-white text-2xl"></i>
                            </div>
                            <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">للتقديم على هذه الوظيفة</h3>
                            <p class="text-gray-600 dark:text-gray-400 mb-6">يجب عليك تسجيل الدخول أولاً</p>
                            <div class="flex gap-4 justify-center">
                                <a href="{{ route('login') }}" class="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-6 py-2 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl">
                                    تسجيل الدخول
                                </a>
                                <a href="{{ route('register') }}" class="border border-blue-600 text-blue-600 dark:text-blue-400 px-6 py-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-colors">
                                    إنشاء حساب
                                </a>
                            </div>
                        </div>
                    @endauth
            </div>
            
                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Company Info -->
                    @if($job->employer)
                        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                                <i class="fas fa-building text-blue-500"></i>
                                معلومات الشركة
                            </h3>
                            <div class="flex items-center gap-4 mb-4">
                                <div class="w-14 h-14 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-lg flex items-center justify-center">
                                    @if($job->employer->company_logo)
                                        <img src="{{ asset('storage/' . $job->employer->company_logo) }}" alt="{{ $job->employer->company_name }}" class="w-12 h-12 object-contain rounded-lg">
                                    @else
                                        <i class="fas fa-building text-gray-500 text-xl"></i>
                                    @endif
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 dark:text-white">{{ $job->employer->company_name }}</h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">{{ $job->employer->industry ?? 'صناعة غير محددة' }}</p>
                                </div>
                            </div>

                            @if($job->employer->company_description)
                                <p class="text-gray-600 dark:text-gray-400 text-sm mb-4 leading-relaxed">
                                    {{ \Illuminate\Support\Str::limit($job->employer->company_description, 150) }}
                                </p>
                            @endif

                            <a href="{{ route('employers.show', $job->employer) }}" class="block w-full text-center px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl">
                                عرض الملف الكامل
                            </a>
                        </div>
                    @endif
                
                    <!-- Job Details -->
                    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                            <i class="fas fa-info-circle text-green-500"></i>
                            تفاصيل الوظيفة
                        </h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                                <span class="text-gray-600 dark:text-gray-400 flex items-center gap-2">
                                    <i class="fas fa-calendar-plus text-blue-500"></i>
                                    تاريخ النشر:
                                </span>
                                <span class="font-medium text-gray-900 dark:text-white">{{ $job->created_at->format('Y/m/d') }}</span>
                            </div>

                            <div class="flex items-center justify-between p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                                <span class="text-gray-600 dark:text-gray-400 flex items-center gap-2">
                                    <i class="fas fa-calendar-times text-red-500"></i>
                                    تاريخ الانتهاء:
                                </span>
                                <span class="font-medium text-gray-900 dark:text-white">{{ $job->expires_at ? $job->expires_at->format('Y/m/d') : 'غير محدد' }}</span>
                            </div>

                            <div class="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                                <span class="text-gray-600 dark:text-gray-400 flex items-center gap-2">
                                    <i class="fas fa-clock text-green-500"></i>
                                    نوع الوظيفة:
                                </span>
                                <span class="font-medium text-gray-900 dark:text-white">
                                    @switch($job->job_type)
                                        @case('full_time')
                                            دوام كامل
                                            @break
                                        @case('part_time')
                                            دوام جزئي
                                            @break
                                        @case('remote')
                                            عن بعد
                                            @break
                                        @case('freelance')
                                            عمل حر
                                            @break
                                        @default
                                            {{ $job->job_type }}
                                    @endswitch
                                </span>
                            </div>

                            @if($job->salary_min || $job->salary_max)
                                <div class="flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                                    <span class="text-gray-600 dark:text-gray-400 flex items-center gap-2">
                                        <i class="fas fa-money-bill-wave text-yellow-500"></i>
                                        نطاق الراتب:
                                    </span>
                                    <span class="font-medium text-gray-900 dark:text-white">{{ $job->salary_range }}</span>
                                </div>
                            @endif

                            <div class="flex items-center justify-between p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                                <span class="text-gray-600 dark:text-gray-400 flex items-center gap-2">
                                    <i class="fas fa-map-marker-alt text-purple-500"></i>
                                    الموقع:
                                </span>
                                <span class="font-medium text-gray-900 dark:text-white">{{ $job->location }}</span>
                            </div>

                            @if($job->category)
                                <div class="flex items-center justify-between p-3 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg">
                                    <span class="text-gray-600 dark:text-gray-400 flex items-center gap-2">
                                        <i class="fas fa-tag text-indigo-500"></i>
                                        الفئة:
                                    </span>
                                    <span class="font-medium text-gray-900 dark:text-white">{{ $job->category->name }}</span>
                                </div>
                            @endif

                            <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-900/20 rounded-lg">
                                <span class="text-gray-600 dark:text-gray-400 flex items-center gap-2">
                                    <i class="fas fa-eye text-gray-500"></i>
                                    عدد المشاهدات:
                                </span>
                                <span class="font-medium text-gray-900 dark:text-white">{{ $job->views ?? 0 }}</span>
                            </div>
                        </div>
                    </div>
                
                    <!-- Share Job -->
                    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                            <i class="fas fa-share-alt text-orange-500"></i>
                            مشاركة الوظيفة
                        </h3>
                        <div class="grid grid-cols-2 gap-3">
                            <button onclick="shareOnFacebook()" class="flex items-center justify-center gap-2 p-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                                <i class="fab fa-facebook-f"></i>
                                <span class="text-sm">فيسبوك</span>
                            </button>
                            <button onclick="shareOnTwitter()" class="flex items-center justify-center gap-2 p-3 bg-sky-500 hover:bg-sky-600 text-white rounded-lg transition-colors">
                                <i class="fab fa-twitter"></i>
                                <span class="text-sm">تويتر</span>
                            </button>
                            <button onclick="shareOnLinkedIn()" class="flex items-center justify-center gap-2 p-3 bg-blue-700 hover:bg-blue-800 text-white rounded-lg transition-colors">
                                <i class="fab fa-linkedin-in"></i>
                                <span class="text-sm">لينكدإن</span>
                            </button>
                            <button onclick="shareOnWhatsApp()" class="flex items-center justify-center gap-2 p-3 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors">
                                <i class="fab fa-whatsapp"></i>
                                <span class="text-sm">واتساب</span>
                            </button>
                        </div>
                        <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                            <button onclick="copyJobLink()" class="w-full flex items-center justify-center gap-2 p-3 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg transition-colors">
                                <i class="fas fa-copy"></i>
                                <span class="text-sm">نسخ الرابط</span>
                            </button>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                            <i class="fas fa-bolt text-yellow-500"></i>
                            إجراءات سريعة
                        </h3>
                        <div class="space-y-3">
                            <button onclick="reportJob()" class="w-full flex items-center justify-center gap-2 p-3 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400 rounded-lg transition-colors">
                                <i class="fas fa-flag"></i>
                                <span class="text-sm">الإبلاغ عن الوظيفة</span>
                            </button>
                            <button onclick="printJob()" class="w-full flex items-center justify-center gap-2 p-3 bg-gray-50 dark:bg-gray-900/20 hover:bg-gray-100 dark:hover:bg-gray-900/30 text-gray-600 dark:text-gray-400 rounded-lg transition-colors">
                                <i class="fas fa-print"></i>
                                <span class="text-sm">طباعة الوظيفة</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Related Jobs -->
        @if(isset($relatedJobs) && $relatedJobs->count() > 0)
            <div class="mt-12">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
                    <i class="fas fa-briefcase text-blue-500"></i>
                    وظائف مشابهة
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    @foreach($relatedJobs as $relatedJob)
                        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm hover:shadow-lg transition-all duration-200 p-6 border border-gray-100 dark:border-gray-700 hover:border-blue-200 dark:hover:border-blue-600">
                            <div class="flex items-start gap-4 mb-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <i class="fas fa-briefcase text-white"></i>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-1">{{ $relatedJob->title }}</h3>
                                    <div class="flex flex-wrap items-center gap-3 text-gray-600 dark:text-gray-400 text-sm">
                                        @if($relatedJob->employer)
                                            <span class="flex items-center gap-1">
                                                <i class="fas fa-building text-blue-500"></i>
                                                {{ $relatedJob->employer->company_name }}
                                            </span>
                                        @endif

                                        <span class="flex items-center gap-1">
                                            <i class="fas fa-map-marker-alt text-red-500"></i>
                                            {{ $relatedJob->location }}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4 flex justify-between items-center">
                                <span class="px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs font-medium">
                                    @switch($relatedJob->job_type)
                                        @case('full_time')
                                            دوام كامل
                                            @break
                                        @case('part_time')
                                            دوام جزئي
                                            @break
                                        @case('remote')
                                            عن بعد
                                            @break
                                        @case('freelance')
                                            عمل حر
                                            @break
                                        @default
                                            {{ $relatedJob->job_type }}
                                    @endswitch
                                </span>
                                <a href="{{ route('jobs.show', $relatedJob) }}" class="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-lg hover:shadow-xl">
                                    عرض التفاصيل
                                </a>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif
    </div>
</div>

<!-- JavaScript for Interactive Features -->
<script>
    // Share Functions
    function shareOnFacebook() {
        const url = encodeURIComponent(window.location.href);
        const title = encodeURIComponent('{{ $job->title }} - {{ $job->employer->company_name ?? "Hire Me" }}');
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}&quote=${title}`, '_blank', 'width=600,height=400');
    }

    function shareOnTwitter() {
        const url = encodeURIComponent(window.location.href);
        const text = encodeURIComponent('{{ $job->title }} في {{ $job->employer->company_name ?? "Hire Me" }} - {{ $job->location }}');
        window.open(`https://twitter.com/intent/tweet?url=${url}&text=${text}`, '_blank', 'width=600,height=400');
    }

    function shareOnLinkedIn() {
        const url = encodeURIComponent(window.location.href);
        const title = encodeURIComponent('{{ $job->title }}');
        const summary = encodeURIComponent('فرصة عمل رائعة في {{ $job->employer->company_name ?? "Hire Me" }}');
        window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}&title=${title}&summary=${summary}`, '_blank', 'width=600,height=400');
    }

    function shareOnWhatsApp() {
        const url = encodeURIComponent(window.location.href);
        const text = encodeURIComponent(`🔥 فرصة عمل رائعة!\n\n*{{ $job->title }}*\n📍 {{ $job->location }}\n🏢 {{ $job->employer->company_name ?? "Hire Me" }}\n\nشاهد التفاصيل: ${window.location.href}`);
        window.open(`https://wa.me/?text=${text}`, '_blank');
    }

    function copyJobLink() {
        navigator.clipboard.writeText(window.location.href).then(function() {
            // Show success message
            const button = event.target.closest('button');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check text-green-500"></i><span class="text-sm text-green-500">تم النسخ!</span>';
            setTimeout(() => {
                button.innerHTML = originalText;
            }, 2000);
        }).catch(function(err) {
            console.error('فشل في نسخ الرابط: ', err);
        });
    }

    function shareJob() {
        if (navigator.share) {
            navigator.share({
                title: '{{ $job->title }}',
                text: 'فرصة عمل رائعة في {{ $job->employer->company_name ?? "Hire Me" }}',
                url: window.location.href
            });
        } else {
            copyJobLink();
        }
    }

    function saveJob() {
        // Add to favorites functionality
        @auth
            fetch('{{ route("jobs.save", $job->id) }}', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const button = event.target.closest('button');
                    const icon = button.querySelector('i');
                    icon.className = 'fas fa-bookmark text-yellow-500';
                    button.querySelector('span').textContent = 'محفوظة';
                }
            })
            .catch(error => {
                console.error('خطأ في حفظ الوظيفة:', error);
            });
        @else
            alert('يجب تسجيل الدخول أولاً لحفظ الوظائف');
        @endauth
    }

    function reportJob() {
        if (confirm('هل أنت متأكد من رغبتك في الإبلاغ عن هذه الوظيفة؟')) {
            // Report job functionality
            alert('تم إرسال البلاغ. شكراً لك على مساعدتنا في تحسين المنصة.');
        }
    }

    function printJob() {
        window.print();
    }

    // Add smooth scrolling for better UX
    document.addEventListener('DOMContentLoaded', function() {
        // Add animation classes
        const cards = document.querySelectorAll('.bg-white, .bg-dark-card');
        cards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
            card.classList.add('animate-fade-in');
        });
    });
</script>

<style>
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-fade-in {
        animation: fadeIn 0.6s ease-out forwards;
    }

    @media print {
        .no-print {
            display: none !important;
        }

        body {
            background: white !important;
        }

        .bg-gradient-to-br {
            background: white !important;
        }
    }
</style>

@endsection
