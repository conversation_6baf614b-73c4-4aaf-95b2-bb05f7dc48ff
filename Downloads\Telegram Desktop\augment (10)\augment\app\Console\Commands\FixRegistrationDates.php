<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Employer;
use Carbon\Carbon;

class FixRegistrationDates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:registration-dates';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix incorrect registration dates for employers';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Fixing registration dates...');
        
        $employers = Employer::with('user')->get();
        $fixed = 0;
        
        foreach ($employers as $employer) {
            $userCreatedAt = $employer->user ? $employer->user->created_at : null;
            $employerCreatedAt = $employer->created_at;
            
            // إذا كان تاريخ إنشاء صاحب العمل في المستقبل أو غير منطقي
            if ($employerCreatedAt && $employerCreatedAt->isFuture()) {
                $newDate = $userCreatedAt ?: now()->subDays(rand(30, 365));
                $employer->update(['created_at' => $newDate]);
                $this->info("Fixed future date for {$employer->company_name}: {$newDate->format('Y-m-d')}");
                $fixed++;
            }
            // إذا كان تاريخ صاحب العمل أحدث بكثير من تاريخ المستخدم
            elseif ($userCreatedAt && $employerCreatedAt && $employerCreatedAt->diffInDays($userCreatedAt) > 365) {
                $newDate = $userCreatedAt->addDays(rand(0, 7));
                $employer->update(['created_at' => $newDate]);
                $this->info("Fixed inconsistent date for {$employer->company_name}: {$newDate->format('Y-m-d')}");
                $fixed++;
            }
            // إذا لم يكن هناك تاريخ إنشاء
            elseif (!$employerCreatedAt) {
                $newDate = $userCreatedAt ?: now()->subDays(rand(30, 365));
                $employer->update(['created_at' => $newDate]);
                $this->info("Set missing date for {$employer->company_name}: {$newDate->format('Y-m-d')}");
                $fixed++;
            }
            
            // عرض الإحصائيات الحالية
            $stats = $employer->getStatistics();
            $this->line("  - {$employer->company_name}: {$stats['days_since_registration']} days since registration");
        }
        
        $this->info("Fixed {$fixed} registration dates.");
        return 0;
    }
}
