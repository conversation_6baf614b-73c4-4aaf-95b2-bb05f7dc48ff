@extends('layouts.employer')

@section('title', 'عرض المستخدم - Hire Me')
@section('header_title', 'عرض المستخدم')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-bold">عرض المستخدم</h2>
                <p class="text-gray-600 dark:text-gray-400 mt-1">تفاصيل المستخدم {{ $user->name }}</p>
            </div>
            <div class="flex gap-3">
                <a href="{{ route('employer.users.edit', $user) }}" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2">
                    <i class="fas fa-edit"></i>
                    <span>تعديل</span>
                </a>
                <a href="{{ route('employer.users.index') }}" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors flex items-center gap-2">
                    <i class="fas fa-arrow-right"></i>
                    <span>العودة</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg">
            <div class="flex items-center">
                <i class="fas fa-check-circle ml-2"></i>
                {{ session('success') }}
            </div>
        </div>
    @endif

    @if(session('error'))
        <div class="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
            <div class="flex items-center">
                <i class="fas fa-exclamation-circle ml-2"></i>
                {{ session('error') }}
            </div>
        </div>
    @endif

    <!-- User Details -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm overflow-hidden">
        <div class="p-6">
            <!-- User Avatar and Basic Info -->
            <div class="flex items-start gap-6 mb-8">
                <div class="w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-2xl font-bold">
                    {{ substr($user->name, 0, 1) }}
                </div>
                <div class="flex-1">
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">{{ $user->name }}</h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-4">{{ $user->email }}</p>
                    
                    <div class="flex items-center gap-4">
                        <span class="px-3 py-1 rounded-full text-sm font-medium
                            @if($user->role === 'admin') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300
                            @elseif($user->role === 'employer') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300
                            @else bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 @endif">
                            @if($user->role === 'admin') مدير
                            @elseif($user->role === 'employer') صاحب عمل
                            @else باحث عن عمل @endif
                        </span>
                        
                        @if($user->email_verified_at)
                            <span class="px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                                <i class="fas fa-check-circle mr-1"></i>
                                مفعل
                            </span>
                        @else
                            <span class="px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">
                                <i class="fas fa-clock mr-1"></i>
                                غير مفعل
                            </span>
                        @endif
                    </div>
                </div>
            </div>

            <!-- User Information Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <h4 class="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                        المعلومات الشخصية
                    </h4>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">الاسم:</span>
                            <span class="font-medium text-gray-900 dark:text-white">{{ $user->name }}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">البريد الإلكتروني:</span>
                            <span class="font-medium text-gray-900 dark:text-white">{{ $user->email }}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">رقم الهاتف:</span>
                            <span class="font-medium text-gray-900 dark:text-white">{{ $user->phone ?? 'غير محدد' }}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">الموقع:</span>
                            <span class="font-medium text-gray-900 dark:text-white">{{ $user->location ?? 'غير محدد' }}</span>
                        </div>
                    </div>
                </div>

                <div class="space-y-4">
                    <h4 class="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                        معلومات الحساب
                    </h4>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">الدور:</span>
                            <span class="font-medium text-gray-900 dark:text-white">
                                @if($user->role === 'admin') مدير
                                @elseif($user->role === 'employer') صاحب عمل
                                @else باحث عن عمل @endif
                            </span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">تاريخ الإنضمام:</span>
                            <span class="font-medium text-gray-900 dark:text-white">{{ $user->created_at->format('Y/m/d H:i') }}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">آخر تحديث:</span>
                            <span class="font-medium text-gray-900 dark:text-white">{{ $user->updated_at->format('Y/m/d H:i') }}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">آخر تسجيل دخول:</span>
                            <span class="font-medium text-gray-900 dark:text-white">
                                {{ $user->last_login_at ? $user->last_login_at->format('Y/m/d H:i') : 'لم يسجل دخول' }}
                            </span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">حالة التفعيل:</span>
                            <span class="font-medium text-gray-900 dark:text-white">
                                {{ $user->email_verified_at ? 'مفعل منذ ' . $user->email_verified_at->format('Y/m/d') : 'غير مفعل' }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                <div class="flex gap-3">
                    <a href="{{ route('employer.users.edit', $user) }}" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2">
                        <i class="fas fa-edit"></i>
                        تعديل المستخدم
                    </a>
                    
                    <form action="{{ route('employer.users.toggle-verification', $user) }}" method="POST" class="inline">
                        @csrf
                        <button type="submit" class="px-4 py-2 bg-{{ $user->email_verified_at ? 'orange' : 'green' }}-600 text-white rounded-lg hover:bg-{{ $user->email_verified_at ? 'orange' : 'green' }}-700 transition-colors flex items-center gap-2">
                            <i class="fas fa-{{ $user->email_verified_at ? 'user-times' : 'user-check' }}"></i>
                            {{ $user->email_verified_at ? 'إلغاء التفعيل' : 'تفعيل الحساب' }}
                        </button>
                    </form>
                    
                    @if($user->id !== auth()->id())
                        <button onclick="confirmDelete()" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center gap-2">
                            <i class="fas fa-trash"></i>
                            حذف المستخدم
                        </button>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
    <div class="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-md w-full mx-4">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">تأكيد الحذف</h3>
            <button onclick="hideDeleteModal()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="mb-6">
            <div class="flex items-center gap-3 mb-4">
                <div class="w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
                    <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400"></i>
                </div>
                <div>
                    <h4 class="font-medium text-gray-900 dark:text-white">هل أنت متأكد؟</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400">سيتم حذف المستخدم نهائياً</p>
                </div>
            </div>
            <p class="text-gray-700 dark:text-gray-300">
                سيتم حذف المستخدم <span class="font-semibold">{{ $user->name }}</span> نهائياً. هذا الإجراء لا يمكن التراجع عنه.
            </p>
        </div>

        <div class="flex gap-3">
            <button onclick="executeDelete()" class="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                <i class="fas fa-trash mr-2"></i>
                حذف نهائياً
            </button>
            <button onclick="hideDeleteModal()" class="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                إلغاء
            </button>
        </div>
    </div>
</div>

<script>
function confirmDelete() {
    document.getElementById('deleteModal').classList.remove('hidden');
}

function hideDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
}

function executeDelete() {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{{ route("employer.users.destroy", $user) }}';
    
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = '{{ csrf_token() }}';
    
    const methodField = document.createElement('input');
    methodField.type = 'hidden';
    methodField.name = '_method';
    methodField.value = 'DELETE';
    
    form.appendChild(csrfToken);
    form.appendChild(methodField);
    document.body.appendChild(form);
    form.submit();
}

// إغلاق modal عند النقر خارجه
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideDeleteModal();
    }
});
</script>
@endsection
