/* ملف الألوان والتدرجات المتقدمة */

:root {
    /* الألوان الأساسية */
    --primary-50: #f0f9ff;
    --primary-100: #e0f2fe;
    --primary-200: #bae6fd;
    --primary-300: #7dd3fc;
    --primary-400: #38bdf8;
    --primary-500: #0ea5e9;
    --primary-600: #0284c7;
    --primary-700: #0369a1;
    --primary-800: #075985;
    --primary-900: #0c4a6e;

    /* الألوان الثانوية */
    --accent-50: #fdf4ff;
    --accent-100: #fae8ff;
    --accent-200: #f5d0fe;
    --accent-300: #f0abfc;
    --accent-400: #e879f9;
    --accent-500: #d946ef;
    --accent-600: #c026d3;
    --accent-700: #a21caf;
    --accent-800: #86198f;
    --accent-900: #701a75;

    /* ألوان النجاح */
    --success-50: #f0fdf4;
    --success-100: #dcfce7;
    --success-200: #bbf7d0;
    --success-300: #86efac;
    --success-400: #4ade80;
    --success-500: #22c55e;
    --success-600: #16a34a;
    --success-700: #15803d;
    --success-800: #166534;
    --success-900: #14532d;

    /* ألوان التحذير */
    --warning-50: #fffbeb;
    --warning-100: #fef3c7;
    --warning-200: #fde68a;
    --warning-300: #fcd34d;
    --warning-400: #fbbf24;
    --warning-500: #f59e0b;
    --warning-600: #d97706;
    --warning-700: #b45309;
    --warning-800: #92400e;
    --warning-900: #78350f;

    /* ألوان الخطر */
    --danger-50: #fef2f2;
    --danger-100: #fee2e2;
    --danger-200: #fecaca;
    --danger-300: #fca5a5;
    --danger-400: #f87171;
    --danger-500: #ef4444;
    --danger-600: #dc2626;
    --danger-700: #b91c1c;
    --danger-800: #991b1b;
    --danger-900: #7f1d1d;

    /* ألوان محايدة */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
}

/* التدرجات الأساسية */
.gradient-primary {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 50%, var(--primary-700) 100%);
}

.gradient-accent {
    background: linear-gradient(135deg, var(--accent-500) 0%, var(--accent-600) 50%, var(--accent-700) 100%);
}

.gradient-success {
    background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 50%, var(--success-700) 100%);
}

.gradient-warning {
    background: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-600) 50%, var(--warning-700) 100%);
}

.gradient-danger {
    background: linear-gradient(135deg, var(--danger-500) 0%, var(--danger-600) 50%, var(--danger-700) 100%);
}

/* تدرجات متقدمة */
.gradient-rainbow {
    background: linear-gradient(135deg, 
        var(--primary-500) 0%, 
        var(--accent-500) 25%, 
        var(--success-500) 50%, 
        var(--warning-500) 75%, 
        var(--danger-500) 100%);
}

.gradient-ocean {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-sunset {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-forest {
    background: linear-gradient(135deg, #134e5e 0%, #71b280 100%);
}

.gradient-fire {
    background: linear-gradient(135deg, #f12711 0%, #f5af19 100%);
}

.gradient-ice {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

/* تدرجات للخلفيات */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
}

.bg-gradient-accent {
    background: linear-gradient(135deg, var(--accent-50) 0%, var(--accent-100) 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, var(--success-50) 0%, var(--success-100) 100%);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, var(--warning-50) 0%, var(--warning-100) 100%);
}

.bg-gradient-danger {
    background: linear-gradient(135deg, var(--danger-50) 0%, var(--danger-100) 100%);
}

/* تدرجات للنصوص */
.text-gradient-primary {
    background: linear-gradient(135deg, var(--primary-600), var(--accent-600));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-gradient-success {
    background: linear-gradient(135deg, var(--success-600), var(--success-700));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-gradient-warning {
    background: linear-gradient(135deg, var(--warning-600), var(--warning-700));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-gradient-danger {
    background: linear-gradient(135deg, var(--danger-600), var(--danger-700));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* تأثيرات الظلال الملونة */
.shadow-primary {
    box-shadow: 0 10px 25px rgba(14, 165, 233, 0.3);
}

.shadow-accent {
    box-shadow: 0 10px 25px rgba(217, 70, 239, 0.3);
}

.shadow-success {
    box-shadow: 0 10px 25px rgba(34, 197, 94, 0.3);
}

.shadow-warning {
    box-shadow: 0 10px 25px rgba(245, 158, 11, 0.3);
}

.shadow-danger {
    box-shadow: 0 10px 25px rgba(239, 68, 68, 0.3);
}

/* تأثيرات الهوفر للظلال */
.hover-shadow-primary:hover {
    box-shadow: 0 15px 35px rgba(14, 165, 233, 0.4);
    transform: translateY(-2px);
}

.hover-shadow-accent:hover {
    box-shadow: 0 15px 35px rgba(217, 70, 239, 0.4);
    transform: translateY(-2px);
}

.hover-shadow-success:hover {
    box-shadow: 0 15px 35px rgba(34, 197, 94, 0.4);
    transform: translateY(-2px);
}

.hover-shadow-warning:hover {
    box-shadow: 0 15px 35px rgba(245, 158, 11, 0.4);
    transform: translateY(-2px);
}

.hover-shadow-danger:hover {
    box-shadow: 0 15px 35px rgba(239, 68, 68, 0.4);
    transform: translateY(-2px);
}

/* تأثيرات الحدود الملونة */
.border-gradient-primary {
    border: 2px solid;
    border-image: linear-gradient(135deg, var(--primary-500), var(--accent-500)) 1;
}

.border-gradient-success {
    border: 2px solid;
    border-image: linear-gradient(135deg, var(--success-500), var(--success-600)) 1;
}

.border-gradient-warning {
    border: 2px solid;
    border-image: linear-gradient(135deg, var(--warning-500), var(--warning-600)) 1;
}

.border-gradient-danger {
    border: 2px solid;
    border-image: linear-gradient(135deg, var(--danger-500), var(--danger-600)) 1;
}

/* تأثيرات الخلفيات المتحركة */
.animated-gradient {
    background: linear-gradient(-45deg, var(--primary-500), var(--accent-500), var(--success-500), var(--warning-500));
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* تأثيرات الوضع المظلم */
.dark {
    --primary-50: #0c4a6e;
    --primary-100: #075985;
    --primary-200: #0369a1;
    --primary-300: #0284c7;
    --primary-400: #0ea5e9;
    --primary-500: #38bdf8;
    --primary-600: #7dd3fc;
    --primary-700: #bae6fd;
    --primary-800: #e0f2fe;
    --primary-900: #f0f9ff;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .gradient-primary,
    .gradient-accent,
    .gradient-success,
    .gradient-warning,
    .gradient-danger {
        background-attachment: scroll;
    }
    
    .shadow-primary,
    .shadow-accent,
    .shadow-success,
    .shadow-warning,
    .shadow-danger {
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }
}

/* تأثيرات خاصة للعناصر التفاعلية */
.interactive-gradient {
    background: linear-gradient(135deg, var(--primary-500), var(--accent-500));
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.interactive-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.interactive-gradient:hover::before {
    left: 100%;
}

.interactive-gradient:hover {
    transform: scale(1.05);
    box-shadow: 0 15px 30px rgba(14, 165, 233, 0.4);
}

/* تأثيرات النيون */
.neon-primary {
    color: var(--primary-400);
    text-shadow: 
        0 0 5px var(--primary-400),
        0 0 10px var(--primary-400),
        0 0 15px var(--primary-400),
        0 0 20px var(--primary-400);
}

.neon-accent {
    color: var(--accent-400);
    text-shadow: 
        0 0 5px var(--accent-400),
        0 0 10px var(--accent-400),
        0 0 15px var(--accent-400),
        0 0 20px var(--accent-400);
}

.neon-success {
    color: var(--success-400);
    text-shadow: 
        0 0 5px var(--success-400),
        0 0 10px var(--success-400),
        0 0 15px var(--success-400),
        0 0 20px var(--success-400);
}
