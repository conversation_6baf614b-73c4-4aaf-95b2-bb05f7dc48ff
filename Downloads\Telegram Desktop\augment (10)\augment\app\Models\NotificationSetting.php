<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class NotificationSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'type',
        'web_enabled',
        'email_enabled',
        'push_enabled',
    ];

    protected $casts = [
        'web_enabled' => 'boolean',
        'email_enabled' => 'boolean',
        'push_enabled' => 'boolean',
    ];

    /**
     * Get the user that owns the notification setting.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * الحصول على الإعدادات الافتراضية لنوع إشعار معين
     */
    public static function getDefaultSettings(): array
    {
        return [
            'job_application' => [
                'web_enabled' => true,
                'email_enabled' => true,
                'push_enabled' => false,
            ],
            'application_update' => [
                'web_enabled' => true,
                'email_enabled' => true,
                'push_enabled' => false,
            ],
            'job_posted' => [
                'web_enabled' => true,
                'email_enabled' => false,
                'push_enabled' => false,
            ],
            'job_expired' => [
                'web_enabled' => true,
                'email_enabled' => true,
                'push_enabled' => false,
            ],
            'payment_success' => [
                'web_enabled' => true,
                'email_enabled' => true,
                'push_enabled' => false,
            ],
            'payment_failed' => [
                'web_enabled' => true,
                'email_enabled' => true,
                'push_enabled' => false,
            ],
            'profile_rating' => [
                'web_enabled' => true,
                'email_enabled' => false,
                'push_enabled' => false,
            ],
            'company_rating' => [
                'web_enabled' => true,
                'email_enabled' => false,
                'push_enabled' => false,
            ],
            'system_announcement' => [
                'web_enabled' => true,
                'email_enabled' => false,
                'push_enabled' => false,
            ],
            'account_verification' => [
                'web_enabled' => true,
                'email_enabled' => true,
                'push_enabled' => false,
            ],
            'password_reset' => [
                'web_enabled' => true,
                'email_enabled' => true,
                'push_enabled' => false,
            ],
            'interview_scheduled' => [
                'web_enabled' => true,
                'email_enabled' => true,
                'push_enabled' => false,
            ],
            'job_recommendation' => [
                'web_enabled' => true,
                'email_enabled' => false,
                'push_enabled' => false,
            ],
        ];
    }

    /**
     * إنشاء الإعدادات الافتراضية للمستخدم
     */
    public static function createDefaultSettingsForUser(User $user): void
    {
        $defaultSettings = self::getDefaultSettings();
        
        foreach ($defaultSettings as $type => $settings) {
            self::firstOrCreate(
                [
                    'user_id' => $user->id,
                    'type' => $type,
                ],
                $settings
            );
        }
    }

    /**
     * الحصول على إعدادات المستخدم لنوع إشعار معين
     */
    public static function getUserSetting(User $user, string $type): ?NotificationSetting
    {
        return self::where('user_id', $user->id)
            ->where('type', $type)
            ->first();
    }

    /**
     * التحقق من تفعيل نوع إشعار معين للمستخدم
     */
    public static function isEnabled(User $user, string $type, string $channel = 'web'): bool
    {
        $setting = self::getUserSetting($user, $type);
        
        if (!$setting) {
            // إذا لم توجد إعدادات، استخدم الافتراضية
            $defaultSettings = self::getDefaultSettings();
            return $defaultSettings[$type][$channel . '_enabled'] ?? true;
        }
        
        return $setting->{$channel . '_enabled'} ?? false;
    }

    /**
     * تحديث إعدادات الإشعار للمستخدم
     */
    public static function updateUserSetting(User $user, string $type, array $settings): void
    {
        self::updateOrCreate(
            [
                'user_id' => $user->id,
                'type' => $type,
            ],
            $settings
        );
    }
}
