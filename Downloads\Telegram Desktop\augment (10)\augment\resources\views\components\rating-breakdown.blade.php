@props([
    'stats',
    'compact' => false
])

<div class="rating-breakdown {{ $compact ? 'compact' : '' }} card">
    <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">تفصيل التقييمات</h3>
        
        <!-- Overall Rating -->
        <div class="text-center mb-8">
            <div class="text-4xl font-bold text-gray-900 dark:text-white mb-2">
                {{ $stats['average'] ?? 0 }}
            </div>
            <div class="flex items-center justify-center mb-2">
                @for($i = 1; $i <= 5; $i++)
                    <i class="fas fa-star text-2xl {{ $i <= ($stats['average'] ?? 0) ? 'text-yellow-400' : 'text-gray-300' }}"></i>
                @endfor
            </div>
            <p class="text-gray-600 dark:text-gray-400">
                بناءً على {{ $stats['total'] ?? 0 }} تقييم
            </p>
        </div>

        <!-- Rating Breakdown -->
        <div class="space-y-3">
            @for($rating = 5; $rating >= 1; $rating--)
                @php
                    $count = $stats["{$rating}_star"] ?? 0;
                    $percentage = $stats['total'] > 0 ? ($count / $stats['total']) * 100 : 0;
                @endphp
                <div class="flex items-center gap-3">
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300 w-12">
                        {{ $rating }} نجوم
                    </span>
                    <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div class="bg-yellow-400 h-2 rounded-full transition-all duration-500" 
                             style="width: {{ $percentage }}%"></div>
                    </div>
                    <span class="text-sm text-gray-600 dark:text-gray-400 w-8">
                        {{ $count }}
                    </span>
                </div>
            @endfor
        </div>

        <!-- Status Summary -->
        <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
            <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-4">حالة التقييمات</h4>
            <div class="grid grid-cols-3 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                        {{ $stats['approved'] ?? 0 }}
                    </div>
                    <div class="text-xs text-gray-600 dark:text-gray-400">معتمد</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
                        {{ $stats['pending'] ?? 0 }}
                    </div>
                    <div class="text-xs text-gray-600 dark:text-gray-400">في الانتظار</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-red-600 dark:text-red-400">
                        {{ $stats['rejected'] ?? 0 }}
                    </div>
                    <div class="text-xs text-gray-600 dark:text-gray-400">مرفوض</div>
                </div>
            </div>
        </div>

        <!-- Recent Trend (if available) -->
        @if(isset($stats['recent_trend']))
            <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-3">الاتجاه الأخير</h4>
                <div class="flex items-center gap-2">
                    @if($stats['recent_trend'] > 0)
                        <i class="fas fa-arrow-up text-green-500"></i>
                        <span class="text-sm text-green-600 dark:text-green-400">
                            تحسن بنسبة {{ number_format($stats['recent_trend'], 1) }}%
                        </span>
                    @elseif($stats['recent_trend'] < 0)
                        <i class="fas fa-arrow-down text-red-500"></i>
                        <span class="text-sm text-red-600 dark:text-red-400">
                            انخفض بنسبة {{ number_format(abs($stats['recent_trend']), 1) }}%
                        </span>
                    @else
                        <i class="fas fa-minus text-gray-500"></i>
                        <span class="text-sm text-gray-600 dark:text-gray-400">
                            لا تغيير
                        </span>
                    @endif
                </div>
            </div>
        @endif
    </div>
</div>

<style>
.rating-breakdown {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
}

.dark .rating-breakdown {
    background: linear-gradient(135deg, rgba(31, 41, 55, 0.9) 0%, rgba(17, 24, 39, 0.9) 100%);
}

.rating-breakdown.compact {
    padding: 1rem;
}

.rating-breakdown.compact h3 {
    font-size: 1rem;
    margin-bottom: 1rem;
}

.rating-breakdown.compact .text-4xl {
    font-size: 2rem;
}

.rating-breakdown.compact .text-2xl {
    font-size: 1.5rem;
}

/* Animation for progress bars */
.rating-breakdown .bg-yellow-400 {
    animation: fillProgress 1s ease-out;
}

@keyframes fillProgress {
    from {
        width: 0%;
    }
}

/* Hover effects */
.rating-breakdown:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .rating-breakdown:hover {
        transform: none;
        box-shadow: none;
    }
    
    .rating-breakdown .grid-cols-3 {
        grid-template-columns: repeat(1, minmax(0, 1fr));
        gap: 1rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animate progress bars on load
    const progressBars = document.querySelectorAll('.rating-breakdown .bg-yellow-400');
    progressBars.forEach((bar, index) => {
        setTimeout(() => {
            bar.style.width = bar.style.width || '0%';
        }, index * 100);
    });
});
</script>
