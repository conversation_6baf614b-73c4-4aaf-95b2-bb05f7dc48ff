@extends('layouts.app')

@section('title', 'قائمة الشركات')

@section('content')
<!-- Hero Section -->
<div class="gradient-bg py-16">
    <div class="p-6">
        <div class="max-w-7xl mx-auto text-center">
            <div class="animate-fade-in">
                <h1 class="text-4xl md:text-5xl font-bold text-white mb-6">
                    <i class="fas fa-building mr-3"></i>
                    الشركات الرائدة
                </h1>
                <p class="text-xl text-white/90 max-w-3xl mx-auto mb-8">
                    اكتشف أفضل الشركات وأصحاب العمل الذين يبحثون عن المواهب المميزة مثلك
                </p>

                <!-- Quick Stats -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto">
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                        <div class="text-2xl font-bold text-white">{{ $employers->total() }}</div>
                        <div class="text-white/80 text-sm">شركة مسجلة</div>
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                        <div class="text-2xl font-bold text-white">{{ \App\Models\Job::count() }}</div>
                        <div class="text-white/80 text-sm">وظيفة متاحة</div>
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                        <div class="text-2xl font-bold text-white">{{ \App\Models\Employer::distinct('industry')->count('industry') }}</div>
                        <div class="text-white/80 text-sm">مجال مختلف</div>
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                        <div class="text-2xl font-bold text-white">{{ \App\Models\Application::count() }}</div>
                        <div class="text-white/80 text-sm">طلب توظيف</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter Section -->
<div class="bg-white dark:bg-gray-900 py-8 border-b border-gray-200 dark:border-gray-700">
    <div class="p-6">
        <div class="max-w-7xl mx-auto">

            <form method="GET" action="{{ route('employers.index') }}" class="bg-white dark:bg-dark-card rounded-2xl shadow-lg p-6 border border-gray-100 dark:border-gray-700">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-search mr-2 text-primary"></i>
                            البحث
                        </label>
                        <input type="text" name="search" value="{{ request('search') }}"
                               placeholder="اسم الشركة أو الوصف"
                               class="block w-full p-3 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-industry mr-2 text-primary"></i>
                            المجال
                        </label>
                        <select name="industry" class="block w-full p-3 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary">
                            <option value="">جميع المجالات</option>
                            <option value="E-commerce" {{ request('industry') == 'E-commerce' ? 'selected' : '' }}>التجارة الإلكترونية</option>
                            <option value="Software Development" {{ request('industry') == 'Software Development' ? 'selected' : '' }}>تطوير البرمجيات</option>
                            <option value="Information Technology" {{ request('industry') == 'Information Technology' ? 'selected' : '' }}>تقنية المعلومات</option>
                            <option value="Digital Marketing" {{ request('industry') == 'Digital Marketing' ? 'selected' : '' }}>التسويق الرقمي</option>
                            <option value="Consulting" {{ request('industry') == 'Consulting' ? 'selected' : '' }}>الاستشارات</option>
                            <option value="الصحة" {{ request('industry') == 'الصحة' ? 'selected' : '' }}>الصحة</option>
                            <option value="التعليم" {{ request('industry') == 'التعليم' ? 'selected' : '' }}>التعليم</option>
                            <option value="المالية" {{ request('industry') == 'المالية' ? 'selected' : '' }}>المالية</option>
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button type="submit" class="w-full gradient-bg text-white py-3 px-6 rounded-lg hover:opacity-90 transition-opacity font-medium">
                            <i class="fas fa-search mr-2"></i>
                            بحث
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Companies Section -->
<div class="py-16 bg-gray-50 dark:bg-gray-900">
    <div class="p-6">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-building text-primary mr-3"></i>
                    جميع الشركات
                </h2>
                <p class="text-lg text-gray-600 dark:text-gray-400">تصفح الشركات واكتشف الفرص الوظيفية المتاحة</p>
            </div>

            <!-- Companies Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @forelse($employers as $employer)
                    <div class="bg-white dark:bg-dark-card rounded-2xl shadow-sm hover:shadow-xl hover:scale-105 transition-all duration-300 p-6 border border-gray-100 dark:border-gray-700 group">
                        <!-- Company Header -->
                        <div class="flex items-center mb-6">
                            <div class="w-16 h-16 gradient-bg rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300 mr-4">
                                @if($employer->company_logo)
                                    <img src="{{ Storage::url($employer->company_logo) }}"
                                         alt="{{ $employer->company_name }}"
                                         class="w-14 h-14 rounded-xl object-cover">
                                @else
                                    <i class="fas fa-building text-2xl text-white"></i>
                                @endif
                            </div>
                            <div class="flex-1">
                                <h3 class="text-xl font-bold text-gray-900 dark:text-white group-hover:text-primary transition-colors duration-300">
                                    {{ $employer->company_name }}
                                </h3>
                                <p class="text-primary font-medium">{{ $employer->industry ?: 'غير محدد' }}</p>
                            </div>
                        </div>

                        <!-- Company Description -->
                        @if($employer->company_description)
                            <p class="text-gray-600 dark:text-gray-400 mb-4 line-clamp-3 text-sm">
                                {{ Str::limit($employer->company_description, 120) }}
                            </p>
                        @endif

                        <!-- Company Info Tags -->
                        <div class="flex flex-wrap gap-2 mb-6">
                            @if($employer->location)
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">
                                    <i class="fas fa-map-marker-alt mr-1"></i>
                                    {{ $employer->location }}
                                </span>
                            @endif

                            @if($employer->company_size)
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400">
                                    <i class="fas fa-users mr-1"></i>
                                    {{ $employer->company_size }} موظف
                                </span>
                            @endif

                            @if($employer->founded_year)
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400">
                                    <i class="fas fa-calendar-alt mr-1"></i>
                                    تأسست {{ $employer->founded_year }}
                                </span>
                            @endif
                        </div>

                        <!-- Footer -->
                        <div class="flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-700">
                            @php
                                $jobsCount = $employer->jobs()->where('expires_at', '>', now())->count();
                            @endphp

                            <div class="flex items-center gap-2">
                                @if($jobsCount > 0)
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary">
                                        <i class="fas fa-briefcase mr-1"></i>
                                        {{ $jobsCount }} وظيفة شاغرة
                                    </span>
                                @else
                                    <span class="text-gray-500 dark:text-gray-400 text-sm">لا توجد وظائف متاحة</span>
                                @endif
                            </div>
                            <a href="{{ route('employers.show', $employer) }}" class="gradient-bg text-white px-4 py-2 rounded-lg hover:opacity-90 hover:scale-105 transition-all duration-300 text-sm font-medium shadow-lg">
                                <i class="fas fa-eye mr-1"></i>
                                عرض الشركة
                            </a>
                        </div>
                    </div>
                @empty
                    <div class="col-span-full">
                        <div class="bg-white dark:bg-dark-card rounded-2xl shadow-sm p-16 text-center border border-gray-100 dark:border-gray-700">
                            <div class="w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-6">
                                <i class="fas fa-building text-4xl text-gray-400 dark:text-gray-600"></i>
                            </div>
                            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">لا توجد شركات</h3>
                            <p class="text-gray-600 dark:text-gray-400 mb-8 max-w-md mx-auto">
                                لم يتم العثور على أي شركات تطابق معايير البحث. جرب تعديل المعايير أو تصفح جميع الشركات
                            </p>
                            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                                <a href="{{ route('employers.index') }}" class="gradient-bg text-white px-6 py-3 rounded-lg hover:opacity-90 transition-opacity font-medium">
                                    <i class="fas fa-refresh mr-2"></i>
                                    عرض جميع الشركات
                                </a>
                                <a href="{{ route('home') }}" class="bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors font-medium">
                                    <i class="fas fa-home mr-2"></i>
                                    العودة للرئيسية
                                </a>
                            </div>
                        </div>
                    </div>
                @endforelse
            </div>

            <!-- Pagination -->
            @if($employers->hasPages())
                <div class="mt-12 flex justify-center">
                    <div class="bg-white dark:bg-dark-card rounded-2xl shadow-sm p-4 border border-gray-100 dark:border-gray-700">
                        {{ $employers->links() }}
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Call to Action Section -->
<div class="gradient-bg py-16">
    <div class="p-6">
        <div class="max-w-4xl mx-auto text-center">
            <h2 class="text-3xl font-bold text-white mb-4">
                هل أنت صاحب عمل؟
            </h2>
            <p class="text-xl text-white/90 mb-8">
                انضم إلى منصتنا وابدأ في نشر الوظائف والعثور على أفضل المواهب
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                @guest
                    <a href="{{ route('register') }}" class="bg-white text-primary px-8 py-4 rounded-lg hover:bg-gray-100 transition-colors font-bold text-lg shadow-lg">
                        <i class="fas fa-building mr-2"></i>
                        تسجيل كصاحب عمل
                    </a>
                @else
                    @if(auth()->user()->role === 'employer')
                        <a href="{{ route('employer.profile') }}" class="bg-white text-primary px-8 py-4 rounded-lg hover:bg-gray-100 transition-colors font-bold text-lg shadow-lg">
                            <i class="fas fa-user mr-2"></i>
                            إدارة الملف الشخصي
                        </a>
                    @else
                        <a href="{{ route('jobs.index') }}" class="bg-white text-primary px-8 py-4 rounded-lg hover:bg-gray-100 transition-colors font-bold text-lg shadow-lg">
                            <i class="fas fa-briefcase mr-2"></i>
                            تصفح الوظائف
                        </a>
                    @endif
                @endguest
            </div>
        </div>
    </div>
</div>
@endsection
