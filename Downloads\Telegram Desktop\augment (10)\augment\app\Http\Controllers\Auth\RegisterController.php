<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\JobSeeker;
use App\Models\Employer;
use App\Providers\RouteServiceProvider;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Illuminate\View\View;

class RegisterController extends Controller
{
    /**
     * Display the registration view.
     */
    public function create(): View
    {
        return view('auth.register');
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'role' => ['required', 'in:job_seeker,employer'],
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ]);
        
        // Assign role based on account type
        $user->assignRole($request->role);

        // Create profile based on account type
        if ($request->role === 'job_seeker') {
            JobSeeker::create([
                'user_id' => $user->id,
            ]);
            $redirectTo = route('job-seeker.profile');
        } else {
            Employer::create([
                'user_id' => $user->id,
                'company_name' => $request->name . ' - شركة'
            ]);
            $redirectTo = route('employer.profile');
        }

        event(new Registered($user));

        Auth::login($user);

        return redirect($redirectTo);
    }
}