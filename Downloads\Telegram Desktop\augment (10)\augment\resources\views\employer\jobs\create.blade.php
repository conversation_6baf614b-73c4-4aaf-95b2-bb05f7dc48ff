@extends('layouts.employer')

@section('title', 'نشر وظيفة جديدة - Hire Me')
@section('header_title', 'نشر وظيفة جديدة')

@php
    use Illuminate\Support\Facades\Schema;
@endphp

@section('content')
<div class="p-8 space-y-8">
    <!-- Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">نشر وظيفة جديدة</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">أضف وظيفة جديدة لجذب أفضل المواهب</p>
        </div>

        <div class="flex gap-3 mt-4 md:mt-0">
            <a href="{{ route('employer.jobs.index') }}" class="px-6 py-3 bg-gray-600 text-white rounded-xl hover:bg-gray-700 transition-all duration-300 flex items-center gap-2">
                <i class="fas fa-arrow-right"></i>
                العودة للقائمة
            </a>
        </div>
    </div>

    <!-- Form -->
    <form action="{{ route('employer.jobs.store') }}" method="POST" class="space-y-8">
        @csrf

        <!-- Basic Information -->
        <div class="glass-card rounded-xl p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
                <i class="fas fa-info-circle text-blue-600"></i>
                المعلومات الأساسية
            </h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="md:col-span-2">
                    <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        عنوان الوظيفة *
                    </label>
                    <input type="text" id="title" name="title" value="{{ old('title') }}" required
                           class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    @error('title')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="category_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        التصنيف *
                    </label>
                    <select id="category_id" name="category_id" required
                            class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                        <option value="">اختر التصنيف</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->id }}" {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                {{ $category->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('category_id')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="location" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        الموقع *
                    </label>
                    <input type="text" id="location" name="location" value="{{ old('location') }}" required
                           class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    @error('location')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="job_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        نوع الوظيفة *
                    </label>
                    <select id="job_type" name="job_type" required
                            class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                        <option value="">اختر نوع الوظيفة</option>
                        <option value="full_time" {{ old('job_type') == 'full_time' ? 'selected' : '' }}>دوام كامل</option>
                        <option value="part_time" {{ old('job_type') == 'part_time' ? 'selected' : '' }}>دوام جزئي</option>
                        <option value="remote" {{ old('job_type') == 'remote' ? 'selected' : '' }}>عمل عن بُعد</option>
                        <option value="freelance" {{ old('job_type') == 'freelance' ? 'selected' : '' }}>عمل حر</option>
                    </select>
                    @error('job_type')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div class="space-y-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                        <i class="fas fa-money-bill-wave text-green-600"></i>
                        معلومات الراتب
                    </h3>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- الحد الأدنى للراتب -->
                        <div>
                            <label for="salary_min" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                <i class="fas fa-arrow-down text-blue-500 mr-1"></i>
                                الحد الأدنى للراتب
                            </label>
                            <div class="relative">
                                <input type="number" id="salary_min" name="salary_min" value="{{ old('salary_min') }}"
                                       placeholder="مثال: 750"
                                       min="0"
                                       step="0.01"
                                       class="w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <i class="fas fa-coins text-gray-400"></i>
                                </div>
                            </div>
                            @error('salary_min')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">اتركه فارغاً إذا كان غير محدد</p>
                        </div>

                        <!-- الحد الأعلى للراتب -->
                        <div>
                            <label for="salary_max" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                <i class="fas fa-arrow-up text-green-500 mr-1"></i>
                                الحد الأعلى للراتب
                            </label>
                            <div class="relative">
                                <input type="number" id="salary_max" name="salary_max" value="{{ old('salary_max') }}"
                                       placeholder="مثال: 1200"
                                       min="0"
                                       step="0.01"
                                       class="w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <i class="fas fa-coins text-gray-400"></i>
                                </div>
                            </div>
                            @error('salary_max')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">اتركه فارغاً إذا كان غير محدد</p>
                        </div>

                        <!-- العملة -->
                        <div>
                            <label for="salary_currency" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                <i class="fas fa-globe text-purple-500 mr-1"></i>
                                العملة *
                            </label>
                            <div class="relative">
                                <select id="salary_currency" name="salary_currency" required
                                        class="w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white appearance-none">
                                    <option value="">اختر العملة</option>
                                    <option value="LYD" {{ old('salary_currency', 'LYD') == 'LYD' ? 'selected' : '' }}>🇱🇾 دينار ليبي (LYD)</option>
                                    <option value="USD" {{ old('salary_currency') == 'USD' ? 'selected' : '' }}>🇺🇸 دولار أمريكي (USD)</option>
                                    <option value="EUR" {{ old('salary_currency') == 'EUR' ? 'selected' : '' }}>🇪🇺 يورو (EUR)</option>
                                    <option value="GBP" {{ old('salary_currency') == 'GBP' ? 'selected' : '' }}>🇬🇧 جنيه إسترليني (GBP)</option>
                                    <option value="SAR" {{ old('salary_currency') == 'SAR' ? 'selected' : '' }}>🇸🇦 ريال سعودي (SAR)</option>
                                    <option value="AED" {{ old('salary_currency') == 'AED' ? 'selected' : '' }}>🇦🇪 درهم إماراتي (AED)</option>
                                    <option value="QAR" {{ old('salary_currency') == 'QAR' ? 'selected' : '' }}>🇶🇦 ريال قطري (QAR)</option>
                                    <option value="KWD" {{ old('salary_currency') == 'KWD' ? 'selected' : '' }}>🇰🇼 دينار كويتي (KWD)</option>
                                    <option value="BHD" {{ old('salary_currency') == 'BHD' ? 'selected' : '' }}>🇧🇭 دينار بحريني (BHD)</option>
                                    <option value="OMR" {{ old('salary_currency') == 'OMR' ? 'selected' : '' }}>🇴🇲 ريال عماني (OMR)</option>
                                    <option value="JOD" {{ old('salary_currency') == 'JOD' ? 'selected' : '' }}>🇯🇴 دينار أردني (JOD)</option>
                                    <option value="EGP" {{ old('salary_currency') == 'EGP' ? 'selected' : '' }}>🇪🇬 جنيه مصري (EGP)</option>
                                    <option value="TND" {{ old('salary_currency') == 'TND' ? 'selected' : '' }}>🇹🇳 دينار تونسي (TND)</option>
                                    <option value="MAD" {{ old('salary_currency') == 'MAD' ? 'selected' : '' }}>🇲🇦 درهم مغربي (MAD)</option>
                                    <option value="DZD" {{ old('salary_currency') == 'DZD' ? 'selected' : '' }}>🇩🇿 دينار جزائري (DZD)</option>
                                    <option value="TRY" {{ old('salary_currency') == 'TRY' ? 'selected' : '' }}>🇹🇷 ليرة تركية (TRY)</option>
                                    <option value="CAD" {{ old('salary_currency') == 'CAD' ? 'selected' : '' }}>🇨🇦 دولار كندي (CAD)</option>
                                    <option value="AUD" {{ old('salary_currency') == 'AUD' ? 'selected' : '' }}>🇦🇺 دولار أسترالي (AUD)</option>
                                    <option value="CHF" {{ old('salary_currency') == 'CHF' ? 'selected' : '' }}>🇨🇭 فرنك سويسري (CHF)</option>
                                    <option value="JPY" {{ old('salary_currency') == 'JPY' ? 'selected' : '' }}>🇯🇵 ين ياباني (JPY)</option>
                                </select>
                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                    <i class="fas fa-chevron-down text-gray-400"></i>
                                </div>
                            </div>
                            @error('salary_currency')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">اختر العملة المناسبة للراتب</p>
                        </div>
                    </div>

                    <!-- معاينة الراتب -->
                    <div id="salary_preview" class="hidden mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                        <div class="flex items-center gap-2 mb-2">
                            <i class="fas fa-eye text-blue-600 dark:text-blue-400"></i>
                            <span class="text-sm font-medium text-blue-800 dark:text-blue-300">معاينة نطاق الراتب:</span>
                        </div>
                        <div id="salary_preview_text" class="text-lg font-bold text-blue-900 dark:text-blue-100"></div>
                    </div>

                    <!-- نصائح مفيدة -->
                    <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                        <div class="flex items-start gap-3">
                            <i class="fas fa-lightbulb text-yellow-600 dark:text-yellow-400 mt-1"></i>
                            <div>
                                <h4 class="text-sm font-medium text-yellow-800 dark:text-yellow-300 mb-2">نصائح لتحديد الراتب:</h4>
                                <ul class="text-xs text-yellow-700 dark:text-yellow-400 space-y-1">
                                    <li>• يمكنك تحديد حد أدنى فقط أو حد أعلى فقط</li>
                                    <li>• تأكد من أن الحد الأعلى أكبر من الحد الأدنى</li>
                                    <li>• اختر العملة المناسبة لموقع العمل</li>
                                    <li>• الراتب المناسب يجذب المرشحين المؤهلين</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Job Description -->
        <div class="glass-card rounded-xl p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
                <i class="fas fa-file-alt text-green-600"></i>
                وصف الوظيفة
            </h2>

            <div class="space-y-6">
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        وصف الوظيفة *
                    </label>
                    <textarea id="description" name="description" rows="6" required
                              class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">{{ old('description') }}</textarea>
                    @error('description')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="requirements" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        المتطلبات *
                    </label>
                    <textarea id="requirements" name="requirements" rows="6" required
                              class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">{{ old('requirements') }}</textarea>
                    @error('requirements')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>


            </div>
        </div>

        <!-- Dates -->
        <div class="glass-card rounded-xl p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
                <i class="fas fa-calendar text-yellow-600"></i>
                التواريخ
            </h2>

            <div class="grid grid-cols-1 md:grid-cols-1 gap-6">
                <div>
                    <label for="expires_at" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        تاريخ انتهاء الإعلان *
                    </label>
                    <input type="date" id="expires_at" name="expires_at" value="{{ old('expires_at', now()->addDays(30)->format('Y-m-d')) }}" required
                           class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    @error('expires_at')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Options -->
        <div class="glass-card rounded-xl p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
                <i class="fas fa-cog text-purple-600"></i>
                خيارات إضافية
            </h2>

            <div class="space-y-4">
                <div class="flex items-center">
                    <input type="checkbox" id="is_featured" name="is_featured" value="1" {{ old('is_featured') ? 'checked' : '' }}
                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                    <label for="is_featured" class="mr-2 text-sm font-medium text-gray-900 dark:text-gray-300">
                        وظيفة مميزة (رسوم إضافية)
                    </label>
                </div>
            </div>
        </div>

        <!-- Submit Buttons -->
        <div class="flex flex-col md:flex-row gap-4 justify-end">
            <button type="submit" name="save_as_draft" value="1" class="px-8 py-3 bg-gray-600 text-white rounded-xl hover:bg-gray-700 transition-all duration-300 flex items-center justify-center gap-2">
                <i class="fas fa-save"></i>
                حفظ كمسودة
            </button>

            <button type="submit" class="px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 flex items-center justify-center gap-2 shadow-lg">
                <i class="fas fa-paper-plane"></i>
                نشر الوظيفة
            </button>
        </div>
    </form>
</div>

<script>
// تم إزالة function saveDraft لأنه لم يعد مطلوباً

// التحقق من نطاق الراتب ومعاينة النتيجة
document.addEventListener('DOMContentLoaded', function() {
    const salaryMin = document.getElementById('salary_min');
    const salaryMax = document.getElementById('salary_max');
    const salaryCurrency = document.getElementById('salary_currency');
    const salaryPreview = document.getElementById('salary_preview');
    const salaryPreviewText = document.getElementById('salary_preview_text');

    // دالة تنسيق الأرقام
    function formatNumber(num) {
        return new Intl.NumberFormat('ar-LY').format(num);
    }

    // دالة التحقق من صحة النطاق
    function validateSalaryRange() {
        const minValue = parseFloat(salaryMin.value) || 0;
        const maxValue = parseFloat(salaryMax.value) || 0;

        // إزالة رسائل الخطأ السابقة
        salaryMin.setCustomValidity('');
        salaryMax.setCustomValidity('');

        // التحقق من صحة النطاق
        if (minValue > 0 && maxValue > 0 && minValue > maxValue) {
            salaryMax.setCustomValidity('الحد الأعلى يجب أن يكون أكبر من أو يساوي الحد الأدنى');
            salaryMax.classList.add('border-red-500');
            salaryMin.classList.add('border-red-500');
        } else {
            salaryMax.classList.remove('border-red-500');
            salaryMin.classList.remove('border-red-500');
        }

        // تحديث المعاينة
        updateSalaryPreview();
    }

    // دالة تحديث معاينة الراتب
    function updateSalaryPreview() {
        const minValue = parseFloat(salaryMin.value) || 0;
        const maxValue = parseFloat(salaryMax.value) || 0;
        const currency = salaryCurrency.value;

        let previewText = '';

        if (minValue > 0 && maxValue > 0) {
            previewText = `${formatNumber(minValue)} - ${formatNumber(maxValue)} ${currency}`;
        } else if (minValue > 0) {
            previewText = `من ${formatNumber(minValue)} ${currency}`;
        } else if (maxValue > 0) {
            previewText = `حتى ${formatNumber(maxValue)} ${currency}`;
        }

        if (previewText && currency) {
            salaryPreviewText.textContent = previewText;
            salaryPreview.classList.remove('hidden');
        } else {
            salaryPreview.classList.add('hidden');
        }
    }

    // ربط الأحداث
    if (salaryMin && salaryMax && salaryCurrency) {
        salaryMin.addEventListener('input', validateSalaryRange);
        salaryMax.addEventListener('input', validateSalaryRange);
        salaryCurrency.addEventListener('change', updateSalaryPreview);

        // تحديث المعاينة عند تحميل الصفحة
        updateSalaryPreview();
    }

    // تحسين تجربة المستخدم - إضافة تأثيرات بصرية
    const salaryInputs = [salaryMin, salaryMax];
    salaryInputs.forEach(input => {
        if (input) {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('ring-2', 'ring-blue-500');
            });

            input.addEventListener('blur', function() {
                this.parentElement.classList.remove('ring-2', 'ring-blue-500');
            });
        }
    });
});
</script>
@endsection
