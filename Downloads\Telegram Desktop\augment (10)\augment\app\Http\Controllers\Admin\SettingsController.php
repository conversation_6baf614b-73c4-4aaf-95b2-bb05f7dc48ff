<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class SettingsController extends Controller
{
    /**
     * Display the main settings index page.
     */
    public function index()
    {
        return view('admin.settings.index');
    }

    /**
     * Display the general settings page.
     */
    public function general()
    {
        // Get settings from cache or database
        $settings = $this->getSettings();

        return view('admin.settings.general', compact('settings'));
    }

    /**
     * Display the system settings page (moved from employer).
     */
    public function system()
    {
        $settings = $this->getSystemSettings();
        return view('admin.settings.system', compact('settings'));
    }

    /**
     * Update system settings (moved from employer).
     */
    public function updateSystem(Request $request)
    {
        $request->validate([
            'system_name' => 'required|string|max:255',
            'system_email' => 'required|email|max:255',
            'system_phone' => 'nullable|string|max:20',
            'system_address' => 'nullable|string|max:500',
            'maintenance_mode' => 'boolean',
            'registration_enabled' => 'boolean',
            'email_verification_required' => 'boolean',
            'auto_approve_jobs' => 'boolean',
            'max_applications_per_job' => 'nullable|integer|min:1',
            'job_expiry_days' => 'nullable|integer|min:1',
        ]);

        // Store system settings in cache for now (you can implement database storage later)
        $systemSettings = $request->except(['_token']);
        Cache::put('system_settings', $systemSettings, now()->addDays(30));

        return redirect()->route('admin.settings.system')
            ->with('success', 'تم حفظ إعدادات النظام بنجاح');
    }

    /**
     * Update general settings.
     */
    public function updateGeneral(Request $request)
    {
        $request->validate([
            'company_name' => 'required|string|max:255',
            'company_tagline' => 'nullable|string|max:255',
            'industry' => 'required|string',
            'company_size' => 'nullable|string',
            'about_company' => 'nullable|string',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:100',
            'country' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'website' => 'nullable|url|max:255',
            'linkedin' => 'nullable|url|max:255',
            'facebook' => 'nullable|url|max:255',
            'twitter' => 'nullable|url|max:255',
            'instagram' => 'nullable|url|max:255',
            'youtube' => 'nullable|url|max:255',
            'default_language' => 'required|string|in:ar,en',
            'timezone' => 'required|string',
            'email_notifications' => 'boolean',
            'application_notifications' => 'boolean',
            'message_notifications' => 'boolean',
        ]);

        // Store settings in cache for now (you can implement database storage later)
        $settings = $request->except(['_token']);
        Cache::put('general_settings', $settings, now()->addDays(30));

        return redirect()->route('admin.settings.general')
            ->with('success', 'تم حفظ الإعدادات بنجاح');
    }

    /**
     * Display the users settings page.
     */
    public function users()
    {
        return view('admin.settings.users');
    }

    /**
     * Display the payment settings page.
     */
    public function payment()
    {
        return view('admin.settings.payment');
    }

    /**
     * Get settings from cache or return defaults.
     */
    private function getSettings()
    {
        return Cache::get('general_settings', (object) [
            'company_name' => 'شركة التقنية المتطورة',
            'company_tagline' => 'حلول تقنية متطورة لعالم متغير',
            'industry' => 'tech',
            'company_size' => '51-200',
            'about_company' => 'شركة رائدة في مجال تقنية المعلومات، متخصصة في تطوير البرمجيات وتقديم الحلول التقنية المبتكرة للمؤسسات والشركات. تأسست الشركة في عام 2010 ومنذ ذلك الحين نمت لتصبح من الشركات الرائدة في المنطقة.',
            'email' => '<EMAIL>',
            'phone' => '+218 21 234 5678',
            'address' => 'شارع الجمهورية، حي الأندلس، طرابلس، ليبيا',
            'city' => 'طرابلس',
            'country' => 'ليبيا',
            'postal_code' => '12345',
            'website' => 'https://www.techcompany.com',
            'linkedin' => 'https://www.linkedin.com/company/techcompany',
            'facebook' => 'https://www.facebook.com/techcompany',
            'twitter' => 'https://twitter.com/techcompany',
            'instagram' => '',
            'youtube' => '',
            'default_language' => 'ar',
            'timezone' => 'Asia/Riyadh',
            'email_notifications' => true,
            'application_notifications' => true,
            'message_notifications' => true,
            'logo' => null,
        ]);
    }

    /**
     * Get system settings from cache or return defaults.
     */
    private function getSystemSettings()
    {
        return Cache::get('system_settings', (object) [
            'system_name' => 'Hire Me',
            'system_email' => '<EMAIL>',
            'system_phone' => '+218 21 123 4567',
            'system_address' => 'طرابلس، ليبيا',
            'maintenance_mode' => false,
            'registration_enabled' => true,
            'email_verification_required' => true,
            'auto_approve_jobs' => false,
            'max_applications_per_job' => 100,
            'job_expiry_days' => 30,
        ]);
    }
}
