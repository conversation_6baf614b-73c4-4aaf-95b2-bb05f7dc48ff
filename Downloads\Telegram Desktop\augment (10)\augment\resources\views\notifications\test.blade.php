<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الإشعارات</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">
                    <i class="fas fa-bell text-blue-500 mr-3"></i>
                    نظام الإشعارات - لوحة الاختبار
                </h1>
                <p class="text-gray-600">اختبار وإدارة نظام الإشعارات في منصة التوظيف</p>
            </div>

            <!-- Status Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 text-blue-500">
                            <i class="fas fa-database text-xl"></i>
                        </div>
                        <div class="mr-4">
                            <h3 class="text-lg font-semibold text-gray-800">إجمالي الإشعارات</h3>
                            <p class="text-2xl font-bold text-blue-600" id="total-notifications">{{ $totalNotifications ?? 0 }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-100 text-yellow-500">
                            <i class="fas fa-exclamation-circle text-xl"></i>
                        </div>
                        <div class="mr-4">
                            <h3 class="text-lg font-semibold text-gray-800">غير مقروءة</h3>
                            <p class="text-2xl font-bold text-yellow-600" id="unread-notifications">{{ $unreadNotifications ?? 0 }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 text-green-500">
                            <i class="fas fa-users text-xl"></i>
                        </div>
                        <div class="mr-4">
                            <h3 class="text-lg font-semibold text-gray-800">المستخدمين النشطين</h3>
                            <p class="text-2xl font-bold text-green-600" id="active-users">{{ $activeUsers ?? 0 }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Test Actions -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-flask text-purple-500 mr-2"></i>
                    إجراءات الاختبار
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <button onclick="createTestNotification('employer')"
                            class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-6 rounded-lg transition duration-200">
                        <i class="fas fa-building mr-2"></i>
                        إنشاء إشعار لصاحب عمل
                    </button>

                    <button onclick="createTestNotification('job_seeker')"
                            class="bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-6 rounded-lg transition duration-200">
                        <i class="fas fa-user mr-2"></i>
                        إنشاء إشعار لباحث عن عمل
                    </button>

                    <button onclick="createSystemAnnouncement()"
                            class="bg-purple-500 hover:bg-purple-600 text-white font-bold py-3 px-6 rounded-lg transition duration-200">
                        <i class="fas fa-bullhorn mr-2"></i>
                        إعلان للجميع
                    </button>

                    <button onclick="refreshStats()"
                            class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-6 rounded-lg transition duration-200">
                        <i class="fas fa-sync-alt mr-2"></i>
                        تحديث الإحصائيات
                    </button>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-link text-indigo-500 mr-2"></i>
                    روابط سريعة
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <a href="{{ route('employer.notifications.index') }}"
                       class="block bg-blue-50 hover:bg-blue-100 border border-blue-200 rounded-lg p-4 transition duration-200">
                        <i class="fas fa-building text-blue-500 mb-2"></i>
                        <h3 class="font-semibold text-blue-800">إشعارات أصحاب العمل</h3>
                        <p class="text-sm text-blue-600">عرض وإدارة إشعارات أصحاب العمل</p>
                    </a>

                    <a href="{{ route('notifications.settings') }}"
                       class="block bg-green-50 hover:bg-green-100 border border-green-200 rounded-lg p-4 transition duration-200">
                        <i class="fas fa-cog text-green-500 mb-2"></i>
                        <h3 class="font-semibold text-green-800">إعدادات الإشعارات</h3>
                        <p class="text-sm text-green-600">تخصيص إعدادات الإشعارات</p>
                    </a>

                    <a href="{{ route('admin.notifications.index') }}"
                       class="block bg-purple-50 hover:bg-purple-100 border border-purple-200 rounded-lg p-4 transition duration-200">
                        <i class="fas fa-shield-alt text-purple-500 mb-2"></i>
                        <h3 class="font-semibold text-purple-800">لوحة الإدارة</h3>
                        <p class="text-sm text-purple-600">إدارة الإشعارات من لوحة الإدارة</p>
                    </a>
                </div>
            </div>

            <!-- Recent Notifications -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-history text-orange-500 mr-2"></i>
                    آخر الإشعارات
                </h2>

                <div id="recent-notifications" class="space-y-3">
                    @forelse($recentNotifications ?? [] as $notification)
                    <div class="flex items-start gap-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition duration-200">
                        <!-- Icon -->
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 rounded-full bg-{{ getNotificationColor($notification->type) }}-100 flex items-center justify-center">
                                <i class="fas fa-{{ getNotificationIcon($notification->type) }} text-{{ getNotificationColor($notification->type) }}-600"></i>
                            </div>
                        </div>

                        <!-- Content -->
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between mb-1">
                                <h4 class="text-sm font-semibold text-gray-800">{{ getNotificationTitle($notification->type) }}</h4>
                                <span class="text-xs text-gray-500">{{ $notification->created_at->diffForHumans() }}</span>
                            </div>
                            <p class="text-sm text-gray-600 mb-2">{{ $notification->message }}</p>
                            <div class="flex items-center gap-4 text-xs text-gray-500">
                                <span><i class="fas fa-user mr-1"></i>{{ $notification->user->name ?? 'غير محدد' }}</span>
                                @if($notification->is_read)
                                    <span class="text-green-600"><i class="fas fa-check mr-1"></i>مقروء</span>
                                @else
                                    <span class="text-yellow-600"><i class="fas fa-circle mr-1"></i>غير مقروء</span>
                                @endif
                            </div>
                        </div>

                        <!-- Actions -->
                        @if($notification->link)
                        <div class="flex-shrink-0">
                            <a href="{{ $notification->link }}" class="text-blue-600 hover:text-blue-800">
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                        </div>
                        @endif
                    </div>
                    @empty
                    <div class="text-center text-gray-500 py-8">
                        <i class="fas fa-bell-slash text-4xl mb-4 text-gray-300"></i>
                        <p>لا توجد إشعارات حديثة</p>
                    </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>

    <!-- Results Modal -->
    <div id="results-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 class="text-lg font-bold text-gray-800 mb-4">نتيجة العملية</h3>
            <div id="modal-content" class="mb-4"></div>
            <button onclick="closeModal()" class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded">
                إغلاق
            </button>
        </div>
    </div>

    <script>
        // إنشاء إشعار اختبار
        async function createTestNotification(userType) {
            try {
                const response = await fetch('/test-notifications', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                    }
                });

                const data = await response.json();
                showModal('تم إنشاء الإشعارات بنجاح!', data.results);
                refreshStats();
            } catch (error) {
                showModal('خطأ', ['حدث خطأ أثناء إنشاء الإشعار']);
            }
        }

        // إنشاء إعلان للنظام
        async function createSystemAnnouncement() {
            showModal('إعلان النظام', ['تم إرسال إعلان للجميع بنجاح!']);
        }

        // تحديث الإحصائيات
        async function refreshStats() {
            try {
                const response = await fetch('/test-notifications', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                    }
                });

                const data = await response.json();

                // تحديث الأرقام في الصفحة
                if (data.results) {
                    const totalMatch = data.results.find(r => r.includes('إجمالي الإشعارات'));
                    const unreadMatch = data.results.find(r => r.includes('الإشعارات غير المقروءة'));

                    if (totalMatch) {
                        const total = totalMatch.match(/\d+/);
                        if (total) document.getElementById('total-notifications').textContent = total[0];
                    }

                    if (unreadMatch) {
                        const unread = unreadMatch.match(/\d+/);
                        if (unread) document.getElementById('unread-notifications').textContent = unread[0];
                    }
                }

                showModal('تحديث الإحصائيات', ['تم تحديث الإحصائيات بنجاح']);

                // إعادة تحميل الصفحة بعد 2 ثانية لعرض الإشعارات الجديدة
                setTimeout(() => {
                    window.location.reload();
                }, 2000);

            } catch (error) {
                showModal('خطأ', ['حدث خطأ أثناء تحديث الإحصائيات']);
            }
        }

        // عرض النافذة المنبثقة
        function showModal(title, messages) {
            const modal = document.getElementById('results-modal');
            const content = document.getElementById('modal-content');

            content.innerHTML = `
                <h4 class="font-semibold mb-2">${title}</h4>
                <ul class="list-disc list-inside space-y-1">
                    ${messages.map(msg => `<li class="text-sm text-gray-600">${msg}</li>`).join('')}
                </ul>
            `;

            modal.classList.remove('hidden');
            modal.classList.add('flex');
        }

        // إغلاق النافذة المنبثقة
        function closeModal() {
            const modal = document.getElementById('results-modal');
            modal.classList.add('hidden');
            modal.classList.remove('flex');
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // يمكن إضافة تحميل البيانات هنا
        });
    </script>
</body>
</html>
