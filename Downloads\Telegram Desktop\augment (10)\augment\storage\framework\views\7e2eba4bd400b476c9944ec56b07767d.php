<?php if($paginator->hasPages()): ?>
    <nav role="navigation" aria-label="<?php echo e(__('Pagination Navigation')); ?>" class="flex items-center justify-center">
        <div class="flex justify-between flex-1 sm:hidden">
            <?php if($paginator->onFirstPage()): ?>
                <span class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-400 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 cursor-default leading-5 rounded-lg">
                    <i class="fas fa-chevron-right mr-2"></i>
                    السابق
                </span>
            <?php else: ?>
                <a href="<?php echo e($paginator->previousPageUrl()); ?>" class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 leading-5 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200">
                    <i class="fas fa-chevron-right mr-2"></i>
                    السابق
                </a>
            <?php endif; ?>

            <?php if($paginator->hasMorePages()): ?>
                <a href="<?php echo e($paginator->nextPageUrl()); ?>" class="relative inline-flex items-center px-4 py-2 ml-3 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 leading-5 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200">
                    التالي
                    <i class="fas fa-chevron-left ml-2"></i>
                </a>
            <?php else: ?>
                <span class="relative inline-flex items-center px-4 py-2 ml-3 text-sm font-medium text-gray-400 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 cursor-default leading-5 rounded-lg">
                    التالي
                    <i class="fas fa-chevron-left ml-2"></i>
                </span>
            <?php endif; ?>
        </div>

        <div class="hidden sm:flex sm:items-center sm:justify-center">
            <div class="flex items-center gap-2">
                
                <?php if($paginator->onFirstPage()): ?>
                    <span aria-disabled="true" aria-label="<?php echo e(__('pagination.previous')); ?>" class="relative inline-flex items-center justify-center w-10 h-10 text-sm font-medium text-gray-400 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 cursor-default rounded-lg">
                        <i class="fas fa-chevron-right"></i>
                    </span>
                <?php else: ?>
                    <a href="<?php echo e($paginator->previousPageUrl()); ?>" rel="prev" class="relative inline-flex items-center justify-center w-10 h-10 text-sm font-medium text-gray-600 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200" aria-label="<?php echo e(__('pagination.previous')); ?>">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                <?php endif; ?>

                
                <?php $__currentLoopData = $elements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $element): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    
                    <?php if(is_string($element)): ?>
                        <span aria-disabled="true" class="relative inline-flex items-center justify-center w-10 h-10 text-sm font-medium text-gray-400 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 cursor-default rounded-lg">
                            <?php echo e($element); ?>

                        </span>
                    <?php endif; ?>

                    
                    <?php if(is_array($element)): ?>
                        <?php $__currentLoopData = $element; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $page => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if($page == $paginator->currentPage()): ?>
                                <span aria-current="page" class="relative inline-flex items-center justify-center w-10 h-10 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-purple-600 border border-transparent cursor-default rounded-lg shadow-lg">
                                    <?php echo e($page); ?>

                                </span>
                            <?php else: ?>
                                <a href="<?php echo e($url); ?>" class="relative inline-flex items-center justify-center w-10 h-10 text-sm font-medium text-gray-600 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200" aria-label="<?php echo e(__('Go to page :page', ['page' => $page])); ?>">
                                    <?php echo e($page); ?>

                                </a>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                
                <?php if($paginator->hasMorePages()): ?>
                    <a href="<?php echo e($paginator->nextPageUrl()); ?>" rel="next" class="relative inline-flex items-center justify-center w-10 h-10 text-sm font-medium text-gray-600 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200" aria-label="<?php echo e(__('pagination.next')); ?>">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                <?php else: ?>
                    <span aria-disabled="true" aria-label="<?php echo e(__('pagination.next')); ?>" class="relative inline-flex items-center justify-center w-10 h-10 text-sm font-medium text-gray-400 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 cursor-default rounded-lg">
                        <i class="fas fa-chevron-left"></i>
                    </span>
                <?php endif; ?>
            </div>
        </div>
    </nav>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Downloads\Telegram Desktop\augment (10)\augment\resources\views/pagination/tailwind.blade.php ENDPATH**/ ?>