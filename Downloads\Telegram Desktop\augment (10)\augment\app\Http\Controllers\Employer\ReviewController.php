<?php

namespace App\Http\Controllers\Employer;

use App\Http\Controllers\Controller;
use App\Models\Review;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ReviewController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:employer');
    }

    /**
     * عرض قائمة التقييمات
     */
    public function index(Request $request)
    {
        $employer = Auth::user();

        $query = Review::where('reviewed_id', $employer->id)
                      ->where('type', 'company')
                      ->with(['reviewer', 'approvedBy']);

        // فلترة حسب التقييم
        if ($request->filled('rating')) {
            $query->where('rating', $request->rating);
        }

        // البحث
        if ($request->filled('search')) {
            $query->whereHas('reviewer', function($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%');
            });
        }

        // فلترة حسب الحالة
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $reviews = $query->latest()->paginate(15);

        // إحصائيات التقييمات
        $baseQuery = Review::where('reviewed_id', $employer->id)->where('type', 'company');
        $stats = [
            'total' => $baseQuery->count(),
            'average' => round($baseQuery->avg('rating'), 1),
            'approved' => $baseQuery->where('status', 'approved')->count(),
            'pending' => $baseQuery->where('status', 'pending')->count(),
            'rejected' => $baseQuery->where('status', 'rejected')->count(),
            'five_star' => $baseQuery->where('rating', 5)->count(),
            'four_star' => $baseQuery->where('rating', 4)->count(),
            'three_star' => $baseQuery->where('rating', 3)->count(),
            'two_star' => $baseQuery->where('rating', 2)->count(),
            'one_star' => $baseQuery->where('rating', 1)->count(),
        ];

        return view('employer.reviews.index', compact('reviews', 'stats'));
    }

    /**
     * عرض تفاصيل التقييم
     */
    public function show(Review $review)
    {
        // التحقق من أن التقييم يخص صاحب العمل
        if ($review->reviewed_id !== Auth::id() || $review->type !== 'company') {
            abort(403, 'غير مصرح لك بعرض هذا التقييم');
        }

        $review->load(['reviewer', 'approvedBy']);

        return view('employer.reviews.show', compact('review'));
    }

    /**
     * الرد على التقييم
     */
    public function respond(Request $request, Review $review)
    {
        // التحقق من أن التقييم يخص صاحب العمل
        if ($review->reviewed_id !== Auth::id() || $review->type !== 'company') {
            abort(403, 'غير مصرح لك بالرد على هذا التقييم');
        }

        $request->validate([
            'response' => 'required|string|max:1000',
        ]);

        $review->update([
            'employer_response' => $request->response,
            'responded_at' => now(),
        ]);

        return redirect()->back()->with('success', 'تم إضافة ردك على التقييم بنجاح');
    }
}
