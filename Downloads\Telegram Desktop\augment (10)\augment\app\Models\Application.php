<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

class Application extends Model
{
    use HasFactory;

    // حالات الطلب المدعومة
    const STATUS_PENDING = 'pending';
    const STATUS_REVIEWED = 'reviewed';
    const STATUS_SHORTLISTED = 'shortlisted';
    const STATUS_REJECTED = 'rejected';
    const STATUS_HIRED = 'hired';
    const STATUS_ACCEPTED = 'accepted'; // للتوافق مع النظام القديم

    protected $fillable = [
        'job_id',
        'job_seeker_id',
        'cover_letter',
        'status',
        'applied_at',
        'admin_notes',
        'employer_notes',
        'employer_rating',
        'reviewed_at',
    ];

    protected $casts = [
        'applied_at' => 'datetime',
        'reviewed_at' => 'datetime',
        'employer_rating' => 'integer',
    ];

    /**
     * Get the job that owns the application.
     */
    public function job(): BelongsTo
    {
        return $this->belongsTo(Job::class);
    }

    /**
     * Get the job seeker that owns the application.
     */
    public function jobSeeker(): BelongsTo
    {
        return $this->belongsTo(JobSeeker::class);
    }

    /**
     * Get the user through job seeker relationship.
     */
    public function user()
    {
        return $this->hasOneThrough(
            User::class,
            JobSeeker::class,
            'id',           // Foreign key on JobSeeker table
            'id',           // Foreign key on User table
            'job_seeker_id', // Local key on Application table
            'user_id'       // Local key on JobSeeker table
        );
    }

    /**
     * Get the user relationship through job seeker (accessor).
     */
    public function getUserAttribute()
    {
        return $this->jobSeeker?->user;
    }

    /**
     * Get applicant name safely.
     */
    public function getApplicantNameAttribute()
    {
        return $this->jobSeeker && $this->jobSeeker->user
            ? $this->jobSeeker->user->name
            : 'مستخدم غير معروف';
    }

    /**
     * Get applicant email safely.
     */
    public function getApplicantEmailAttribute()
    {
        return $this->jobSeeker && $this->jobSeeker->user
            ? $this->jobSeeker->user->email
            : 'غير متوفر';
    }

    /**
     * Get company name safely.
     */
    public function getCompanyNameAttribute()
    {
        return $this->job && $this->job->employer
            ? $this->job->employer->company_name
            : 'غير محدد';
    }

    /**
     * Scope a query to only include pending applications.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include accepted applications.
     */
    public function scopeAccepted($query)
    {
        return $query->where('status', 'accepted');
    }

    /**
     * Scope a query to only include rejected applications.
     */
    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    /**
     * Get all available status options.
     */
    public static function getStatusOptions()
    {
        return [
            self::STATUS_PENDING => 'قيد المراجعة',
            self::STATUS_REVIEWED => 'تمت المراجعة',
            self::STATUS_SHORTLISTED => 'في القائمة المختصرة',
            self::STATUS_REJECTED => 'مرفوض',
            self::STATUS_HIRED => 'تم التوظيف',
            self::STATUS_ACCEPTED => 'مقبول',
        ];
    }

    /**
     * Get status label in Arabic.
     */
    public function getStatusLabelAttribute()
    {
        $options = self::getStatusOptions();
        return $options[$this->status] ?? $this->status;
    }
}
