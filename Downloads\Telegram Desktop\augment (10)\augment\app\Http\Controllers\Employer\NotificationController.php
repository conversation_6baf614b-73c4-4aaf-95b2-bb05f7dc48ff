<?php

namespace App\Http\Controllers\Employer;

use App\Http\Controllers\Controller;
use App\Models\Notification;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class NotificationController extends Controller
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->middleware('auth');
        $this->middleware('role:employer');
        $this->notificationService = $notificationService;
    }

    /**
     * عرض إشعارات صاحب العمل
     */
    public function index(Request $request)
    {
        $employer = Auth::user();
        $query = Notification::where('user_id', $employer->id);

        // فلترة حسب النوع
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // فلترة حسب حالة القراءة
        if ($request->filled('is_read')) {
            $query->where('is_read', $request->is_read);
        }

        // البحث في الرسائل
        if ($request->filled('search')) {
            $query->where('message', 'like', '%' . $request->search . '%');
        }

        $notifications = $query->latest()->paginate(15);
        $unreadCount = $this->notificationService->getUnreadCount($employer);

        return view('employer.notifications.index', compact('notifications', 'unreadCount'));
    }

    /**
     * تحديد إشعار كمقروء والانتقال إلى الرابط
     */
    public function markAsRead(Notification $notification)
    {
        try {
            // التحقق من أن الإشعار يخص المستخدم الحالي
            if ($notification->user_id !== Auth::id()) {
                abort(403, 'غير مصرح لك بالوصول إلى هذا الإشعار');
            }

            $notification->markAsRead();

            if ($notification->link) {
                return redirect($notification->link);
            }

            return redirect()->back()->with('success', 'تم تحديد الإشعار كمقروء');

        } catch (\Exception $e) {
            Log::error('خطأ في تحديد الإشعار كمقروء (Employer): ' . $e->getMessage(), [
                'notification_id' => $notification->id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return redirect()->back()->with('error', 'حدث خطأ أثناء معالجة الإشعار');
        }
    }

    /**
     * تحديد جميع الإشعارات كمقروءة
     */
    public function markAllAsRead()
    {
        Notification::where('user_id', Auth::id())->update(['is_read' => true]);
        return redirect()->back()->with('success', 'تم تحديد جميع الإشعارات كمقروءة');
    }

    /**
     * حذف إشعار
     */
    public function destroy(Notification $notification)
    {
        try {
            // التحقق من أن الإشعار يخص المستخدم الحالي
            if ($notification->user_id !== Auth::id()) {
                abort(403, 'غير مصرح لك بحذف هذا الإشعار');
            }

            $notification->delete();

            return redirect()->back()->with('success', 'تم حذف الإشعار بنجاح');

        } catch (\Exception $e) {
            Log::error('خطأ في حذف الإشعار (Employer): ' . $e->getMessage(), [
                'notification_id' => $notification->id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return redirect()->back()->with('error', 'حدث خطأ أثناء حذف الإشعار');
        }
    }
}
