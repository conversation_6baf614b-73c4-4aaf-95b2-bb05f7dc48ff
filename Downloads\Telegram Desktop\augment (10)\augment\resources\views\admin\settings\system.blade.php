@extends('layouts.admin')

@section('title', 'إعدادات النظام - Hire Me')
@section('header_title', 'إعدادات النظام')

@section('content')
<div class="p-8 space-y-8">
    <!-- Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">إعدادات النظام</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">إدارة إعدادات النظام العامة والتفضيلات</p>
        </div>
        <div class="mt-4 md:mt-0">
            <a href="{{ route('admin.settings.general') }}" class="btn-gradient text-white px-6 py-3 rounded-xl hover:shadow-lg transition-all duration-300">
                <i class="fas fa-cog ml-2"></i>
                الإعدادات العامة
            </a>
        </div>
    </div>

    @if(session('success'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-xl">
            {{ session('success') }}
        </div>
    @endif

    <form action="{{ route('admin.settings.system.update') }}" method="POST" class="space-y-8">
        @csrf

        <!-- معلومات النظام الأساسية -->
        <div class="glass-card rounded-2xl p-8">
            <h2 class="text-2xl font-bold text-gradient mb-6 flex items-center gap-3">
                <div class="w-10 h-10 gradient-primary rounded-xl flex items-center justify-center">
                    <i class="fas fa-info-circle text-white"></i>
                </div>
                معلومات النظام الأساسية
            </h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        اسم النظام
                    </label>
                    <input type="text" name="system_name" value="{{ old('system_name', $settings->system_name ?? 'Hire Me') }}" 
                           class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    @error('system_name')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        البريد الإلكتروني للنظام
                    </label>
                    <input type="email" name="system_email" value="{{ old('system_email', $settings->system_email ?? '<EMAIL>') }}" 
                           class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    @error('system_email')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        رقم الهاتف
                    </label>
                    <input type="text" name="system_phone" value="{{ old('system_phone', $settings->system_phone ?? '') }}" 
                           class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    @error('system_phone')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        العنوان
                    </label>
                    <input type="text" name="system_address" value="{{ old('system_address', $settings->system_address ?? '') }}" 
                           class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    @error('system_address')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- إعدادات النظام -->
        <div class="glass-card rounded-2xl p-8">
            <h2 class="text-2xl font-bold text-gradient mb-6 flex items-center gap-3">
                <div class="w-10 h-10 gradient-accent rounded-xl flex items-center justify-center">
                    <i class="fas fa-cogs text-white"></i>
                </div>
                إعدادات النظام
            </h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div>
                        <label class="text-sm font-medium text-gray-700 dark:text-gray-300">وضع الصيانة</label>
                        <p class="text-xs text-gray-500 dark:text-gray-400">تفعيل وضع الصيانة للموقع</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" name="maintenance_mode" value="1" 
                               {{ old('maintenance_mode', $settings->maintenance_mode ?? false) ? 'checked' : '' }}
                               class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                </div>

                <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div>
                        <label class="text-sm font-medium text-gray-700 dark:text-gray-300">تفعيل التسجيل</label>
                        <p class="text-xs text-gray-500 dark:text-gray-400">السماح للمستخدمين الجدد بالتسجيل</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" name="registration_enabled" value="1" 
                               {{ old('registration_enabled', $settings->registration_enabled ?? true) ? 'checked' : '' }}
                               class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                </div>

                <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div>
                        <label class="text-sm font-medium text-gray-700 dark:text-gray-300">تأكيد البريد الإلكتروني</label>
                        <p class="text-xs text-gray-500 dark:text-gray-400">طلب تأكيد البريد الإلكتروني عند التسجيل</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" name="email_verification_required" value="1" 
                               {{ old('email_verification_required', $settings->email_verification_required ?? true) ? 'checked' : '' }}
                               class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                </div>

                <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div>
                        <label class="text-sm font-medium text-gray-700 dark:text-gray-300">الموافقة التلقائية على الوظائف</label>
                        <p class="text-xs text-gray-500 dark:text-gray-400">الموافقة التلقائية على الوظائف المنشورة</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" name="auto_approve_jobs" value="1" 
                               {{ old('auto_approve_jobs', $settings->auto_approve_jobs ?? false) ? 'checked' : '' }}
                               class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                </div>
            </div>
        </div>

        <!-- إعدادات الوظائف -->
        <div class="glass-card rounded-2xl p-8">
            <h2 class="text-2xl font-bold text-gradient mb-6 flex items-center gap-3">
                <div class="w-10 h-10 gradient-success rounded-xl flex items-center justify-center">
                    <i class="fas fa-briefcase text-white"></i>
                </div>
                إعدادات الوظائف
            </h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        الحد الأقصى للطلبات لكل وظيفة
                    </label>
                    <input type="number" name="max_applications_per_job" min="1" 
                           value="{{ old('max_applications_per_job', $settings->max_applications_per_job ?? 100) }}" 
                           class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    @error('max_applications_per_job')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        مدة انتهاء الوظائف (بالأيام)
                    </label>
                    <input type="number" name="job_expiry_days" min="1" 
                           value="{{ old('job_expiry_days', $settings->job_expiry_days ?? 30) }}" 
                           class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    @error('job_expiry_days')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- أزرار الحفظ -->
        <div class="flex justify-end gap-4">
            <a href="{{ route('admin.settings.general') }}" class="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                إلغاء
            </a>
            <button type="submit" class="btn-gradient text-white px-8 py-3 rounded-xl hover:shadow-lg transition-all duration-300">
                <i class="fas fa-save ml-2"></i>
                حفظ الإعدادات
            </button>
        </div>
    </form>
</div>
@endsection

@section('scripts')
<script>
    // Add any JavaScript functionality here if needed
    document.addEventListener('DOMContentLoaded', function() {
        // Form validation or other interactive features can be added here
        console.log('System settings page loaded');
    });
</script>
@endsection
