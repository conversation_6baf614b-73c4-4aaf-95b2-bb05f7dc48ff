@extends('layouts.admin')

@section('title', 'التقارير المالية - Hire Me')
@section('header_title', 'التقارير المالية')

@section('content')
<div class="p-6">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
            <h2 class="text-2xl font-bold">التقارير المالية</h2>
            <p class="text-gray-600 dark:text-gray-400 mt-1">تحليل الإيرادات والمصروفات ومؤشرات الأداء المالي</p>
        </div>
        <div class="mt-4 md:mt-0 flex gap-3">
            <button id="printReportBtn" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors flex items-center gap-2">
                <i class="fas fa-print"></i>
                <span>طباعة</span>
            </button>
            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors flex items-center gap-2">
                    <i class="fas fa-download"></i>
                    <span>تصدير</span>
                    <i class="fas fa-chevron-down text-xs"></i>
                </button>
                <div x-show="open" @click.away="open = false" class="absolute left-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg z-10">
                    <a href="{{ route('admin.reports.export.pdf', 'financial') }}?start_date={{ $filters->start_date ?? '' }}&end_date={{ $filters->end_date ?? '' }}&payment_type={{ $filters->payment_type ?? '' }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="far fa-file-pdf ml-2 text-red-500"></i>
                        تصدير PDF
                    </a>
                    <a href="{{ route('admin.reports.export.excel', 'financial') }}?start_date={{ $filters->start_date ?? '' }}&end_date={{ $filters->end_date ?? '' }}&payment_type={{ $filters->payment_type ?? '' }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="far fa-file-excel ml-2 text-green-500"></i>
                        تصدير Excel
                    </a>
                </div>
            </div>
            <button id="sharingOptionsBtn" class="px-4 py-2 gradient-bg text-white rounded-lg hover:opacity-90 transition-colors flex items-center gap-2">
                <i class="fas fa-share-alt"></i>
                <span>مشاركة</span>
            </button>
        </div>
    </div>

    <!-- Date Range and Filters -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6 mb-6">
        <div class="flex flex-col md:flex-row gap-4">
            <div class="flex-1 grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">من تاريخ</label>
                    <input type="date" id="start_date" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" value="{{ $filters->start_date ?? '2023-01-01' }}">
                </div>
                <div>
                    <label for="end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">إلى تاريخ</label>
                    <input type="date" id="end_date" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" value="{{ $filters->end_date ?? date('Y-m-d') }}">
                </div>
            </div>
            <div class="w-full md:w-48">
                <label for="subscription_filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">نوع الاشتراك</label>
                <select id="subscription_filter" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                    <option value="">جميع الاشتراكات</option>
                    <option value="basic" {{ ($filters->subscription_type ?? '') == 'basic' ? 'selected' : '' }}>أساسي</option>
                    <option value="standard" {{ ($filters->subscription_type ?? '') == 'standard' ? 'selected' : '' }}>قياسي</option>
                    <option value="premium" {{ ($filters->subscription_type ?? '') == 'premium' ? 'selected' : '' }}>متميز</option>
                </select>
            </div>
            <div class="w-full md:w-48">
                <label for="payment_method_filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">طريقة الدفع</label>
                <select id="payment_method_filter" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                    <option value="">جميع الطرق</option>
                    <option value="credit_card" {{ ($filters->payment_method ?? '') == 'credit_card' ? 'selected' : '' }}>بطاقة ائتمان</option>
                    <option value="bank_transfer" {{ ($filters->payment_method ?? '') == 'bank_transfer' ? 'selected' : '' }}>تحويل بنكي</option>
                    <option value="paypal" {{ ($filters->payment_method ?? '') == 'paypal' ? 'selected' : '' }}>باي بال</option>
                </select>
            </div>
            <div class="flex items-end">
                <button id="applyFiltersBtn" class="px-4 py-2.5 gradient-bg text-white rounded-lg hover:opacity-90 transition-colors w-full md:w-auto">
                    تطبيق
                </button>
            </div>
        </div>
    </div>

    <!-- Financial Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <!-- Total Revenue Card -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <div class="flex items-center justify-between mb-4">
                <div class="w-14 h-14 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                    <i class="fas fa-money-bill-wave text-green-600 dark:text-green-400 text-xl"></i>
                </div>
                <div class="text-left">
                    <p class="text-gray-500 dark:text-gray-400 text-sm">إجمالي الإيرادات</p>
                    <p class="text-3xl font-bold">{{ $stats->total_revenue ?? '6,427' }} <span class="text-lg">د.ل</span></p>
                </div>
            </div>
            <div class="flex items-center justify-between text-sm">
                <div class="flex items-center gap-1 {{ ($stats->revenue_change ?? 18) >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                    <i class="fas fa-{{ ($stats->revenue_change ?? 18) >= 0 ? 'arrow-up' : 'arrow-down' }}"></i>
                    <span>{{ abs($stats->revenue_change ?? 18) }}%</span>
                </div>
                <p class="text-gray-500 dark:text-gray-400">مقارنة بالفترة السابقة</p>
            </div>
        </div>

        <!-- Monthly Recurring Revenue Card -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <div class="flex items-center justify-between mb-4">
                <div class="w-14 h-14 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                    <i class="fas fa-sync-alt text-blue-600 dark:text-blue-400 text-xl"></i>
                </div>
                <div class="text-left">
                    <p class="text-gray-500 dark:text-gray-400 text-sm">الإيراد الشهري المتكرر</p>
                    <p class="text-3xl font-bold">{{ $stats->monthly_recurring_revenue ?? '1,867' }} <span class="text-lg">د.ل</span></p>
                </div>
            </div>
            <div class="flex items-center justify-between text-sm">
                <div class="flex items-center gap-1 {{ ($stats->mrr_change ?? 12) >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                    <i class="fas fa-{{ ($stats->mrr_change ?? 12) >= 0 ? 'arrow-up' : 'arrow-down' }}"></i>
                    <span>{{ abs($stats->mrr_change ?? 12) }}%</span>
                </div>
                <p class="text-gray-500 dark:text-gray-400">مقارنة بالفترة السابقة</p>
            </div>
        </div>

        <!-- Active Subscriptions Card -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <div class="flex items-center justify-between mb-4">
                <div class="w-14 h-14 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
                    <i class="fas fa-users text-purple-600 dark:text-purple-400 text-xl"></i>
                </div>
                <div class="text-left">
                    <p class="text-gray-500 dark:text-gray-400 text-sm">الاشتراكات النشطة</p>
                    <p class="text-3xl font-bold">{{ $stats->active_subscriptions ?? '28' }}</p>
                </div>
            </div>
            <div class="flex items-center justify-between text-sm">
                <div class="flex items-center gap-1 {{ ($stats->subscriptions_change ?? 8) >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                    <i class="fas fa-{{ ($stats->subscriptions_change ?? 8) >= 0 ? 'arrow-up' : 'arrow-down' }}"></i>
                    <span>{{ abs($stats->subscriptions_change ?? 8) }}%</span>
                </div>
                <p class="text-gray-500 dark:text-gray-400">مقارنة بالفترة السابقة</p>
            </div>
        </div>

        <!-- Average Revenue Per User Card -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <div class="flex items-center justify-between mb-4">
                <div class="w-14 h-14 rounded-full bg-yellow-100 dark:bg-yellow-900/30 flex items-center justify-center">
                    <i class="fas fa-user-tag text-yellow-600 dark:text-yellow-400 text-xl"></i>
                </div>
                <div class="text-left">
                    <p class="text-gray-500 dark:text-gray-400 text-sm">متوسط الإيراد للمستخدم</p>
                    <p class="text-3xl font-bold">{{ $stats->average_revenue_per_user ?? '229' }} <span class="text-lg">د.ل</span></p>
                </div>
            </div>
            <div class="flex items-center justify-between text-sm">
                <div class="flex items-center gap-1 {{ ($stats->arpu_change ?? 5) >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                    <i class="fas fa-{{ ($stats->arpu_change ?? 5) >= 0 ? 'arrow-up' : 'arrow-down' }}"></i>
                    <span>{{ abs($stats->arpu_change ?? 5) }}%</span>
                </div>
                <p class="text-gray-500 dark:text-gray-400">مقارنة بالفترة السابقة</p>
            </div>
        </div>
    </div>

    <!-- Revenue Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Revenue Over Time Chart -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-bold mb-4">الإيرادات بمرور الوقت</h3>
            <div id="revenueTimelineChart" class="h-72"></div>
        </div>

        <!-- Revenue by Subscription Type Chart -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-bold mb-4">الإيرادات حسب نوع الاشتراك</h3>
            <div id="subscriptionRevenueChart" class="h-72"></div>
        </div>
    </div>

    <!-- Additional Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <!-- Subscriptions by Type Chart -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-bold mb-4">الاشتراكات حسب النوع</h3>
            <div id="subscriptionTypeChart" class="h-64"></div>
        </div>

        <!-- Payments by Method Chart -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-bold mb-4">المدفوعات حسب طريقة الدفع</h3>
            <div id="paymentMethodChart" class="h-64"></div>
        </div>

        <!-- Customer Lifetime Value Chart -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-bold mb-4">قيمة العميل على مدار الاشتراك (LTV)</h3>
            <div id="ltvChart" class="h-64"></div>
        </div>
    </div>

    <!-- Recent Transactions Table -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm overflow-hidden mb-6">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-bold">المعاملات الأخيرة</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="w-full text-right text-sm">
                <thead class="bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-300">
                    <tr>
                        <th class="px-6 py-3">رقم المعاملة</th>
                        <th class="px-6 py-3">الشركة</th>
                        <th class="px-6 py-3">نوع الاشتراك</th>
                        <th class="px-6 py-3">المبلغ</th>
                        <th class="px-6 py-3">طريقة الدفع</th>
                        <th class="px-6 py-3">التاريخ</th>
                        <th class="px-6 py-3">الحالة</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse($recent_transactions ?? [] as $transaction)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">{{ $transaction->id }}</td>
                            <td class="px-6 py-4">{{ $transaction->company_name }}</td>
                            <td class="px-6 py-4">{{ $transaction->subscription_type }}</td>
                            <td class="px-6 py-4">{{ $transaction->amount }} ر.س</td>
                            <td class="px-6 py-4">{{ $transaction->payment_method }}</td>
                            <td class="px-6 py-4">{{ $transaction->date }}</td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5
                                    @if($transaction->status == 'completed') bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400
                                    @elseif($transaction->status == 'pending') bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400
                                    @elseif($transaction->status == 'failed') bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400
                                    @endif rounded-full text-xs">
                                    {{ $transaction->status_label }}
                                </span>
                            </td>
                        </tr>
                    @empty
                        <!-- Sample Transaction Data -->
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">TRX-2023-125</td>
                            <td class="px-6 py-4">شركة التقنية المتطورة</td>
                            <td class="px-6 py-4">متميز</td>
                            <td class="px-6 py-4">5,999 ر.س</td>
                            <td class="px-6 py-4">بطاقة ائتمان</td>
                            <td class="px-6 py-4">15 يوليو، 2023</td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">مكتمل</span>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">TRX-2023-124</td>
                            <td class="px-6 py-4">مستشفى الصحة الوطني</td>
                            <td class="px-6 py-4">قياسي</td>
                            <td class="px-6 py-4">3,999 ر.س</td>
                            <td class="px-6 py-4">تحويل بنكي</td>
                            <td class="px-6 py-4">10 يوليو، 2023</td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">مكتمل</span>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">TRX-2023-123</td>
                            <td class="px-6 py-4">جامعة المستقبل</td>
                            <td class="px-6 py-4">أساسي</td>
                            <td class="px-6 py-4">1,999 ر.س</td>
                            <td class="px-6 py-4">بطاقة ائتمان</td>
                            <td class="px-6 py-4">5 يوليو، 2023</td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">مكتمل</span>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">TRX-2023-122</td>
                            <td class="px-6 py-4">مركز التدريب المهني</td>
                            <td class="px-6 py-4">متميز</td>
                            <td class="px-6 py-4">5,999 ر.س</td>
                            <td class="px-6 py-4">باي بال</td>
                            <td class="px-6 py-4">2 يوليو، 2023</td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">مكتمل</span>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">TRX-2023-121</td>
                            <td class="px-6 py-4">شركة الخدمات المالية</td>
                            <td class="px-6 py-4">قياسي</td>
                            <td class="px-6 py-4">3,999 ر.س</td>
                            <td class="px-6 py-4">بطاقة ائتمان</td>
                            <td class="px-6 py-4">28 يونيو، 2023</td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400 rounded-full text-xs">قيد المعالجة</span>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700 text-center">
            <button class="text-primary hover:underline">عرض جميع المعاملات</button>
        </div>
    </div>

    <!-- Subscription Renewal Forecast -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm overflow-hidden mb-6">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-bold">توقعات تجديد الاشتراكات</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="w-full text-right text-sm">
                <thead class="bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-300">
                    <tr>
                        <th class="px-6 py-3">الشهر</th>
                        <th class="px-6 py-3">عدد الاشتراكات المتوقع تجديدها</th>
                        <th class="px-6 py-3">الإيراد المتوقع</th>
                        <th class="px-6 py-3">خطر عدم التجديد</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse($renewal_forecast ?? [] as $forecast)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">{{ $forecast->month }}</td>
                            <td class="px-6 py-4">{{ $forecast->subscriptions_count }}</td>
                            <td class="px-6 py-4">{{ $forecast->expected_revenue }} د.ل</td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5
                                    @if($forecast->churn_risk == 'low') bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400
                                    @elseif($forecast->churn_risk == 'medium') bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400
                                    @elseif($forecast->churn_risk == 'high') bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400
                                    @endif rounded-full text-xs">
                                    {{ $forecast->churn_risk_label }}
                                </span>
                            </td>
                        </tr>
                    @empty
                        <!-- Sample Forecast Data -->
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">أغسطس 2023</td>
                            <td class="px-6 py-4">5</td>
                            <td class="px-6 py-4">23,995 ر.س</td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">منخفض</span>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">سبتمبر 2023</td>
                            <td class="px-6 py-4">3</td>
                            <td class="px-6 py-4">13,997 ر.س</td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">منخفض</span>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">أكتوبر 2023</td>
                            <td class="px-6 py-4">7</td>
                            <td class="px-6 py-4">29,993 ر.س</td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400 rounded-full text-xs">متوسط</span>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">نوفمبر 2023</td>
                            <td class="px-6 py-4">4</td>
                            <td class="px-6 py-4">17,996 ر.س</td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 rounded-full text-xs">مرتفع</span>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">ديسمبر 2023</td>
                            <td class="px-6 py-4">9</td>
                            <td class="px-6 py-4">41,991 ر.س</td>
                            <td class="px-6 py-4">
                                <span class="px-2.5 py-0.5 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400 rounded-full text-xs">متوسط</span>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    <!-- Key Insights Card -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
        <h3 class="text-lg font-bold mb-4">أهم الرؤى والتوصيات المالية</h3>

        <div class="space-y-4">
            <div class="flex items-start gap-3">
                <div class="w-10 h-10 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-chart-line text-green-600 dark:text-green-400"></i>
                </div>
                <div>
                    <h4 class="font-medium">نمو الإيرادات</h4>
                    <p class="text-gray-600 dark:text-gray-400">شهدت الإيرادات زيادة بنسبة 18% مقارنة بالفترة السابقة، ويرجع ذلك أساسًا إلى زيادة عدد المشتركين في الباقة المتميزة.</p>
                </div>
            </div>

            <div class="flex items-start gap-3">
                <div class="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-user-tag text-blue-600 dark:text-blue-400"></i>
                </div>
                <div>
                    <h4 class="font-medium">ارتفاع متوسط الإيراد للمستخدم</h4>
                    <p class="text-gray-600 dark:text-gray-400">ارتفع متوسط الإيراد للمستخدم (ARPU) بنسبة 5%، مما يشير إلى نجاح استراتيجيات ترقية الاشتراكات وزيادة قيمة العملاء.</p>
                </div>
            </div>

            <div class="flex items-start gap-3">
                <div class="w-10 h-10 rounded-full bg-yellow-100 dark:bg-yellow-900/30 flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-yellow-600 dark:text-yellow-400"></i>
                </div>
                <div>
                    <h4 class="font-medium">خطر عدم التجديد</h4>
                    <p class="text-gray-600 dark:text-gray-400">توجد 4 اشتراكات معرضة لخطر مرتفع لعدم التجديد في نوفمبر 2023. من المستحسن التواصل مع هؤلاء العملاء وتقديم عروض خاصة للاحتفاظ بهم.</p>
                </div>
            </div>

            <div class="flex items-start gap-3">
                <div class="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-bullseye text-purple-600 dark:text-purple-400"></i>
                </div>
                <div>
                    <h4 class="font-medium">توصيات لتحسين الأداء المالي</h4>
                    <p class="text-gray-600 dark:text-gray-400">1. تقديم خصومات للدفع السنوي لزيادة الاستقرار المالي.<br>2. استهداف تحويل المشتركين من الباقة القياسية إلى المتميزة من خلال إبراز القيمة المضافة.<br>3. تنويع طرق الدفع لتسهيل عملية الاشتراك والتجديد.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sharing Options Modal -->
<div id="sharingModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
        </div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white dark:bg-dark-card rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white dark:bg-dark-card px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold">مشاركة التقرير</h3>
                    <button type="button" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300" id="closeModal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="space-y-4">
                    <div>
                        <label for="share_email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">مشاركة عبر البريد الإلكتروني</label>
                        <input type="email" name="share_email" id="share_email" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" placeholder="أدخل البريد الإلكتروني">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">مشاركة مباشرة</label>
                        <div class="flex gap-2">
                            <button class="w-12 h-12 bg-blue-600 hover:bg-blue-700 transition-colors text-white rounded-full flex items-center justify-center">
                                <i class="fab fa-linkedin-in"></i>
                            </button>
                            <button class="w-12 h-12 bg-sky-500 hover:bg-sky-600 transition-colors text-white rounded-full flex items-center justify-center">
                                <i class="fab fa-twitter"></i>
                            </button>
                            <button class="w-12 h-12 bg-green-600 hover:bg-green-700 transition-colors text-white rounded-full flex items-center justify-center">
                                <i class="fab fa-whatsapp"></i>
                            </button>
                            <button class="w-12 h-12 bg-primary hover:opacity-90 transition-colors text-white rounded-full flex items-center justify-center">
                                <i class="fas fa-envelope"></i>
                            </button>
                        </div>
                    </div>

                    <div>
                        <label for="share_link" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">رابط مباشر للتقرير</label>
                        <div class="relative">
                            <input type="text" name="share_link" id="share_link" class="block w-full pr-10 p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" value="https://hireme.com/admin/reports/financial?id=27594" readonly>
                            <button id="copyLink" class="absolute left-2.5 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400 hover:text-primary transition-colors">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>

                    <div>
                        <label for="schedule_report" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">جدولة التقرير</label>
                        <div class="grid grid-cols-2 gap-4">
                            <select id="schedule_frequency" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                                <option value="weekly">أسبوعي</option>
                                <option value="biweekly">كل أسبوعين</option>
                                <option value="monthly">شهري</option>
                            </select>
                            <input type="email" id="schedule_email" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" placeholder="البريد الإلكتروني">
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="confirmShare" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 gradient-bg text-base font-medium text-white hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm">
                    مشاركة
                </button>
                <button type="button" id="scheduleReport" class="w-full inline-flex justify-center rounded-md border border-primary text-primary shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium hover:bg-primary/10 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm">
                    جدولة
                </button>
                <button type="button" id="cancelModal" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    إلغاء
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<script>
    // Initialize Charts
    document.addEventListener('DOMContentLoaded', function () {
        // Detect dark mode
        const isDarkMode = document.documentElement.classList.contains('dark');
        const textColor = isDarkMode ? '#d1d5db' : '#374151';
        const gridColor = isDarkMode ? '#374151' : '#e5e7eb';

        // Revenue Timeline Chart (Line & Area Chart)
        const revenueTimelineOptions = {
            series: [{
                name: 'الإيرادات',
                type: 'area',
                data: [8500, 10200, 9800, 12500, 15800, 14500, 17850]
            }, {
                name: 'الإيراد الشهري المتكرر',
                type: 'line',
                data: [4200, 4800, 5100, 5700, 6300, 6900, 7500]
            }],
            chart: {
                height: 300,
                type: 'line',
                toolbar: {
                    show: false
                },
                fontFamily: 'inherit',
            },
            stroke: {
                width: [0, 3],
                curve: 'smooth'
            },
            fill: {
                type: 'gradient',
                gradient: {
                    shadeIntensity: 1,
                    opacityFrom: 0.7,
                    opacityTo: 0.2,
                    stops: [0, 90, 100]
                }
            },
            colors: ['#5D5CDE', '#10B981'],
            dataLabels: {
                enabled: false,
            },
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو'],
            markers: {
                size: 4
            },
            yaxis: {
                labels: {
                    formatter: function (val) {
                        return val.toLocaleString() + ' ر.س';
                    },
                    style: {
                        colors: textColor
                    }
                }
            },
            xaxis: {
                labels: {
                    style: {
                        colors: textColor
                    }
                }
            },
            grid: {
                borderColor: gridColor,
                row: {
                    colors: [isDarkMode ? 'transparent' : '#f8fafc', 'transparent'],
                    opacity: 0.5
                },
            },
            tooltip: {
                y: {
                    formatter: function (val) {
                        return val.toLocaleString() + ' ر.س';
                    }
                }
            },
            legend: {
                position: 'top',
                horizontalAlign: 'left',
                offsetY: -10,
                labels: {
                    colors: textColor
                }
            }
        };
        const revenueTimelineChart = new ApexCharts(document.querySelector('#revenueTimelineChart'), revenueTimelineOptions);
        revenueTimelineChart.render();

        // Subscription Revenue Chart (Bar Chart)
        const subscriptionRevenueOptions = {
            series: [{
                name: 'الإيرادات',
                data: [8997, 11997, 17997]
            }],
            chart: {
                type: 'bar',
                height: 300,
                fontFamily: 'inherit',
                toolbar: {
                    show: false
                }
            },
            plotOptions: {
                bar: {
                    borderRadius: 4,
                    dataLabels: {
                        position: 'top',
                    },
                }
            },
            colors: ['#5D5CDE'],
            dataLabels: {
                enabled: true,
                formatter: function (val) {
                    return val.toLocaleString() + ' ر.س';
                },
                offsetY: -20,
                style: {
                    fontSize: '12px',
                    colors: [textColor]
                }
            },
            xaxis: {
                categories: ['أساسي', 'قياسي', 'متميز'],
                position: 'bottom',
                labels: {
                    style: {
                        colors: textColor
                    }
                },
                axisBorder: {
                    show: false
                },
                axisTicks: {
                    show: false
                },
            },
            yaxis: {
                labels: {
                    formatter: function (val) {
                        return val.toLocaleString() + ' ر.س';
                    },
                    style: {
                        colors: textColor
                    }
                }
            },
            grid: {
                borderColor: gridColor,
            }
        };
        const subscriptionRevenueChart = new ApexCharts(document.querySelector('#subscriptionRevenueChart'), subscriptionRevenueOptions);
        subscriptionRevenueChart.render();

        // Subscription Type Chart (Donut Chart)
        const subscriptionTypeOptions = {
            series: [6, 10, 12],
            chart: {
                type: 'donut',
                height: 250,
                fontFamily: 'inherit',
            },
            labels: ['أساسي', 'قياسي', 'متميز'],
            colors: ['#10B981', '#3B82F6', '#5D5CDE'],
            legend: {
                position: 'bottom',
                offsetY: 0,
                labels: {
                    colors: textColor
                }
            },
            plotOptions: {
                pie: {
                    donut: {
                        size: '65%'
                    }
                }
            },
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        height: 250
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }],
            tooltip: {
                y: {
                    formatter: function(val) {
                        return val + ' اشتراك'
                    }
                }
            }
        };
        const subscriptionTypeChart = new ApexCharts(document.querySelector('#subscriptionTypeChart'), subscriptionTypeOptions);
        subscriptionTypeChart.render();

        // Payment Method Chart (Donut Chart)
        const paymentMethodOptions = {
            series: [65, 25, 10],
            chart: {
                type: 'donut',
                height: 250,
                fontFamily: 'inherit',
            },
            labels: ['بطاقة ائتمان', 'تحويل بنكي', 'باي بال'],
            colors: ['#5D5CDE', '#F59E0B', '#06B6D4'],
            legend: {
                position: 'bottom',
                offsetY: 0,
                labels: {
                    colors: textColor
                }
            },
            plotOptions: {
                pie: {
                    donut: {
                        size: '65%'
                    }
                }
            },
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        height: 250
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }],
            tooltip: {
                y: {
                    formatter: function(val) {
                        return val + '%'
                    }
                }
            }
        };
        const paymentMethodChart = new ApexCharts(document.querySelector('#paymentMethodChart'), paymentMethodOptions);
        paymentMethodChart.render();

        // LTV Chart (Bar Chart)
        const ltvOptions = {
            series: [{
                name: 'قيمة العميل على مدار الاشتراك',
                data: [9995, 15996, 29995]
            }],
            chart: {
                type: 'bar',
                height: 250,
                fontFamily: 'inherit',
                toolbar: {
                    show: false
                }
            },
            plotOptions: {
                bar: {
                    borderRadius: 4,
                    dataLabels: {
                        position: 'top',
                    },
                }
            },
            colors: ['#5D5CDE'],
            dataLabels: {
                enabled: true,
                formatter: function (val) {
                    return val.toLocaleString() + ' ر.س';
                },
                offsetY: -20,
                style: {
                    fontSize: '12px',
                    colors: [textColor]
                }
            },
            xaxis: {
                categories: ['أساسي', 'قياسي', 'متميز'],
                position: 'bottom',
                labels: {
                    style: {
                        colors: textColor
                    }
                },
                axisBorder: {
                    show: false
                },
                axisTicks: {
                    show: false
                },
            },
            yaxis: {
                labels: {
                    formatter: function (val) {
                        return val.toLocaleString() + ' ر.س';
                    },
                    style: {
                        colors: textColor
                    }
                }
            },
            grid: {
                borderColor: gridColor,
            }
        };
        const ltvChart = new ApexCharts(document.querySelector('#ltvChart'), ltvOptions);
        ltvChart.render();
    });

    // Sharing Modal
    const sharingOptionsBtn = document.getElementById('sharingOptionsBtn');
    const sharingModal = document.getElementById('sharingModal');
    const closeModal = document.getElementById('closeModal');
    const cancelModal = document.getElementById('cancelModal');
    const copyLink = document.getElementById('copyLink');
    const confirmShare = document.getElementById('confirmShare');
    const scheduleReport = document.getElementById('scheduleReport');

    // Show sharing modal
    sharingOptionsBtn.addEventListener('click', () => {
        sharingModal.classList.remove('hidden');
    });

    // Hide modal
    closeModal.addEventListener('click', () => {
        sharingModal.classList.add('hidden');
    });

    cancelModal.addEventListener('click', () => {
        sharingModal.classList.add('hidden');
    });

    // Copy link
    copyLink.addEventListener('click', () => {
        const linkInput = document.getElementById('share_link');
        linkInput.select();
        document.execCommand('copy');

        // Show copied feedback
        copyLink.innerHTML = '<i class="fas fa-check"></i>';
        setTimeout(() => {
            copyLink.innerHTML = '<i class="fas fa-copy"></i>';
        }, 2000);
    });

    // Share report
    confirmShare.addEventListener('click', () => {
        const emailInput = document.getElementById('share_email');
        if (emailInput.value) {
            // Here you would normally send an AJAX request to share the report
            console.log(`Sharing report with: ${emailInput.value}`);

            // Close modal
            sharingModal.classList.add('hidden');

            // Show success message
            alert('تم مشاركة التقرير بنجاح!');
        } else {
            emailInput.focus();
        }
    });

    // Schedule report
    scheduleReport.addEventListener('click', () => {
        const frequency = document.getElementById('schedule_frequency').value;
        const email = document.getElementById('schedule_email').value;

        if (email) {
            // Here you would normally send an AJAX request to schedule the report
            console.log(`Scheduling report: ${frequency} to ${email}`);

            // Close modal
            sharingModal.classList.add('hidden');

            // Show success message
            alert(`تم جدولة إرسال التقرير ${frequency === 'weekly' ? 'أسبوعياً' : frequency === 'biweekly' ? 'كل أسبوعين' : 'شهرياً'} إلى ${email}`);
        } else {
            document.getElementById('schedule_email').focus();
        }
    });

    // Close modal when clicking outside
    sharingModal.addEventListener('click', (e) => {
        if (e.target === sharingModal) {
            sharingModal.classList.add('hidden');
        }
    });

    // Print report
    const printReportBtn = document.getElementById('printReportBtn');

    printReportBtn.addEventListener('click', () => {
        window.print();
    });

    // Export report
    const exportReportBtn = document.getElementById('exportReportBtn');

    exportReportBtn.addEventListener('click', () => {
        // In a real app, this would trigger a download
        alert('جاري تصدير التقرير بصيغة PDF...');
    });

    // Apply filters
    const applyFiltersBtn = document.getElementById('applyFiltersBtn');

    applyFiltersBtn.addEventListener('click', () => {
        const startDate = document.getElementById('start_date').value;
        const endDate = document.getElementById('end_date').value;
        const subscription = document.getElementById('subscription_filter').value;
        const paymentMethod = document.getElementById('payment_method_filter').value;

        // Here you would normally send an AJAX request to update the report
        console.log('Applying filters:', { startDate, endDate, subscription, paymentMethod });

        // For demo purposes, show a loading message
        alert('جاري تحديث التقرير...');
    });
</script>
@endsection