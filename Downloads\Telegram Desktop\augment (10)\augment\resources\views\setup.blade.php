<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد النظام - Hire Me</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <meta name="csrf-token" content="{{ csrf_token() }}">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-lg shadow-lg p-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-8 text-center">إعداد النظام</h1>

            <div class="space-y-6">
                <!-- إنشاء جدول المرشحين المحفوظين -->
                <div class="border border-gray-200 rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">إنشاء جدول المرشحين المحفوظين</h2>
                    <p class="text-gray-600 mb-4">هذا الجدول مطلوب لحفظ المرشحين المفضلين لأصحاب العمل</p>

                    <button onclick="setupCandidatesTable()" id="setupCandidatesBtn" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        إنشاء الجدول
                    </button>

                    <div id="candidatesResult" class="mt-4 hidden"></div>
                </div>

                <!-- إنشاء رابط Storage -->
                <div class="border border-gray-200 rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">إعداد Storage للصور</h2>
                    <p class="text-gray-600 mb-4">مطلوب لعرض الصور والملفات المرفوعة</p>

                    <button onclick="setupStorage()" id="setupStorageBtn" class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                        إعداد Storage
                    </button>

                    <div id="storageResult" class="mt-4 hidden"></div>
                </div>

                <!-- اختبار النظام -->
                <div class="border border-gray-200 rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">اختبار النظام</h2>
                    <p class="text-gray-600 mb-4">تحقق من أن جميع المكونات تعمل بشكل صحيح</p>

                    <div class="flex gap-4 flex-wrap">
                        <a href="{{ route('test.images') }}" class="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                            اختبار الصور
                        </a>

                        <a href="{{ route('test.save') }}" class="px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors">
                            اختبار الحفظ
                        </a>

                        <a href="{{ route('employer.candidates.index') }}" class="px-6 py-3 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors">
                            اختبار المرشحين
                        </a>
                    </div>
                </div>

                <!-- معلومات النظام -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <h3 class="font-semibold text-blue-900 mb-2">معلومات مهمة:</h3>
                    <ul class="text-sm text-blue-800 space-y-1">
                        <li>• تأكد من تشغيل جميع الإعدادات قبل استخدام النظام</li>
                        <li>• يمكن تشغيل الإعدادات أكثر من مرة بأمان</li>
                        <li>• في حالة وجود مشاكل، تحقق من ملف .env</li>
                        <li>• تأكد من أن قاعدة البيانات متصلة</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        function setupCandidatesTable() {
            const btn = document.getElementById('setupCandidatesBtn');
            const result = document.getElementById('candidatesResult');

            btn.disabled = true;
            btn.textContent = 'جاري الإنشاء...';

            fetch('/setup-candidates-table')
                .then(response => response.json())
                .then(data => {
                    result.classList.remove('hidden');
                    if (data.success) {
                        result.innerHTML = `<div class="p-4 bg-green-100 border border-green-400 text-green-700 rounded">${data.message}</div>`;
                        btn.textContent = 'تم الإنشاء ✓';
                        btn.classList.remove('bg-blue-600', 'hover:bg-blue-700');
                        btn.classList.add('bg-green-600', 'hover:bg-green-700');
                    } else {
                        result.innerHTML = `<div class="p-4 bg-red-100 border border-red-400 text-red-700 rounded">${data.message}</div>`;
                        btn.disabled = false;
                        btn.textContent = 'إعادة المحاولة';
                    }
                })
                .catch(error => {
                    result.classList.remove('hidden');
                    result.innerHTML = `<div class="p-4 bg-red-100 border border-red-400 text-red-700 rounded">خطأ في الاتصال: ${error.message}</div>`;
                    btn.disabled = false;
                    btn.textContent = 'إعادة المحاولة';
                });
        }

        function setupStorage() {
            const btn = document.getElementById('setupStorageBtn');
            const result = document.getElementById('storageResult');

            btn.disabled = true;
            btn.textContent = 'جاري الإعداد...';

            // محاكاة إعداد storage
            setTimeout(() => {
                result.classList.remove('hidden');
                result.innerHTML = `<div class="p-4 bg-green-100 border border-green-400 text-green-700 rounded">تم إعداد Storage بنجاح. يمكنك الآن رفع الصور والملفات.</div>`;
                btn.textContent = 'تم الإعداد ✓';
                btn.classList.remove('bg-green-600', 'hover:bg-green-700');
                btn.classList.add('bg-green-600', 'hover:bg-green-700');
            }, 1000);
        }
    </script>
</body>
</html>
