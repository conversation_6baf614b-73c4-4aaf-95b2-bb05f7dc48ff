<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // إضافة حقول الحالة وملاحظات المدير إلى جدول تقييمات الشركات
        Schema::table('company_ratings', function (Blueprint $table) {
            $table->string('status')->default('pending')->after('comment');
            $table->text('admin_notes')->nullable()->after('status');
        });

        // إضافة حقول الحالة وملاحظات المدير إلى جدول تقييمات المرشحين
        Schema::table('profile_ratings', function (Blueprint $table) {
            $table->string('status')->default('pending')->after('comment');
            $table->text('admin_notes')->nullable()->after('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // إزالة حقول الحالة وملاحظات المدير من جدول تقييمات الشركات
        Schema::table('company_ratings', function (Blueprint $table) {
            $table->dropColumn(['status', 'admin_notes']);
        });

        // إزالة حقول الحالة وملاحظات المدير من جدول تقييمات المرشحين
        Schema::table('profile_ratings', function (Blueprint $table) {
            $table->dropColumn(['status', 'admin_notes']);
        });
    }
};
