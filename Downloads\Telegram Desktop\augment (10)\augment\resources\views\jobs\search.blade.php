@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                @if(request('keyword'))
                    نتائج البحث عن "{{ request('keyword') }}"
                @else
                    البحث عن الوظائف
                @endif
            </h1>
            @if(isset($totalJobs))
                <p class="text-gray-600 dark:text-gray-400">
                    تم العثور على {{ $totalJobs }} وظيفة
                    @if(request('keyword'))
                        تحتوي على "{{ request('keyword') }}"
                    @endif
                </p>
            @endif
        </div>

        <!-- Enhanced Search and Filter -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-md p-6 mb-8">
            <form action="{{ route('jobs.search') }}" method="GET" class="space-y-6">
                <!-- Main Search Row -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-search mr-2 text-primary"></i>
                            الكلمات المفتاحية
                        </label>
                        <div class="relative">
                            <input type="text" name="keyword" value="{{ request('keyword') }}"
                                   class="w-full px-4 py-3 pr-12 bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-primary focus:border-primary"
                                   placeholder="مثال: مطور ويب، مصمم، محاسب، Laravel، PHP">
                            <button type="submit" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary hover:text-primary-dark">
                                <i class="fas fa-search text-lg"></i>
                            </button>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-map-marker-alt mr-2 text-primary"></i>
                            الموقع
                        </label>
                        <input type="text" name="location" value="{{ request('location') }}"
                               class="w-full px-4 py-3 bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-primary focus:border-primary"
                               placeholder="طرابلس، مصراتة، بنغازي">
                    </div>
                </div>

                <!-- Advanced Filters Row -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-tags mr-2 text-primary"></i>
                            التصنيف
                        </label>
                        <select name="category" class="w-full px-4 py-3 bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-primary focus:border-primary">
                            <option value="">كل التصنيفات</option>
                            @if(isset($categories))
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}" {{ request('category') == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            @endif
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-clock mr-2 text-primary"></i>
                            نوع الوظيفة
                        </label>
                        <select name="type" class="w-full px-4 py-3 bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-primary focus:border-primary">
                            <option value="">كل الأنواع</option>
                            <option value="full_time" {{ request('type') == 'full_time' ? 'selected' : '' }}>دوام كامل</option>
                            <option value="part_time" {{ request('type') == 'part_time' ? 'selected' : '' }}>دوام جزئي</option>
                            <option value="remote" {{ request('type') == 'remote' ? 'selected' : '' }}>عن بعد</option>
                            <option value="freelance" {{ request('type') == 'freelance' ? 'selected' : '' }}>عمل حر</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-sort mr-2 text-primary"></i>
                            ترتيب النتائج
                        </label>
                        <select name="sort" class="w-full px-4 py-3 bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-primary focus:border-primary">
                            <option value="latest" {{ request('sort') == 'latest' ? 'selected' : '' }}>الأحدث</option>
                            <option value="oldest" {{ request('sort') == 'oldest' ? 'selected' : '' }}>الأقدم</option>
                            <option value="title" {{ request('sort') == 'title' ? 'selected' : '' }}>حسب العنوان</option>
                            <option value="company" {{ request('sort') == 'company' ? 'selected' : '' }}>حسب الشركة</option>
                        </select>
                    </div>

                    <div class="flex items-end">
                        <button type="submit" class="w-full bg-primary text-white font-semibold py-3 px-6 rounded-lg hover:bg-primary-dark transition-colors">
                            <i class="fas fa-search mr-2"></i>
                            بحث
                        </button>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="flex flex-wrap gap-2 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <span class="text-sm text-gray-600 dark:text-gray-400 mr-2">بحث سريع:</span>
                    <a href="{{ route('jobs.search', ['keyword' => 'مطور']) }}" class="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full text-sm hover:bg-primary hover:text-white transition-colors">مطور</a>
                    <a href="{{ route('jobs.search', ['keyword' => 'مصمم']) }}" class="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full text-sm hover:bg-primary hover:text-white transition-colors">مصمم</a>
                    <a href="{{ route('jobs.search', ['keyword' => 'محاسب']) }}" class="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full text-sm hover:bg-primary hover:text-white transition-colors">محاسب</a>
                    <a href="{{ route('jobs.search', ['keyword' => 'مسوق']) }}" class="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full text-sm hover:bg-primary hover:text-white transition-colors">مسوق</a>
                    <a href="{{ route('jobs.search', ['type' => 'remote']) }}" class="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full text-sm hover:bg-primary hover:text-white transition-colors">عن بعد</a>
                </div>
            </form>
        </div>
        
        <!-- Search Results -->
        <div class="space-y-6">
            @if(isset($jobs) && $jobs->count() > 0)
                <!-- Results Header -->
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center gap-4">
                        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                            النتائج ({{ $jobs->total() }})
                        </h2>
                        @if(request()->hasAny(['keyword', 'location', 'category', 'type']))
                            <a href="{{ route('jobs.search') }}" class="text-sm text-primary hover:underline">
                                <i class="fas fa-times mr-1"></i>
                                مسح الفلاتر
                            </a>
                        @endif
                    </div>

                    <!-- View Toggle -->
                    <div class="flex items-center gap-2">
                        <button onclick="toggleView('grid')" id="gridBtn" class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-primary hover:text-white transition-colors">
                            <i class="fas fa-th-large"></i>
                        </button>
                        <button onclick="toggleView('list')" id="listBtn" class="p-2 rounded-lg bg-primary text-white">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>

                <!-- Jobs Grid/List -->
                <div id="jobsContainer" class="space-y-6">
                    @foreach($jobs as $job)
                        <div class="job-card bg-white dark:bg-dark-card rounded-xl shadow-md hover:shadow-lg transition-all duration-300 p-6 border border-gray-100 dark:border-gray-700 hover:border-primary/30">
                            <div class="flex flex-col md:flex-row md:items-start md:justify-between">
                                <div class="flex items-start gap-4 mb-4 md:mb-0 flex-1">
                                    <!-- Company Logo -->
                                    <div class="w-16 h-16 bg-gradient-to-br from-primary/10 to-primary/5 rounded-xl flex items-center justify-center flex-shrink-0">
                                        @if($job->employer && $job->employer->company_logo)
                                            <img src="{{ asset('storage/' . $job->employer->company_logo) }}" alt="{{ $job->employer->company_name }}" class="w-12 h-12 rounded-lg object-cover">
                                        @else
                                            <i class="fas fa-building text-primary text-xl"></i>
                                        @endif
                                    </div>

                                    <div class="flex-1">
                                        <div class="flex items-start justify-between mb-2">
                                            <h3 class="text-xl font-bold text-gray-900 dark:text-white hover:text-primary transition-colors">
                                                <a href="{{ route('jobs.show', $job) }}">{{ $job->title }}</a>
                                            </h3>
                                            @if($job->is_featured)
                                                <span class="px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 rounded-full text-xs font-medium">
                                                    <i class="fas fa-star mr-1"></i>
                                                    مميزة
                                                </span>
                                            @endif
                                        </div>

                                        <div class="flex flex-wrap items-center gap-4 mb-3 text-sm text-gray-600 dark:text-gray-400">
                                            @if($job->employer)
                                                <span class="flex items-center gap-1 font-medium">
                                                    <i class="fas fa-building text-primary"></i>
                                                    {{ $job->employer->company_name }}
                                                </span>
                                            @endif

                                            <span class="flex items-center gap-1">
                                                <i class="fas fa-map-marker-alt text-primary"></i>
                                                {{ $job->location }}
                                            </span>

                                            <span class="flex items-center gap-1">
                                                <i class="fas fa-clock text-primary"></i>
                                                @switch($job->job_type)
                                                    @case('full_time')
                                                        دوام كامل
                                                        @break
                                                    @case('part_time')
                                                        دوام جزئي
                                                        @break
                                                    @case('remote')
                                                        عن بعد
                                                        @break
                                                    @case('freelance')
                                                        عمل حر
                                                        @break
                                                    @default
                                                        {{ $job->job_type }}
                                                @endswitch
                                            </span>

                                            @if($job->salary_range)
                                                <span class="flex items-center gap-1 text-green-600 dark:text-green-400 font-medium">
                                                    <i class="fas fa-money-bill-wave"></i>
                                                    {{ $job->salary_range }}
                                                </span>
                                            @endif

                                            @if($job->category)
                                                <span class="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full text-xs">
                                                    {{ $job->category->name }}
                                                </span>
                                            @endif
                                        </div>

                                        <div class="text-gray-600 dark:text-gray-400 mb-4">
                                            <p>{{ \Illuminate\Support\Str::limit(strip_tags($job->description), 150) }}</p>
                                        </div>

                                        <!-- Job Tags -->
                                        @if($job->requirements)
                                            <div class="flex flex-wrap gap-2 mb-4">
                                                @php
                                                    $requirements = explode(',', $job->requirements);
                                                    $requirements = array_slice($requirements, 0, 3);
                                                @endphp
                                                @foreach($requirements as $requirement)
                                                    <span class="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs">
                                                        {{ trim($requirement) }}
                                                    </span>
                                                @endforeach
                                            </div>
                                        @endif
                                    </div>
                                </div>

                                <div class="flex flex-col items-end gap-3 md:ml-6">
                                    <div class="text-right">
                                        <div class="text-xs text-gray-500 dark:text-gray-400 mb-1">
                                            نُشرت {{ $job->posted_at ? $job->posted_at->diffForHumans() : $job->created_at->diffForHumans() }}
                                        </div>
                                        @if($job->expires_at)
                                            <div class="text-xs text-orange-600 dark:text-orange-400">
                                                تنتهي {{ $job->expires_at->diffForHumans() }}
                                            </div>
                                        @endif
                                    </div>

                                    <div class="flex flex-col gap-2">
                                        <a href="{{ route('jobs.show', $job) }}" class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors text-center font-medium">
                                            <i class="fas fa-eye mr-2"></i>
                                            عرض التفاصيل
                                        </a>

                                        @auth
                                            @if(auth()->user()->isJobSeeker())
                                                <button onclick="saveJob({{ $job->id }})" class="px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-center">
                                                    <i class="fas fa-bookmark mr-2"></i>
                                                    حفظ
                                                </button>
                                            @endif
                                        @endauth
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-8 flex justify-center">
                    {{ $jobs->appends(request()->query())->links() }}
                </div>
            @else
                <!-- No Results -->
                <div class="bg-white dark:bg-dark-card rounded-xl shadow-md p-16 text-center">
                    <div class="mx-auto w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-6">
                        <i class="fas fa-search text-3xl text-gray-400 dark:text-gray-600"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">لم يتم العثور على وظائف</h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-8 max-w-md mx-auto">
                        @if(request('keyword'))
                            لم نجد أي وظائف تحتوي على "{{ request('keyword') }}". حاول استخدام كلمات مفتاحية أخرى أو تقليل الفلاتر.
                        @else
                            لا توجد وظائف متاحة حالياً. تحقق مرة أخرى لاحقاً أو قم بتوسيع معايير البحث.
                        @endif
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="{{ route('jobs.search') }}" class="px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors">
                            <i class="fas fa-search mr-2"></i>
                            بحث جديد
                        </a>
                        <a href="{{ route('jobs.index') }}" class="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                            <i class="fas fa-list mr-2"></i>
                            عرض كل الوظائف
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

<script>
// Toggle between grid and list view
function toggleView(viewType) {
    const container = document.getElementById('jobsContainer');
    const gridBtn = document.getElementById('gridBtn');
    const listBtn = document.getElementById('listBtn');

    if (viewType === 'grid') {
        container.className = 'grid grid-cols-1 md:grid-cols-2 gap-6';
        gridBtn.className = 'p-2 rounded-lg bg-primary text-white';
        listBtn.className = 'p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-primary hover:text-white transition-colors';
    } else {
        container.className = 'space-y-6';
        listBtn.className = 'p-2 rounded-lg bg-primary text-white';
        gridBtn.className = 'p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-primary hover:text-white transition-colors';
    }

    // Save preference
    localStorage.setItem('jobsViewType', viewType);
}

// Save job function
function saveJob(jobId) {
    // This would typically make an AJAX request to save the job
    alert('تم حفظ الوظيفة! (هذه ميزة تجريبية)');
}

// Auto-submit form on filter change
document.addEventListener('DOMContentLoaded', function() {
    // Load saved view preference
    const savedView = localStorage.getItem('jobsViewType') || 'list';
    toggleView(savedView);

    // Auto-submit on select change
    const selects = document.querySelectorAll('select[name="category"], select[name="type"], select[name="sort"]');
    selects.forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });

    // Highlight search terms in results
    const searchTerm = '{{ request("keyword") }}';
    if (searchTerm) {
        highlightSearchTerms(searchTerm);
    }
});

// Highlight search terms in job titles and descriptions
function highlightSearchTerms(term) {
    if (!term) return;

    const jobCards = document.querySelectorAll('.job-card');
    jobCards.forEach(card => {
        const title = card.querySelector('h3 a');
        const description = card.querySelector('.text-gray-600 p');

        if (title) {
            title.innerHTML = highlightText(title.innerHTML, term);
        }
        if (description) {
            description.innerHTML = highlightText(description.innerHTML, term);
        }
    });
}

function highlightText(text, term) {
    const regex = new RegExp(`(${term})`, 'gi');
    return text.replace(regex, '<mark class="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">$1</mark>');
}

// Smooth scroll to results after form submission
if (window.location.search) {
    setTimeout(() => {
        const resultsSection = document.getElementById('jobsContainer');
        if (resultsSection) {
            resultsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }, 100);
}
</script>
@endsection
