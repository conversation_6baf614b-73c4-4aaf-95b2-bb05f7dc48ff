<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\ScheduledReport;
use App\Http\Controllers\ReportController;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

class SendScheduledReports extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reports:send-scheduled';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'إرسال التقارير المجدولة عبر البريد الإلكتروني';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('بدء إرسال التقارير المجدولة...');

        // في بيئة الإنتاج، سنقوم بإنشاء جدول للتقارير المجدولة
        // لكن هنا سنفترض وجود جدول scheduled_reports

        try {
            // التحقق من وجود جدول التقارير المجدولة
            if (Schema::hasTable('scheduled_reports')) {
                // الحصول على التقارير المجدولة التي يجب إرسالها اليوم
                $dayOfWeek = Carbon::now()->dayOfWeek;
                $dayOfMonth = Carbon::now()->day;

                $scheduledReports = ScheduledReport::where(function($query) use ($dayOfWeek, $dayOfMonth) {
                    // التقارير اليومية
                    $query->where('frequency', 'daily')
                        // التقارير الأسبوعية التي يجب إرسالها في هذا اليوم من الأسبوع
                        ->orWhere(function($q) use ($dayOfWeek) {
                            $q->where('frequency', 'weekly')
                                ->where('day_of_week', $dayOfWeek);
                        })
                        // التقارير الشهرية التي يجب إرسالها في هذا اليوم من الشهر
                        ->orWhere(function($q) use ($dayOfMonth) {
                            $q->where('frequency', 'monthly')
                                ->where('day_of_month', $dayOfMonth);
                        });
                })
                ->get();

                $this->info('تم العثور على ' . $scheduledReports->count() . ' تقرير مجدول للإرسال.');

                // إرسال كل تقرير مجدول
                foreach ($scheduledReports as $report) {
                    $this->sendReport($report);
                }

                $this->info('تم إرسال التقارير المجدولة بنجاح.');
                return 0;
            } else {
                $this->warn('جدول التقارير المجدولة غير موجود. يرجى إنشاء الجدول أولاً.');
                return 1;
            }
        } catch (\Exception $e) {
            $this->error('حدث خطأ أثناء إرسال التقارير المجدولة: ' . $e->getMessage());
            Log::error('خطأ في إرسال التقارير المجدولة: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * إرسال تقرير مجدول
     *
     * @param ScheduledReport $report
     * @return void
     */
    private function sendReport($report)
    {
        $this->info('إرسال تقرير: ' . $report->name . ' إلى: ' . $report->email);

        // إنشاء كائن ReportController
        $reportController = app()->make(ReportController::class);

        // تحديد نطاق التاريخ بناءً على تردد التقرير
        $endDate = Carbon::now();
        $startDate = null;

        switch ($report->frequency) {
            case 'daily':
                $startDate = Carbon::now()->subDay();
                break;
            case 'weekly':
                $startDate = Carbon::now()->subWeek();
                break;
            case 'monthly':
                $startDate = Carbon::now()->subMonth();
                break;
            default:
                $startDate = Carbon::now()->subMonth();
        }

        // إنشاء طلب وهمي مع المعلمات المطلوبة
        $request = new \Illuminate\Http\Request([
            'start_date' => $startDate->format('Y-m-d'),
            'end_date' => $endDate->format('Y-m-d'),
            'department' => $report->filters['department'] ?? null,
            'status' => $report->filters['status'] ?? null,
            'payment_type' => $report->filters['payment_type'] ?? null,
        ]);

        // الحصول على بيانات التقرير
        $data = null;

        switch ($report->report_type) {
            case 'jobs':
                $stats = $reportController->getJobStats((object) $request->all());
                $job_performance = $reportController->getJobPerformance((object) $request->all());
                $data = [
                    'stats' => $stats,
                    'job_performance' => $job_performance,
                    'filters' => (object) $request->all(),
                ];
                break;

            case 'applications':
                $stats = $reportController->getApplicationStats((object) $request->all());
                $data = [
                    'stats' => $stats,
                    'filters' => (object) $request->all(),
                ];
                break;

            case 'financial':
                $stats = $reportController->getFinancialStats((object) $request->all());
                $data = [
                    'stats' => $stats,
                    'filters' => (object) $request->all(),
                ];
                break;
        }

        // في بيئة الإنتاج، سنقوم بإرسال بريد إلكتروني مع التقرير
        // مثال:
        /*
        Mail::send('emails.report', $data, function($message) use ($report) {
            $message->to($report->email, $report->name)
                ->subject('تقرير مجدول: ' . $report->name);

            // إذا كان التقرير يتضمن مرفقًا
            if ($report->include_attachment) {
                // إنشاء ملف PDF أو Excel
                $pdf = PDF::loadView('reports.pdf.' . $report->report_type, $data);
                $message->attachData($pdf->output(), 'report.pdf');
            }
        });
        */

        // تحديث وقت آخر إرسال
        $report->last_sent_at = Carbon::now();
        $report->save();

        $this->info('تم إرسال التقرير بنجاح.');
    }
}
