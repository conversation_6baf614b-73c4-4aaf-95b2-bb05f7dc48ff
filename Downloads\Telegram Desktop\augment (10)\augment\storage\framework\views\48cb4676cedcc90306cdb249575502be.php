<?php $__env->startSection('title', 'طلبات التوظيف - Hire Me'); ?>
<?php $__env->startSection('header_title', 'طلبات التوظيف'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
            <h2 class="text-2xl font-bold">طلبات التوظيف</h2>
            <p class="text-gray-600 dark:text-gray-400 mt-1">إدارة ومراجعة طلبات التوظيف المقدمة</p>
        </div>
        <div class="mt-4 md:mt-0 flex gap-3">
            <button class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors flex items-center gap-2">
                <i class="fas fa-filter"></i>
                <span>تصفية</span>
            </button>
            <button class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors flex items-center gap-2">
                <i class="fas fa-download"></i>
                <span>تصدير</span>
            </button>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6 mb-6">
        <form action="<?php echo e(route('admin.applications.index')); ?>" method="GET" class="flex flex-col md:flex-row gap-4">
            <div class="flex-1">
                <div class="relative">
                    <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" name="search" id="search" value="<?php echo e(request('search')); ?>" class="block w-full pr-10 p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" placeholder="البحث عن مرشح أو وظيفة...">
                </div>
            </div>
            <div class="w-full md:w-48">
                <select name="job" id="jobFilter" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                    <option value="">كل الوظائف</option>
                    <?php $__currentLoopData = $jobs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($job->id); ?>" <?php echo e(request('job') == $job->id ? 'selected' : ''); ?>><?php echo e($job->title); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            <div class="w-full md:w-48">
                <select name="status" id="statusFilter" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                    <option value="">كل الحالات</option>
                    <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>قيد المراجعة</option>
                    <option value="reviewed" <?php echo e(request('status') == 'reviewed' ? 'selected' : ''); ?>>تمت المراجعة</option>
                    <option value="shortlisted" <?php echo e(request('status') == 'shortlisted' ? 'selected' : ''); ?>>القائمة المختصرة</option>
                    <option value="hired" <?php echo e(request('status') == 'hired' ? 'selected' : ''); ?>>تم التعيين</option>
                    <option value="rejected" <?php echo e(request('status') == 'rejected' ? 'selected' : ''); ?>>مرفوض</option>
                </select>
            </div>
            <div class="w-full md:w-48">
                <select name="date" id="dateFilter" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                    <option value="">التاريخ</option>
                    <option value="today" <?php echo e(request('date') == 'today' ? 'selected' : ''); ?>>اليوم</option>
                    <option value="week" <?php echo e(request('date') == 'week' ? 'selected' : ''); ?>>هذا الأسبوع</option>
                    <option value="month" <?php echo e(request('date') == 'month' ? 'selected' : ''); ?>>هذا الشهر</option>
                </select>
            </div>
            <div>
                <button type="submit" class="h-full px-4 py-2.5 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors">
                    <i class="fas fa-filter ml-1"></i>
                    تصفية
                </button>
            </div>
        </form>
    </div>

    <!-- Applications List -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm overflow-hidden">
        <div class="overflow-x-auto">
            <table class="w-full text-right text-sm">
                <thead class="bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-300">
                    <tr>
                        <th class="px-6 py-4">المرشح</th>
                        <th class="px-6 py-4">الوظيفة</th>
                        <th class="px-6 py-4">تاريخ التقديم</th>
                        <th class="px-6 py-4">التقييم</th>
                        <th class="px-6 py-4">الحالة</th>
                        <th class="px-6 py-4">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    <?php $__empty_1 = true; $__currentLoopData = $applications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $application): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 font-medium">
                                <div class="flex items-center gap-3">
                                    <div class="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                                        <?php if($application->jobSeeker && $application->jobSeeker->user && $application->jobSeeker->user->avatar): ?>
                                            <img src="<?php echo e(asset('storage/' . $application->jobSeeker->user->avatar)); ?>" alt="<?php echo e($application->jobSeeker->user->name); ?>" class="w-8 h-8 rounded-full object-cover">
                                        <?php else: ?>
                                            <i class="fas fa-user text-primary"></i>
                                        <?php endif; ?>
                                    </div>
                                    <div>
                                        <p class="font-medium"><?php echo e($application->jobSeeker && $application->jobSeeker->user ? $application->jobSeeker->user->name : 'مستخدم محذوف'); ?></p>
                                        <p class="text-gray-500 dark:text-gray-400 text-xs"><?php echo e($application->jobSeeker && $application->jobSeeker->user ? $application->jobSeeker->user->email : 'غير متوفر'); ?></p>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div>
                                    <p class="font-medium"><?php echo e($application->job ? $application->job->title : 'وظيفة محذوفة'); ?></p>
                                    <p class="text-gray-500 dark:text-gray-400 text-xs">
                                        <?php echo e($application->job && $application->job->employer ? $application->job->employer->company_name : 'غير محدد'); ?>

                                    </p>
                                </div>
                            </td>
                            <td class="px-6 py-4 text-gray-600 dark:text-gray-400">
                                <?php echo e($application->created_at->format('Y/m/d')); ?>

                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center gap-1">
                                    <?php
                                        $rating = $application->rating ?? 0;
                                    ?>
                                    <?php for($i = 1; $i <= 5; $i++): ?>
                                        <?php if($i <= $rating): ?>
                                            <i class="fas fa-star text-yellow-400"></i>
                                        <?php else: ?>
                                            <i class="far fa-star text-gray-300 dark:text-gray-600"></i>
                                        <?php endif; ?>
                                    <?php endfor; ?>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <?php if($application->status == 'pending'): ?>
                                    <span class="px-2.5 py-1 text-xs font-medium text-yellow-700 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/30 rounded-full">قيد المراجعة</span>
                                <?php elseif($application->status == 'reviewed'): ?>
                                    <span class="px-2.5 py-1 text-xs font-medium text-blue-700 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30 rounded-full">تمت المراجعة</span>
                                <?php elseif($application->status == 'shortlisted'): ?>
                                    <span class="px-2.5 py-1 text-xs font-medium text-indigo-700 dark:text-indigo-400 bg-indigo-100 dark:bg-indigo-900/30 rounded-full">القائمة المختصرة</span>
                                <?php elseif($application->status == 'hired'): ?>
                                    <span class="px-2.5 py-1 text-xs font-medium text-green-700 dark:text-green-400 bg-green-100 dark:bg-green-900/30 rounded-full">تم التعيين</span>
                                <?php elseif($application->status == 'rejected'): ?>
                                    <span class="px-2.5 py-1 text-xs font-medium text-red-700 dark:text-red-400 bg-red-100 dark:bg-red-900/30 rounded-full">مرفوض</span>
                                <?php else: ?>
                                    <span class="px-2.5 py-1 text-xs font-medium text-gray-700 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/30 rounded-full"><?php echo e($application->status); ?></span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                                    <a href="<?php echo e(route('admin.applications.show', $application)); ?>" class="text-primary hover:text-primary/80">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <div class="relative status-dropdown" data-application-id="<?php echo e($application->id); ?>">
                                        <button type="button" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 status-dropdown-btn">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <div class="absolute left-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg z-10 hidden status-dropdown-menu">
                                            <div class="py-1">
                                                <form action="<?php echo e(route('admin.applications.update-status', $application)); ?>" method="POST">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('PUT'); ?>
                                                    <button type="submit" name="status" value="pending" class="w-full text-right px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                                        تحديث إلى: قيد المراجعة
                                                    </button>
                                                    <button type="submit" name="status" value="reviewed" class="w-full text-right px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                                        تحديث إلى: تمت المراجعة
                                                    </button>
                                                    <button type="submit" name="status" value="shortlisted" class="w-full text-right px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                                        تحديث إلى: القائمة المختصرة
                                                    </button>
                                                    <button type="submit" name="status" value="hired" class="w-full text-right px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                                        تحديث إلى: تم التعيين
                                                    </button>
                                                    <button type="submit" name="status" value="rejected" class="w-full text-right px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                                        تحديث إلى: مرفوض
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <div class="flex flex-col items-center justify-center">
                                    <i class="fas fa-file-alt text-5xl text-gray-300 dark:text-gray-700 mb-4"></i>
                                    <h3 class="text-xl font-bold text-gray-500 dark:text-gray-400">لا توجد طلبات توظيف</h3>
                                    <p class="text-gray-500 dark:text-gray-500 mt-2">لم يتم العثور على طلبات توظيف مطابقة للفلتر المحدد</p>
                                </div>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="px-6 py-4 flex items-center justify-between border-t border-gray-200 dark:border-gray-700">
            <div class="text-sm text-gray-700 dark:text-gray-300">
                عرض <span class="font-medium"><?php echo e($applications->firstItem() ?? 0); ?></span> إلى <span class="font-medium"><?php echo e($applications->lastItem() ?? 0); ?></span> من <span class="font-medium"><?php echo e($applications->total()); ?></span> النتائج
            </div>
            <div>
                <?php echo e($applications->withQueryString()->links()); ?>

            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    // Status dropdown toggle
    const statusDropdowns = document.querySelectorAll('.status-dropdown');

    statusDropdowns.forEach(dropdown => {
        const dropdownButton = dropdown.querySelector('.status-dropdown-btn');
        const dropdownMenu = dropdown.querySelector('.status-dropdown-menu');

        if (dropdownButton && dropdownMenu) {
            dropdownButton.addEventListener('click', (e) => {
                e.stopPropagation();

                // Close all other dropdowns
                document.querySelectorAll('.status-dropdown-menu').forEach(menu => {
                    if (menu !== dropdownMenu) {
                        menu.classList.add('hidden');
                    }
                });

                // Toggle this dropdown
                dropdownMenu.classList.toggle('hidden');
            });
        }
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', (e) => {
        if (!e.target.closest('.status-dropdown')) {
            document.querySelectorAll('.status-dropdown-menu').forEach(menu => {
                menu.classList.add('hidden');
            });
        }
    });

    // Auto-submit form on filter change
    const filterForm = document.querySelector('form');
    const jobFilter = document.getElementById('jobFilter');
    const statusFilter = document.getElementById('statusFilter');
    const dateFilter = document.getElementById('dateFilter');

    // Add event listeners for filters to auto-submit the form
    if (jobFilter && statusFilter && dateFilter) {
        [jobFilter, statusFilter, dateFilter].forEach(element => {
            element.addEventListener('change', () => {
                filterForm.submit();
            });
        });
    }
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\Telegram Desktop\augment (10)\augment\resources\views/admin/applications/index.blade.php ENDPATH**/ ?>