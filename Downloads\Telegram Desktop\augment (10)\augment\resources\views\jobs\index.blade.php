@extends('layouts.app')

@section('content')
<!-- Hero Section -->
<div class="gradient-bg py-16">
    <div class="p-6">
        <div class="max-w-7xl mx-auto text-center">
            <div class="animate-fade-in">
                <h1 class="text-4xl md:text-5xl font-bold text-white mb-6">
                    <i class="fas fa-briefcase mr-3"></i>
                    الوظائف المتاحة
                </h1>
                <p class="text-xl text-white/90 max-w-3xl mx-auto mb-8">
                    اكتشف آلاف الفرص الوظيفية من أفضل الشركات واختر ما يناسب مهاراتك وطموحاتك
                </p>

                <!-- Quick Stats -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto">
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                        <div class="text-2xl font-bold text-white">{{ $jobs->total() }}</div>
                        <div class="text-white/80 text-sm">وظيفة متاحة</div>
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                        <div class="text-2xl font-bold text-white">{{ \App\Models\Employer::count() }}</div>
                        <div class="text-white/80 text-sm">شركة</div>
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                        <div class="text-2xl font-bold text-white">{{ \App\Models\Category::count() }}</div>
                        <div class="text-white/80 text-sm">تصنيف</div>
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                        <div class="text-2xl font-bold text-white">{{ \App\Models\Application::count() }}</div>
                        <div class="text-white/80 text-sm">طلب توظيف</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter Section -->
<div class="bg-white dark:bg-gray-900 py-8 border-b border-gray-200 dark:border-gray-700">
    <div class="p-6">
        <div class="max-w-7xl mx-auto">
            <form method="GET" action="{{ route('jobs.index') }}" class="bg-white dark:bg-dark-card rounded-2xl shadow-lg p-6 border border-gray-100 dark:border-gray-700">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-search mr-2 text-primary"></i>
                            البحث
                        </label>
                        <input type="text" name="keyword" value="{{ request('keyword') ?? request('search') }}"
                               placeholder="مثال: مطور ويب، مصمم، محاسب، Laravel"
                               class="block w-full p-3 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-map-marker-alt mr-2 text-primary"></i>
                            الموقع
                        </label>
                        <input type="text" name="location" value="{{ request('location') }}"
                               placeholder="المدينة أو المنطقة"
                               class="block w-full p-3 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-th-large mr-2 text-primary"></i>
                            التصنيف
                        </label>
                        <select name="category" class="block w-full p-3 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary">
                            <option value="">جميع التصنيفات</option>
                            @foreach(\App\Models\Category::all() as $category)
                                <option value="{{ $category->id }}" {{ request('category') == $category->id ? 'selected' : '' }}>
                                    {{ $category->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button type="submit" class="w-full gradient-bg text-white py-3 px-6 rounded-lg hover:opacity-90 transition-opacity font-medium">
                            <i class="fas fa-search mr-2"></i>
                            بحث
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Jobs Section -->
<div class="py-16 bg-gray-50 dark:bg-gray-900">
    <div class="p-6">
        <div class="max-w-7xl mx-auto">
            <!-- Featured Jobs -->
            @if(isset($featuredJobs) && $featuredJobs->count() > 0)
                <div class="mb-16">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                            <i class="fas fa-star text-yellow-500 mr-3"></i>
                            الوظائف المميزة
                        </h2>
                        <p class="text-lg text-gray-600 dark:text-gray-400">أفضل الفرص الوظيفية المختارة خصيصاً لك</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                        @foreach($featuredJobs as $job)
                            <div class="bg-white dark:bg-dark-card rounded-2xl shadow-sm hover:shadow-xl hover:scale-105 transition-all duration-300 p-6 border border-gray-100 dark:border-gray-700 group">
                                <!-- Header -->
                                <div class="flex justify-between items-start mb-4">
                                    <div class="w-16 h-16 gradient-bg rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                                        <i class="fas fa-star text-2xl text-white"></i>
                                    </div>
                                    <div class="flex flex-col items-end gap-2">
                                        <span class="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded-full">
                                            <i class="fas fa-clock mr-1"></i>
                                            {{ $job->created_at->diffForHumans() }}
                                        </span>
                                        <span class="text-xs font-medium px-3 py-1 rounded-full
                                            @if($job->job_type == 'full_time') bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400
                                            @elseif($job->job_type == 'part_time') bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400
                                            @elseif($job->job_type == 'remote') bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400
                                            @else bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400
                                            @endif">
                                            @switch($job->job_type)
                                                @case('full_time') دوام كامل @break
                                                @case('part_time') دوام جزئي @break
                                                @case('remote') عن بعد @break
                                                @case('freelance') عمل حر @break
                                                @default {{ $job->job_type }}
                                            @endswitch
                                        </span>
                                    </div>
                                </div>

                                <!-- Job Title -->
                                <h3 class="text-xl font-bold mb-2 group-hover:text-primary transition-colors duration-300">
                                    <a href="{{ route('jobs.show', $job) }}">{{ $job->title }}</a>
                                </h3>

                                <!-- Company -->
                                <div class="flex items-center mb-4">
                                    <i class="fas fa-building text-primary mr-2"></i>
                                    <h4 class="text-primary font-medium">
                                        @if($job->employer)
                                            {{ $job->employer->company_name }}
                                        @else
                                            شركة غير محددة
                                        @endif
                                    </h4>
                                </div>

                                <!-- Tags -->
                                <div class="flex flex-wrap gap-2 mb-4">
                                    @if($job->category)
                                        <span class="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium">
                                            <i class="fas fa-tag mr-1"></i>
                                            {{ $job->category->name }}
                                        </span>
                                    @endif
                                    @if($job->location)
                                        <span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 rounded-full text-sm">
                                            <i class="fas fa-map-marker-alt mr-1"></i>
                                            {{ $job->location }}
                                        </span>
                                    @endif
                                </div>

                                <!-- Description -->
                                <p class="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2">
                                    {{ Str::limit($job->description, 100) }}
                                </p>

                                <!-- Footer -->
                                <div class="flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-700">
                                    <div class="flex items-center gap-2">
                                        @if($job->salary_min || $job->salary_max)
                                            <div class="flex items-center text-primary font-bold">
                                                <i class="fas fa-money-bill-wave mr-1"></i>
                                                {{ $job->salary_range }}
                                            </div>
                                        @else
                                            <span class="text-gray-500 dark:text-gray-400 text-sm">راتب قابل للتفاوض</span>
                                        @endif
                                    </div>
                                    <a href="{{ route('jobs.show', $job) }}" class="gradient-bg text-white px-4 py-2 rounded-lg hover:opacity-90 hover:scale-105 transition-all duration-300 text-sm font-medium shadow-lg">
                                        <i class="fas fa-eye mr-1"></i>
                                        عرض التفاصيل
                                    </a>
                                </div>
                            </div>
                        @endforeach
                </div>
            </div>
        @endif

        <!-- All Jobs -->
        <div>
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-briefcase text-primary mr-3"></i>
                    جميع الوظائف المتاحة
                </h2>
                <p class="text-lg text-gray-600 dark:text-gray-400">تصفح جميع الفرص الوظيفية المتاحة</p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                @if(isset($jobs) && $jobs->count() > 0)
                    @foreach($jobs as $job)
                        <div class="bg-white dark:bg-dark-card rounded-2xl shadow-sm hover:shadow-xl hover:scale-105 transition-all duration-300 p-6 border border-gray-100 dark:border-gray-700 group">
                            <!-- Header -->
                            <div class="flex justify-between items-start mb-4">
                                <div class="w-16 h-16 bg-primary/10 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                                    <i class="fas fa-briefcase text-2xl text-primary"></i>
                                </div>
                                <div class="flex flex-col items-end gap-2">
                                    <span class="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded-full">
                                        <i class="fas fa-clock mr-1"></i>
                                        {{ $job->created_at->diffForHumans() }}
                                    </span>
                                    <span class="text-xs font-medium px-3 py-1 rounded-full
                                        @if($job->job_type == 'full_time') bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400
                                        @elseif($job->job_type == 'part_time') bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400
                                        @elseif($job->job_type == 'remote') bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400
                                        @else bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400
                                        @endif">
                                        @switch($job->job_type)
                                            @case('full_time') دوام كامل @break
                                            @case('part_time') دوام جزئي @break
                                            @case('remote') عن بعد @break
                                            @case('freelance') عمل حر @break
                                            @default {{ $job->job_type }}
                                        @endswitch
                                    </span>
                                </div>
                            </div>

                            <!-- Job Title -->
                            <h3 class="text-xl font-bold mb-2 group-hover:text-primary transition-colors duration-300">
                                <a href="{{ route('jobs.show', $job) }}">{{ $job->title }}</a>
                            </h3>

                            <!-- Company -->
                            <div class="flex items-center mb-4">
                                <i class="fas fa-building text-primary mr-2"></i>
                                <h4 class="text-primary font-medium">
                                    @if($job->employer)
                                        {{ $job->employer->company_name }}
                                    @else
                                        شركة غير محددة
                                    @endif
                                </h4>
                            </div>

                            <!-- Tags -->
                            <div class="flex flex-wrap gap-2 mb-4">
                                @if($job->category)
                                    <span class="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium">
                                        <i class="fas fa-tag mr-1"></i>
                                        {{ $job->category->name }}
                                    </span>
                                @endif
                                @if($job->location)
                                    <span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 rounded-full text-sm">
                                        <i class="fas fa-map-marker-alt mr-1"></i>
                                        {{ $job->location }}
                                    </span>
                                @endif
                            </div>

                            <!-- Description -->
                            <p class="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-3">
                                {{ Str::limit(strip_tags($job->description), 150) }}
                            </p>

                            <!-- Footer -->
                            <div class="flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-700">
                                <div class="flex items-center gap-2">
                                    @if($job->salary_min || $job->salary_max)
                                        <div class="flex items-center text-primary font-bold">
                                            <i class="fas fa-money-bill-wave mr-1"></i>
                                            {{ $job->salary_range }}
                                        </div>
                                    @else
                                        <span class="text-gray-500 dark:text-gray-400 text-sm">راتب قابل للتفاوض</span>
                                    @endif
                                </div>
                                <a href="{{ route('jobs.show', $job) }}" class="gradient-bg text-white px-4 py-2 rounded-lg hover:opacity-90 hover:scale-105 transition-all duration-300 text-sm font-medium shadow-lg">
                                    <i class="fas fa-eye mr-1"></i>
                                    عرض التفاصيل
                                </a>
                            </div>
                        </div>
                    @endforeach
                @else
                    <div class="col-span-full">
                        <div class="bg-white dark:bg-dark-card rounded-2xl shadow-sm p-16 text-center border border-gray-100 dark:border-gray-700">
                            <div class="w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-6">
                                <i class="fas fa-briefcase text-4xl text-gray-400 dark:text-gray-600"></i>
                            </div>
                            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">لا توجد وظائف متاحة حاليًا</h3>
                            <p class="text-gray-600 dark:text-gray-400 mb-8 max-w-md mx-auto">
                                تحقق مرة أخرى لاحقًا أو قم بتعديل معايير البحث للعثور على الوظائف المناسبة
                            </p>
                            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                                <a href="{{ route('jobs.index') }}" class="gradient-bg text-white px-6 py-3 rounded-lg hover:opacity-90 transition-opacity font-medium">
                                    <i class="fas fa-refresh mr-2"></i>
                                    تحديث الصفحة
                                </a>
                                <a href="{{ route('home') }}" class="bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors font-medium">
                                    <i class="fas fa-home mr-2"></i>
                                    العودة للرئيسية
                                </a>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Pagination -->
            @if(isset($jobs) && $jobs->hasPages())
                <div class="mt-12 flex justify-center">
                    <div class="bg-white dark:bg-dark-card rounded-2xl shadow-sm p-4 border border-gray-100 dark:border-gray-700">
                        {{ $jobs->links() }}
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Call to Action Section -->
<div class="gradient-bg py-16">
    <div class="p-6">
        <div class="max-w-4xl mx-auto text-center">
            <h2 class="text-3xl font-bold text-white mb-4">
                لم تجد الوظيفة المناسبة؟
            </h2>
            <p class="text-xl text-white/90 mb-8">
                أنشئ حسابك الآن واحصل على إشعارات فورية عند نشر وظائف جديدة تناسب مهاراتك
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                @guest
                    <a href="{{ route('register') }}" class="bg-white text-primary px-8 py-4 rounded-lg hover:bg-gray-100 transition-colors font-bold text-lg shadow-lg">
                        <i class="fas fa-user-plus mr-2"></i>
                        إنشاء حساب جديد
                    </a>
                    <a href="{{ route('login') }}" class="bg-white/10 backdrop-blur-sm text-white px-8 py-4 rounded-lg hover:bg-white/20 transition-colors font-medium text-lg border border-white/20">
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        تسجيل الدخول
                    </a>
                @else
                    <a href="{{ route('job-seeker.profile') }}" class="bg-white text-primary px-8 py-4 rounded-lg hover:bg-gray-100 transition-colors font-bold text-lg shadow-lg">
                        <i class="fas fa-user mr-2"></i>
                        إدارة الملف الشخصي
                    </a>
                @endguest
            </div>
        </div>
    </div>
</div>
@endsection
