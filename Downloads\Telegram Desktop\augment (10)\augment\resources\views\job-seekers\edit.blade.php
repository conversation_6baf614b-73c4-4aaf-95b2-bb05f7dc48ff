@extends('layouts.app')

@section('title', 'تعديل الملف الشخصي')

@section('content')
<!-- Hero Section -->
<div class="gradient-bg py-16">
    <div class="p-6">
        <div class="max-w-5xl mx-auto text-center">
            <h1 class="text-4xl font-bold text-white mb-4">تعديل الملف الشخصي</h1>
            <p class="text-xl text-white/90">قم بتحديث معلومات ملفك الشخصي لزيادة فرص الحصول على وظيفة</p>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="py-16 bg-gray-50 dark:bg-gray-900">
    <div class="p-6">
        <div class="max-w-5xl mx-auto">
            <div class="bg-white dark:bg-dark-card rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden">
                <div class="p-8">
                    <form action="{{ route('job-seeker.profile.update') }}" method="POST" enctype="multipart/form-data" id="profileForm">
                        @csrf
                        @method('PUT')

                        <!-- Profile Photo Section -->
                        <div class="mb-12">
                            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">الصورة الشخصية</h2>
                            <div class="flex flex-col lg:flex-row items-center gap-8">
                                <!-- Current Photo -->
                                <div class="relative">
                                    <div class="w-32 h-32 rounded-full overflow-hidden border-4 border-gray-200 dark:border-gray-700 shadow-lg">
                                        @if(Auth::user()->avatar)
                                            <img src="{{ asset('storage/' . Auth::user()->avatar) }}" alt="{{ Auth::user()->name }}"
                                                 class="w-full h-full object-cover" id="avatarPreview">
                                        @else
                                            <div class="w-full h-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center" id="avatarPreview">
                                                <i class="fas fa-user text-4xl text-gray-400"></i>
                                            </div>
                                        @endif
                                    </div>
                                    @if(Auth::user()->avatar)
                                        <button type="button" onclick="removeAvatar()"
                                                class="absolute -top-2 -right-2 w-8 h-8 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors">
                                            <i class="fas fa-times text-sm"></i>
                                        </button>
                                    @endif
                                </div>

                                <!-- Upload Controls -->
                                <div class="flex-1">
                                    <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl p-6 text-center hover:border-primary transition-colors">
                                        <input type="file" name="avatar" id="avatar" accept="image/*" class="hidden" onchange="previewAvatar(this)">
                                        <label for="avatar" class="cursor-pointer">
                                            <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                                            <p class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">اختر صورة شخصية</p>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">PNG, JPG, GIF حتى 2MB</p>
                                        </label>
                                    </div>
                                    @error('avatar')
                                        <p class="text-red-500 text-sm mt-2">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Basic Information -->
                        <div class="mb-12">
                            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">المعلومات الأساسية</h2>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                                <div>
                                    <label for="job_title" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">المسمى الوظيفي</label>
                                    <input type="text" name="job_title" id="job_title" value="{{ old('job_title', $jobSeeker->job_title) }}"
                                           placeholder="مثال: مطور ويب، مصمم جرافيك"
                                           class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all">
                                    @error('job_title')
                                        <span class="text-red-500 text-sm mt-2 block">{{ $message }}</span>
                                    @enderror
                                </div>

                                <div>
                                    <label for="location" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">الموقع</label>
                                    <input type="text" name="location" id="location" value="{{ old('location', $jobSeeker->location) }}"
                                           placeholder="مثال: طرابلس، بنغازي"
                                           class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all">
                                    @error('location')
                                        <span class="text-red-500 text-sm mt-2 block">{{ $message }}</span>
                                    @enderror
                                </div>

                                <div class="md:col-span-2">
                                    <label class="flex items-center p-4 border border-gray-300 dark:border-gray-600 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer">
                                        <input type="checkbox" name="is_available" id="is_available" value="1"
                                               {{ old('is_available', $jobSeeker->is_available) ? 'checked' : '' }}
                                               class="h-5 w-5 text-primary focus:ring-primary border-gray-300 dark:border-gray-600 rounded">
                                        <div class="mr-3">
                                            <span class="block text-sm font-semibold text-gray-700 dark:text-gray-300">متاح للعمل</span>
                                            <span class="block text-xs text-gray-500 dark:text-gray-400">سيظهر ملفك للشركات كمتاح للتوظيف</span>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Resume Upload -->
                        <div class="mb-12">
                            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">السيرة الذاتية</h2>
                            <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl p-8 text-center hover:border-primary transition-colors">
                                <input type="file" name="resume" id="resume" accept=".pdf,.doc,.docx" class="hidden" onchange="previewResume(this)">
                                <label for="resume" class="cursor-pointer">
                                    <i class="fas fa-file-upload text-5xl text-gray-400 mb-4"></i>
                                    <p class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">اختر ملف السيرة الذاتية</p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">PDF, DOC, DOCX حتى 2MB</p>
                                </label>

                                @if($jobSeeker->resume)
                                    <div class="mt-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                                        <div class="flex items-center justify-center gap-3">
                                            <i class="fas fa-file-alt text-green-600 dark:text-green-400"></i>
                                            <span class="text-green-800 dark:text-green-300 font-medium">السيرة الذاتية الحالية</span>
                                            <a href="{{ Storage::url($jobSeeker->resume) }}" target="_blank"
                                               class="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200 transition-colors">
                                                <i class="fas fa-external-link-alt"></i>
                                                عرض
                                            </a>
                                        </div>
                                    </div>
                                @endif
                            </div>
                            @error('resume')
                                <p class="text-red-500 text-sm mt-2">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Skills Section -->
                        <div class="mb-12">
                            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">المهارات</h2>
                            <div>
                                <label for="skills" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">المهارات التقنية والشخصية</label>
                                <textarea name="skills" id="skills" rows="4"
                                          placeholder="مثال: HTML, CSS, JavaScript, التواصل الفعال, العمل الجماعي"
                                          class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all resize-none">{{ old('skills', $jobSeeker->skills) }}</textarea>
                                <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">أدخل مهاراتك مفصولة بفواصل</p>
                                @error('skills')
                                    <span class="text-red-500 text-sm mt-2 block">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>

                        <!-- Experience Section -->
                        <div class="mb-12">
                            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">الخبرات المهنية</h2>
                            <div>
                                <label for="experience" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">تفاصيل الخبرات السابقة</label>
                                <textarea name="experience" id="experience" rows="6"
                                          placeholder="اذكر خبراتك المهنية السابقة، المناصب التي شغلتها، والمسؤوليات التي توليتها..."
                                          class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all resize-none">{{ old('experience', $jobSeeker->experience) }}</textarea>
                                @error('experience')
                                    <span class="text-red-500 text-sm mt-2 block">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>

                        <!-- Education Section -->
                        <div class="mb-12">
                            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">التعليم والمؤهلات</h2>
                            <div>
                                <label for="education" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">المؤهلات التعليمية والشهادات</label>
                                <textarea name="education" id="education" rows="4"
                                          placeholder="اذكر مؤهلاتك التعليمية، الشهادات الحاصل عليها، والدورات التدريبية..."
                                          class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all resize-none">{{ old('education', $jobSeeker->education) }}</textarea>
                                @error('education')
                                    <span class="text-red-500 text-sm mt-2 block">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex flex-col sm:flex-row justify-end gap-4 pt-8 border-t border-gray-200 dark:border-gray-700">
                            <a href="{{ route('job-seeker.profile') }}"
                               class="px-8 py-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors font-medium text-center">
                                <i class="fas fa-times mr-2"></i>
                                إلغاء
                            </a>
                            <button type="submit"
                                    class="px-8 py-3 bg-primary hover:bg-primary-dark text-white rounded-xl transition-colors font-medium flex items-center justify-center gap-2">
                                <i class="fas fa-save"></i>
                                حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Preview avatar function
function previewAvatar(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('avatarPreview');
            preview.innerHTML = `<img src="${e.target.result}" alt="Preview" class="w-full h-full object-cover">`;
        }
        reader.readAsDataURL(input.files[0]);
    }
}

// Remove avatar function
function removeAvatar() {
    if (confirm('هل أنت متأكد من حذف الصورة الشخصية؟')) {
        fetch('{{ route("job-seeker.profile.remove-avatar") }}', {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const preview = document.getElementById('avatarPreview');
                preview.innerHTML = `<div class="w-full h-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                                        <i class="fas fa-user text-4xl text-gray-400"></i>
                                    </div>`;
                document.getElementById('avatar').value = '';

                // Hide remove button
                const removeBtn = document.querySelector('button[onclick="removeAvatar()"]');
                if (removeBtn) {
                    removeBtn.style.display = 'none';
                }

                // Show success message
                showMessage(data.message, 'success');
            } else {
                showMessage(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showMessage('حدث خطأ أثناء حذف الصورة', 'error');
        });
    }
}

// Show message function
function showMessage(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
        type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
    }`;
    messageDiv.textContent = message;

    document.body.appendChild(messageDiv);

    setTimeout(() => {
        messageDiv.remove();
    }, 3000);
}

// Preview resume function
function previewResume(input) {
    if (input.files && input.files[0]) {
        const fileName = input.files[0].name;
        const fileSize = (input.files[0].size / 1024 / 1024).toFixed(2);

        // Show file info
        const label = input.nextElementSibling;
        label.innerHTML = `
            <i class="fas fa-file-check text-4xl text-green-500 mb-4"></i>
            <p class="text-lg font-medium text-green-700 dark:text-green-300 mb-2">تم اختيار الملف</p>
            <p class="text-sm text-gray-600 dark:text-gray-400">${fileName} (${fileSize} MB)</p>
        `;
    }
}

// Form validation
document.getElementById('profileForm').addEventListener('submit', function(e) {
    const requiredFields = ['job_title', 'location'];
    let hasError = false;

    requiredFields.forEach(field => {
        const input = document.getElementById(field);
        if (!input.value.trim()) {
            hasError = true;
            input.classList.add('border-red-500');
        } else {
            input.classList.remove('border-red-500');
        }
    });

    if (hasError) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة');
    }
});
</script>
@endsection
