<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('job_seekers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('resume')->nullable(); // السيرة الذاتية
            $table->text('skills')->nullable(); // المهارات
            $table->text('experience')->nullable(); // الخبرات
            $table->text('education')->nullable(); // التعليم
            $table->string('job_title')->nullable(); // المسمى الوظيفي
            $table->string('location')->nullable(); // الموقع
            $table->decimal('expected_salary', 10, 2)->nullable(); // الراتب المتوقع
            $table->boolean('is_available')->default(true); // متاح للعمل
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('job_seekers');
    }
};