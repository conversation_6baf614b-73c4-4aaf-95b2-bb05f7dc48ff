<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Application;
use App\Models\Job;
use App\Models\JobSeeker;
use App\Models\Employer;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ApplicationController extends Controller
{
    /**
     * إنشاء مثيل جديد من الكنترولر
     */
    public function __construct()
    {
        // تطبيق middleware على الدوال التي تتطلب تسجيل دخول كمدير
        $this->middleware(['auth', 'role:admin']);
    }

    /**
     * عرض قائمة طلبات التوظيف
     */
    public function index(Request $request)
    {
        // حذف الطلبات المرتبطة بوظائف محذوفة أولاً
        $this->cleanupOrphanedApplications();

        // استعلام طلبات التوظيف مع علاقاتها (فقط الطلبات التي لها وظائف صحيحة)
        $applicationsQuery = Application::with(['jobSeeker.user', 'job.employer.user'])
            ->whereHas('job') // التأكد من وجود الوظيفة
            ->whereHas('jobSeeker.user'); // التأكد من وجود المستخدم

        // تطبيق الفلاتر إذا وجدت
        if ($request->filled('search')) {
            $search = $request->search;
            $applicationsQuery->whereHas('jobSeeker.user', function($query) use ($search) {
                $query->where('name', 'like', '%' . $search . '%')
                      ->orWhere('email', 'like', '%' . $search . '%');
            })->orWhereHas('job', function($query) use ($search) {
                $query->where('title', 'like', '%' . $search . '%');
            });
        }

        if ($request->filled('job')) {
            $applicationsQuery->where('job_id', $request->job);
        }

        if ($request->filled('status')) {
            $applicationsQuery->where('status', $request->status);
        }

        if ($request->filled('date')) {
            $date = $request->date;
            if ($date === 'today') {
                $applicationsQuery->whereDate('created_at', today());
            } elseif ($date === 'week') {
                $applicationsQuery->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
            } elseif ($date === 'month') {
                $applicationsQuery->whereMonth('created_at', now()->month)
                                  ->whereYear('created_at', now()->year);
            }
        }

        // ترتيب النتائج
        $applicationsQuery->orderBy('created_at', 'desc');

        // الحصول على طلبات التوظيف مع التقسيم
        $applications = $applicationsQuery->paginate(10);

        // الحصول على قائمة الوظائف للفلتر
        $jobs = Job::all();

        // إحصائيات طلبات التوظيف
        $totalApplications = Application::count();
        $pendingApplications = Application::where('status', 'pending')->count();
        $reviewedApplications = Application::where('status', 'reviewed')->count();
        $shortlistedApplications = Application::where('status', 'shortlisted')->count();
        $hiredApplications = Application::where('status', 'hired')->count();
        $rejectedApplications = Application::where('status', 'rejected')->count();

        return view('admin.applications.index', compact(
            'applications',
            'jobs',
            'totalApplications',
            'pendingApplications',
            'reviewedApplications',
            'shortlistedApplications',
            'hiredApplications',
            'rejectedApplications'
        ));
    }

    /**
     * عرض تفاصيل طلب توظيف محدد
     */
    public function show(Application $application)
    {
        // تحميل العلاقات
        $application->load(['jobSeeker.user', 'job.employer']);

        return view('admin.applications.show', compact('application'));
    }

    /**
     * تحديث حالة طلب التوظيف
     */
    public function updateStatus(Request $request, Application $application)
    {
        $validStatuses = implode(',', array_keys(\App\Models\Application::getStatusOptions()));
        $request->validate([
            'status' => 'required|in:' . $validStatuses,
            'admin_notes' => 'nullable|string',
        ]);

        // تحديث حالة الطلب
        $application->status = $request->status;

        // إضافة ملاحظات المدير إذا وجدت
        if ($request->filled('admin_notes')) {
            $application->admin_notes = $request->admin_notes;
        }

        $application->save();

        // إنشاء إشعار للباحث عن عمل
        $notification = new Notification();
        $notification->user_id = $application->jobSeeker->user->id;
        $notification->type = 'application_update';
        $notification->message = 'تم تحديث حالة طلبك للوظيفة "' . $application->job->title . '" إلى ' . $this->getStatusInArabic($request->status);
        $notification->link = route('job-seeker.applications');
        $notification->save();

        // إنشاء إشعار لصاحب العمل
        $notification = new Notification();
        $notification->user_id = $application->job->employer->user->id;
        $notification->type = 'application_update';
        $notification->message = 'تم تحديث حالة طلب "' . $application->jobSeeker->user->name . '" للوظيفة "' . $application->job->title . '" إلى ' . $this->getStatusInArabic($request->status) . ' بواسطة المدير';
        $notification->link = route('employer.applications.index');
        $notification->save();

        return redirect()->back()->with('success', 'تم تحديث حالة الطلب بنجاح');
    }

    /**
     * حذف طلب توظيف
     */
    public function destroy(Application $application)
    {
        // حذف الطلب
        $application->delete();

        return redirect()->route('admin.applications.index')->with('success', 'تم حذف الطلب بنجاح');
    }

    /**
     * تصدير طلبات التوظيف
     */
    public function export(Request $request)
    {
        // استعلام طلبات التوظيف مع علاقاتها
        $applicationsQuery = Application::with(['jobSeeker.user', 'job.employer']);

        // تطبيق الفلاتر إذا وجدت
        if ($request->filled('job')) {
            $applicationsQuery->where('job_id', $request->job);
        }

        if ($request->filled('status')) {
            $applicationsQuery->where('status', $request->status);
        }

        // الحصول على طلبات التوظيف
        $applications = $applicationsQuery->get();

        // تصدير البيانات (يمكن استخدام مكتبة مثل maatwebsite/excel)
        return response()->json([
            'success' => true,
            'applications' => $applications
        ]);
    }

    /**
     * الحصول على حالة الطلب باللغة العربية
     */
    private function getStatusInArabic($status)
    {
        switch ($status) {
            case 'pending':
                return 'قيد المراجعة';
            case 'reviewed':
                return 'تمت المراجعة';
            case 'shortlisted':
                return 'القائمة المختصرة';
            case 'rejected':
                return 'مرفوض';
            case 'hired':
                return 'تم التعيين';
            default:
                return $status;
        }
    }

    /**
     * تنظيف الطلبات المرتبطة بوظائف أو مستخدمين محذوفين
     */
    private function cleanupOrphanedApplications()
    {
        try {
            // حذف الطلبات المرتبطة بوظائف محذوفة
            $orphanedApplications = Application::whereDoesntHave('job')->get();
            foreach ($orphanedApplications as $app) {
                $app->delete();
            }

            // حذف الطلبات المرتبطة بباحثين عن عمل محذوفين
            $orphanedJobSeekerApplications = Application::whereDoesntHave('jobSeeker')->get();
            foreach ($orphanedJobSeekerApplications as $app) {
                $app->delete();
            }

            // حذف الطلبات المرتبطة بمستخدمين محذوفين
            $orphanedUserApplications = Application::whereDoesntHave('jobSeeker.user')->get();
            foreach ($orphanedUserApplications as $app) {
                $app->delete();
            }
        } catch (\Exception $e) {
            // في حالة حدوث خطأ، نتجاهله ونكمل
            Log::warning('Error cleaning up orphaned applications: ' . $e->getMessage());
        }
    }
}
