<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SavedCandidate extends Model
{
    use HasFactory;

    protected $fillable = [
        'employer_id',
        'job_seeker_id',
        'notes',
    ];

    /**
     * Get the employer that saved the candidate.
     */
    public function employer()
    {
        return $this->belongsTo(Employer::class);
    }

    /**
     * Get the job seeker that was saved.
     */
    public function jobSeeker()
    {
        return $this->belongsTo(JobSeeker::class);
    }
}
