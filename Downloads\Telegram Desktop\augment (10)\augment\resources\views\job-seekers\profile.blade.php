@extends('layouts.app')

@section('title', 'الملف الشخصي للباحث عن عمل')

@section('content')
<!-- Hero Section -->
<div class="gradient-bg py-16">
    <div class="p-6">
        <div class="max-w-7xl mx-auto">
            <div class="flex flex-col lg:flex-row items-center gap-8">
                <!-- Profile Image -->
                <div class="relative">
                    <div class="w-32 h-32 lg:w-40 lg:h-40 rounded-full overflow-hidden border-4 border-white/20 shadow-2xl">
                        @if(Auth::user()->avatar)
                            <img src="{{ asset('storage/' . Auth::user()->avatar) }}" alt="{{ Auth::user()->name }}" class="w-full h-full object-cover">
                        @else
                            <div class="w-full h-full bg-white/10 flex items-center justify-center">
                                <i class="fas fa-user text-4xl lg:text-5xl text-white/80"></i>
                            </div>
                        @endif
                    </div>
                    <!-- Status Badge -->
                    @if($jobSeeker->is_available)
                        <div class="absolute -bottom-2 -right-2 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg">
                            <i class="fas fa-check-circle mr-1"></i>
                            متاح للعمل
                        </div>
                    @else
                        <div class="absolute -bottom-2 -right-2 bg-gray-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg">
                            <i class="fas fa-pause-circle mr-1"></i>
                            غير متاح
                        </div>
                    @endif
                </div>

                <!-- Profile Info -->
                <div class="text-center lg:text-right flex-1">
                    <h1 class="text-3xl lg:text-4xl font-bold text-white mb-2">{{ Auth::user()->name }}</h1>
                    <p class="text-xl text-white/90 mb-4">{{ $jobSeeker->job_title ?? 'باحث عن عمل' }}</p>
                    <div class="flex flex-wrap justify-center lg:justify-start gap-4 text-white/80 mb-6">
                        <div class="flex items-center gap-2">
                            <i class="fas fa-envelope"></i>
                            <span>{{ Auth::user()->email }}</span>
                        </div>
                        @if($jobSeeker->location)
                            <div class="flex items-center gap-2">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>{{ $jobSeeker->location }}</span>
                            </div>
                        @endif
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex flex-col sm:flex-row gap-3 justify-center lg:justify-start">
                        <a href="{{ route('job-seeker.profile.edit') }}" class="bg-white text-primary px-6 py-3 rounded-lg hover:bg-gray-100 transition-colors font-medium shadow-lg">
                            <i class="fas fa-edit mr-2"></i>
                            تعديل الملف الشخصي
                        </a>
                        @if($jobSeeker->resume)
                            <a href="{{ Storage::url($jobSeeker->resume) }}" target="_blank" class="bg-white/10 backdrop-blur-sm text-white px-6 py-3 rounded-lg hover:bg-white/20 transition-colors font-medium border border-white/20">
                                <i class="fas fa-file-download mr-2"></i>
                                تحميل السيرة الذاتية
                            </a>
                        @endif
                        <a href="{{ route('jobs.index') }}" class="bg-white/10 backdrop-blur-sm text-white px-6 py-3 rounded-lg hover:bg-white/20 transition-colors font-medium border border-white/20">
                            <i class="fas fa-briefcase mr-2"></i>
                            تصفح الوظائف
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="py-16 bg-gray-50 dark:bg-gray-900">
    <div class="p-6">
        <div class="max-w-7xl mx-auto">

            <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
                <!-- Quick Stats -->
                <div class="lg:col-span-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <!-- Applications Count -->
                        <div class="bg-white dark:bg-dark-card rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي التقديمات</p>
                                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $jobSeeker->applications->count() }}</p>
                                </div>
                                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-paper-plane text-blue-600 dark:text-blue-400"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Pending Applications -->
                        <div class="bg-white dark:bg-dark-card rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">قيد المراجعة</p>
                                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $jobSeeker->applications->where('status', 'pending')->count() }}</p>
                                </div>
                                <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-clock text-yellow-600 dark:text-yellow-400"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Accepted Applications -->
                        <div class="bg-white dark:bg-dark-card rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">مقبولة</p>
                                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $jobSeeker->applications->where('status', 'accepted')->count() }}</p>
                                </div>
                                <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-check-circle text-green-600 dark:text-green-400"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Profile Completion -->
                        <div class="bg-white dark:bg-dark-card rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">اكتمال الملف</p>
                                    <p class="text-2xl font-bold text-gray-900 dark:text-white">
                                        @php
                                            $completion = 0;
                                            if($jobSeeker->job_title) $completion += 25;
                                            if($jobSeeker->location) $completion += 25;
                                            if($jobSeeker->skills) $completion += 25;
                                            if($jobSeeker->experience) $completion += 25;
                                            if($jobSeeker->resume) $completion += 25;
                                            if(Auth::user()->avatar) $completion += 25;
                                            $completion = min($completion, 100); // التأكد من عدم تجاوز 100%
                                        @endphp
                                        {{ $completion }}%
                                    </p>
                                </div>
                                <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-user-check text-purple-600 dark:text-purple-400"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="lg:col-span-1">
                    <!-- Profile Summary Card -->
                    <div class="bg-white dark:bg-dark-card rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">ملخص الملف</h3>

                        <div class="space-y-4">
                            <!-- Resume Status -->
                            <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                                <div class="flex items-center gap-3">
                                    <i class="fas fa-file-alt text-blue-600 dark:text-blue-400"></i>
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">السيرة الذاتية</span>
                                </div>
                                @if($jobSeeker->resume)
                                    <span class="text-green-600 dark:text-green-400 text-sm">
                                        <i class="fas fa-check-circle"></i>
                                    </span>
                                @else
                                    <span class="text-red-600 dark:text-red-400 text-sm">
                                        <i class="fas fa-times-circle"></i>
                                    </span>
                                @endif
                            </div>

                            <!-- Profile Photo -->
                            <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                                <div class="flex items-center gap-3">
                                    <i class="fas fa-camera text-purple-600 dark:text-purple-400"></i>
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">الصورة الشخصية</span>
                                </div>
                                @if(Auth::user()->avatar)
                                    <span class="text-green-600 dark:text-green-400 text-sm">
                                        <i class="fas fa-check-circle"></i>
                                    </span>
                                @else
                                    <span class="text-red-600 dark:text-red-400 text-sm">
                                        <i class="fas fa-times-circle"></i>
                                    </span>
                                @endif
                            </div>

                            <!-- Skills -->
                            <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                                <div class="flex items-center gap-3">
                                    <i class="fas fa-cogs text-orange-600 dark:text-orange-400"></i>
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">المهارات</span>
                                </div>
                                @if($jobSeeker->skills)
                                    <span class="text-green-600 dark:text-green-400 text-sm">
                                        <i class="fas fa-check-circle"></i>
                                    </span>
                                @else
                                    <span class="text-red-600 dark:text-red-400 text-sm">
                                        <i class="fas fa-times-circle"></i>
                                    </span>
                                @endif
                            </div>
                        </div>

                        @if($completion < 100)
                            <div class="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                                <p class="text-sm text-yellow-800 dark:text-yellow-200">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>
                                    أكمل ملفك الشخصي لزيادة فرص الحصول على وظيفة
                                </p>
                            </div>
                        @endif
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white dark:bg-dark-card rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">إجراءات سريعة</h3>

                        <div class="space-y-3">
                            <a href="{{ route('job-seeker.applications') }}" class="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                                <i class="fas fa-list text-blue-600 dark:text-blue-400"></i>
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">عرض التقديمات</span>
                            </a>

                            <a href="{{ route('jobs.index') }}" class="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                                <i class="fas fa-search text-green-600 dark:text-green-400"></i>
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">البحث عن وظائف</span>
                            </a>

                            <a href="{{ route('notifications.index') }}" class="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                                <i class="fas fa-bell text-purple-600 dark:text-purple-400"></i>
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">الإشعارات</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="lg:col-span-3 space-y-8">
                    <!-- Professional Information -->
                    <div class="bg-white dark:bg-dark-card rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700 p-8">
                        <div class="flex items-center justify-between mb-6">
                            <h2 class="text-2xl font-bold text-gray-900 dark:text-white">المعلومات المهنية</h2>
                            <a href="{{ route('job-seeker.profile.edit') }}" class="text-primary hover:text-primary-dark transition-colors">
                                <i class="fas fa-edit mr-1"></i>
                                تعديل
                            </a>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <div class="space-y-6">
                                <div>
                                    <h3 class="text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2">المسمى الوظيفي</h3>
                                    <p class="text-lg font-medium text-gray-900 dark:text-white">
                                        {{ $jobSeeker->job_title ?? 'غير محدد' }}
                                    </p>
                                </div>

                                <div>
                                    <h3 class="text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2">الموقع</h3>
                                    <p class="text-lg font-medium text-gray-900 dark:text-white flex items-center gap-2">
                                        <i class="fas fa-map-marker-alt text-red-500"></i>
                                        {{ $jobSeeker->location ?? 'غير محدد' }}
                                    </p>
                                </div>
                            </div>

                            <div class="space-y-6">
                                <div>
                                    <h3 class="text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2">حالة التوظيف</h3>
                                    <div class="flex items-center gap-2">
                                        @if($jobSeeker->is_available)
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                                                <i class="fas fa-check-circle mr-1"></i>
                                                متاح للعمل
                                            </span>
                                        @else
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300">
                                                <i class="fas fa-times-circle mr-1"></i>
                                                غير متاح
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Skills Section -->
                    <div class="bg-white dark:bg-dark-card rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700 p-8">
                        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">المهارات</h2>

                        @if($jobSeeker->skills)
                            <div class="flex flex-wrap gap-3">
                                @foreach(explode(',', $jobSeeker->skills) as $skill)
                                    <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                                        <i class="fas fa-check mr-2"></i>
                                        {{ trim($skill) }}
                                    </span>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-8">
                                <i class="fas fa-cogs text-4xl text-gray-300 dark:text-gray-600 mb-3"></i>
                                <p class="text-gray-500 dark:text-gray-400 mb-4">لم يتم إضافة مهارات بعد</p>
                                <a href="{{ route('job-seeker.profile.edit') }}" class="text-primary hover:text-primary-dark font-medium">
                                    إضافة مهارات
                                </a>
                            </div>
                        @endif
                    </div>

                    <!-- Experience Section -->
                    <div class="bg-white dark:bg-dark-card rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700 p-8">
                        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">الخبرات المهنية</h2>

                        @if($jobSeeker->experience)
                            <div class="prose dark:prose-invert max-w-none">
                                <div class="p-6 bg-gray-50 dark:bg-gray-800 rounded-xl">
                                    {!! nl2br(e($jobSeeker->experience)) !!}
                                </div>
                            </div>
                        @else
                            <div class="text-center py-8">
                                <i class="fas fa-briefcase text-4xl text-gray-300 dark:text-gray-600 mb-3"></i>
                                <p class="text-gray-500 dark:text-gray-400 mb-4">لم يتم إضافة خبرات مهنية بعد</p>
                                <a href="{{ route('job-seeker.profile.edit') }}" class="text-primary hover:text-primary-dark font-medium">
                                    إضافة خبرات
                                </a>
                            </div>
                        @endif
                    </div>

                    <!-- Education Section -->
                    <div class="bg-white dark:bg-dark-card rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700 p-8">
                        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">التعليم والمؤهلات</h2>

                        @if($jobSeeker->education)
                            <div class="prose dark:prose-invert max-w-none">
                                <div class="p-6 bg-gray-50 dark:bg-gray-800 rounded-xl">
                                    {!! nl2br(e($jobSeeker->education)) !!}
                                </div>
                            </div>
                        @else
                            <div class="text-center py-8">
                                <i class="fas fa-graduation-cap text-4xl text-gray-300 dark:text-gray-600 mb-3"></i>
                                <p class="text-gray-500 dark:text-gray-400 mb-4">لم يتم إضافة معلومات تعليمية بعد</p>
                                <a href="{{ route('job-seeker.profile.edit') }}" class="text-primary hover:text-primary-dark font-medium">
                                    إضافة تعليم
                                </a>
                            </div>
                        @endif
                    </div>

                    <!-- Recent Applications -->
                    <div class="bg-white dark:bg-dark-card rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700 p-8">
                        <div class="flex justify-between items-center mb-6">
                            <h2 class="text-2xl font-bold text-gray-900 dark:text-white">التقديمات الأخيرة</h2>
                            <a href="{{ route('job-seeker.applications') }}" class="text-primary hover:text-primary-dark flex items-center gap-2 font-medium">
                                عرض الكل
                                <i class="fas fa-arrow-left text-sm"></i>
                            </a>
                        </div>

                        @if($jobSeeker->applications->count() > 0)
                            <div class="space-y-4">
                                @foreach($jobSeeker->applications->take(5) as $application)
                                    <div class="border border-gray-200 dark:border-gray-700 rounded-xl p-6 hover:shadow-md transition-shadow">
                                        <div class="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                                            <div class="flex-1">
                                                <div class="flex items-start gap-4">
                                                    <!-- Company Logo Placeholder -->
                                                    <div class="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center flex-shrink-0">
                                                        <i class="fas fa-building text-gray-500"></i>
                                                    </div>

                                                    <div class="flex-1">
                                                        <h3 class="font-semibold text-gray-900 dark:text-white mb-1">
                                                            <a href="{{ route('jobs.show', $application->job) }}" class="hover:text-primary transition-colors">
                                                                {{ $application->job->title }}
                                                            </a>
                                                        </h3>
                                                        <p class="text-gray-600 dark:text-gray-400 mb-2">{{ $application->job->employer->company_name }}</p>
                                                        <div class="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                                                            <span>
                                                                <i class="fas fa-calendar mr-1"></i>
                                                                {{ $application->created_at->format('d M Y') }}
                                                            </span>
                                                            @if($application->job->location)
                                                                <span>
                                                                    <i class="fas fa-map-marker-alt mr-1"></i>
                                                                    {{ $application->job->location }}
                                                                </span>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="flex items-center gap-3">
                                                <!-- Status Badge -->
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                                    @if($application->status == 'pending') bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300
                                                    @elseif($application->status == 'accepted') bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300
                                                    @elseif($application->status == 'rejected') bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300
                                                    @elseif($application->status == 'reviewed') bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300
                                                    @endif">
                                                    @switch($application->status)
                                                        @case('pending')
                                                            <i class="fas fa-clock mr-1"></i>
                                                            قيد المراجعة
                                                            @break
                                                        @case('accepted')
                                                            <i class="fas fa-check-circle mr-1"></i>
                                                            مقبول
                                                            @break
                                                        @case('rejected')
                                                            <i class="fas fa-times-circle mr-1"></i>
                                                            مرفوض
                                                            @break
                                                        @case('reviewed')
                                                            <i class="fas fa-eye mr-1"></i>
                                                            تمت المراجعة
                                                            @break
                                                        @default
                                                            {{ $application->status }}
                                                    @endswitch
                                                </span>

                                                <!-- View Button -->
                                                <a href="{{ route('jobs.show', $application->job) }}" class="text-gray-400 hover:text-primary transition-colors">
                                                    <i class="fas fa-external-link-alt"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-12">
                                <div class="w-24 h-24 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-paper-plane text-4xl text-gray-400"></i>
                                </div>
                                <h3 class="text-xl font-medium text-gray-900 dark:text-white mb-2">لم تقم بالتقديم على أي وظيفة بعد</h3>
                                <p class="text-gray-500 dark:text-gray-400 mb-6">ابدأ رحلتك المهنية بالتقديم على الوظائف المناسبة لك</p>
                                <div class="flex flex-col sm:flex-row gap-3 justify-center">
                                    <a href="{{ route('jobs.index') }}" class="bg-primary hover:bg-primary-dark text-white px-6 py-3 rounded-lg font-medium transition-colors">
                                        <i class="fas fa-search mr-2"></i>
                                        تصفح الوظائف المتاحة
                                    </a>
                                    <a href="{{ route('job-seeker.profile.edit') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                                        <i class="fas fa-user-edit mr-2"></i>
                                        أكمل ملفك الشخصي
                                    </a>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
