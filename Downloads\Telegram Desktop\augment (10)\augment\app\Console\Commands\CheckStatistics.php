<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Employer;
use App\Models\JobSeeker;
use App\Models\Job;
use App\Models\Application;
use App\Models\Category;

class CheckStatistics extends Command
{
    protected $signature = 'test:check-stats {--employer-id=}';
    protected $description = 'فحص الإحصائيات في النظام';

    public function handle()
    {
        $this->info('=== فحص الإحصائيات ===');
        
        // الإحصائيات العامة
        $this->info('الإحصائيات العامة:');
        $this->info('المستخدمين: ' . User::count());
        $this->info('أصحاب العمل: ' . Employer::count());
        $this->info('الباحثين عن عمل: ' . JobSeeker::count());
        $this->info('الوظائف: ' . Job::count());
        $this->info('الطلبات: ' . Application::count());
        $this->info('الفئات: ' . Category::count());
        
        $this->info('');
        
        // إحصائيات الوظائف
        $this->info('إحصائيات الوظائف:');
        $this->info('الوظائف النشطة: ' . Job::where('is_active', true)->count());
        $this->info('الوظائف غير النشطة: ' . Job::where('is_active', false)->count());
        $this->info('الوظائف المنتهية الصلاحية: ' . Job::where('expires_at', '<', now())->count());
        
        $this->info('');
        
        // إحصائيات الطلبات
        $this->info('إحصائيات الطلبات:');
        $this->info('قيد المراجعة: ' . Application::where('status', 'pending')->count());
        $this->info('تمت المراجعة: ' . Application::where('status', 'reviewed')->count());
        $this->info('مقبولة: ' . Application::where('status', 'accepted')->count());
        $this->info('مرفوضة: ' . Application::where('status', 'rejected')->count());
        
        $this->info('');
        
        // إحصائيات صاحب عمل محدد
        $employerId = $this->option('employer-id');
        if ($employerId) {
            $employer = Employer::find($employerId);
            if ($employer) {
                $this->info("إحصائيات صاحب العمل: {$employer->company_name}");
                $this->info('الوظائف: ' . $employer->jobs()->count());
                $this->info('الوظائف النشطة: ' . $employer->jobs()->where('is_active', true)->count());
                
                $totalApplications = Application::whereHas('job', function($query) use ($employer) {
                    $query->where('employer_id', $employer->id);
                })->count();
                
                $this->info('إجمالي الطلبات: ' . $totalApplications);
                
                $pendingApplications = Application::whereHas('job', function($query) use ($employer) {
                    $query->where('employer_id', $employer->id);
                })->where('status', 'pending')->count();
                
                $this->info('الطلبات المعلقة: ' . $pendingApplications);
            } else {
                $this->error('صاحب العمل غير موجود');
            }
        } else {
            // عرض قائمة أصحاب العمل
            $this->info('أصحاب العمل المتاحين:');
            $employers = Employer::with('user')->get();
            foreach ($employers as $employer) {
                $this->info("ID: {$employer->id} - {$employer->company_name} ({$employer->user->email})");
            }
        }
        
        $this->info('');
        
        // فحص العلاقات
        $this->info('فحص العلاقات:');
        
        $jobsWithoutEmployer = Job::whereNotIn('employer_id', Employer::pluck('id'))->count();
        if ($jobsWithoutEmployer > 0) {
            $this->warn("وظائف بدون صاحب عمل: {$jobsWithoutEmployer}");
        } else {
            $this->info('جميع الوظائف مرتبطة بأصحاب عمل ✓');
        }
        
        $applicationsWithoutJob = Application::whereNotIn('job_id', Job::pluck('id'))->count();
        if ($applicationsWithoutJob > 0) {
            $this->warn("طلبات بدون وظيفة: {$applicationsWithoutJob}");
        } else {
            $this->info('جميع الطلبات مرتبطة بوظائف ✓');
        }
        
        $applicationsWithoutJobSeeker = Application::whereNotIn('job_seeker_id', JobSeeker::pluck('id'))->count();
        if ($applicationsWithoutJobSeeker > 0) {
            $this->warn("طلبات بدون باحث عن عمل: {$applicationsWithoutJobSeeker}");
        } else {
            $this->info('جميع الطلبات مرتبطة بباحثين عن عمل ✓');
        }
    }
}
