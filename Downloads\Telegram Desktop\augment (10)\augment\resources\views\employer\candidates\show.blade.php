@extends('layouts.employer')

@section('title', 'ملف المرشح - Hire Me')
@section('header_title', 'ملف المرشح')

@section('content')
<div class="p-8 space-y-8">
    <!-- Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ $jobSeeker->user->name }}</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">{{ $jobSeeker->job_title ?? 'غير محدد' }}</p>
        </div>

        <div class="flex gap-3 mt-4 md:mt-0">
            <a href="{{ route('employer.candidates.index') }}" class="px-6 py-3 bg-gray-600 text-white rounded-xl hover:bg-gray-700 transition-all duration-300 flex items-center gap-2">
                <i class="fas fa-arrow-right"></i>
                العودة للقائمة
            </a>
            @if($isSaved)
                <button onclick="unsaveCandidate({{ $jobSeeker->id }})" class="px-6 py-3 bg-red-600 text-white rounded-xl hover:bg-red-700 transition-all duration-300 flex items-center gap-2">
                    <i class="fas fa-bookmark-slash"></i>
                    إلغاء الحفظ
                </button>
            @else
                <button onclick="saveCandidate({{ $jobSeeker->id }})" class="px-6 py-3 bg-green-600 text-white rounded-xl hover:bg-green-700 transition-all duration-300 flex items-center gap-2">
                    <i class="fas fa-bookmark"></i>
                    حفظ المرشح
                </button>
            @endif
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Profile -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Info -->
            <div class="glass-card rounded-xl p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                    <i class="fas fa-user text-blue-600"></i>
                    المعلومات الأساسية
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="text-sm font-medium text-gray-600 dark:text-gray-400">الاسم</label>
                        <p class="text-gray-900 dark:text-white">{{ $jobSeeker->user->name }}</p>
                    </div>

                    <div>
                        <label class="text-sm font-medium text-gray-600 dark:text-gray-400">البريد الإلكتروني</label>
                        <p class="text-gray-900 dark:text-white">{{ $jobSeeker->user->email }}</p>
                    </div>

                    <div>
                        <label class="text-sm font-medium text-gray-600 dark:text-gray-400">المسمى الوظيفي</label>
                        <p class="text-gray-900 dark:text-white">{{ $jobSeeker->job_title ?? 'غير محدد' }}</p>
                    </div>

                    <div>
                        <label class="text-sm font-medium text-gray-600 dark:text-gray-400">الموقع</label>
                        <p class="text-gray-900 dark:text-white">{{ $jobSeeker->location ?? 'غير محدد' }}</p>
                    </div>
                </div>
            </div>

            <!-- Skills -->
            @if($jobSeeker->skills)
            <div class="glass-card rounded-xl p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                    <i class="fas fa-cogs text-green-600"></i>
                    المهارات
                </h2>

                <div class="flex flex-wrap gap-2">
                    @foreach(explode(',', $jobSeeker->skills) as $skill)
                        <span class="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-sm rounded-full">
                            {{ trim($skill) }}
                        </span>
                    @endforeach
                </div>
            </div>
            @endif

            <!-- Experience -->
            @if($jobSeeker->experience)
            <div class="glass-card rounded-xl p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                    <i class="fas fa-briefcase text-purple-600"></i>
                    الخبرة
                </h2>

                <div class="prose dark:prose-invert max-w-none">
                    <p class="text-gray-700 dark:text-gray-300">{{ $jobSeeker->experience }}</p>
                </div>
            </div>
            @endif

            <!-- Education -->
            @if($jobSeeker->education)
            <div class="glass-card rounded-xl p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                    <i class="fas fa-graduation-cap text-orange-600"></i>
                    التعليم
                </h2>

                <div class="prose dark:prose-invert max-w-none">
                    <p class="text-gray-700 dark:text-gray-300">{{ $jobSeeker->education }}</p>
                </div>
            </div>
            @endif

            <!-- Applications History -->
            @if($jobSeeker->applications->count() > 0)
            <div class="glass-card rounded-xl p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                    <i class="fas fa-history text-red-600"></i>
                    تاريخ التقديمات لوظائفك
                </h2>

                <div class="space-y-4">
                    @foreach($jobSeeker->applications as $application)
                        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="font-medium text-gray-900 dark:text-white">{{ $application->job->title }}</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">تقدم في {{ $application->created_at->format('Y-m-d') }}</p>
                                </div>
                                <div>
                                    @if($application->status == 'pending')
                                        <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">قيد المراجعة</span>
                                    @elseif($application->status == 'accepted')
                                        <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">مقبول</span>
                                    @elseif($application->status == 'rejected')
                                        <span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">مرفوض</span>
                                    @else
                                        <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">{{ $application->status }}</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Stats -->
            <div class="glass-card rounded-xl p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">إحصائيات سريعة</h3>

                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">تاريخ التسجيل</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $jobSeeker->created_at->format('Y-m-d') }}</span>
                    </div>

                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">إجمالي التقديمات</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $jobSeeker->applications->count() }}</span>
                    </div>

                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">متاح للعمل</span>
                        <span class="text-sm font-medium {{ $jobSeeker->is_available ? 'text-green-600' : 'text-red-600' }}">
                            {{ $jobSeeker->is_available ? 'نعم' : 'لا' }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Resume -->
            @if($jobSeeker->resume)
            <div class="glass-card rounded-xl p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">السيرة الذاتية</h3>

                <a href="{{ storageUrl($jobSeeker->resume) }}" target="_blank" class="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-download"></i>
                    تحميل السيرة الذاتية
                </a>
            </div>
            @endif

            <!-- Contact Actions -->
            <div class="glass-card rounded-xl p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">إجراءات التواصل</h3>

                <div class="space-y-3">
                    <a href="mailto:{{ $jobSeeker->user->email }}" class="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-envelope"></i>
                        إرسال بريد إلكتروني
                    </a>

                    <button onclick="showNotesModal()" class="w-full flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                        <i class="fas fa-sticky-note"></i>
                        إضافة ملاحظة
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.glass-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-card {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
}
</style>

@push('scripts')
<script>
function saveCandidate(candidateId) {
    fetch(`/employer/candidates/${candidateId}/save`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'حدث خطأ');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
}

function unsaveCandidate(candidateId) {
    if (confirm('هل أنت متأكد من إلغاء حفظ هذا المرشح؟')) {
        fetch(`/employer/candidates/${candidateId}/unsave`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'حدث خطأ');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
    }
}

function showNotesModal() {
    // يمكن إضافة modal للملاحظات لاحقاً
    alert('ميزة الملاحظات ستكون متاحة قريباً');
}
</script>
@endpush

@endsection
