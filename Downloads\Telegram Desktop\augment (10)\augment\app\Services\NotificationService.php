<?php

namespace App\Services;

use App\Models\Notification;
use App\Models\NotificationSetting;
use App\Models\User;
use App\Models\Job;
use App\Models\Application;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class NotificationService
{
    /**
     * أنواع الإشعارات المختلفة
     */
    const TYPE_JOB_APPLICATION = 'job_application';
    const TYPE_APPLICATION_UPDATE = 'application_update';
    const TYPE_JOB_POSTED = 'job_posted';
    const TYPE_JOB_EXPIRED = 'job_expired';
    const TYPE_PAYMENT_SUCCESS = 'payment_success';
    const TYPE_PAYMENT_FAILED = 'payment_failed';
    const TYPE_PROFILE_RATING = 'profile_rating';
    const TYPE_COMPANY_RATING = 'company_rating';
    const TYPE_SYSTEM_ANNOUNCEMENT = 'system_announcement';
    const TYPE_ACCOUNT_VERIFICATION = 'account_verification';
    const TYPE_PASSWORD_RESET = 'password_reset';
    const TYPE_INTERVIEW_SCHEDULED = 'interview_scheduled';
    const TYPE_JOB_RECOMMENDATION = 'job_recommendation';

    /**
     * إنشاء إشعار جديد
     */
    public function create(User $user, string $type, string $message, ?string $link = null, array $data = []): Notification
    {
        $notification = Notification::create([
            'user_id' => $user->id,
            'type' => $type,
            'message' => $message,
            'link' => $link,
            'data' => json_encode($data),
            'is_read' => false,
        ]);

        // إرسال الإشعار الفوري إذا كان المستخدم متصلاً
        $this->sendRealTimeNotification($user, $notification);

        // إرسال إشعار بالبريد الإلكتروني إذا كان مطلوباً
        if ($this->shouldSendEmail($user, $type)) {
            $this->sendEmailNotification($user, $notification);
        }

        return $notification;
    }

    /**
     * إشعار طلب توظيف جديد
     */
    public function jobApplicationReceived(User $employer, Application $application): void
    {
        $message = "تم استلام طلب توظيف جديد للوظيفة: {$application->job->title}";
        $link = route('employer.applications');

        $this->create($employer, self::TYPE_JOB_APPLICATION, $message, $link, [
            'application_id' => $application->id,
            'job_id' => $application->job_id,
            'applicant_name' => $application->jobSeeker->user->name,
        ]);
    }

    /**
     * إشعار تحديث حالة الطلب
     */
    public function applicationStatusUpdated(User $jobSeeker, Application $application, string $newStatus): void
    {
        $statusText = $this->getStatusInArabic($newStatus);
        $message = "تم تحديث حالة طلبك للوظيفة \"{$application->job->title}\" إلى: {$statusText}";
        $link = route('job-seeker.applications');

        $this->create($jobSeeker, self::TYPE_APPLICATION_UPDATE, $message, $link, [
            'application_id' => $application->id,
            'job_id' => $application->job_id,
            'status' => $newStatus,
        ]);
    }

    /**
     * إشعار نشر وظيفة جديدة
     */
    public function jobPosted(User $employer, Job $job): void
    {
        $message = "تم نشر وظيفتك \"{$job->title}\" بنجاح";
        $link = route('employer.jobs.index');

        $this->create($employer, self::TYPE_JOB_POSTED, $message, $link, [
            'job_id' => $job->id,
        ]);

        // إشعار الباحثين عن عمل المهتمين
        $this->notifyInterestedJobSeekers($job);
    }

    /**
     * إشعار انتهاء صلاحية الوظيفة
     */
    public function jobExpired(User $employer, Job $job): void
    {
        $message = "انتهت صلاحية الوظيفة \"{$job->title}\"";
        $link = route('employer.jobs.index');

        $this->create($employer, self::TYPE_JOB_EXPIRED, $message, $link, [
            'job_id' => $job->id,
        ]);
    }

    /**
     * إشعار نجاح الدفع
     */
    public function paymentSuccess(User $user, array $paymentData): void
    {
        $amount = $paymentData['amount'] ?? 0;
        $message = "تم الدفع بنجاح بمبلغ {$amount} ريال";
        $link = route('employer.profile');

        $this->create($user, self::TYPE_PAYMENT_SUCCESS, $message, $link, $paymentData);
    }

    /**
     * إشعار فشل الدفع
     */
    public function paymentFailed(User $user, array $paymentData): void
    {
        $message = "فشل في عملية الدفع. يرجى المحاولة مرة أخرى";
        $link = route('payments.checkout');

        $this->create($user, self::TYPE_PAYMENT_FAILED, $message, $link, $paymentData);
    }

    /**
     * إشعار تقييم الملف الشخصي
     */
    public function profileRated(User $user, int $rating, ?string $comment = null): void
    {
        $message = "تم تقييم ملفك الشخصي بـ {$rating} نجوم";
        $link = route('job-seeker.profile');

        $this->create($user, self::TYPE_PROFILE_RATING, $message, $link, [
            'rating' => $rating,
            'comment' => $comment,
        ]);
    }

    /**
     * إشعار تقييم الشركة
     */
    public function companyRated(User $employer, int $rating, ?string $comment = null): void
    {
        $message = "تم تقييم شركتك بـ {$rating} نجوم";
        $link = route('employer.profile');

        $this->create($employer, self::TYPE_COMPANY_RATING, $message, $link, [
            'rating' => $rating,
            'comment' => $comment,
        ]);
    }

    /**
     * إشعار إعلان النظام
     */
    public function systemAnnouncement(string $message, ?string $link = null): void
    {
        $users = User::all();

        foreach ($users as $user) {
            $this->create($user, self::TYPE_SYSTEM_ANNOUNCEMENT, $message, $link);
        }
    }

    /**
     * إشعار جدولة مقابلة
     */
    public function interviewScheduled(User $jobSeeker, Application $application, array $interviewData): void
    {
        $date = $interviewData['date'] ?? 'غير محدد';
        $time = $interviewData['time'] ?? 'غير محدد';
        $message = "تم جدولة مقابلة للوظيفة \"{$application->job->title}\" في {$date} الساعة {$time}";
        $link = route('job-seeker.applications');

        $this->create($jobSeeker, self::TYPE_INTERVIEW_SCHEDULED, $message, $link, $interviewData);
    }

    /**
     * إشعار توصية وظيفة
     */
    public function jobRecommendation(User $jobSeeker, Job $job): void
    {
        $message = "وظيفة قد تهمك: {$job->title}";
        $link = route('jobs.show', $job);

        $this->create($jobSeeker, self::TYPE_JOB_RECOMMENDATION, $message, $link, [
            'job_id' => $job->id,
        ]);
    }

    /**
     * الحصول على الإشعارات غير المقروءة للمستخدم
     */
    public function getUnreadNotifications(User $user, int $limit = 10)
    {
        return $user->notifications()
            ->where('is_read', false)
            ->latest()
            ->limit($limit)
            ->get();
    }

    /**
     * الحصول على عدد الإشعارات غير المقروءة
     */
    public function getUnreadCount(User $user): int
    {
        return $user->notifications()->where('is_read', false)->count();
    }

    /**
     * تحديد ما إذا كان يجب إرسال إشعار بالبريد الإلكتروني
     */
    private function shouldSendEmail(User $user, string $type): bool
    {
        // التحقق من إعدادات المستخدم
        return NotificationSetting::isEnabled($user, $type, 'email');
    }

    /**
     * إرسال إشعار بالبريد الإلكتروني
     */
    private function sendEmailNotification(User $user, Notification $notification): void
    {
        try {
            // يمكن إنشاء Mailable classes مختلفة لكل نوع إشعار
            // Mail::to($user->email)->send(new NotificationMail($notification));

            // للآن سنستخدم log
            Log::info("Email notification sent to {$user->email}: {$notification->message}");
        } catch (\Exception $e) {
            Log::error("Failed to send email notification: " . $e->getMessage());
        }
    }

    /**
     * إرسال إشعار فوري
     */
    private function sendRealTimeNotification(User $user, Notification $notification): void
    {
        // يمكن استخدام Pusher أو WebSockets هنا
        // للآن سنستخدم session flash
        if (session()->has('user_id') && session('user_id') == $user->id) {
            session()->flash('realtime_notification', [
                'type' => $notification->type,
                'message' => $notification->message,
                'link' => $notification->link,
            ]);
        }
    }

    /**
     * إشعار الباحثين عن عمل المهتمين بوظيفة جديدة
     */
    private function notifyInterestedJobSeekers(Job $job): void
    {
        // منطق لإيجاد الباحثين عن عمل المهتمين بناءً على المهارات والموقع
        $interestedJobSeekers = User::where('role', 'job_seeker')
            ->whereHas('jobSeeker', function($query) use ($job) {
                // يمكن إضافة شروط أكثر تعقيداً هنا
                $query->where('is_available', true);
            })
            ->limit(50) // تحديد العدد لتجنب الإرسال المفرط
            ->get();

        foreach ($interestedJobSeekers as $jobSeeker) {
            $this->jobRecommendation($jobSeeker, $job);
        }
    }

    /**
     * ترجمة حالة الطلب إلى العربية
     */
    private function getStatusInArabic(string $status): string
    {
        $statuses = [
            'pending' => 'قيد المراجعة',
            'reviewed' => 'تمت المراجعة',
            'accepted' => 'مقبول',
            'rejected' => 'مرفوض',
            'interview' => 'مقابلة',
            'hired' => 'تم التوظيف',
        ];

        return $statuses[$status] ?? $status;
    }
}
