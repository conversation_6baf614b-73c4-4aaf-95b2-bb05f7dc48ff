<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'لوحة تحكم صاحب العمل - Hire Me')</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <link rel="stylesheet" href="{{ asset('css/color-themes.css') }}">
    <link rel="stylesheet" href="{{ asset('css/admin-enhancements.css') }}">
    <link rel="stylesheet" href="{{ asset('css/applications-enhancements.css') }}">
    <link rel="stylesheet" href="{{ asset('css/employer-jobs.css') }}">
    <link rel="stylesheet" href="{{ asset('css/notifications-enhancements.css') }}">
    <link rel="stylesheet" href="{{ asset('css/theme-transitions.css') }}">
    <!-- نظام الثيم البسيط -->
    <script>
        // تطبيق الثيم فوراً
        (function() {
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark') {
                document.documentElement.classList.add('dark');
            } else if (savedTheme === 'light') {
                document.documentElement.classList.remove('dark');
            } else {
                // استخدام تفضيل النظام
                const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
                if (prefersDark) {
                    document.documentElement.classList.add('dark');
                }
            }
        })();
    </script>
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        // نظام ألوان متدرج وجميل - نفس ألوان لوحة الإدارة
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                        accent: {
                            50: '#fdf4ff',
                            100: '#fae8ff',
                            200: '#f5d0fe',
                            300: '#f0abfc',
                            400: '#e879f9',
                            500: '#d946ef',
                            600: '#c026d3',
                            700: '#a21caf',
                            800: '#86198f',
                            900: '#701a75',
                        },
                        success: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                        },
                        warning: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            200: '#fde68a',
                            300: '#fcd34d',
                            400: '#fbbf24',
                            500: '#f59e0b',
                            600: '#d97706',
                            700: '#b45309',
                            800: '#92400e',
                            900: '#78350f',
                        },
                        danger: {
                            50: '#fef2f2',
                            100: '#fee2e2',
                            200: '#fecaca',
                            300: '#fca5a5',
                            400: '#f87171',
                            500: '#ef4444',
                            600: '#dc2626',
                            700: '#b91c1c',
                            800: '#991b1b',
                            900: '#7f1d1d',
                        },
                        // ألوان الوضع المظلم المحسنة
                        'dark-bg': '#0f172a',
                        'dark-card': '#1e293b',
                        'dark-sidebar': '#0f172a',
                        'dark-surface': '#334155',
                    },
                    fontFamily: {
                        'tajawal': ['Tajawal', 'sans-serif']
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-in': 'slideIn 0.3s ease-out',
                        'bounce-in': 'bounceIn 0.6s ease-out',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideIn: {
                            '0%': { transform: 'translateX(-100%)' },
                            '100%': { transform: 'translateX(0)' },
                        },
                        bounceIn: {
                            '0%': { transform: 'scale(0.3)', opacity: '0' },
                            '50%': { transform: 'scale(1.05)' },
                            '70%': { transform: 'scale(0.9)' },
                            '100%': { transform: 'scale(1)', opacity: '1' },
                        },
                        glow: {
                            '0%': { boxShadow: '0 0 5px rgba(14, 165, 233, 0.5)' },
                            '100%': { boxShadow: '0 0 20px rgba(14, 165, 233, 0.8)' },
                        },
                    },
                    backdropBlur: {
                        xs: '2px',
                    }
                },
            }
        }

        // تم نقل تطبيق الثيم إلى الأعلى
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;600;700;800&display=swap');

        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #fefefe 0%, #f8fafc 50%, #f1f5f9 100%);
        }

        .dark body {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
        }

        /* تدرجات لونية جميلة */
        .gradient-primary {
            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 50%, #0369a1 100%);
        }

        .gradient-accent {
            background: linear-gradient(135deg, #d946ef 0%, #c026d3 50%, #a21caf 100%);
        }

        .gradient-success {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 50%, #15803d 100%);
        }

        .gradient-warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #b45309 100%);
        }

        .gradient-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%);
        }

        /* خلفية الشريط الجانبي مع تأثير زجاجي */
        .glass-sidebar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            height: 100vh;
            max-height: 100vh;
        }

        .dark .glass-sidebar {
            background: rgba(15, 23, 42, 0.95);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* إصلاح مشكلة التمرير في الشريط الجانبي */
        .sidebar-content {
            height: calc(100vh - 140px);
            overflow-y: auto !important;
            overflow-x: hidden;
            max-height: calc(100vh - 140px);
        }

        /* شريط التمرير المخصص */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(148, 163, 184, 0.1);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #0ea5e9, #d946ef);
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #0284c7, #c026d3);
        }

        /* تأثيرات البطاقات المحسنة */
        .glass-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            transition: all 0.3s ease;
        }

        .dark .glass-card {
            background: rgba(15, 23, 42, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
        }

        /* شريط علوي غير شفاف للوحة صاحب العمل */
        .employer-header {
            background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(248, 250, 252, 1) 100%) !important;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(229, 231, 235, 0.8);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08),
                        0 1px 3px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .dark .employer-header {
            background: linear-gradient(135deg, rgba(15, 23, 42, 1) 0%, rgba(30, 41, 59, 1) 100%) !important;
            border-bottom: 1px solid rgba(55, 65, 81, 0.8);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3),
                        0 1px 3px rgba(0, 0, 0, 0.2);
        }

        /* تأثير إضافي للشريط العلوي */
        .employer-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(14, 165, 233, 0.5), transparent);
        }

        .dark .employer-header::before {
            background: linear-gradient(90deg, transparent, rgba(217, 70, 239, 0.5), transparent);
        }

        /* تحسين أزرار الشريط العلوي */
        .header-btn {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(229, 231, 235, 0.8);
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .dark .header-btn {
            background: rgba(30, 41, 59, 0.9);
            border: 1px solid rgba(55, 65, 81, 0.8);
        }

        .header-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        /* تحسين الفواصل في الشريط العلوي */
        .header-divider {
            background: linear-gradient(to bottom, transparent, rgba(156, 163, 175, 0.5), transparent);
        }

        .dark .header-divider {
            background: linear-gradient(to bottom, transparent, rgba(75, 85, 99, 0.5), transparent);
        }

        /* تحسين شكل العنوان */
        .header-title {
            background: linear-gradient(135deg, #1f2937, #374151);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .dark .header-title {
            background: linear-gradient(135deg, #f9fafb, #e5e7eb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* تأثير انيميشن للشريط العلوي */
        .employer-header {
            animation: slideDown 0.6s ease-out;
        }

        @keyframes slideDown {
            from {
                transform: translateY(-100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        /* تأثير hover للأزرار */
        .header-btn {
            position: relative;
            overflow: hidden;
        }

        .header-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .header-btn:hover::before {
            left: 100%;
        }

        .glass-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(31, 38, 135, 0.4);
        }

        .dark .glass-card:hover {
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6);
        }

        /* Enhanced Stat Cards */
        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .dark .stat-card {
            background: rgba(15, 23, 42, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(14, 165, 233, 0.1), rgba(217, 70, 239, 0.1));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .stat-card:hover::before {
            opacity: 1;
        }

        .stat-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        /* Enhanced Navigation */
        .nav-item-hover {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .nav-item-hover::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(14, 165, 233, 0.1), rgba(217, 70, 239, 0.1));
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }

        .nav-item-hover:hover::before {
            transform: translateX(0);
        }

        .active-nav-item {
            background: linear-gradient(135deg, #0ea5e9, #d946ef);
            color: white;
            box-shadow: 0 8px 25px rgba(14, 165, 233, 0.4);
            position: relative;
        }

        .active-nav-item::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -8px;
            transform: translateY(-50%);
            width: 4px;
            height: 20px;
            background: linear-gradient(to bottom, #0ea5e9, #d946ef);
            border-radius: 2px;
        }

        /* Enhanced Gradients */
        .gradient-primary {
            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 50%, #0369a1 100%);
        }

        .text-gradient {
            background: linear-gradient(135deg, #0ea5e9 0%, #d946ef 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .glow-effect {
            box-shadow: 0 0 20px rgba(14, 165, 233, 0.4);
        }

        /* Enhanced Animations */
        @keyframes blob {
            0% { transform: translate(0px, 0px) scale(1); }
            33% { transform: translate(30px, -50px) scale(1.1); }
            66% { transform: translate(-20px, 20px) scale(0.9); }
            100% { transform: translate(0px, 0px) scale(1); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateX(-100%); }
            to { opacity: 1; transform: translateX(0); }
        }

        .animate-blob { animation: blob 7s infinite; }
        .animate-float { animation: float 6s ease-in-out infinite; }
        .animate-fade-in { animation: fadeIn 0.6s ease-out; }
        .animate-slide-in { animation: slideIn 0.5s ease-out; }

        /* Enhanced Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, rgba(14, 165, 233, 0.6), rgba(217, 70, 239, 0.6));
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, rgba(14, 165, 233, 0.8), rgba(217, 70, 239, 0.8));
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        /* Navigation active state */
        .nav-link.active {
            background: linear-gradient(135deg, #0ea5e9, #0369a1);
            color: white;
        }

        .nav-link.active i {
            color: white;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(-2px);
        }

        .nav-link {
            transition: all 0.3s ease;
        }

        .nav-link i {
            transition: all 0.3s ease;
        }

        .nav-link:hover i {
            transform: scale(1.1);
        }

        /* تحسينات للنوافذ المنبثقة */
        [x-cloak] {
            display: none !important;
        }

        .notification-dropdown {
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            will-change: transform, opacity;
        }

        .notification-item {
            transition: all 0.2s ease-in-out;
        }

        .notification-item:hover {
            transform: translateX(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* تحسينات للأجهزة المحمولة */
        @media (max-width: 768px) {
            .notification-dropdown {
                position: fixed !important;
                top: 60px !important;
                left: 10px !important;
                right: 10px !important;
                width: auto !important;
                max-width: none !important;
            }

            .notification-item:hover {
                transform: none;
                box-shadow: none;
            }
        }

        /* تحسين إمكانية الوصول */
        .notification-dropdown:focus-within {
            outline: 2px solid #3b82f6;
            outline-offset: 2px;
        }
    </style>
    @yield('styles')
</head>
<body class="bg-gradient-to-br from-slate-50 to-slate-200 dark:from-dark-bg dark:to-slate-900 text-gray-800 dark:text-gray-200 transition-all duration-500 min-h-screen">
    <div class="flex min-h-screen relative">
        <!-- خلفية متحركة بألوان هادئة وجميلة -->
        <div class="fixed inset-0 overflow-hidden pointer-events-none">
            <div class="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-sky-100 to-blue-200 dark:from-sky-900/30 dark:to-blue-900/30 rounded-full filter blur-2xl opacity-30 animate-blob"></div>
            <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-fuchsia-100 to-purple-200 dark:from-fuchsia-900/30 dark:to-purple-900/30 rounded-full filter blur-2xl opacity-30 animate-blob animation-delay-2000"></div>
            <div class="absolute top-40 left-40 w-80 h-80 bg-gradient-to-br from-emerald-100 to-teal-200 dark:from-emerald-900/30 dark:to-teal-900/30 rounded-full filter blur-2xl opacity-30 animate-blob animation-delay-4000"></div>
        </div>

        <!-- Mobile Sidebar Toggle -->
        <button class="lg:hidden fixed top-4 right-4 z-50 p-3 rounded-xl bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-700 dark:to-slate-800 shadow-lg hover:shadow-xl transition-all duration-300" onclick="toggleSidebar()">
            <i class="fas fa-bars text-gray-600 dark:text-gray-300"></i>
        </button>

        <!-- Sidebar -->
        <aside class="w-64 glass-sidebar shadow-2xl fixed h-full z-20 hidden lg:block animate-slide-in" style="display: flex; flex-direction: column; height: 100vh;" id="sidebar">
            <div class="p-6 border-b border-white/20 dark:border-gray-700/50 flex-shrink-0">
                <div class="flex items-center justify-center">
                    <div class="text-center">
                        <div class="w-16 h-16 mx-auto mb-3 gradient-primary rounded-2xl flex items-center justify-center shadow-lg glow-effect">
                            <i class="fas fa-building text-2xl text-white"></i>
                        </div>
                        <h1 class="text-white text-xl font-bold tracking-wide">Hire Me</h1>
                        <p class="text-xs text-gray-400 mt-1">لوحة صاحب العمل</p>
                    </div>
                </div>
            </div>

            <!-- الإحصائيات السريعة -->
            <div class="px-6 py-4 border-b border-white/10 dark:border-gray-700/30">
                <div class="grid grid-cols-2 gap-3">
                    <div class="text-center p-3 rounded-xl bg-gradient-to-br from-sky-500/20 to-blue-600/20 border border-sky-500/30">
                        @php
                            $employer = auth()->user()->employer ?? null;
                            $activeJobsCount = $employer ? $employer->jobs()->where('is_active', true)->count() : 0;
                        @endphp
                        <div class="text-lg font-bold text-sky-400">{{ $activeJobsCount }}</div>
                        <div class="text-xs text-gray-400">الوظائف</div>
                    </div>
                    <div class="text-center p-3 rounded-xl bg-gradient-to-br from-emerald-500/20 to-green-600/20 border border-emerald-500/30">
                        @php
                            $totalApplicationsCount = $employer ? \App\Models\Application::whereHas('job', function($q) use ($employer) {
                                $q->where('employer_id', $employer->id);
                            })->count() : 0;
                        @endphp
                        <div class="text-lg font-bold text-emerald-400">{{ $totalApplicationsCount }}</div>
                        <div class="text-xs text-gray-400">الطلبات</div>
                    </div>
                </div>
            </div>

            <!-- القائمة الرئيسية -->
            <div class="flex-1 overflow-y-auto">
                <div class="px-6 mb-4 mt-6">
                    <h3 class="text-xs text-gray-500 dark:text-gray-400 uppercase font-semibold tracking-wider flex items-center gap-2">
                        <div class="w-2 h-2 bg-sky-500 rounded-full"></div>
                        القائمة الرئيسية
                    </h3>
                </div>
                <nav class="space-y-1 px-3">
                    <a href="{{ route('employer.dashboard') }}" class="group flex items-center gap-3 px-3 py-3 rounded-xl {{ request()->routeIs('employer.dashboard') ? 'active-nav-item' : 'nav-item-hover text-gray-700 dark:text-gray-300' }}">
                        <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-sky-400 to-sky-600 flex items-center justify-center text-white shadow-lg group-hover:shadow-sky-500/25 transition-all duration-300">
                            <i class="fas fa-tachometer-alt text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <div class="font-medium">لوحة التحكم</div>
                            <div class="text-xs opacity-75">الصفحة الرئيسية</div>
                        </div>
                    </a>

                    <a href="{{ route('employer.jobs.index') }}" class="group flex items-center gap-3 px-3 py-3 rounded-xl {{ request()->routeIs('employer.jobs.*') ? 'active-nav-item' : 'nav-item-hover text-gray-700 dark:text-gray-300' }}">
                        <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-fuchsia-400 to-fuchsia-600 flex items-center justify-center text-white shadow-lg group-hover:shadow-fuchsia-500/25 transition-all duration-300">
                            <i class="fas fa-briefcase text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <div class="font-medium">إدارة الوظائف</div>
                            <div class="text-xs opacity-75">نشر وإدارة الوظائف</div>
                        </div>
                    </a>

                    <a href="{{ route('employer.applications.index') }}" class="group flex items-center gap-3 px-3 py-3 rounded-xl {{ request()->routeIs('employer.applications.*') ? 'active-nav-item' : 'nav-item-hover text-gray-700 dark:text-gray-300' }}">
                        <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-amber-400 to-amber-600 flex items-center justify-center text-white shadow-lg group-hover:shadow-amber-500/25 transition-all duration-300">
                            <i class="fas fa-file-alt text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <div class="font-medium">طلبات التوظيف</div>
                            <div class="text-xs opacity-75">مراجعة الطلبات</div>
                        </div>
                        @php
                            $pendingApplicationsCount = \App\Models\Application::whereHas('job', function($query) {
                                $query->whereHas('employer', function($q) {
                                    $q->where('user_id', auth()->id());
                                });
                            })->where('status', 'pending')->count();
                        @endphp
                        @if($pendingApplicationsCount > 0)
                            <div class="w-6 h-6 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center text-white text-xs font-bold shadow-lg">
                                {{ $pendingApplicationsCount > 9 ? '9+' : $pendingApplicationsCount }}
                            </div>
                        @endif
                    </a>

                    <a href="{{ route('employer.reports') }}" class="group flex items-center gap-3 px-3 py-3 rounded-xl {{ request()->routeIs('employer.reports') ? 'active-nav-item' : 'nav-item-hover text-gray-700 dark:text-gray-300' }}">
                        <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-emerald-400 to-emerald-600 flex items-center justify-center text-white shadow-lg group-hover:shadow-emerald-500/25 transition-all duration-300">
                            <i class="fas fa-chart-line text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <div class="font-medium">لوحة التقارير</div>
                            <div class="text-xs opacity-75">تقارير وتحليلات مفصلة</div>
                        </div>
                    </a>

                    <a href="{{ route('employer.candidates.index') }}" class="group flex items-center gap-3 px-3 py-3 rounded-xl {{ request()->routeIs('employer.candidates.*') ? 'active-nav-item' : 'nav-item-hover text-gray-700 dark:text-gray-300' }}">
                        <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-teal-400 to-teal-600 flex items-center justify-center text-white shadow-lg group-hover:shadow-teal-500/25 transition-all duration-300">
                            <i class="fas fa-user-tie text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <div class="font-medium">المرشحون</div>
                            <div class="text-xs opacity-75">المحفوظون والمفضلون</div>
                        </div>
                    </a>

                    <a href="{{ route('employer.notifications.index') }}" class="group flex items-center gap-3 px-3 py-3 rounded-xl {{ request()->routeIs('employer.notifications.*') ? 'active-nav-item' : 'nav-item-hover text-gray-700 dark:text-gray-300' }}">
                        <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-orange-400 to-orange-600 flex items-center justify-center text-white shadow-lg group-hover:shadow-orange-500/25 transition-all duration-300">
                            <i class="fas fa-bell text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <div class="font-medium">الإشعارات</div>
                            <div class="text-xs opacity-75">التنبيهات والرسائل</div>
                        </div>
                        @php
                            $notificationService = app(App\Services\NotificationService::class);
                            $unreadCount = $notificationService->getUnreadCount(auth()->user());
                        @endphp
                        @if($unreadCount > 0)
                            <div class="w-6 h-6 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center text-white text-xs font-bold shadow-lg animate-pulse">
                                {{ $unreadCount > 9 ? '9+' : $unreadCount }}
                            </div>
                        @endif
                    </a>

                <div class="px-6 mb-4 mt-8">
                    <h3 class="text-xs text-gray-500 dark:text-gray-400 uppercase font-semibold tracking-wider flex items-center gap-2">
                        <div class="w-2 h-2 bg-fuchsia-500 rounded-full"></div>
                        الخدمات المتقدمة
                    </h3>
                </div>
                <nav class="space-y-1 px-3">
                    <a href="{{ route('employer.packages') }}" class="group flex items-center gap-3 px-3 py-3 rounded-xl {{ request()->routeIs('employer.packages.*') ? 'active-nav-item' : 'nav-item-hover text-gray-700 dark:text-gray-300' }}">
                        <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-yellow-400 to-amber-600 flex items-center justify-center text-white shadow-lg group-hover:shadow-yellow-500/25 transition-all duration-300">
                            <i class="fas fa-crown text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <div class="font-medium">الباقات</div>
                            <div class="text-xs opacity-75">الاشتراكات والدفع</div>
                        </div>
                    </a>

                    <a href="{{ route('employer.reviews.index') }}" class="group flex items-center gap-3 px-3 py-3 rounded-xl {{ request()->routeIs('employer.reviews.*') ? 'active-nav-item' : 'nav-item-hover text-gray-700 dark:text-gray-300' }}">
                        <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-rose-400 to-pink-600 flex items-center justify-center text-white shadow-lg group-hover:shadow-rose-500/25 transition-all duration-300">
                            <i class="fas fa-star text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <div class="font-medium">التقييمات</div>
                            <div class="text-xs opacity-75">المراجعات والتقييمات</div>
                        </div>
                    </a>
                </nav>

                <div class="border-t border-white/10 dark:border-gray-700/30 my-6"></div>

                <!-- إدارة النظام -->
                <div class="px-4 py-2">
                    <h3 class="text-xs font-semibold text-gray-400 uppercase tracking-wider">إدارة النظام</h3>
                </div>

                <a href="{{ route('notifications.settings') }}" class="nav-link flex items-center px-4 py-3 text-gray-300 rounded-lg hover:bg-slate-700 transition-colors {{ request()->routeIs('notifications.settings') ? 'active' : '' }}">
                    <i class="fas fa-bell-slash w-5 h-5 ml-3 text-purple-400"></i>
                    إعدادات الإشعارات
                </a>

                <nav class="space-y-1 px-3">
                    <a href="{{ route('employer.profile') }}" class="group flex items-center gap-3 px-3 py-3 rounded-xl {{ request()->routeIs('employer.profile') ? 'active-nav-item' : 'nav-item-hover text-gray-700 dark:text-gray-300' }}">
                        <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-violet-400 to-violet-600 flex items-center justify-center text-white shadow-lg group-hover:shadow-violet-500/25 transition-all duration-300">
                            <i class="fas fa-user-circle text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <div class="font-medium">الملف الشخصي</div>
                            <div class="text-xs opacity-75">معلومات الحساب</div>
                        </div>
                    </a>

                    <a href="{{ route('employer.settings') }}" class="group flex items-center gap-3 px-3 py-3 rounded-xl {{ request()->routeIs('employer.settings') ? 'active-nav-item' : 'nav-item-hover text-gray-700 dark:text-gray-300' }}">
                        <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-slate-400 to-slate-600 flex items-center justify-center text-white shadow-lg group-hover:shadow-slate-500/25 transition-all duration-300">
                            <i class="fas fa-cog text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <div class="font-medium">الإعدادات</div>
                            <div class="text-xs opacity-75">تخصيص النظام</div>
                        </div>
                    </a>
                </nav>
            </div>

            <!-- معلومات المستخدم -->
            <div class="p-6 border-t border-white/10 dark:border-gray-700/30 mt-auto">
                <div class="flex items-center gap-3 p-3 rounded-xl bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-white/10">
                    <div class="w-10 h-10 bg-gradient-to-br from-sky-500 to-fuchsia-600 rounded-xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-user text-white text-sm"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-white truncate">{{ Auth::user()->name }}</p>
                        <p class="text-xs text-gray-400">صاحب عمل</p>
                    </div>
                    <form method="POST" action="{{ route('logout') }}" class="inline">
                        @csrf
                        <button type="submit" class="p-2 rounded-lg bg-gradient-to-br from-red-500/20 to-red-600/20 border border-red-500/30 text-red-400 hover:text-red-300 hover:bg-red-500/30 transition-all duration-300">
                            <i class="fas fa-sign-out-alt text-sm"></i>
                        </button>
                    </form>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 lg:mr-64 transition-all duration-500 relative z-10">
            <!-- Header -->
            <header class="employer-header sticky top-0 z-50 border-0 animate-fade-in">
                <div class="flex items-center justify-between px-8 py-6">
                    <div class="flex items-center gap-6">
                        <button id="darkModeToggle" class="relative p-3 rounded-xl bg-white dark:bg-gray-800 border-2 border-blue-500 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-300 shadow-lg hover:shadow-xl cursor-pointer" style="z-index: 9999; pointer-events: auto;" title="تبديل الوضع الليلي/النهاري">
                            <i class="fas fa-moon dark:hidden text-slate-600 transition-colors text-lg"></i>
                            <i class="fas fa-sun hidden dark:block text-yellow-400 transition-colors text-lg"></i>
                        </button>
                        <div>
                            <h1 class="text-2xl font-bold header-title">@yield('header_title', 'لوحة التحكم')</h1>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1 font-medium">مرحباً بك في نظام إدارة التوظيف</p>
                        </div>
                    </div>

                    <div class="flex items-center gap-3">
                        <!-- الإشعارات -->
                        @include('components.notification-dropdown')

                        <div class="w-px h-8 header-divider"></div>

                        <!-- الصفحة الرئيسية -->
                        <a href="/" class="header-btn group p-3 rounded-xl hover:shadow-lg transition-all duration-300" title="الصفحة الرئيسية">
                            <i class="fas fa-home text-emerald-600 dark:text-emerald-400 group-hover:scale-110 transition-transform"></i>
                        </a>
                        <div class="w-px h-8 header-divider"></div>

                        <!-- Quick Actions -->
                        <a href="{{ route('employer.jobs.create') }}" class="px-6 py-3 bg-gradient-to-r from-sky-600 to-fuchsia-600 text-white rounded-xl hover:from-sky-700 hover:to-fuchsia-700 transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl font-medium hover:scale-105">
                            <i class="fas fa-plus"></i>
                            نشر وظيفة
                        </a>

                        <div class="w-px h-8 header-divider"></div>

                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit" class="header-btn group p-3 rounded-xl hover:shadow-lg transition-all duration-300">
                                <i class="fas fa-sign-out-alt text-red-600 dark:text-red-400 group-hover:scale-110 transition-transform"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <div class="min-h-screen">
                @if(session('success'))
                    <div class="mx-8 mt-4 p-4 bg-gradient-to-r from-emerald-50 to-emerald-100 border border-emerald-200 text-emerald-800 rounded-xl shadow-lg animate-fade-in">
                        <div class="flex items-center gap-3">
                            <i class="fas fa-check-circle text-emerald-600"></i>
                            {{ session('success') }}
                        </div>
                    </div>
                @endif

                @if(session('error'))
                    <div class="mx-8 mt-4 p-4 bg-gradient-to-r from-red-50 to-red-100 border border-red-200 text-red-800 rounded-xl shadow-lg animate-fade-in">
                        <div class="flex items-center gap-3">
                            <i class="fas fa-exclamation-circle text-red-600"></i>
                            {{ session('error') }}
                        </div>
                    </div>
                @endif

                @yield('content')
            </div>
        </main>
    </div>

    <!-- Mobile Sidebar Overlay -->
    <div class="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden hidden" id="sidebarOverlay" onclick="toggleSidebar()"></div>

    <script>
        // نظام الثيم البسيط والمباشر
        console.log('🚀 Script loaded');

        function setupThemeToggle() {
            console.log('🔍 Looking for dark mode toggle...');
            const darkModeToggle = document.getElementById('darkModeToggle');

            if (darkModeToggle) {
                console.log('✅ Dark mode toggle found:', darkModeToggle);
                console.log('📍 Button position:', darkModeToggle.getBoundingClientRect());
                console.log('👆 Button clickable:', !darkModeToggle.disabled, 'visible:', darkModeToggle.offsetParent !== null);

                // إزالة أي معالجات سابقة
                darkModeToggle.replaceWith(darkModeToggle.cloneNode(true));
                const newToggle = document.getElementById('darkModeToggle');

                newToggle.addEventListener('click', function(e) {
                    console.log('🔄 Toggle clicked!');
                    e.preventDefault();
                    e.stopPropagation();

                    // تبديل الثيم
                    const isDarkBefore = document.documentElement.classList.contains('dark');
                    document.documentElement.classList.toggle('dark');
                    const isDarkAfter = document.documentElement.classList.contains('dark');

                    // حفظ الإعداد
                    const newTheme = isDarkAfter ? 'dark' : 'light';
                    localStorage.setItem('theme', newTheme);

                    console.log('🎨 Theme changed from', isDarkBefore ? 'dark' : 'light', 'to', newTheme);

                    // تأثير بصري
                    this.style.transform = 'scale(0.9)';
                    this.style.backgroundColor = isDarkAfter ? '#374151' : '#f3f4f6';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 100);
                });

                // اختبار النقر
                newToggle.addEventListener('mousedown', function() {
                    console.log('👇 Mouse down on toggle');
                });

                newToggle.addEventListener('mouseup', function() {
                    console.log('👆 Mouse up on toggle');
                });

            } else {
                console.log('❌ Dark mode toggle NOT found');
                console.log('🔍 Available elements with IDs:', Array.from(document.querySelectorAll('[id]')).map(el => el.id));
            }
        }

        // تشغيل فوري
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', setupThemeToggle);
        } else {
            setupThemeToggle();
        }

        // تشغيل إضافي بعد ثانية للتأكد
        setTimeout(setupThemeToggle, 1000);

        // معلومات تشخيصية إضافية
        setTimeout(() => {
            console.log('🔍 Final diagnostic:');
            console.log('- Document ready state:', document.readyState);
            console.log('- Dark mode toggle exists:', !!document.getElementById('darkModeToggle'));
            console.log('- Current theme class:', document.documentElement.classList.contains('dark') ? 'dark' : 'light');
            console.log('- Saved theme:', localStorage.getItem('theme'));

            const toggle = document.getElementById('darkModeToggle');
            if (toggle) {
                console.log('- Toggle visible:', toggle.offsetParent !== null);
                console.log('- Toggle disabled:', toggle.disabled);
                console.log('- Toggle style display:', getComputedStyle(toggle).display);
                console.log('- Toggle style pointer-events:', getComputedStyle(toggle).pointerEvents);
            }
        }, 2000);

        // Sidebar Toggle for Mobile
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');

            sidebar.classList.toggle('-translate-x-full');
            overlay.classList.toggle('hidden');
        }

        // تحسينات Alpine.js للإشعارات
        document.addEventListener('alpine:init', () => {
            console.log('Alpine.js initialized for employer dashboard');
        });

        // تحديث الإشعارات كل 30 ثانية
        setInterval(function() {
            fetch('/notifications/unread')
                .then(response => response.json())
                .then(data => {
                    // تحديث عداد الإشعارات في الهيدر
                    const notificationBadges = document.querySelectorAll('.notification-badge, [class*="bg-red-500"]');
                    notificationBadges.forEach(badge => {
                        if (badge) {
                            badge.textContent = data.count > 9 ? '9+' : data.count;
                            badge.style.display = data.count > 0 ? 'flex' : 'none';
                        }
                    });
                })
                .catch(error => console.error('Error fetching notifications:', error));
        }, 30000);
    </script>

    @stack('styles')
    @yield('scripts')
</body>
</html>
