@props([
    'review',
    'showActions' => true,
    'compact' => false,
    'showResponse' => true
])

@php
    $statusColors = [
        'approved' => 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
        'pending' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
        'rejected' => 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
    ];
    
    $statusLabels = [
        'approved' => 'معتمد',
        'pending' => 'في الانتظار',
        'rejected' => 'مرفوض'
    ];
@endphp

<div class="review-card {{ $compact ? 'compact' : '' }} p-{{ $compact ? '4' : '6' }} rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300 bg-white dark:bg-gray-800"
     data-review-id="{{ $review->id }}">
    
    <div class="flex items-start gap-{{ $compact ? '3' : '4' }}">
        <!-- Avatar -->
        <div class="flex-shrink-0">
            <div class="w-{{ $compact ? '10' : '12' }} h-{{ $compact ? '10' : '12' }} rounded-full bg-gradient-to-br from-blue-400 to-purple-600 flex items-center justify-center text-white font-bold text-{{ $compact ? 'base' : 'lg' }} shadow-lg">
                {{ substr($review->reviewer->name, 0, 1) }}
            </div>
        </div>

        <!-- Content -->
        <div class="flex-1 min-w-0">
            <!-- Header -->
            <div class="flex items-start justify-between mb-3">
                <div>
                    <h4 class="text-{{ $compact ? 'base' : 'lg' }} font-semibold text-gray-900 dark:text-white">
                        {{ $review->reviewer->name }}
                    </h4>
                    <p class="text-{{ $compact ? 'xs' : 'sm' }} text-gray-500 dark:text-gray-400">
                        {{ $review->reviewer->email }}
                    </p>
                </div>
                
                <div class="flex items-center gap-3">
                    <!-- Status Badge -->
                    <span class="inline-flex items-center px-{{ $compact ? '2' : '3' }} py-1 rounded-full text-xs font-medium {{ $statusColors[$review->status] ?? $statusColors['pending'] }}">
                        {{ $statusLabels[$review->status] ?? $review->status }}
                    </span>
                    
                    <!-- Actions -->
                    @if($showActions)
                        <div class="flex items-center gap-2">
                            <a href="{{ route('employer.reviews.show', $review) }}"
                               class="inline-flex items-center justify-center w-{{ $compact ? '7' : '8' }} h-{{ $compact ? '7' : '8' }} rounded-lg bg-blue-100 hover:bg-blue-200 text-blue-600 transition-colors duration-200"
                               title="عرض التفاصيل">
                                <i class="fas fa-eye text-{{ $compact ? 'xs' : 'sm' }}"></i>
                            </a>
                            @if($review->status === 'approved' && !$review->employer_response)
                                <button type="button"
                                        class="inline-flex items-center justify-center w-{{ $compact ? '7' : '8' }} h-{{ $compact ? '7' : '8' }} rounded-lg bg-green-100 hover:bg-green-200 text-green-600 transition-colors duration-200"
                                        onclick="openResponseModal({{ $review->id }})"
                                        title="الرد على التقييم">
                                    <i class="fas fa-reply text-{{ $compact ? 'xs' : 'sm' }}"></i>
                                </button>
                            @endif
                        </div>
                    @endif
                </div>
            </div>

            <!-- Rating -->
            <div class="flex items-center gap-3 mb-3">
                <div class="flex items-center">
                    @for($i = 1; $i <= 5; $i++)
                        <i class="fas fa-star text-{{ $compact ? 'base' : 'lg' }} {{ $i <= $review->rating ? 'text-yellow-400' : 'text-gray-300' }}"></i>
                    @endfor
                </div>
                <span class="text-{{ $compact ? 'base' : 'lg' }} font-semibold text-gray-900 dark:text-white">
                    {{ $review->rating }}/5
                </span>
            </div>

            <!-- Comment -->
            @if($review->comment)
                <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-{{ $compact ? '3' : '4' }} mb-3">
                    <p class="text-gray-700 dark:text-gray-300 leading-relaxed {{ $compact ? 'text-sm' : '' }}">
                        {{ $compact ? Str::limit($review->comment, 100) : $review->comment }}
                    </p>
                </div>
            @endif

            <!-- Response -->
            @if($showResponse && $review->employer_response)
                <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-{{ $compact ? '3' : '4' }} border-r-4 border-blue-400 mb-3">
                    <div class="flex items-center gap-2 mb-2">
                        <i class="fas fa-reply text-blue-600 dark:text-blue-400"></i>
                        <span class="text-{{ $compact ? 'xs' : 'sm' }} font-medium text-blue-600 dark:text-blue-400">رد الشركة:</span>
                    </div>
                    <p class="text-gray-700 dark:text-gray-300 {{ $compact ? 'text-sm' : '' }}">
                        {{ $compact ? Str::limit($review->employer_response, 80) : $review->employer_response }}
                    </p>
                </div>
            @endif

            <!-- Date -->
            <div class="flex items-center gap-4 text-{{ $compact ? 'xs' : 'sm' }} text-gray-500 dark:text-gray-400">
                <span class="flex items-center gap-1">
                    <i class="fas fa-calendar text-xs"></i>
                    {{ $review->created_at->format('Y-m-d') }}
                </span>
                <span class="flex items-center gap-1">
                    <i class="fas fa-clock text-xs"></i>
                    {{ $review->created_at->format('H:i') }}
                </span>
                @if(!$compact)
                    <span class="text-gray-400">
                        {{ $review->created_at->diffForHumans() }}
                    </span>
                @endif
            </div>
        </div>
    </div>
</div>

<style>
.review-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.review-card:hover {
    transform: translateY(-1px);
}

.review-card.compact {
    padding: 1rem;
}

@media (max-width: 768px) {
    .review-card:hover {
        transform: none;
    }
    
    .review-card {
        padding: 1rem;
    }
    
    .review-card .flex {
        flex-direction: column;
        gap: 0.75rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add click handler for review cards (if needed)
    document.querySelectorAll('.review-card').forEach(card => {
        // Add any specific interactions here
    });
});
</script>
