<!-- How It Works Section -->
<section class="py-16 bg-gray-50 dark:bg-gray-900">
    <div class="p-6">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-cogs text-primary mr-3"></i>
                    كيف يعمل Hire Me؟
                </h2>
                <p class="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                    منصة Hire Me توفر تجربة سلسة لكل من الباحثين عن عمل وأصحاب العمل ببضع خطوات بسيطة
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 relative">
                <!-- Connection Lines -->
                <div class="hidden md:block absolute top-1/2 left-1/4 right-1/4 h-0.5 bg-gradient-to-r from-primary to-secondary transform -translate-y-1/2 z-0"></div>

                <!-- Step 1 -->
                <div class="text-center relative z-10">
                    <div class="bg-white dark:bg-dark-card rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 p-8 border border-gray-100 dark:border-gray-700 hover:scale-105">
                        <div class="w-24 h-24 gradient-bg rounded-full flex items-center justify-center mx-auto mb-6 relative shadow-lg">
                            <span class="text-white text-3xl font-bold">1</span>
                            <div class="absolute -inset-2 rounded-full border-2 border-primary/20 animate-pulse"></div>
                        </div>

                        <h3 class="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
                            <a href="{{ route('register') }}" class="hover:text-primary transition-colors duration-300">
                                <i class="fas fa-user-plus mr-2"></i>
                                إنشاء حساب
                            </a>
                        </h3>
                        <p class="text-gray-600 dark:text-gray-400 leading-relaxed">
                            سجّل كباحث عن عمل أو صاحب عمل وأنشئ ملفك الشخصي المكتمل
                        </p>
                    </div>
                </div>

                <!-- Step 2 -->
                <div class="text-center relative z-10">
                    <div class="bg-white dark:bg-dark-card rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 p-8 border border-gray-100 dark:border-gray-700 hover:scale-105">
                        <div class="w-24 h-24 gradient-bg rounded-full flex items-center justify-center mx-auto mb-6 relative shadow-lg">
                            <span class="text-white text-3xl font-bold">2</span>
                            <div class="absolute -inset-2 rounded-full border-2 border-primary/20 animate-pulse" style="animation-delay: 0.5s"></div>
                        </div>

                        <h3 class="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
                            <a href="{{ route('jobs.index') }}" class="hover:text-primary transition-colors duration-300">
                                <i class="fas fa-search mr-2"></i>
                                بحث أو نشر
                            </a>
                        </h3>
                        <p class="text-gray-600 dark:text-gray-400 leading-relaxed">
                            ابحث عن وظائف مناسبة كباحث عن عمل، أو انشر وظائف لشركتك كصاحب عمل
                        </p>
                    </div>
                </div>

                <!-- Step 3 -->
                <div class="text-center relative z-10">
                    <div class="bg-white dark:bg-dark-card rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 p-8 border border-gray-100 dark:border-gray-700 hover:scale-105">
                        <div class="w-24 h-24 gradient-bg rounded-full flex items-center justify-center mx-auto mb-6 relative shadow-lg">
                            <span class="text-white text-3xl font-bold">3</span>
                            <div class="absolute -inset-2 rounded-full border-2 border-primary/20 animate-pulse" style="animation-delay: 1s"></div>
                        </div>

                        <h3 class="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
                            <a href="{{ auth()->check() ? (auth()->user()->isEmployer() ? route('employer.applications.index') : route('job-seeker.applications')) : route('login') }}" class="hover:text-primary transition-colors duration-300">
                                <i class="fas fa-handshake mr-2"></i>
                                تقديم أو توظيف
                            </a>
                        </h3>
                        <p class="text-gray-600 dark:text-gray-400 leading-relaxed">
                            قدّم طلباتك للوظائف المناسبة أو راجع المتقدمين واختر أفضل المواهب لشركتك
                        </p>
                    </div>
                </div>
            </div>

            <!-- Call to Action -->
            <div class="text-center mt-16">
                <div class="bg-white dark:bg-dark-card rounded-2xl shadow-lg p-8 border border-gray-100 dark:border-gray-700">
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                        جاهز للبدء؟
                    </h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-6">
                        انضم إلى آلاف المستخدمين الذين وجدوا فرصهم المثالية معنا
                    </p>
                    <div class="flex flex-col sm:flex-row justify-center gap-4">
                        <a href="{{ route('register') }}" class="gradient-bg text-white px-8 py-4 rounded-xl hover:opacity-90 hover:scale-105 transition-all duration-300 font-semibold shadow-lg">
                            <i class="fas fa-rocket mr-2"></i>
                            ابدأ الآن مجاناً
                        </a>
                        <a href="{{ route('jobs.index') }}" class="bg-white dark:bg-dark-card text-primary border-2 border-primary px-8 py-4 rounded-xl hover:bg-primary hover:text-white transition-all duration-300 font-semibold shadow-lg">
                            <i class="fas fa-eye mr-2"></i>
                            تصفح الوظائف
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
