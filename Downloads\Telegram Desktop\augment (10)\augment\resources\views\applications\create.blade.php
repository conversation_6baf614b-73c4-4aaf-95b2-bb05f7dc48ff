@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-12">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-6">
            <h1 class="text-2xl font-bold mb-6">تقديم طلب للوظيفة</h1>
            
            <!-- معلومات الوظيفة -->
            <div class="mb-8 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="flex items-start gap-4">
                    <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-briefcase text-primary"></i>
                    </div>
                    <div>
                        <h2 class="text-xl font-bold">{{ $job->title }}</h2>
                        <div class="flex flex-wrap items-center gap-3 mt-1 text-gray-600 dark:text-gray-400 text-sm">
                            @if($job->employer)
                                <span class="flex items-center gap-1">
                                    <i class="fas fa-building"></i>
                                    {{ $job->employer->company_name }}
                                </span>
                            @endif
                            
                            <span class="flex items-center gap-1">
                                <i class="fas fa-map-marker-alt"></i>
                                {{ $job->location }}
                            </span>
                            
                            <span class="flex items-center gap-1">
                                <i class="fas fa-clock"></i>
                                @if($job->job_type == 'full_time')
                                    دوام كامل
                                @elseif($job->job_type == 'part_time')
                                    دوام جزئي
                                @elseif($job->job_type == 'remote')
                                    عن بعد
                                @elseif($job->job_type == 'freelance')
                                    عمل حر
                                @endif
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- نموذج التقديم -->
            <form action="{{ route('applications.store', $job) }}" method="POST" enctype="multipart/form-data">
                @csrf
                
                @if(session('error'))
                    <div class="mb-4 p-4 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 rounded-lg">
                        {{ session('error') }}
                    </div>
                @endif
                
                <div class="space-y-6">
                    <!-- خطاب التقديم -->
                    <div>
                        <label for="cover_letter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">خطاب التقديم</label>
                        <textarea id="cover_letter" name="cover_letter" rows="6" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" placeholder="اكتب هنا لماذا أنت مناسب لهذه الوظيفة وما هي خبراتك ومهاراتك ذات الصلة...">{{ old('cover_letter') }}</textarea>
                        @error('cover_letter')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <!-- السيرة الذاتية -->
                    <div>
                        <label for="resume" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">السيرة الذاتية (اختياري)</label>
                        <div class="flex items-center justify-center w-full">
                            <label for="resume" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-800 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500">
                                <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                    <i class="fas fa-file-upload text-2xl text-gray-400 mb-2"></i>
                                    <p class="mb-2 text-sm text-gray-500 dark:text-gray-400"><span class="font-semibold">اضغط للتحميل</span> أو اسحب وأفلت</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">PDF, DOC, DOCX (الحد الأقصى 2 ميجابايت)</p>
                                </div>
                                <input id="resume" name="resume" type="file" class="hidden" accept=".pdf,.doc,.docx" />
                            </label>
                        </div>
                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                            إذا لم تقم بتحميل سيرة ذاتية، سيتم استخدام السيرة الذاتية المرفقة بملفك الشخصي.
                        </p>
                        @error('resume')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <!-- زر التقديم -->
                    <div class="flex justify-end">
                        <button type="submit" class="px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark transition">
                            تقديم الطلب
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // عرض اسم الملف المحدد
    document.getElementById('resume').addEventListener('change', function(e) {
        const fileName = e.target.files[0]?.name;
        if (fileName) {
            const fileInfoElement = document.createElement('p');
            fileInfoElement.classList.add('mt-2', 'text-sm', 'text-green-600', 'dark:text-green-400');
            fileInfoElement.textContent = `تم اختيار: ${fileName}`;
            
            // إزالة أي رسائل سابقة
            const previousInfo = this.parentElement.parentElement.nextElementSibling.nextElementSibling;
            if (previousInfo && previousInfo.classList.contains('text-green-600')) {
                previousInfo.remove();
            }
            
            this.parentElement.parentElement.insertAdjacentElement('afterend', fileInfoElement);
        }
    });
</script>
@endsection
