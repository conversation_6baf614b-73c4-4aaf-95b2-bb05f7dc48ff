@extends('layouts.admin')

@section('title', 'التقارير المجدولة - Hire Me')
@section('header_title', 'التقارير المجدولة')

@section('content')
<div class="p-6">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
            <h2 class="text-2xl font-bold">التقارير المجدولة</h2>
            <p class="text-gray-600 dark:text-gray-400 mt-1">إدارة التقارير المجدولة للإرسال بالبريد الإلكتروني</p>
        </div>
        <div class="mt-4 md:mt-0">
            <button id="createScheduledReportBtn" class="px-4 py-2 gradient-bg text-white rounded-lg hover:opacity-90 transition-colors flex items-center gap-2">
                <i class="fas fa-plus"></i>
                <span>إنشاء تقرير مجدول</span>
            </button>
        </div>
    </div>

    <!-- Scheduled Reports List -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-bold">قائمة التقارير المجدولة</h3>
        </div>
        
        @if($scheduledReports->isEmpty())
            <div class="p-6 text-center">
                <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                    <i class="fas fa-calendar-alt text-gray-500 dark:text-gray-400 text-xl"></i>
                </div>
                <h4 class="text-lg font-medium mb-2">لا توجد تقارير مجدولة</h4>
                <p class="text-gray-500 dark:text-gray-400 mb-4">لم تقم بإنشاء أي تقارير مجدولة بعد.</p>
                <button id="createFirstReportBtn" class="px-4 py-2 gradient-bg text-white rounded-lg hover:opacity-90 transition-colors">
                    إنشاء تقرير مجدول
                </button>
            </div>
        @else
            <div class="overflow-x-auto">
                <table class="w-full text-right text-sm">
                    <thead class="bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-300">
                        <tr>
                            <th class="px-6 py-3">اسم التقرير</th>
                            <th class="px-6 py-3">نوع التقرير</th>
                            <th class="px-6 py-3">البريد الإلكتروني</th>
                            <th class="px-6 py-3">التكرار</th>
                            <th class="px-6 py-3">آخر إرسال</th>
                            <th class="px-6 py-3">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                        @foreach($scheduledReports as $report)
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                                <td class="px-6 py-4 font-medium">{{ $report->name }}</td>
                                <td class="px-6 py-4">
                                    @if($report->report_type == 'jobs')
                                        <span class="px-2.5 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 rounded-full text-xs">تقرير الوظائف</span>
                                    @elseif($report->report_type == 'applications')
                                        <span class="px-2.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">تقرير الطلبات</span>
                                    @elseif($report->report_type == 'financial')
                                        <span class="px-2.5 py-0.5 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 rounded-full text-xs">التقرير المالي</span>
                                    @endif
                                </td>
                                <td class="px-6 py-4">{{ $report->email }}</td>
                                <td class="px-6 py-4">
                                    @if($report->frequency == 'daily')
                                        <span class="px-2.5 py-0.5 bg-indigo-100 dark:bg-indigo-900/30 text-indigo-700 dark:text-indigo-400 rounded-full text-xs">يومي</span>
                                    @elseif($report->frequency == 'weekly')
                                        <span class="px-2.5 py-0.5 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400 rounded-full text-xs">
                                            أسبوعي ({{ ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'][$report->day_of_week] }})
                                        </span>
                                    @elseif($report->frequency == 'monthly')
                                        <span class="px-2.5 py-0.5 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 rounded-full text-xs">
                                            شهري (اليوم {{ $report->day_of_month }})
                                        </span>
                                    @endif
                                </td>
                                <td class="px-6 py-4">
                                    @if($report->last_sent_at)
                                        {{ $report->last_sent_at->format('d/m/Y H:i') }}
                                    @else
                                        <span class="text-gray-500 dark:text-gray-400">لم يتم الإرسال بعد</span>
                                    @endif
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center gap-2">
                                        <button class="edit-report-btn text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300" data-id="{{ $report->id }}">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="delete-report-btn text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300" data-id="{{ $report->id }}">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @endif
    </div>
</div>

<!-- Create/Edit Scheduled Report Modal -->
<div id="scheduledReportModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
            <h3 class="text-lg font-bold" id="modalTitle">إنشاء تقرير مجدول</h3>
            <button id="closeModalBtn" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <form id="scheduledReportForm" class="p-6">
            <input type="hidden" id="reportId" name="id">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                    <label for="reportName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">اسم التقرير</label>
                    <input type="text" id="reportName" name="name" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" required>
                </div>
                <div>
                    <label for="reportEmail" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">البريد الإلكتروني</label>
                    <input type="email" id="reportEmail" name="email" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" required>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                    <label for="reportType" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">نوع التقرير</label>
                    <select id="reportType" name="report_type" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" required>
                        <option value="">اختر نوع التقرير</option>
                        <option value="jobs">تقرير الوظائف</option>
                        <option value="applications">تقرير الطلبات</option>
                        <option value="financial">التقرير المالي</option>
                    </select>
                </div>
                <div>
                    <label for="reportFrequency" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">تكرار الإرسال</label>
                    <select id="reportFrequency" name="frequency" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" required>
                        <option value="">اختر تكرار الإرسال</option>
                        <option value="daily">يومي</option>
                        <option value="weekly">أسبوعي</option>
                        <option value="monthly">شهري</option>
                    </select>
                </div>
            </div>
            
            <div id="weeklyOptions" class="mb-6 hidden">
                <label for="dayOfWeek" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">يوم الإرسال</label>
                <select id="dayOfWeek" name="day_of_week" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                    <option value="0">الأحد</option>
                    <option value="1">الإثنين</option>
                    <option value="2">الثلاثاء</option>
                    <option value="3">الأربعاء</option>
                    <option value="4">الخميس</option>
                    <option value="5">الجمعة</option>
                    <option value="6">السبت</option>
                </select>
            </div>
            
            <div id="monthlyOptions" class="mb-6 hidden">
                <label for="dayOfMonth" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">يوم الشهر</label>
                <select id="dayOfMonth" name="day_of_month" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base">
                    @for($i = 1; $i <= 31; $i++)
                        <option value="{{ $i }}">{{ $i }}</option>
                    @endfor
                </select>
            </div>
            
            <div class="mb-6">
                <div class="flex items-center">
                    <input type="checkbox" id="includeAttachment" name="include_attachment" class="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary dark:focus:ring-primary dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600" checked>
                    <label for="includeAttachment" class="mr-2 text-sm font-medium text-gray-700 dark:text-gray-300">إرفاق ملف التقرير (PDF)</label>
                </div>
            </div>
            
            <div class="flex justify-end">
                <button type="button" id="cancelBtn" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors ml-2">
                    إلغاء
                </button>
                <button type="submit" class="px-4 py-2 gradient-bg text-white rounded-lg hover:opacity-90 transition-colors">
                    حفظ
                </button>
            </div>
        </form>
    </div>
</div>

@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const modal = document.getElementById('scheduledReportModal');
        const form = document.getElementById('scheduledReportForm');
        const createBtn = document.getElementById('createScheduledReportBtn');
        const createFirstBtn = document.getElementById('createFirstReportBtn');
        const closeBtn = document.getElementById('closeModalBtn');
        const cancelBtn = document.getElementById('cancelBtn');
        const modalTitle = document.getElementById('modalTitle');
        const reportFrequency = document.getElementById('reportFrequency');
        const weeklyOptions = document.getElementById('weeklyOptions');
        const monthlyOptions = document.getElementById('monthlyOptions');
        
        // Show/hide frequency options
        reportFrequency.addEventListener('change', function() {
            weeklyOptions.classList.add('hidden');
            monthlyOptions.classList.add('hidden');
            
            if (this.value === 'weekly') {
                weeklyOptions.classList.remove('hidden');
            } else if (this.value === 'monthly') {
                monthlyOptions.classList.remove('hidden');
            }
        });
        
        // Open modal for creating new report
        function openCreateModal() {
            form.reset();
            document.getElementById('reportId').value = '';
            modalTitle.textContent = 'إنشاء تقرير مجدول';
            modal.classList.remove('hidden');
        }
        
        // Open modal for editing report
        function openEditModal(reportId) {
            form.reset();
            document.getElementById('reportId').value = reportId;
            modalTitle.textContent = 'تعديل تقرير مجدول';
            
            // Fetch report data and populate form
            fetch(`/admin/reports/scheduled/${reportId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const report = data.report;
                        document.getElementById('reportName').value = report.name;
                        document.getElementById('reportEmail').value = report.email;
                        document.getElementById('reportType').value = report.report_type;
                        document.getElementById('reportFrequency').value = report.frequency;
                        
                        if (report.frequency === 'weekly') {
                            weeklyOptions.classList.remove('hidden');
                            document.getElementById('dayOfWeek').value = report.day_of_week;
                        } else if (report.frequency === 'monthly') {
                            monthlyOptions.classList.remove('hidden');
                            document.getElementById('dayOfMonth').value = report.day_of_month;
                        }
                        
                        document.getElementById('includeAttachment').checked = report.include_attachment;
                        
                        modal.classList.remove('hidden');
                    } else {
                        alert('حدث خطأ أثناء تحميل بيانات التقرير');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء تحميل بيانات التقرير');
                });
        }
        
        // Close modal
        function closeModal() {
            modal.classList.add('hidden');
        }
        
        // Submit form
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(form);
            const reportId = document.getElementById('reportId').value;
            
            let url = '{{ route("admin.reports.schedule") }}';
            let method = 'POST';
            
            if (reportId) {
                url = `/admin/reports/scheduled/${reportId}`;
                method = 'PUT';
            }
            
            fetch(url, {
                method: method,
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(Object.fromEntries(formData))
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    closeModal();
                    window.location.reload();
                } else {
                    alert(data.message || 'حدث خطأ أثناء حفظ التقرير');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء حفظ التقرير');
            });
        });
        
        // Delete report
        document.querySelectorAll('.delete-report-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                if (confirm('هل أنت متأكد من حذف هذا التقرير المجدول؟')) {
                    const reportId = this.dataset.id;
                    
                    fetch(`/admin/reports/scheduled/${reportId}`, {
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': '{{ csrf_token() }}',
                            'Accept': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        } else {
                            alert(data.message || 'حدث خطأ أثناء حذف التقرير');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('حدث خطأ أثناء حذف التقرير');
                    });
                }
            });
        });
        
        // Edit report
        document.querySelectorAll('.edit-report-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                openEditModal(this.dataset.id);
            });
        });
        
        // Event listeners
        if (createBtn) createBtn.addEventListener('click', openCreateModal);
        if (createFirstBtn) createFirstBtn.addEventListener('click', openCreateModal);
        closeBtn.addEventListener('click', closeModal);
        cancelBtn.addEventListener('click', closeModal);
    });
</script>
@endsection
