@extends('layouts.app')

@section('title', 'تعديل ملف الشركة')

@section('content')
<div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div class="p-6 bg-white border-b border-gray-200">
            <div class="mb-6">
                <h2 class="text-2xl font-bold text-gray-800">تعديل ملف الشركة</h2>
                <p class="text-gray-600 mt-1">قم بتحديث معلومات شركتك</p>
            </div>

            <form action="{{ route('employer.profile.update') }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="company_name" class="block text-sm font-medium text-gray-700 mb-1">اسم الشركة</label>
                        <input type="text" name="company_name" id="company_name" value="{{ old('company_name', $employer->company_name) }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        @error('company_name')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                        @enderror
                    </div>

                    <div>
                        <label for="website" class="block text-sm font-medium text-gray-700 mb-1">الموقع الإلكتروني</label>
                        <input type="url" name="website" id="website" value="{{ old('website', $employer->website) }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        @error('website')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                        @enderror
                    </div>

                    <div>
                        <label for="industry" class="block text-sm font-medium text-gray-700 mb-1">الصناعة</label>
                        <input type="text" name="industry" id="industry" value="{{ old('industry', $employer->industry) }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        @error('industry')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                        @enderror
                    </div>

                    <div>
                        <label for="location" class="block text-sm font-medium text-gray-700 mb-1">الموقع</label>
                        <input type="text" name="location" id="location" value="{{ old('location', $employer->location) }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        @error('location')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                        @enderror
                    </div>

                    <div>
                        <label for="company_size" class="block text-sm font-medium text-gray-700 mb-1">حجم الشركة (عدد الموظفين)</label>
                        <input type="number" name="company_size" id="company_size" value="{{ old('company_size', $employer->company_size) }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        @error('company_size')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                        @enderror
                    </div>

                    <div>
                        <label for="founded_year" class="block text-sm font-medium text-gray-700 mb-1">سنة التأسيس</label>
                        <input type="number" name="founded_year" id="founded_year" value="{{ old('founded_year', $employer->founded_year) }}" 
                               min="1900" max="{{ date('Y') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        @error('founded_year')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                        @enderror
                    </div>
                </div>

                <div class="mt-6">
                    <label for="company_logo" class="block text-sm font-medium text-gray-700 mb-1">شعار الشركة</label>
                    <input type="file" name="company_logo" id="company_logo" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <p class="text-xs text-gray-500 mt-1">الملفات المسموح بها: JPG, PNG, GIF. الحد الأقصى للحجم: 2MB</p>
                    @if($employer->company_logo)
                        <div class="mt-2 flex items-center">
                            <img src="{{ Storage::url($employer->company_logo) }}" alt="{{ $employer->company_name }}" class="h-16 w-16 object-cover rounded">
                            <span class="mr-2 text-sm text-gray-600">الشعار الحالي</span>
                        </div>
                    @endif
                    @error('company_logo')
                        <span class="text-red-500 text-xs">{{ $message }}</span>
                    @enderror
                </div>

                <div class="mt-6">
                    <label for="company_description" class="block text-sm font-medium text-gray-700 mb-1">وصف الشركة</label>
                    <textarea name="company_description" id="company_description" rows="5" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">{{ old('company_description', $employer->company_description) }}</textarea>
                    @error('company_description')
                        <span class="text-red-500 text-xs">{{ $message }}</span>
                    @enderror
                </div>

                <div class="mt-8 flex justify-end">
                    <a href="{{ route('employer.profile') }}" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ml-3">
                        إلغاء
                    </a>
                    <button type="submit" class="bg-indigo-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
