<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Employer;
use App\Models\JobSeeker;
use App\Models\Job;
use App\Models\Application;
use App\Models\Category;
use Illuminate\Support\Facades\Hash;

class CreateTestData extends Command
{
    protected $signature = 'test:create-data';
    protected $description = 'إنشاء بيانات تجريبية للاختبار';

    public function handle()
    {
        $this->info('بدء إنشاء البيانات التجريبية...');

        // إنشاء فئات الوظائف
        $categories = [
            'تقنية المعلومات',
            'التسويق',
            'المبيعات',
            'الموارد البشرية',
            'المحاسبة',
            'الهندسة',
            'الطب',
            'التعليم'
        ];

        foreach ($categories as $categoryName) {
            Category::firstOrCreate(['name' => $categoryName]);
        }

        // إنشاء مستخدم صاحب عمل
        $employerUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'شركة الاختبار',
                'password' => Hash::make('password'),
                'role' => 'employer',
                'email_verified_at' => now(),
            ]
        );

        // إنشاء ملف صاحب العمل
        $employer = Employer::firstOrCreate(
            ['user_id' => $employerUser->id],
            [
                'company_name' => 'شركة الاختبار للتقنية',
                'company_description' => 'شركة رائدة في مجال التقنية',
                'industry' => 'تقنية المعلومات',
                'location' => 'الرياض',
                'company_size' => '50-100',
                'founded_year' => 2020,
            ]
        );

        // إنشاء مستخدمين باحثين عن عمل
        for ($i = 1; $i <= 5; $i++) {
            $jobSeekerUser = User::firstOrCreate(
                ['email' => "jobseeker{$i}@test.com"],
                [
                    'name' => "باحث عن عمل {$i}",
                    'password' => Hash::make('password'),
                    'role' => 'job_seeker',
                    'email_verified_at' => now(),
                ]
            );

            JobSeeker::firstOrCreate(
                ['user_id' => $jobSeekerUser->id],
                [
                    'job_title' => 'مطور برمجيات',
                    'location' => 'الرياض',
                    'experience' => 'خبرة ' . rand(1, 5) . ' سنوات',
                    'skills' => 'PHP, Laravel, JavaScript, MySQL',
                    'is_available' => true,
                ]
            );
        }

        // إنشاء وظائف
        $jobTitles = [
            'مطور ويب',
            'مصمم جرافيك',
            'مسوق رقمي',
            'محاسب',
            'مهندس برمجيات',
            'مدير مشروع',
            'أخصائي موارد بشرية',
            'محلل بيانات'
        ];

        foreach ($jobTitles as $index => $title) {
            $job = Job::firstOrCreate(
                [
                    'title' => $title,
                    'employer_id' => $employer->id
                ],
                [
                    'description' => "وصف تفصيلي للوظيفة: {$title}",
                    'requirements' => "متطلبات الوظيفة: {$title}",
                    'location' => 'الرياض',
                    'job_type' => ['full_time', 'part_time', 'remote'][rand(0, 2)],
                    'salary_range' => rand(5000, 15000) . ' - ' . rand(15000, 25000) . ' ريال',
                    'category_id' => Category::inRandomOrder()->first()->id,
                    'is_active' => rand(0, 1) == 1,
                    'expires_at' => now()->addDays(rand(10, 60)),
                    'posted_at' => now()->subDays(rand(1, 30)),
                    'views' => rand(10, 100),
                ]
            );

            // إنشاء طلبات للوظيفة
            $jobSeekers = JobSeeker::all();
            $numApplications = rand(1, min(3, $jobSeekers->count()));
            
            $selectedJobSeekers = $jobSeekers->random($numApplications);
            
            foreach ($selectedJobSeekers as $jobSeeker) {
                Application::firstOrCreate(
                    [
                        'job_id' => $job->id,
                        'job_seeker_id' => $jobSeeker->id
                    ],
                    [
                        'cover_letter' => 'رسالة تغطية للتقديم على وظيفة ' . $title,
                        'status' => ['pending', 'reviewed', 'accepted', 'rejected'][rand(0, 3)],
                        'applied_at' => now()->subDays(rand(1, 20)),
                    ]
                );
            }
        }

        $this->info('تم إنشاء البيانات التجريبية بنجاح!');
        
        // عرض الإحصائيات
        $this->info('الإحصائيات:');
        $this->info('المستخدمين: ' . User::count());
        $this->info('أصحاب العمل: ' . Employer::count());
        $this->info('الباحثين عن عمل: ' . JobSeeker::count());
        $this->info('الوظائف: ' . Job::count());
        $this->info('الطلبات: ' . Application::count());
        $this->info('الفئات: ' . Category::count());
    }
}
