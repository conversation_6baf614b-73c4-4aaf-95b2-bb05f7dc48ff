<?php $__env->startSection('title', 'إدارة المستخدمين - Hire Me'); ?>
<?php $__env->startSection('header_title', 'إدارة المستخدمين'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-bold">إدارة المستخدمين</h2>
                <p class="text-gray-600 dark:text-gray-400 mt-1">إدارة حسابات المستخدمين والصلاحيات</p>
            </div>
            <div class="flex gap-3">
                <button onclick="showExportModal()" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors flex items-center gap-2">
                    <i class="fas fa-download"></i>
                    <span>تصدير</span>
                </button>
                <a href="<?php echo e(route('admin.users.create')); ?>" class="btn-gradient text-white px-6 py-3 rounded-lg hover:opacity-90 transition-colors flex items-center gap-2">
                    <i class="fas fa-plus"></i>
                    <span>إضافة مستخدم جديد</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if(session('success')): ?>
        <div class="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg">
            <div class="flex items-center">
                <i class="fas fa-check-circle ml-2"></i>
                <?php echo e(session('success')); ?>

            </div>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
            <div class="flex items-center">
                <i class="fas fa-exclamation-circle ml-2"></i>
                <?php echo e(session('error')); ?>

            </div>
        </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                    <i class="fas fa-users text-blue-600 dark:text-blue-400 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm text-gray-500 dark:text-gray-400">إجمالي المستخدمين</p>
                    <p class="text-2xl font-bold"><?php echo e($users->total()); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                    <i class="fas fa-user-check text-green-600 dark:text-green-400 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm text-gray-500 dark:text-gray-400">المستخدمون المفعلون</p>
                    <p class="text-2xl font-bold"><?php echo e($users->where('email_verified_at', '!=', null)->count()); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
                    <i class="fas fa-briefcase text-purple-600 dark:text-purple-400 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm text-gray-500 dark:text-gray-400">أصحاب العمل</p>
                    <p class="text-2xl font-bold"><?php echo e($users->where('role', 'employer')->count()); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center">
                    <i class="fas fa-user-graduate text-orange-600 dark:text-orange-400 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm text-gray-500 dark:text-gray-400">المرشحون</p>
                    <p class="text-2xl font-bold"><?php echo e($users->where('role', 'candidate')->count()); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6 mb-6">
        <form method="GET" action="<?php echo e(route('admin.users.index')); ?>" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">البحث</label>
                <input type="text" name="search" id="search" value="<?php echo e(request('search')); ?>"
                       class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary"
                       placeholder="البحث بالاسم أو البريد الإلكتروني">
            </div>

            <div>
                <label for="role" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الدور</label>
                <select name="role" id="role" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary">
                    <option value="">جميع الأدوار</option>
                    <option value="admin" <?php echo e(request('role') === 'admin' ? 'selected' : ''); ?>>مدير</option>
                    <option value="employer" <?php echo e(request('role') === 'employer' ? 'selected' : ''); ?>>صاحب عمل</option>
                    <option value="candidate" <?php echo e(request('role') === 'candidate' ? 'selected' : ''); ?>>مرشح</option>
                </select>
            </div>

            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الحالة</label>
                <select name="status" id="status" class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary">
                    <option value="">جميع الحالات</option>
                    <option value="verified" <?php echo e(request('status') === 'verified' ? 'selected' : ''); ?>>مفعل</option>
                    <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>غير مفعل</option>
                </select>
            </div>

            <div class="flex items-end">
                <button type="submit" class="w-full px-4 py-2.5 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors flex items-center justify-center gap-2">
                    <i class="fas fa-search"></i>
                    <span>بحث</span>
                </button>
            </div>
        </form>
    </div>

    <!-- Users Table -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-800">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">المستخدم</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الدور</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الحالة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">تاريخ التسجيل</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-dark-card divide-y divide-gray-200 dark:divide-gray-700">
                    <?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-br from-primary-400 to-primary-600 rounded-full flex items-center justify-center text-white font-bold">
                                        <?php echo e(strtoupper(substr($user->name, 0, 1))); ?>

                                    </div>
                                    <div class="mr-4">
                                        <div class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e($user->name); ?></div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400"><?php echo e($user->email); ?></div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                    <?php if($user->role === 'admin'): ?> bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400
                                    <?php elseif($user->role === 'employer'): ?> bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400
                                    <?php else: ?> bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 <?php endif; ?>">
                                    <?php if($user->role === 'admin'): ?> مدير
                                    <?php elseif($user->role === 'employer'): ?> صاحب عمل
                                    <?php else: ?> مرشح <?php endif; ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php if($user->email_verified_at): ?>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                                        <i class="fas fa-check-circle ml-1"></i>
                                        مفعل
                                    </span>
                                <?php else: ?>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400">
                                        <i class="fas fa-clock ml-1"></i>
                                        غير مفعل
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                <?php echo e($user->created_at->format('Y/m/d')); ?>

                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center gap-2">
                                    <a href="<?php echo e(route('admin.users.show', $user)); ?>" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?php echo e(route('admin.users.edit', $user)); ?>" class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <?php if($user->id !== auth()->id()): ?>
                                        <form action="<?php echo e(route('admin.users.destroy', $user)); ?>" method="POST" class="inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا المستخدم؟')">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="5" class="px-6 py-12 text-center text-gray-500 dark:text-gray-400">
                                <i class="fas fa-users text-4xl mb-4"></i>
                                <p>لا توجد مستخدمون</p>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if($users->hasPages()): ?>
            <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                <?php echo e($users->appends(request()->query())->links()); ?>

            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Export Modal -->
<div id="exportModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
    <div class="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-md w-full mx-4">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">تصدير بيانات المستخدمين</h3>
            <button onclick="hideExportModal()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    اختر صيغة التصدير
                </label>
                <div class="space-y-2">
                    <label class="flex items-center">
                        <input type="radio" name="export_format" value="csv" checked class="text-blue-600 focus:ring-blue-500">
                        <span class="mr-2 text-gray-700 dark:text-gray-300">CSV (Excel) - جميع البيانات</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="export_format" value="pdf" class="text-blue-600 focus:ring-blue-500">
                        <span class="mr-2 text-gray-700 dark:text-gray-300">PDF - تقرير مفصل</span>
                    </label>
                </div>
            </div>

            <div class="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                <div class="flex items-start gap-2">
                    <i class="fas fa-info-circle text-blue-500 mt-0.5"></i>
                    <div class="text-sm text-blue-700 dark:text-blue-300">
                        <p class="font-medium mb-1">سيتم تصدير:</p>
                        <ul class="text-xs space-y-1">
                            <li>• أسماء المستخدمين وبياناتهم</li>
                            <li>• الأدوار والصلاحيات</li>
                            <li>• حالات التفعيل</li>
                            <li>• تواريخ الإنضمام</li>
                            <li>• إحصائيات شاملة</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="flex gap-3 pt-4">
                <button onclick="exportUsers()" class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-download mr-2"></i>
                    تصدير
                </button>
                <button onclick="hideExportModal()" class="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    إلغاء
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Show export modal
function showExportModal() {
    document.getElementById('exportModal').classList.remove('hidden');
}

// Hide export modal
function hideExportModal() {
    document.getElementById('exportModal').classList.add('hidden');
}

// Export users
function exportUsers() {
    const selectedFormat = document.querySelector('input[name="export_format"]:checked').value;
    const button = event.target.closest('button');
    const originalText = button.innerHTML;

    // إظهار رسالة تحميل
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>جاري التصدير...';
    button.disabled = true;

    // بناء URL مع المعاملات
    let exportUrl = '<?php echo e(route("admin.users.export")); ?>';
    const urlParams = new URLSearchParams();

    // إضافة نوع التصدير
    if (selectedFormat === 'pdf') {
        urlParams.append('format', 'pdf');
    }

    // إضافة الفلاتر الحالية من URL
    const currentParams = new URLSearchParams(window.location.search);
    for (const [key, value] of currentParams) {
        if (['search', 'role', 'status'].includes(key)) {
            urlParams.append(key, value);
        }
    }

    if (urlParams.toString()) {
        exportUrl += '?' + urlParams.toString();
    }

    // تحميل الملف
    window.location.href = exportUrl;

    // إعادة تعيين الزر وإخفاء modal
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
        hideExportModal();
        showSuccessMessage('تم تصدير الملف بنجاح!');
    }, 2000);
}

// وظيفة إظهار رسالة النجاح
function showSuccessMessage(message) {
    const successDiv = document.createElement('div');
    successDiv.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 flex items-center gap-2';
    successDiv.innerHTML = `
        <i class="fas fa-check-circle"></i>
        <span>${message}</span>
    `;

    document.body.appendChild(successDiv);

    setTimeout(() => {
        if (document.body.contains(successDiv)) {
            document.body.removeChild(successDiv);
        }
    }, 3000);
}

// إغلاق modal عند النقر خارجه
document.getElementById('exportModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideExportModal();
    }
});
</script>

<style>
/* تحسينات modal التصدير */
#exportModal {
    backdrop-filter: blur(4px);
}

#exportModal .bg-white {
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسينات أزرار الراديو */
input[type="radio"] {
    width: 18px;
    height: 18px;
}
</style>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\Telegram Desktop\augment (10)\augment\resources\views/admin/users/index.blade.php ENDPATH**/ ?>