<?php

namespace App\Providers;
use Illuminate\Support\Facades\Schema;
use Illuminate\Pagination\Paginator;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot()
    {
        Schema::defaultStringLength(125);
        Paginator::defaultView('pagination.tailwind');

        // إنشاء جدول saved_candidates إذا لم يكن موجوداً
        try {
            if (!Schema::hasTable('saved_candidates')) {
                Schema::create('saved_candidates', function (\Illuminate\Database\Schema\Blueprint $table) {
                    $table->id();
                    $table->foreignId('employer_id')->constrained('employers')->onDelete('cascade');
                    $table->foreignId('job_seeker_id')->constrained('job_seekers')->onDelete('cascade');
                    $table->text('notes')->nullable();
                    $table->timestamps();
                    $table->unique(['employer_id', 'job_seeker_id']);
                });
            }
        } catch (\Exception $e) {
            // تسجيل الخطأ في logs بدون إيقاف التطبيق
            \Log::error('خطأ في إنشاء جدول saved_candidates: ' . $e->getMessage());
        }
    }
}
