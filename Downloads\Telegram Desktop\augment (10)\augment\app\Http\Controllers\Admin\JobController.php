<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Job;
use App\Models\Employer;
use App\Models\Category;
use Illuminate\Http\Request;
class JobController extends Controller
{
    /**
     * إنشاء مثيل جديد من الكنترولر
     */
    public function __construct()
    {
        // تطبيق middleware على الدوال التي تتطلب تسجيل دخول كمدير
        $this->middleware(['auth', 'role:admin']);
    }

    /**
     * عرض قائمة الوظائف
     */
    public function index(Request $request)
    {
        // استعلام الوظائف مع علاقاتها
        $jobsQuery = Job::with(['employer.user', 'category', 'applications']);

        // تطبيق الفلاتر إذا وجدت
        if ($request->filled('search')) {
            $jobsQuery->where(function($query) use ($request) {
                $query->where('title', 'like', '%' . $request->search . '%')
                      ->orWhere('description', 'like', '%' . $request->search . '%');
            });
        }

        if ($request->filled('location')) {
            $jobsQuery->where('location', 'like', '%' . $request->location . '%');
        }

        if ($request->filled('type')) {
            $jobsQuery->where('job_type', $request->type);
        }

        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $jobsQuery->where('expires_at', '>', now());
            } elseif ($request->status === 'expired') {
                $jobsQuery->where('expires_at', '<=', now());
            }
        }

        // ترتيب النتائج
        $jobsQuery->orderBy('created_at', 'desc');

        // الحصول على الوظائف مع التقسيم
        $jobs = $jobsQuery->paginate(10);

        // الحصول على قائمة المواقع والأنواع للفلاتر
        $locations = Job::select('location')->distinct()->pluck('location');
        $jobTypes = ['full_time' => 'دوام كامل', 'part_time' => 'دوام جزئي', 'remote' => 'عن بعد', 'freelance' => 'عمل حر'];

        return view('admin.jobs.index', compact('jobs', 'locations', 'jobTypes'));
    }

    /**
     * عرض نموذج إنشاء وظيفة جديدة
     */
    public function create()
    {
        // الحصول على قائمة الشركات والتصنيفات
        $employers = Employer::with('user')->get();
        $categories = Category::all();
        $jobTypes = ['full_time' => 'دوام كامل', 'part_time' => 'دوام جزئي', 'remote' => 'عن بعد', 'freelance' => 'عمل حر'];

        // تعيين تاريخ افتراضي لانتهاء الوظيفة (بعد 30 يوم من اليوم)
        $defaultExpiryDate = now()->addDays(30)->format('Y-m-d');

        return view('admin.jobs.create', compact('employers', 'categories', 'jobTypes', 'defaultExpiryDate'));
    }

    /**
     * تخزين وظيفة جديدة
     */
    public function store(Request $request)
    {
        // التحقق من البيانات
        $validated = $request->validate([
            'employer_id' => 'required|exists:employers,id',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'requirements' => 'required|string',
            'location' => 'required|string|max:255',
            'job_type' => 'required|in:full_time,part_time,remote,freelance',
            'category_id' => 'required|exists:categories,id',
            'salary_range' => 'nullable|string|max:255',
            'salary_currency' => 'required|string|in:LYD,USD,EUR',
            'expires_at' => 'required|date|after:today',
        ]);

        // إنشاء وظيفة جديدة
        $job = new Job();
        $job->employer_id = $validated['employer_id'];
        $job->title = $validated['title'];
        $job->description = $validated['description'];
        $job->requirements = $validated['requirements'];
        $job->location = $validated['location'];
        $job->job_type = $validated['job_type'];
        $job->category_id = $validated['category_id'];
        $job->salary_range = $validated['salary_range'];
        $job->salary_currency = $validated['salary_currency'];
        $job->posted_at = now();
        $job->expires_at = $validated['expires_at'];
        $job->views = 0; // إضافة عدد المشاهدات الافتراضي
        $job->save();

        return redirect()->route('admin.jobs.index')->with('success', 'تم إضافة الوظيفة بنجاح');
    }

    /**
     * عرض تفاصيل وظيفة محددة
     */
    public function show(Job $job)
    {
        // تحميل العلاقات
        $job->load(['employer.user', 'category', 'applications.jobSeeker.user']);

        // زيادة عدد المشاهدات
        $job->increment('views');

        return view('admin.jobs.show', compact('job'));
    }

    /**
     * عرض نموذج تعديل وظيفة
     */
    public function edit(Job $job)
    {
        // تحميل العلاقات
        $job->load(['employer', 'category']);

        // الحصول على قائمة الشركات والتصنيفات
        $employers = Employer::with('user')->get();
        $categories = Category::all();
        $jobTypes = ['full_time' => 'دوام كامل', 'part_time' => 'دوام جزئي', 'remote' => 'عن بعد', 'freelance' => 'عمل حر'];

        return view('admin.jobs.edit', compact('job', 'employers', 'categories', 'jobTypes'));
    }

    /**
     * تحديث وظيفة محددة
     */
    public function update(Request $request, Job $job)
    {
        // التحقق من البيانات
        $validated = $request->validate([
            'employer_id' => 'required|exists:employers,id',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'requirements' => 'required|string',
            'location' => 'required|string|max:255',
            'job_type' => 'required|in:full_time,part_time,remote,freelance',
            'category_id' => 'required|exists:categories,id',
            'salary_range' => 'nullable|string|max:255',
            'salary_currency' => 'required|string|in:LYD,USD,EUR',
            'expires_at' => 'required|date',
        ]);

        // تحديث الوظيفة
        $job->employer_id = $validated['employer_id'];
        $job->title = $validated['title'];
        $job->description = $validated['description'];
        $job->requirements = $validated['requirements'];
        $job->location = $validated['location'];
        $job->job_type = $validated['job_type'];
        $job->category_id = $validated['category_id'];
        $job->salary_range = $validated['salary_range'];
        $job->salary_currency = $validated['salary_currency'];
        $job->expires_at = $validated['expires_at'];
        $job->save();

        return redirect()->route('admin.jobs.index')->with('success', 'تم تحديث الوظيفة بنجاح');
    }

    /**
     * حذف وظيفة محددة
     */
    public function destroy(Job $job)
    {
        // حذف الوظيفة
        $job->delete();

        return redirect()->route('admin.jobs.index')->with('success', 'تم حذف الوظيفة بنجاح');
    }
}
