@props([
    'notification',
    'showActions' => true,
    'compact' => false
])

@php
    $isUnread = !$notification->is_read;
    $iconClass = match($notification->type) {
        'job_application_received' => 'fas fa-file-alt',
        'application_status_update' => 'fas fa-sync-alt',
        'profile_rated' => 'fas fa-star',
        'company_rated' => 'fas fa-building',
        'job_posted' => 'fas fa-briefcase',
        'job_expired' => 'fas fa-clock',
        'payment_success' => 'fas fa-check-circle',
        'payment_failed' => 'fas fa-times-circle',
        'interview_scheduled' => 'fas fa-calendar-alt',
        'job_recommendation' => 'fas fa-lightbulb',
        default => 'fas fa-bell'
    };
    
    $iconBgClass = match($notification->type) {
        'job_application_received' => 'bg-gradient-to-br from-blue-400 to-blue-600',
        'application_status_update' => 'bg-gradient-to-br from-cyan-400 to-cyan-600',
        'profile_rated' => 'bg-gradient-to-br from-yellow-400 to-yellow-600',
        'company_rated' => 'bg-gradient-to-br from-green-400 to-green-600',
        'job_posted' => 'bg-gradient-to-br from-purple-400 to-purple-600',
        'job_expired' => 'bg-gradient-to-br from-red-400 to-red-600',
        'payment_success' => 'bg-gradient-to-br from-green-400 to-green-600',
        'payment_failed' => 'bg-gradient-to-br from-red-400 to-red-600',
        'interview_scheduled' => 'bg-gradient-to-br from-indigo-400 to-indigo-600',
        'job_recommendation' => 'bg-gradient-to-br from-amber-400 to-amber-600',
        default => 'bg-gradient-to-br from-gray-400 to-gray-600'
    };
    
    $typeLabel = match($notification->type) {
        'job_application_received' => 'طلب توظيف جديد',
        'application_status_update' => 'تحديث حالة طلب',
        'profile_rated' => 'تقييم ملف شخصي',
        'company_rated' => 'تقييم شركة',
        'job_posted' => 'وظيفة جديدة',
        'job_expired' => 'انتهاء وظيفة',
        'payment_success' => 'دفع ناجح',
        'payment_failed' => 'فشل في الدفع',
        'interview_scheduled' => 'مقابلة مجدولة',
        'job_recommendation' => 'توصية وظيفة',
        default => 'إشعار عام'
    };
@endphp

<div class="notification-card {{ $isUnread ? 'unread' : 'read' }} {{ $compact ? 'compact' : '' }} 
            p-{{ $compact ? '3' : '4' }} rounded-xl border border-gray-200 dark:border-gray-700 
            hover:shadow-md transition-all duration-300 cursor-pointer
            {{ $isUnread ? 'bg-blue-50 dark:bg-blue-900/10 border-blue-200 dark:border-blue-800' : 'bg-white dark:bg-gray-800' }}"
     data-notification-id="{{ $notification->id }}"
     tabindex="0"
     role="button"
     aria-label="إشعار: {{ $notification->message }}">
    
    <div class="flex items-start gap-{{ $compact ? '3' : '4' }}">
        <!-- Icon -->
        <div class="flex-shrink-0">
            <div class="w-{{ $compact ? '10' : '12' }} h-{{ $compact ? '10' : '12' }} rounded-xl 
                        {{ $iconBgClass }} text-white flex items-center justify-center shadow-lg">
                <i class="{{ $iconClass }} text-{{ $compact ? 'base' : 'lg' }}"></i>
            </div>
        </div>

        <!-- Content -->
        <div class="flex-1 min-w-0">
            <div class="flex items-start justify-between">
                <div class="flex-1">
                    <!-- Type and Status -->
                    <div class="flex items-center gap-2 mb-1">
                        <span class="text-{{ $compact ? 'xs' : 'sm' }} font-medium text-gray-600 dark:text-gray-400">
                            {{ $typeLabel }}
                        </span>
                        @if($isUnread)
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                                         bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400">
                                جديد
                            </span>
                        @endif
                    </div>
                    
                    <!-- Message -->
                    <p class="text-gray-900 dark:text-white {{ $isUnread ? 'font-semibold' : '' }} 
                             {{ $compact ? 'text-sm' : '' }} mb-2 leading-relaxed">
                        {{ $compact ? Str::limit($notification->message, 60) : $notification->message }}
                    </p>
                    
                    <!-- Date and Time -->
                    <div class="flex items-center gap-4 text-{{ $compact ? 'xs' : 'sm' }} text-gray-500 dark:text-gray-400">
                        <span class="flex items-center gap-1">
                            <i class="fas fa-calendar text-xs"></i>
                            {{ $notification->created_at->format('Y-m-d') }}
                        </span>
                        <span class="flex items-center gap-1">
                            <i class="fas fa-clock text-xs"></i>
                            {{ $notification->created_at->format('H:i') }}
                        </span>
                        @if(!$compact)
                            <span class="text-gray-400">
                                {{ $notification->created_at->diffForHumans() }}
                            </span>
                        @endif
                    </div>
                </div>

                <!-- Actions -->
                @if($showActions)
                    <div class="flex items-center gap-2 {{ $compact ? 'mr-2' : 'mr-4' }}">
                        @if($notification->link)
                            <a href="{{ route('employer.notifications.mark-read', $notification) }}"
                               class="action-btn inline-flex items-center justify-center 
                                      w-{{ $compact ? '7' : '8' }} h-{{ $compact ? '7' : '8' }} rounded-lg 
                                      bg-blue-100 hover:bg-blue-200 text-blue-600 transition-colors duration-200"
                               title="عرض التفاصيل"
                               onclick="event.stopPropagation()">
                                <i class="fas fa-external-link-alt text-{{ $compact ? 'xs' : 'sm' }}"></i>
                            </a>
                        @endif
                        @if($isUnread)
                            <form action="{{ route('employer.notifications.mark-read', $notification) }}" 
                                  method="POST" class="inline" onclick="event.stopPropagation()">
                                @csrf
                                <button type="submit"
                                        class="action-btn inline-flex items-center justify-center 
                                               w-{{ $compact ? '7' : '8' }} h-{{ $compact ? '7' : '8' }} rounded-lg 
                                               bg-green-100 hover:bg-green-200 text-green-600 transition-colors duration-200"
                                        title="تحديد كمقروء">
                                    <i class="fas fa-check text-{{ $compact ? 'xs' : 'sm' }}"></i>
                                </button>
                            </form>
                        @endif
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<style>
.notification-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notification-card:hover {
    transform: translateY(-1px);
}

.notification-card.unread {
    position: relative;
}

.notification-card.unread::before {
    content: '';
    position: absolute;
    right: -1px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 70%;
    background: linear-gradient(to bottom, #3b82f6, #8b5cf6);
    border-radius: 2px;
}

.notification-card:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

.action-btn {
    transition: all 0.2s ease-in-out;
}

.action-btn:hover {
    transform: scale(1.1);
}

.action-btn:active {
    transform: scale(0.95);
}

.notification-card.compact {
    padding: 0.75rem;
}

.notification-card.compact .action-btn {
    width: 1.75rem;
    height: 1.75rem;
}

@media (max-width: 768px) {
    .notification-card:hover {
        transform: none;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add click handler for notification cards
    document.querySelectorAll('.notification-card').forEach(card => {
        card.addEventListener('click', function(e) {
            // Don't trigger if clicking on action buttons
            if (e.target.closest('.action-btn, form, a')) {
                return;
            }
            
            const markReadForm = this.querySelector('form[action*="mark-read"]');
            if (markReadForm) {
                markReadForm.submit();
            }
        });

        // Add keyboard navigation
        card.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
    });
});
</script>
