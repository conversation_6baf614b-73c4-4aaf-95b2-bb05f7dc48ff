@extends('layouts.admin')

@section('title', 'إشعارات الإدارة - Hire Me')
@section('header_title', 'إشعارات الإدارة')

@section('content')
<div class="p-8 space-y-8">
    <!-- Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">إشعارات الإدارة</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">
                @if($unreadCount > 0)
                    لديك {{ $unreadCount }} إشعار غير مقروء
                @else
                    جميع الإشعارات مقروءة
                @endif
            </p>
        </div>

        <div class="flex gap-3 mt-4 md:mt-0">
            <a href="{{ route('admin.notifications.create') }}" class="px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-300 flex items-center gap-2 shadow-lg">
                <i class="fas fa-plus"></i>
                إرسال إشعار جديد
            </a>

            <a href="{{ route('admin.notifications.statistics') }}" class="px-4 py-2 bg-gradient-to-r from-green-600 to-teal-600 text-white rounded-lg hover:from-green-700 hover:to-teal-700 transition-all duration-300 flex items-center gap-2 shadow-lg">
                <i class="fas fa-chart-bar"></i>
                الإحصائيات
            </a>

            @if($unreadCount > 0)
            <form action="{{ route('admin.notifications.mark-all-read') }}" method="POST" class="inline">
                @csrf
                <button type="submit" class="px-4 py-2 bg-gradient-to-r from-yellow-600 to-orange-600 text-white rounded-lg hover:from-yellow-700 hover:to-orange-700 transition-all duration-300 flex items-center gap-2 shadow-lg">
                    <i class="fas fa-check-double"></i>
                    تحديد الكل كمقروء
                </button>
            </form>
            @endif
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="stat-card p-6 rounded-xl">
            <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-bell text-white"></i>
                </div>
                <div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">إجمالي الإشعارات</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $notifications->total() }}</p>
                </div>
            </div>
        </div>

        <div class="stat-card p-6 rounded-xl">
            <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-exclamation-circle text-white"></i>
                </div>
                <div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">غير مقروءة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $unreadCount }}</p>
                </div>
            </div>
        </div>

        <div class="stat-card p-6 rounded-xl">
            <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-check-circle text-white"></i>
                </div>
                <div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">مقروءة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $notifications->total() - $unreadCount }}</p>
                </div>
            </div>
        </div>

        <div class="stat-card p-6 rounded-xl">
            <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-calendar-day text-white"></i>
                </div>
                <div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">اليوم</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $notifications->where('created_at', '>=', today())->count() }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Notifications List -->
    <div class="glass-card rounded-xl overflow-hidden">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">قائمة الإشعارات</h2>
        </div>

        <div class="divide-y divide-gray-200 dark:divide-gray-700">
            @forelse($notifications as $notification)
            <div class="p-6 {{ $notification->is_read ? '' : 'bg-blue-50 dark:bg-blue-900/20' }}">
                <div class="flex items-start gap-4">
                    <!-- Icon -->
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 rounded-full {{ $notification->is_read ? 'bg-gray-100 dark:bg-gray-700' : 'bg-blue-100 dark:bg-blue-900/30' }} flex items-center justify-center">
                            <i class="fas fa-{{ getNotificationIcon($notification->type) }} {{ $notification->is_read ? 'text-gray-500' : 'text-blue-600 dark:text-blue-400' }}"></i>
                        </div>
                    </div>

                    <!-- Content -->
                    <div class="flex-1 min-w-0">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <p class="text-gray-900 dark:text-white {{ $notification->is_read ? '' : 'font-semibold' }}">
                                    {{ $notification->message }}
                                </p>
                                <div class="flex items-center gap-4 mt-2 text-sm text-gray-500 dark:text-gray-400">
                                    <span>{{ $notification->created_at->diffForHumans() }}</span>
                                    <span class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">{{ getNotificationTypeInArabic($notification->type) }}</span>
                                    @if(!$notification->is_read)
                                    <span class="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded text-xs">جديد</span>
                                    @endif
                                </div>
                            </div>

                            <!-- Actions -->
                            <div class="flex items-center gap-2 ml-4">
                                @if(!$notification->is_read)
                                <span class="w-2 h-2 bg-blue-600 rounded-full"></span>
                                @endif

                                <div class="relative" x-data="{ open: false }">
                                    <button @click="open = !open"
                                            @keydown.enter="open = !open"
                                            @keydown.space.prevent="open = !open"
                                            :aria-expanded="open"
                                            aria-haspopup="true"
                                            aria-label="خيارات الإشعار"
                                            class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>

                                    <div x-show="open"
                                         x-cloak
                                         @click.away="open = false"
                                         x-transition:enter="transition ease-out duration-200"
                                         x-transition:enter-start="opacity-0 scale-95"
                                         x-transition:enter-end="opacity-100 scale-100"
                                         x-transition:leave="transition ease-in duration-150"
                                         x-transition:leave-start="opacity-100 scale-100"
                                         x-transition:leave-end="opacity-0 scale-95"
                                         role="menu"
                                         aria-label="خيارات الإشعار"
                                         class="absolute left-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-[9999] dropdown-menu">
                                        @if($notification->link)
                                        <a href="{{ route('admin.notifications.mark-read', $notification) }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-t-lg">
                                            <i class="fas fa-external-link-alt ml-2"></i>
                                            عرض التفاصيل
                                        </a>
                                        @endif

                                        @if(!$notification->is_read)
                                        <form action="{{ route('admin.notifications.mark-read', $notification) }}" method="POST" class="block">
                                            @csrf
                                            <button type="submit" class="w-full text-right px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                                <i class="fas fa-check ml-2"></i>
                                                تحديد كمقروء
                                            </button>
                                        </form>
                                        @endif

                                        <form action="{{ route('admin.notifications.destroy', $notification) }}" method="POST" class="block">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="w-full text-right px-4 py-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-b-lg" onclick="return confirm('هل أنت متأكد من حذف هذا الإشعار؟')">
                                                <i class="fas fa-trash ml-2"></i>
                                                حذف
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @empty
            <div class="text-center py-12">
                <div class="w-24 h-24 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                    <i class="fas fa-bell-slash text-3xl text-gray-400"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">لا توجد إشعارات</h3>
                <p class="text-gray-500 dark:text-gray-400">ستظهر الإشعارات هنا عند وصولها</p>
            </div>
            @endforelse
        </div>
    </div>

    <!-- Pagination -->
    @if($notifications->hasPages())
    <div class="flex justify-center">
        {{ $notifications->links() }}
    </div>
    @endif
</div>


@endsection

@push('styles')
<style>
/* تحسينات للنوافذ المنبثقة */
[x-cloak] {
    display: none !important;
}

.dropdown-menu {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

/* تحسين الظلال */
.notification-dropdown {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* تحسين الانتقالات */
.notification-item {
    transition: all 0.2s ease-in-out;
}

.notification-item:hover {
    transform: translateX(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .dropdown-menu {
        position: fixed !important;
        top: 60px !important;
        left: 10px !important;
        right: 10px !important;
        width: auto !important;
        max-width: none !important;
    }

    .notification-item:hover {
        transform: none;
        box-shadow: none;
    }
}

/* تحسين الأداء */
.dropdown-menu {
    will-change: transform, opacity;
}

/* تحسين إمكانية الوصول */
.dropdown-menu:focus-within {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}
</style>
@endpush

@section('scripts')
<script>
// تحديث الإشعارات كل 30 ثانية
setInterval(function() {
    fetch('/admin/notifications/unread')
        .then(response => response.json())
        .then(data => {
            // تحديث عداد الإشعارات في الهيدر
            const notificationBadge = document.querySelector('.notification-badge');
            if (notificationBadge) {
                notificationBadge.textContent = data.count;
                notificationBadge.style.display = data.count > 0 ? 'flex' : 'none';
            }
        })
        .catch(error => console.error('Error fetching notifications:', error));
}, 30000);

// تحسين تجربة النوافذ المنبثقة
document.addEventListener('alpine:init', () => {
    console.log('Alpine.js initialized successfully!');
});

// إضافة تأثيرات إضافية للنوافذ المنبثقة
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثير hover للعناصر
    const notificationItems = document.querySelectorAll('.notification-item');
    notificationItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(-4px)';
            this.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.15)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
            this.style.boxShadow = 'none';
        });
    });
});
</script>
@endsection
