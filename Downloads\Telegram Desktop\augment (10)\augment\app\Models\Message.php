<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Message extends Model
{
    use HasFactory;

    protected $fillable = [
        'sender_id',
        'receiver_id',
        'subject',
        'body',
        'is_read',
        'read_at',
        'attachments',
    ];

    protected $casts = [
        'is_read' => 'boolean',
        'read_at' => 'datetime',
        'attachments' => 'array',
    ];

    /**
     * العلاقة مع المرسل
     */
    public function sender()
    {
        return $this->belongsTo(User::class, 'sender_id');
    }

    /**
     * العلاقة مع المستقبل
     */
    public function receiver()
    {
        return $this->belongsTo(User::class, 'receiver_id');
    }

    /**
     * تحديد الرسالة كمقروءة
     */
    public function markAsRead()
    {
        if (!$this->is_read) {
            $this->update([
                'is_read' => true,
                'read_at' => now(),
            ]);
        }
    }

    /**
     * فلترة الرسائل الواردة
     */
    public function scopeInbox($query, $userId)
    {
        return $query->where('receiver_id', $userId);
    }

    /**
     * فلترة الرسائل المرسلة
     */
    public function scopeSent($query, $userId)
    {
        return $query->where('sender_id', $userId);
    }

    /**
     * فلترة الرسائل غير المقروءة
     */
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    /**
     * البحث في الرسائل
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('subject', 'like', "%{$search}%")
              ->orWhere('body', 'like', "%{$search}%");
        });
    }

    /**
     * تنسيق تاريخ الإرسال
     */
    public function getFormattedDateAttribute()
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * اختصار المحتوى
     */
    public function getExcerptAttribute()
    {
        return \Str::limit(strip_tags($this->body), 100);
    }
}
