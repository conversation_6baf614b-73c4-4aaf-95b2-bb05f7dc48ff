<?php

// ملف لإنشاء بيانات تجريبية مباشرة في قاعدة البيانات

require_once 'vendor/autoload.php';

// تحميل إعدادات Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use App\Models\Employer;
use App\Models\JobSeeker;
use App\Models\Job;
use App\Models\Application;
use App\Models\Category;
use Illuminate\Support\Facades\Hash;

echo "بدء إنشاء البيانات التجريبية...\n";

try {
    // إنشاء فئات الوظائف
    $categories = [
        'تقنية المعلومات',
        'التسويق',
        'المبيعات',
        'الموارد البشرية',
        'المحاسبة'
    ];

    foreach ($categories as $categoryName) {
        Category::firstOrCreate(['name' => $categoryName]);
    }
    echo "تم إنشاء الفئات\n";

    // إنشاء مستخدم صاحب عمل
    $employerUser = User::firstOrCreate(
        ['email' => '<EMAIL>'],
        [
            'name' => 'شركة الاختبار',
            'password' => Hash::make('password'),
            'role' => 'employer',
            'email_verified_at' => now(),
        ]
    );

    // إنشاء ملف صاحب العمل
    $employer = Employer::firstOrCreate(
        ['user_id' => $employerUser->id],
        [
            'company_name' => 'شركة الاختبار للتقنية',
            'company_description' => 'شركة رائدة في مجال التقنية',
            'industry' => 'تقنية المعلومات',
            'location' => 'الرياض',
            'company_size' => '50-100',
            'founded_year' => 2020,
        ]
    );
    echo "تم إنشاء صاحب العمل\n";

    // إنشاء مستخدمين باحثين عن عمل
    for ($i = 1; $i <= 3; $i++) {
        $jobSeekerUser = User::firstOrCreate(
            ['email' => "jobseeker{$i}@test.com"],
            [
                'name' => "باحث عن عمل {$i}",
                'password' => Hash::make('password'),
                'role' => 'job_seeker',
                'email_verified_at' => now(),
            ]
        );

        JobSeeker::firstOrCreate(
            ['user_id' => $jobSeekerUser->id],
            [
                'job_title' => 'مطور برمجيات',
                'location' => 'الرياض',
                'experience' => 'خبرة ' . rand(1, 5) . ' سنوات',
                'skills' => 'PHP, Laravel, JavaScript, MySQL',
                'is_available' => true,
            ]
        );
    }
    echo "تم إنشاء الباحثين عن عمل\n";

    // إنشاء وظائف
    $jobTitles = [
        'مطور ويب',
        'مصمم جرافيك',
        'مسوق رقمي',
        'محاسب',
        'مهندس برمجيات'
    ];

    foreach ($jobTitles as $title) {
        $job = Job::firstOrCreate(
            [
                'title' => $title,
                'employer_id' => $employer->id
            ],
            [
                'description' => "وصف تفصيلي للوظيفة: {$title}",
                'requirements' => "متطلبات الوظيفة: {$title}",
                'location' => 'الرياض',
                'job_type' => 'full_time',
                'salary_range' => '5000 - 15000 ريال',
                'category_id' => Category::first()->id,
                'is_active' => true,
                'expires_at' => now()->addDays(30),
                'posted_at' => now(),
                'views' => rand(10, 100),
            ]
        );

        // إنشاء طلبات للوظيفة
        $jobSeekers = JobSeeker::all();
        foreach ($jobSeekers as $jobSeeker) {
            Application::firstOrCreate(
                [
                    'job_id' => $job->id,
                    'job_seeker_id' => $jobSeeker->id
                ],
                [
                    'cover_letter' => 'رسالة تغطية للتقديم على وظيفة ' . $title,
                    'status' => ['pending', 'reviewed', 'accepted', 'rejected'][rand(0, 3)],
                    'applied_at' => now()->subDays(rand(1, 10)),
                ]
            );
        }
    }
    echo "تم إنشاء الوظائف والطلبات\n";

    // عرض الإحصائيات
    echo "\n=== الإحصائيات النهائية ===\n";
    echo "المستخدمين: " . User::count() . "\n";
    echo "أصحاب العمل: " . Employer::count() . "\n";
    echo "الباحثين عن عمل: " . JobSeeker::count() . "\n";
    echo "الوظائف: " . Job::count() . "\n";
    echo "الطلبات: " . Application::count() . "\n";
    echo "الفئات: " . Category::count() . "\n";

    echo "\nتم إنشاء البيانات التجريبية بنجاح!\n";

} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage() . "\n";
    echo "التفاصيل: " . $e->getTraceAsString() . "\n";
}
