<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير الإحصائيات - {{ $employer->name }}</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #3b82f6;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #1f2937;
            margin: 0;
            font-size: 28px;
        }
        .header p {
            color: #6b7280;
            margin: 10px 0 0 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }
        .stat-card h3 {
            color: #1f2937;
            margin: 0 0 15px 0;
            font-size: 18px;
        }
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
        }
        .stat-label {
            color: #6b7280;
        }
        .stat-value {
            color: #1f2937;
            font-weight: bold;
        }
        .jobs-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .jobs-table th,
        .jobs-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #e5e7eb;
        }
        .jobs-table th {
            background-color: #f3f4f6;
            color: #374151;
            font-weight: bold;
        }
        .jobs-table tr:hover {
            background-color: #f9fafb;
        }
        .status-active {
            color: #059669;
            font-weight: bold;
        }
        .status-inactive {
            color: #dc2626;
            font-weight: bold;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            text-align: center;
            color: #6b7280;
            font-size: 14px;
        }
        .acceptance-rate {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            margin: 20px 0;
        }
        .acceptance-rate h3 {
            margin: 0;
            font-size: 24px;
        }
        .acceptance-rate p {
            margin: 5px 0 0 0;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>تقرير الإحصائيات والأداء</h1>
            <p>{{ $employer->name }}</p>
            <p>تاريخ التقرير: {{ $reportDate }}</p>
        </div>

        <!-- Statistics Grid -->
        <div class="stats-grid">
            <!-- Job Statistics -->
            <div class="stat-card">
                <h3>إحصائيات الوظائف</h3>
                <div class="stat-item">
                    <span class="stat-label">إجمالي الوظائف:</span>
                    <span class="stat-value">{{ $jobStats['total'] }}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">الوظائف النشطة:</span>
                    <span class="stat-value">{{ $jobStats['active'] }}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">الوظائف المنتهية:</span>
                    <span class="stat-value">{{ $jobStats['expired'] }}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">المسودات:</span>
                    <span class="stat-value">{{ $jobStats['draft'] }}</span>
                </div>
            </div>

            <!-- Application Statistics -->
            <div class="stat-card">
                <h3>إحصائيات الطلبات</h3>
                <div class="stat-item">
                    <span class="stat-label">إجمالي الطلبات:</span>
                    <span class="stat-value">{{ $applicationStats['total'] }}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">قيد المراجعة:</span>
                    <span class="stat-value">{{ $applicationStats['pending'] }}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">مقبولة:</span>
                    <span class="stat-value">{{ $applicationStats['accepted'] }}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">مرفوضة:</span>
                    <span class="stat-value">{{ $applicationStats['rejected'] }}</span>
                </div>
            </div>
        </div>

        <!-- Acceptance Rate -->
        <div class="acceptance-rate">
            <h3>{{ $acceptanceRate }}%</h3>
            <p>معدل قبول الطلبات</p>
        </div>

        <!-- Monthly Statistics -->
        <div style="margin: 30px 0;">
            <h3 style="color: #1f2937; margin-bottom: 15px;">الإحصائيات الشهرية للطلبات</h3>
            <table class="jobs-table">
                <thead>
                    <tr>
                        <th>الشهر</th>
                        <th>عدد الطلبات</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($monthlyApplications as $monthData)
                    <tr>
                        <td>{{ $monthData['month_name'] }}</td>
                        <td>{{ $monthData['count'] }}</td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="2" style="text-align: center; color: #6b7280;">لا توجد بيانات</td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Top Jobs Table -->
        <div>
            <h3 style="color: #1f2937; margin-bottom: 15px;">أفضل الوظائف من حيث عدد الطلبات</h3>
            <table class="jobs-table">
                <thead>
                    <tr>
                        <th>عنوان الوظيفة</th>
                        <th>عدد الطلبات</th>
                        <th>عدد المشاهدات</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($topJobs as $job)
                    <tr>
                        <td>{{ $job->title }}</td>
                        <td>{{ $job->applications_count }}</td>
                        <td>{{ $job->views ?? 0 }}</td>
                        <td>
                            <span class="{{ $job->is_active ? 'status-active' : 'status-inactive' }}">
                                {{ $job->is_active ? 'نشطة' : 'غير نشطة' }}
                            </span>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="4" style="text-align: center; color: #6b7280;">لا توجد وظائف</td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>تم إنشاء هذا التقرير بواسطة نظام Hire Me</p>
            <p>{{ config('app.url') }}</p>
        </div>
    </div>
</body>
</html>
