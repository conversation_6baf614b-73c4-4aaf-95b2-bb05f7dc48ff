<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Run the role and permission seeder first
        $this->call(RoleAndPermissionSeeder::class);

        // Run the category seeder
        $this->call(CategorySeeder::class);

        // Run the testimonial seeder
        $this->call(TestimonialSeeder::class);

        // Run the demo data seeder
        $this->call(DemoDataSeeder::class);
    }
}


