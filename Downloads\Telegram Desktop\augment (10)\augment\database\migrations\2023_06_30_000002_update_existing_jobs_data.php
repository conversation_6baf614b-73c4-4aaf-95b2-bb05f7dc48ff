<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // تحديث البيانات الموجودة في جدول الوظائف
        if (Schema::hasColumn('jobs', 'views')) {
            // تعيين قيمة افتراضية لعمود views لجميع الوظائف الموجودة
            DB::table('jobs')->whereNull('views')->update(['views' => 0]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // لا شيء للتراجع عنه
    }
};
