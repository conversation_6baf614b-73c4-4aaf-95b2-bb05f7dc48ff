@extends('layouts.employer')

@section('title', $job->title . ' - Hire Me')
@section('header_title', 'تفاصيل الوظيفة')

@section('content')
<div class="p-8 space-y-8">
    <!-- Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ $job->title }}</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">تفاصيل الوظيفة ومعلومات الطلبات</p>
        </div>

        <div class="flex gap-3 mt-4 md:mt-0">
            <a href="{{ route('employer.jobs.edit', $job) }}" class="px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-all duration-300 flex items-center gap-2">
                <i class="fas fa-edit"></i>
                تعديل الوظيفة
            </a>
            <a href="{{ route('employer.jobs.index') }}" class="px-6 py-3 bg-gray-600 text-white rounded-xl hover:bg-gray-700 transition-all duration-300 flex items-center gap-2">
                <i class="fas fa-arrow-right"></i>
                العودة للقائمة
            </a>
        </div>
    </div>

    <!-- Job Status -->
    <div class="glass-card rounded-xl p-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center gap-4">
                <div class="w-12 h-12 rounded-lg flex items-center justify-center {{ $job->is_active && $job->expires_at > now() ? 'bg-green-100 dark:bg-green-900/30' : 'bg-red-100 dark:bg-red-900/30' }}">
                    <i class="fas fa-{{ $job->is_active && $job->expires_at > now() ? 'check-circle text-green-600 dark:text-green-400' : 'times-circle text-red-600 dark:text-red-400' }}"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                        حالة الوظيفة: 
                        <span class="{{ $job->is_active && $job->expires_at > now() ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                            {{ $job->is_active && $job->expires_at > now() ? 'نشطة' : ($job->expires_at <= now() ? 'منتهية الصلاحية' : 'غير نشطة') }}
                        </span>
                    </h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        تم النشر: {{ $job->created_at->format('Y-m-d H:i') }} | 
                        ينتهي في: {{ $job->expires_at->format('Y-m-d H:i') }}
                    </p>
                </div>
            </div>

            @if($job->is_featured)
            <span class="px-4 py-2 bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400 rounded-full text-sm font-medium">
                <i class="fas fa-star mr-1"></i>
                وظيفة مميزة
            </span>
            @endif
        </div>
    </div>

    <!-- Job Details -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-8">
            <!-- Job Information -->
            <div class="glass-card rounded-xl p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
                    <i class="fas fa-info-circle text-blue-600"></i>
                    معلومات الوظيفة
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">التصنيف</label>
                        <p class="text-gray-900 dark:text-white">{{ $job->category->name ?? 'غير محدد' }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الموقع</label>
                        <p class="text-gray-900 dark:text-white">{{ $job->location }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع الوظيفة</label>
                        <p class="text-gray-900 dark:text-white">{{ getJobTypeInArabic($job->job_type) }}</p>
                    </div>

                    @if($job->salary_min || $job->salary_max)
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نطاق الراتب</label>
                        <p class="text-gray-900 dark:text-white font-semibold">{{ $job->salary_range }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Job Description -->
            <div class="glass-card rounded-xl p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
                    <i class="fas fa-file-alt text-green-600"></i>
                    وصف الوظيفة
                </h2>
                <div class="prose dark:prose-invert max-w-none">
                    <p class="text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-line">{{ $job->description }}</p>
                </div>
            </div>

            <!-- Job Requirements -->
            <div class="glass-card rounded-xl p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
                    <i class="fas fa-list-check text-purple-600"></i>
                    متطلبات الوظيفة
                </h2>
                <div class="prose dark:prose-invert max-w-none">
                    <p class="text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-line">{{ $job->requirements }}</p>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-8">
            <!-- Statistics -->
            <div class="glass-card rounded-xl p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
                    <i class="fas fa-chart-bar text-blue-600"></i>
                    إحصائيات الوظيفة
                </h2>

                <div class="space-y-4">
                    <div class="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <div class="flex items-center gap-2">
                            <i class="fas fa-eye text-blue-600 dark:text-blue-400"></i>
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">المشاهدات</span>
                        </div>
                        <span class="text-lg font-bold text-blue-600 dark:text-blue-400">{{ $stats['views'] }}</span>
                    </div>

                    <div class="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <div class="flex items-center gap-2">
                            <i class="fas fa-users text-green-600 dark:text-green-400"></i>
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">إجمالي الطلبات</span>
                        </div>
                        <span class="text-lg font-bold text-green-600 dark:text-green-400">{{ $stats['applications'] }}</span>
                    </div>

                    <div class="flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                        <div class="flex items-center gap-2">
                            <i class="fas fa-clock text-yellow-600 dark:text-yellow-400"></i>
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">قيد المراجعة</span>
                        </div>
                        <span class="text-lg font-bold text-yellow-600 dark:text-yellow-400">{{ $stats['pending'] }}</span>
                    </div>

                    <div class="flex items-center justify-between p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                        <div class="flex items-center gap-2">
                            <i class="fas fa-check text-purple-600 dark:text-purple-400"></i>
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">مقبولة</span>
                        </div>
                        <span class="text-lg font-bold text-purple-600 dark:text-purple-400">{{ $stats['accepted'] }}</span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="glass-card rounded-xl p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
                    <i class="fas fa-bolt text-yellow-600"></i>
                    إجراءات سريعة
                </h2>

                <div class="space-y-3">
                    <form action="{{ route('employer.jobs.toggle-status', $job) }}" method="POST">
                        @csrf
                        @method('PATCH')
                        <button type="submit" class="w-full px-4 py-3 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-lg hover:from-yellow-600 hover:to-orange-600 transition-all duration-300 flex items-center justify-center gap-2">
                            <i class="fas fa-{{ $job->is_active ? 'pause' : 'play' }}"></i>
                            {{ $job->is_active ? 'إلغاء التفعيل' : 'تفعيل الوظيفة' }}
                        </button>
                    </form>

                    <form action="{{ route('employer.jobs.duplicate', $job) }}" method="POST">
                        @csrf
                        <button type="submit" class="w-full px-4 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg hover:from-purple-600 hover:to-pink-600 transition-all duration-300 flex items-center justify-center gap-2">
                            <i class="fas fa-copy"></i>
                            نسخ الوظيفة
                        </button>
                    </form>

                    <form action="{{ route('employer.jobs.destroy', $job) }}" method="POST" onsubmit="return confirm('هل أنت متأكد من حذف هذه الوظيفة؟')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="w-full px-4 py-3 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg hover:from-red-600 hover:to-red-700 transition-all duration-300 flex items-center justify-center gap-2">
                            <i class="fas fa-trash"></i>
                            حذف الوظيفة
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Applications Section -->
    @if($job->applications->count() > 0)
    <div class="glass-card rounded-xl p-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
            <i class="fas fa-users text-green-600"></i>
            طلبات التوظيف ({{ $job->applications->count() }})
        </h2>

        <div class="overflow-x-auto">
            <table class="w-full">
                <thead>
                    <tr class="border-b border-gray-200 dark:border-gray-700">
                        <th class="text-right py-3 px-4 font-medium text-gray-700 dark:text-gray-300">المتقدم</th>
                        <th class="text-right py-3 px-4 font-medium text-gray-700 dark:text-gray-300">تاريخ التقديم</th>
                        <th class="text-right py-3 px-4 font-medium text-gray-700 dark:text-gray-300">الحالة</th>
                        <th class="text-right py-3 px-4 font-medium text-gray-700 dark:text-gray-300">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($job->applications as $application)
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                        <td class="py-4 px-4">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                                    {{ substr($application->user->name, 0, 1) }}
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900 dark:text-white">{{ $application->user->name }}</p>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">{{ $application->user->email }}</p>
                                </div>
                            </div>
                        </td>
                        <td class="py-4 px-4 text-gray-700 dark:text-gray-300">
                            {{ $application->created_at->format('Y-m-d H:i') }}
                        </td>
                        <td class="py-4 px-4">
                            <span class="px-3 py-1 text-xs font-medium rounded-full
                                {{ $application->status === 'pending' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400' : '' }}
                                {{ $application->status === 'reviewed' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400' : '' }}
                                {{ $application->status === 'accepted' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' : '' }}
                                {{ $application->status === 'rejected' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400' : '' }}">
                                {{ getApplicationStatusInArabic($application->status) }}
                            </span>
                        </td>
                        <td class="py-4 px-4">
                            <a href="#" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium">
                                عرض التفاصيل
                            </a>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
    @endif
</div>
@endsection
