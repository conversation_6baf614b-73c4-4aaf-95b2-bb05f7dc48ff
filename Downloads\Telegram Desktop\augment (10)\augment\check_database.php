<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

// تحميل Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

try {
    echo "=== فحص حالة قاعدة البيانات ===\n\n";
    
    // التحقق من الاتصال بقاعدة البيانات
    DB::connection()->getPdo();
    echo "✅ الاتصال بقاعدة البيانات: نجح\n";
    
    // التحقق من وجود جدول jobs
    if (Schema::hasTable('jobs')) {
        echo "✅ جدول jobs: موجود\n";
        
        // التحقق من الأعمدة
        $columns = [
            'salary_range' => 'الحقل القديم للراتب',
            'salary_min' => 'الحد الأدنى للراتب',
            'salary_max' => 'الحد الأعلى للراتب',
            'salary_currency' => 'عملة الراتب'
        ];
        
        echo "\n--- فحص أعمدة الراتب ---\n";
        foreach ($columns as $column => $description) {
            if (Schema::hasColumn('jobs', $column)) {
                echo "✅ $column ($description): موجود\n";
            } else {
                echo "❌ $column ($description): غير موجود\n";
            }
        }
        
        // عرض عدد الوظائف
        $jobsCount = DB::table('jobs')->count();
        echo "\n📊 عدد الوظائف في قاعدة البيانات: $jobsCount\n";
        
        // التحقق من الحقول الجديدة
        $hasNewFields = Schema::hasColumn('jobs', 'salary_min');
        if ($hasNewFields) {
            echo "\n🎉 الحقول الجديدة متوفرة! يمكن استخدام نظام الراتب المحسن.\n";
        } else {
            echo "\n⚠️  الحقول الجديدة غير متوفرة. يرجى تشغيل الـ migration:\n";
            echo "   php artisan migrate\n";
        }
        
    } else {
        echo "❌ جدول jobs: غير موجود\n";
        echo "يرجى تشغيل migrations: php artisan migrate\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في الاتصال بقاعدة البيانات:\n";
    echo $e->getMessage() . "\n";
    echo "\nيرجى التأكد من:\n";
    echo "1. تشغيل خادم MySQL\n";
    echo "2. صحة إعدادات قاعدة البيانات في ملف .env\n";
}

echo "\n=== انتهى الفحص ===\n";
