<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscriptions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('employer_id')->constrained()->onDelete('cascade');
            $table->string('package_type')->default('basic'); // basic, premium, enterprise
            $table->decimal('price', 8, 2)->default(0);
            $table->integer('job_limit')->default(3); // عدد الوظائف المسموح بها
            $table->integer('jobs_posted_this_month')->default(0); // عدد الوظائف المنشورة هذا الشهر
            $table->date('starts_at'); // تاريخ بداية الاشتراك
            $table->date('ends_at'); // تاريخ انتهاء الاشتراك
            $table->boolean('is_active')->default(true);
            $table->json('features')->nullable(); // الميزات المتاحة
            $table->timestamp('last_reset_at')->nullable(); // آخر إعادة تعيين للعداد الشهري
            $table->timestamps();
            
            // فهارس للبحث السريع
            $table->index(['employer_id', 'is_active']);
            $table->index(['package_type', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscriptions');
    }
};
