@extends('layouts.employer')

@section('title', 'تحليل المتقدمين - Hire Me')
@section('header_title', 'تحليل المتقدمين')

@section('content')
<div class="p-8 space-y-8">
        <!-- Header -->
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">تحليل المتقدمين</h1>
                <p class="text-gray-600 dark:text-gray-400 mt-2">تحليل بيانات المتقدمين للوظائف واتجاهات التقديم</p>
            </div>
            <div class="mt-4 md:mt-0 flex gap-3">
                <button onclick="showExportModal()" class="px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl flex items-center gap-2">
                    <i class="fas fa-download"></i>
                    <span>تصدير التحليل</span>
                </button>
                <button onclick="printAnalytics()" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors flex items-center gap-2">
                    <i class="fas fa-print"></i>
                    <span>طباعة</span>
                </button>
            </div>
        </div>

        <!-- Key Metrics -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <!-- Total Applicants -->
            <div class="glass-card rounded-xl p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي المتقدمين</p>
                        <p class="text-3xl font-bold text-blue-600 dark:text-blue-400 mt-2">{{ $totalApplicants }}</p>
                    </div>
                    <div class="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                        <i class="fas fa-user-friends text-blue-600 dark:text-blue-400 text-xl"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex items-center text-sm">
                        <span class="text-green-600 dark:text-green-400 font-medium">+12%</span>
                        <span class="text-gray-600 dark:text-gray-400 mr-2">من الشهر الماضي</span>
                    </div>
                </div>
            </div>

            <!-- Average Applications per Job -->
            <div class="glass-card rounded-xl p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">متوسط الطلبات لكل وظيفة</p>
                        <p class="text-3xl font-bold text-green-600 dark:text-green-400 mt-2">
                            {{ $applicationsByJob->count() > 0 ? round($applicationsByJob->avg('applications_count'), 1) : 0 }}
                        </p>
                    </div>
                    <div class="p-3 bg-green-100 dark:bg-green-900/30 rounded-full">
                        <i class="fas fa-chart-line text-green-600 dark:text-green-400 text-xl"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex items-center text-sm">
                        <span class="text-green-600 dark:text-green-400 font-medium">+8%</span>
                        <span class="text-gray-600 dark:text-gray-400 mr-2">من الشهر الماضي</span>
                    </div>
                </div>
            </div>

            <!-- Response Rate -->
            <div class="glass-card rounded-xl p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">معدل الاستجابة</p>
                        <p class="text-3xl font-bold text-purple-600 dark:text-purple-400 mt-2">85%</p>
                    </div>
                    <div class="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-full">
                        <i class="fas fa-reply text-purple-600 dark:text-purple-400 text-xl"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex items-center text-sm">
                        <span class="text-green-600 dark:text-green-400 font-medium">+5%</span>
                        <span class="text-gray-600 dark:text-gray-400 mr-2">من الشهر الماضي</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- Application Trend Chart -->
            <div class="glass-card rounded-xl p-6">
                <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">اتجاه الطلبات (آخر 30 يوم)</h3>
                <div class="h-64">
                    <canvas id="trendChart"></canvas>
                </div>
            </div>

            <!-- Top Jobs by Applications -->
            <div class="glass-card rounded-xl p-6">
                <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">أكثر الوظائف طلباً</h3>
                <div class="h-64">
                    <canvas id="topJobsChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Top Jobs Table -->
        <div class="glass-card rounded-xl overflow-hidden">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-bold text-gray-900 dark:text-white">أكثر الوظائف طلباً</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">عنوان الوظيفة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">عدد الطلبات</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">المشاهدات</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">معدل التحويل</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                        @forelse($applicationsByJob as $job)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900 dark:text-white">{{ $job->title }}</div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">{{ $job->location }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $job->applications_count }}</span>
                                    <div class="mr-3 w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $applicationsByJob->max('applications_count') > 0 ? min(($job->applications_count / $applicationsByJob->max('applications_count')) * 100, 100) : 0 }}%"></div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                {{ number_format($job->views ?? 0) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                {{ $job->views > 0 ? round(($job->applications_count / $job->views) * 100, 1) : 0 }}%
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="{{ route('employer.applications.index', ['job_id' => $job->id]) }}" class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300">
                                    عرض الطلبات
                                </a>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="5" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                                لا توجد بيانات متاحة
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Application Sources -->
        <div class="mt-8 glass-card rounded-xl p-6">
            <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">مصادر الطلبات</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">65%</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">البحث المباشر</div>
                </div>
                <div class="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400">25%</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">وسائل التواصل</div>
                </div>
                <div class="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">10%</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">مصادر أخرى</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Application Trend Chart
const trendCtx = document.getElementById('trendChart').getContext('2d');
const trendChart = new Chart(trendCtx, {
    type: 'line',
    data: {
        labels: [
            @foreach($applicationTrend as $trend)
                '{{ \Carbon\Carbon::parse($trend->date)->format('m/d') }}',
            @endforeach
        ],
        datasets: [{
            label: 'الطلبات اليومية',
            data: [
                @foreach($applicationTrend as $trend)
                    {{ $trend->count }},
                @endforeach
            ],
            borderColor: '#3B82F6',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});

// Top Jobs Chart
const topJobsCtx = document.getElementById('topJobsChart').getContext('2d');
const topJobsChart = new Chart(topJobsCtx, {
    type: 'horizontalBar',
    data: {
        labels: [
            @foreach($applicationsByJob->take(5) as $job)
                '{{ Str::limit($job->title, 25) }}',
            @endforeach
        ],
        datasets: [{
            label: 'عدد الطلبات',
            data: [
                @foreach($applicationsByJob->take(5) as $job)
                    {{ $job->applications_count }},
                @endforeach
            ],
            backgroundColor: [
                '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'
            ],
            borderRadius: 4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        indexAxis: 'y',
        scales: {
            x: {
                beginAtZero: true
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});

function showExportModal() {
    console.log('Export button clicked!');
    document.getElementById('exportModal').classList.remove('hidden');
}

function hideExportModal() {
    document.getElementById('exportModal').classList.add('hidden');
}

// إغلاق modal عند النقر خارجه
document.getElementById('exportModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideExportModal();
    }
});

function startExport() {
    const selectedFormat = document.querySelector('input[name="export_format"]:checked').value;
    hideExportModal();

    console.log('Exporting as:', selectedFormat);

    let format = 'csv';
    if (selectedFormat === 'pdf') {
        format = 'pdf';
    } else if (selectedFormat === 'csv_filtered') {
        format = 'csv';
    }

    // استخدام window.open للتحميل المباشر
    const url = '/reports/applicant-analytics/export?format=' + format;
    console.log('Export URL:', url);

    window.open(url, '_blank');
}

// دالة لتحديد الخيار
function selectOption(value) {
    // إلغاء تحديد جميع الخيارات
    document.querySelectorAll('input[name="export_format"]').forEach(radio => {
        radio.checked = false;
    });

    // تحديد الخيار المطلوب
    document.querySelector(`input[name="export_format"][value="${value}"]`).checked = true;

    // تحديث المؤشرات البصرية
    updateRadioButtons();
}

// تحديث الـ radio buttons بصرياً
function updateRadioButtons() {
    const options = ['csv', 'csv_filtered', 'pdf'];

    options.forEach(option => {
        const radio = document.querySelector(`input[name="export_format"][value="${option}"]`);
        const indicator = document.getElementById(`indicator-${option}`);

        if (radio && indicator) {
            if (radio.checked) {
                indicator.className = 'w-4 h-4 rounded-full border-2 border-red-600 bg-red-600 flex items-center justify-center mr-3';
                indicator.innerHTML = '<div class="w-2 h-2 rounded-full bg-white"></div>';
            } else {
                indicator.className = 'w-4 h-4 rounded-full border-2 border-gray-300 dark:border-gray-600 mr-3';
                indicator.innerHTML = '';
            }
        }
    });
}

document.addEventListener('DOMContentLoaded', function() {
    // تحديث أولي
    updateRadioButtons();
});

function printAnalytics() {
    window.print();
}
</script>

@push('styles')
<style>
@media print {
    .no-print {
        display: none !important;
    }
}
</style>
@endpush

<!-- Export Modal -->
<div id="exportModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-lg w-full mx-4" onclick="event.stopPropagation()">
        <!-- Header -->
        <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">تصدير تحليل المتقدمين</h3>
            <button onclick="hideExportModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <!-- Body -->
        <div class="p-6">
            <div class="mb-6">
                <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4">اختر صيغة التصدير</h4>
                <div class="space-y-3">
                    <label class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors" onclick="selectOption('csv')">
                        <input type="radio" name="export_format" value="csv" checked class="hidden">
                        <div class="flex-1 text-right">
                            <div class="text-gray-900 dark:text-white font-medium">CSV (Excel) - جميع البيانات</div>
                        </div>
                        <div class="w-4 h-4 rounded-full border-2 border-red-600 bg-red-600 flex items-center justify-center mr-3" id="indicator-csv">
                            <div class="w-2 h-2 rounded-full bg-white"></div>
                        </div>
                    </label>

                    <label class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors" onclick="selectOption('csv_filtered')">
                        <input type="radio" name="export_format" value="csv_filtered" class="hidden">
                        <div class="flex-1 text-right">
                            <div class="text-gray-900 dark:text-white font-medium">CSV مع الفلاتر الحالية</div>
                        </div>
                        <div class="w-4 h-4 rounded-full border-2 border-gray-300 dark:border-gray-600 mr-3" id="indicator-csv_filtered"></div>
                    </label>

                    <label class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors" onclick="selectOption('pdf')">
                        <input type="radio" name="export_format" value="pdf" class="hidden">
                        <div class="flex-1 text-right">
                            <div class="text-gray-900 dark:text-white font-medium">PDF - تقرير مفصل</div>
                        </div>
                        <div class="w-4 h-4 rounded-full border-2 border-gray-300 dark:border-gray-600 mr-3" id="indicator-pdf"></div>
                    </label>
                </div>
            </div>

            <!-- Info Box -->
            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <div class="flex items-start">
                    <i class="fas fa-info-circle text-blue-500 mt-1 ml-2"></i>
                    <div class="text-sm text-blue-700 dark:text-blue-300">
                        <div class="font-medium mb-2">سيتم تصدير:</div>
                        <ul class="space-y-1 text-xs">
                            <li>• إحصائيات المتقدمين الشاملة</li>
                            <li>• اتجاهات الطلبات خلال 30 يوم</li>
                            <li>• أكثر الوظائف طلباً</li>
                            <li>• تحليل أداء الوظائف</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="flex gap-3 p-6 border-t border-gray-200 dark:border-gray-700">
            <button onclick="hideExportModal()" class="flex-1 px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors font-medium">
                إلغاء
            </button>
            <button onclick="startExport()" class="flex-1 px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors flex items-center justify-center gap-2 font-medium">
                <i class="fas fa-download"></i>
                تصدير
            </button>
        </div>
    </div>
</div>
@endsection
