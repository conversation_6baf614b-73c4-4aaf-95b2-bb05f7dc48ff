<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Employer extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'company_name',
        'company_logo',
        'company_description',
        'industry',
        'website',
        'location',
        'company_size',
        'founded_year',
        'package_id', // الباقة الحالية
    ];

    /**
     * Get the user that owns the employer profile.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the jobs for the employer.
     */
    public function jobs(): HasMany
    {
        return $this->hasMany(Job::class);
    }

    /**
     * Get the payments for the employer.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get the profile ratings given by this employer.
     */
    public function profileRatings(): HasMany
    {
        return $this->hasMany(ProfileRating::class);
    }

    /**
     * Get the company ratings received by this employer.
     */
    public function companyRatings(): HasMany
    {
        return $this->hasMany(CompanyRating::class);
    }

    /**
     * Get the subscriptions for this employer.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Get the saved candidates for this employer.
     */
    public function savedCandidates(): HasMany
    {
        return $this->hasMany(SavedCandidate::class);
    }

    /**
     * Get the current active subscription.
     */
    public function currentSubscription()
    {
        return $this->subscriptions()
                    ->active()
                    ->latest()
                    ->first();
    }

    /**
     * Get the average rating for this employer's company.
     */
    public function getAverageRatingAttribute()
    {
        return $this->companyRatings()->avg('rating') ?: 0;
    }

    /**
     * Get the company name with fallback to user name.
     */
    public function getCompanyNameAttribute($value)
    {
        return $value ?: ($this->user ? $this->user->name . ' - شركة' : 'شركة غير محددة');
    }

    /**
     * Get accurate statistics for this employer.
     */
    public function getStatistics()
    {
        // إصلاح تاريخ التسجيل إذا كان خاطئاً
        $registrationDate = $this->getCorrectRegistrationDate();

        return [
            'total_jobs' => $this->jobs()->where('is_active', true)->count(),
            'active_jobs' => $this->jobs()->where('is_active', true)->where('expires_at', '>', now())->count(),
            'expired_jobs' => $this->jobs()->where('is_active', true)->where('expires_at', '<=', now())->count(),
            'draft_jobs' => $this->jobs()->where('is_active', false)->count(),
            'total_applications' => \App\Models\Application::whereHas('job', function($query) {
                $query->where('employer_id', $this->id);
            })->count(),
            'pending_applications' => \App\Models\Application::whereHas('job', function($query) {
                $query->where('employer_id', $this->id);
            })->where('status', 'pending')->count(),
            'total_views' => $this->jobs()->sum('views') ?: 0,
            'days_since_registration' => (int) $registrationDate->diffInDays(now()),
            'registration_date' => $registrationDate->format('Y-m-d'),
        ];
    }

    /**
     * Get the correct registration date (employer creation or user creation)
     */
    private function getCorrectRegistrationDate()
    {
        // استخدام تاريخ إنشاء المستخدم كأساس للحساب
        $userCreatedAt = $this->user ? $this->user->created_at : null;
        $employerCreatedAt = $this->created_at;

        // إذا كان تاريخ المستخدم موجود، استخدمه
        if ($userCreatedAt) {
            return $userCreatedAt;
        }

        // وإلا استخدم تاريخ صاحب العمل إذا كان منطقياً
        if ($employerCreatedAt && !$employerCreatedAt->isFuture()) {
            return $employerCreatedAt;
        }

        // في الحالات الأخرى، استخدم تاريخ اليوم
        return now();
    }
}
