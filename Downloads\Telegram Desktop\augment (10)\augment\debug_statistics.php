<?php

// ملف تصحيح للتحقق من مشكلة الإحصائيات

require_once 'vendor/autoload.php';

// تحميل إعدادات Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use App\Models\Employer;
use App\Models\JobSeeker;
use App\Models\Job;
use App\Models\Application;
use Illuminate\Support\Facades\Auth;

echo "=== تصحيح مشكلة الإحصائيات ===\n\n";

try {
    // 1. التحقق من البيانات الأساسية
    echo "1. فحص البيانات الأساسية:\n";
    echo "   المستخدمين: " . User::count() . "\n";
    echo "   أصحاب العمل: " . Employer::count() . "\n";
    echo "   الباحثين عن عمل: " . JobSeeker::count() . "\n";
    echo "   الوظائف: " . Job::count() . "\n";
    echo "   الطلبات: " . Application::count() . "\n\n";

    // 2. التحقق من أصحاب العمل
    echo "2. فحص أصحاب العمل:\n";
    $employers = Employer::with('user')->get();
    foreach ($employers as $employer) {
        echo "   ID: {$employer->id}, الشركة: {$employer->company_name}\n";
        echo "   المستخدم: " . ($employer->user ? $employer->user->name : 'غير موجود') . "\n";
        echo "   الوظائف: " . $employer->jobs()->count() . "\n";
        
        $applications = Application::whereHas('job', function($query) use ($employer) {
            $query->where('employer_id', $employer->id);
        })->count();
        echo "   الطلبات: {$applications}\n\n";
    }

    // 3. محاكاة تسجيل دخول صاحب عمل
    echo "3. محاكاة تسجيل دخول صاحب عمل:\n";
    $employerUser = User::where('role', 'employer')->first();
    if ($employerUser) {
        echo "   تم العثور على مستخدم صاحب عمل: {$employerUser->name}\n";
        
        // محاكاة Auth::user()
        Auth::login($employerUser);
        $user = Auth::user();
        $employer = $user->employer;
        
        if ($employer) {
            echo "   ملف صاحب العمل موجود: {$employer->company_name}\n";
            
            // تطبيق نفس الكود من DashboardController
            $jobStats = [
                'total' => Job::where('employer_id', $employer->id)->count(),
                'active' => Job::where('employer_id', $employer->id)->where('is_active', true)->count(),
                'expired' => Job::where('employer_id', $employer->id)->where('expires_at', '<', now())->count(),
                'draft' => Job::where('employer_id', $employer->id)->where('is_active', false)->count(),
            ];
            
            $applicationStats = [
                'total' => Application::whereHas('job', function($query) use ($employer) {
                    $query->where('employer_id', $employer->id);
                })->count(),
                'pending' => Application::whereHas('job', function($query) use ($employer) {
                    $query->where('employer_id', $employer->id);
                })->where('status', 'pending')->count(),
                'reviewed' => Application::whereHas('job', function($query) use ($employer) {
                    $query->where('employer_id', $employer->id);
                })->where('status', 'reviewed')->count(),
                'accepted' => Application::whereHas('job', function($query) use ($employer) {
                    $query->where('employer_id', $employer->id);
                })->where('status', 'accepted')->count(),
                'rejected' => Application::whereHas('job', function($query) use ($employer) {
                    $query->where('employer_id', $employer->id);
                })->where('status', 'rejected')->count(),
            ];
            
            echo "   إحصائيات الوظائف:\n";
            foreach ($jobStats as $key => $value) {
                echo "     {$key}: {$value}\n";
            }
            
            echo "   إحصائيات الطلبات:\n";
            foreach ($applicationStats as $key => $value) {
                echo "     {$key}: {$value}\n";
            }
        } else {
            echo "   ملف صاحب العمل غير موجود!\n";
        }
    } else {
        echo "   لم يتم العثور على مستخدم صاحب عمل\n";
    }

    // 4. فحص الوظائف والطلبات بالتفصيل
    echo "\n4. فحص الوظائف والطلبات بالتفصيل:\n";
    $jobs = Job::with('applications')->get();
    foreach ($jobs as $job) {
        echo "   الوظيفة: {$job->title} (ID: {$job->id})\n";
        echo "   صاحب العمل: {$job->employer_id}\n";
        echo "   نشطة: " . ($job->is_active ? 'نعم' : 'لا') . "\n";
        echo "   عدد الطلبات: " . $job->applications->count() . "\n";
        
        foreach ($job->applications as $app) {
            echo "     طلب ID: {$app->id}, الحالة: {$app->status}\n";
        }
        echo "\n";
    }

} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage() . "\n";
    echo "الملف: " . $e->getFile() . "\n";
    echo "السطر: " . $e->getLine() . "\n";
}
