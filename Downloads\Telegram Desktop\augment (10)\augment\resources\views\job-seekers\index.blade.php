@extends('layouts.app')

@section('content')
<!-- Hero Section -->
<div class="gradient-bg py-16">
    <div class="p-6">
        <div class="max-w-7xl mx-auto text-center">
            <div class="animate-fade-in">
                <h1 class="text-4xl md:text-5xl font-bold text-white mb-6">
                    <i class="fas fa-users mr-3"></i>
                    المرشحون المميزون
                </h1>
                <p class="text-xl text-white/90 max-w-3xl mx-auto mb-8">
                    اكتشف أفضل المواهب والمرشحين المؤهلين الذين يبحثون عن فرص وظيفية جديدة
                </p>

                <!-- Quick Stats -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto">
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                        <div class="text-2xl font-bold text-white">{{ $jobSeekers->total() }}</div>
                        <div class="text-white/80 text-sm">مرشح متاح</div>
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                        <div class="text-2xl font-bold text-white">{{ \App\Models\JobSeeker::where('is_available', true)->count() }}</div>
                        <div class="text-white/80 text-sm">متاح للعمل</div>
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                        <div class="text-2xl font-bold text-white">{{ \App\Models\JobSeeker::whereNotNull('job_title')->count() }}</div>
                        <div class="text-white/80 text-sm">لديه خبرة</div>
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                        <div class="text-2xl font-bold text-white">{{ \App\Models\JobSeeker::whereNotNull('resume')->count() }}</div>
                        <div class="text-white/80 text-sm">لديه سيرة ذاتية</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter Section -->
<div class="bg-white dark:bg-gray-900 py-8 border-b border-gray-200 dark:border-gray-700">
    <div class="p-6">
        <div class="max-w-7xl mx-auto">
        
            <form method="GET" action="{{ route('job-seekers.index') }}" class="bg-white dark:bg-dark-card rounded-2xl shadow-lg p-6 border border-gray-100 dark:border-gray-700">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-search mr-2 text-primary"></i>
                            البحث
                        </label>
                        <input type="text" name="search" value="{{ request('search') }}"
                               placeholder="الاسم أو المسمى الوظيفي"
                               class="block w-full p-3 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-tools mr-2 text-primary"></i>
                            المهارات
                        </label>
                        <select name="skills" class="block w-full p-3 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary">
                            <option value="">جميع المهارات</option>
                            <option value="تطوير الويب" {{ request('skills') == 'تطوير الويب' ? 'selected' : '' }}>تطوير الويب</option>
                            <option value="تصميم" {{ request('skills') == 'تصميم' ? 'selected' : '' }}>تصميم</option>
                            <option value="تسويق" {{ request('skills') == 'تسويق' ? 'selected' : '' }}>تسويق</option>
                            <option value="إدارة" {{ request('skills') == 'إدارة' ? 'selected' : '' }}>إدارة</option>
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button type="submit" class="w-full gradient-bg text-white py-3 px-6 rounded-lg hover:opacity-90 transition-opacity font-medium">
                            <i class="fas fa-search mr-2"></i>
                            بحث
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Candidates Section -->
<div class="py-16 bg-gray-50 dark:bg-gray-900">
    <div class="p-6">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-users text-primary mr-3"></i>
                    جميع المرشحين
                </h2>
                <p class="text-lg text-gray-600 dark:text-gray-400">تصفح المرشحين واكتشف المواهب المناسبة لشركتك</p>
            </div>
        
            <!-- Candidates Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @forelse($jobSeekers as $jobSeeker)
                    <div class="bg-white dark:bg-dark-card rounded-2xl shadow-sm hover:shadow-xl hover:scale-105 transition-all duration-300 p-6 border border-gray-100 dark:border-gray-700 group">
                        <!-- Candidate Header -->
                        <div class="flex items-center mb-6">
                            <div class="w-16 h-16 gradient-bg rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300 mr-4">
                                @if($jobSeeker->user->avatar)
                                    <img src="{{ asset('storage/' . $jobSeeker->user->avatar) }}"
                                         alt="{{ $jobSeeker->user->name }}"
                                         class="w-14 h-14 rounded-xl object-cover">
                                @else
                                    <i class="fas fa-user text-2xl text-white"></i>
                                @endif
                            </div>
                            <div class="flex-1">
                                <h3 class="text-xl font-bold text-gray-900 dark:text-white group-hover:text-primary transition-colors duration-300">
                                    {{ $jobSeeker->user->name }}
                                </h3>
                                <p class="text-primary font-medium">{{ $jobSeeker->job_title ?: 'باحث عن عمل' }}</p>
                            </div>
                        </div>

                        <!-- Skills -->
                        @if($jobSeeker->skills)
                            <div class="mb-4">
                                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
                                    <i class="fas fa-tools text-primary mr-2"></i>
                                    المهارات
                                </h4>
                                <div class="flex flex-wrap gap-2">
                                    @foreach(array_slice(explode(',', $jobSeeker->skills), 0, 4) as $skill)
                                        @if(trim($skill))
                                            <span class="px-3 py-1 bg-primary/10 text-primary rounded-full text-xs font-medium">
                                                {{ trim($skill) }}
                                            </span>
                                        @endif
                                    @endforeach
                                    @if(count(explode(',', $jobSeeker->skills)) > 4)
                                        <span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 rounded-full text-xs">
                                            +{{ count(explode(',', $jobSeeker->skills)) - 4 }} أخرى
                                        </span>
                                    @endif
                                </div>
                            </div>
                        @endif

                        <!-- Info Tags -->
                        <div class="flex flex-wrap gap-2 mb-6">
                            @if($jobSeeker->location)
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">
                                    <i class="fas fa-map-marker-alt mr-1"></i>
                                    {{ $jobSeeker->location }}
                                </span>
                            @endif

                            @if($jobSeeker->is_available)
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    متاح للعمل
                                </span>
                            @endif
                        </div>

                        <!-- Experience Preview -->
                        @if($jobSeeker->experience)
                            <p class="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2">
                                {{ Str::limit($jobSeeker->experience, 100) }}
                            </p>
                        @endif

                        <!-- Footer -->
                        <div class="flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-700">
                            <div class="flex items-center gap-2">
                                @if($jobSeeker->resume)
                                    <span class="text-green-600 dark:text-green-400 text-sm font-medium">
                                        <i class="fas fa-file-pdf mr-1"></i>
                                        لديه سيرة ذاتية
                                    </span>
                                @else
                                    <span class="text-gray-500 dark:text-gray-400 text-sm">لا توجد سيرة ذاتية</span>
                                @endif
                            </div>
                            <a href="{{ route('job-seekers.show', $jobSeeker) }}" class="gradient-bg text-white px-4 py-2 rounded-lg hover:opacity-90 hover:scale-105 transition-all duration-300 text-sm font-medium shadow-lg">
                                <i class="fas fa-eye mr-1"></i>
                                عرض الملف
                            </a>
                        </div>
                    </div>
                @empty
                    <div class="col-span-full">
                        <div class="bg-white dark:bg-dark-card rounded-2xl shadow-sm p-16 text-center border border-gray-100 dark:border-gray-700">
                            <div class="w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-6">
                                <i class="fas fa-users text-4xl text-gray-400 dark:text-gray-600"></i>
                            </div>
                            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">لا يوجد مرشحين متاحين</h3>
                            <p class="text-gray-600 dark:text-gray-400 mb-8 max-w-md mx-auto">
                                لم يتم العثور على مرشحين يطابقون معايير البحث. جرب تعديل المعايير أو تصفح جميع المرشحين
                            </p>
                            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                                <a href="{{ route('job-seekers.index') }}" class="gradient-bg text-white px-6 py-3 rounded-lg hover:opacity-90 transition-opacity font-medium">
                                    <i class="fas fa-refresh mr-2"></i>
                                    عرض جميع المرشحين
                                </a>
                                <a href="{{ route('home') }}" class="bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors font-medium">
                                    <i class="fas fa-home mr-2"></i>
                                    العودة للرئيسية
                                </a>
                            </div>
                        </div>
                    </div>
                @endforelse
        </div>

            <!-- Pagination -->
            @if($jobSeekers->hasPages())
                <div class="mt-12 flex justify-center">
                    <div class="bg-white dark:bg-dark-card rounded-2xl shadow-sm p-4 border border-gray-100 dark:border-gray-700">
                        {{ $jobSeekers->links() }}
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Call to Action Section -->
<div class="gradient-bg py-16">
    <div class="p-6">
        <div class="max-w-4xl mx-auto text-center">
            <h2 class="text-3xl font-bold text-white mb-4">
                هل تبحث عن عمل؟
            </h2>
            <p class="text-xl text-white/90 mb-8">
                انضم إلى منصتنا وأنشئ ملفك الشخصي للحصول على أفضل الفرص الوظيفية
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                @guest
                    <a href="{{ route('register') }}" class="bg-white text-primary px-8 py-4 rounded-lg hover:bg-gray-100 transition-colors font-bold text-lg shadow-lg">
                        <i class="fas fa-user-plus mr-2"></i>
                        إنشاء حساب جديد
                    </a>
                    <a href="{{ route('login') }}" class="bg-white/10 backdrop-blur-sm text-white px-8 py-4 rounded-lg hover:bg-white/20 transition-colors font-medium text-lg border border-white/20">
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        تسجيل الدخول
                    </a>
                @else
                    @if(auth()->user()->role === 'job_seeker')
                        <a href="{{ route('job-seeker.profile') }}" class="bg-white text-primary px-8 py-4 rounded-lg hover:bg-gray-100 transition-colors font-bold text-lg shadow-lg">
                            <i class="fas fa-user mr-2"></i>
                            إدارة الملف الشخصي
                        </a>
                    @else
                        <a href="{{ route('jobs.index') }}" class="bg-white text-primary px-8 py-4 rounded-lg hover:bg-gray-100 transition-colors font-bold text-lg shadow-lg">
                            <i class="fas fa-briefcase mr-2"></i>
                            تصفح الوظائف
                        </a>
                    @endif
                @endguest
            </div>
        </div>
    </div>
</div>
@endsection
