<!-- resources/views/layouts/header.blade.php -->
<!-- Header -->
<header class="bg-white dark:bg-dark-bg shadow-md sticky top-0 z-50">
    <div class="container mx-auto px-4 py-3 flex items-center justify-between">
        <div class="flex items-center gap-4">
            <div class="flex items-center">
                <a href="{{ route('home') }}" class="text-primary dark:text-white text-3xl font-bold hover:opacity-90 transition">
                    <i class="fas fa-briefcase ml-2"></i>
                    <span>Hire Me</span>
                </a>
            </div>

            <!-- Desktop Navigation -->
            <nav class="hidden md:flex items-center space-x-8 space-x-reverse mr-10">
                <a href="{{ route('home') }}" class="text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-white font-medium">الرئيسية</a>
                <a href="{{ route('jobs.index') }}" class="text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-white font-medium">الوظائف</a>
                <a href="{{ route('employers.index') }}" class="text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-white font-medium">الشركات</a>
                <a href="{{ route('job-seekers.index') }}" class="text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-white font-medium">المرشحين</a>
                @auth
                    @php
                        $notificationService = app(App\Services\NotificationService::class);
                        $unreadCount = $notificationService->getUnreadCount(auth()->user());
                    @endphp
                    <a href="{{ route('notifications.index') }}" class="relative text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-white font-medium">
                        الإشعارات
                        @if($unreadCount > 0)
                            <span class="absolute -top-2 -right-2 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                                {{ $unreadCount > 9 ? '9+' : $unreadCount }}
                            </span>
                        @endif
                    </a>
                @endauth
                <a href="{{ route('about') }}" class="text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-white font-medium">من نحن</a>
            </nav>
        </div>

        <div class="flex items-center gap-4">
            <a href="{{ route('setup') }}" class="p-3 rounded-xl bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-400 hover:bg-orange-200 dark:hover:bg-orange-800 transition-all duration-300 shadow-lg hover:shadow-xl" title="إعداد النظام">
                <i class="fas fa-cog text-lg"></i>
            </a>

            <button id="darkModeToggle" class="relative p-3 rounded-xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-300 shadow-lg hover:shadow-xl cursor-pointer" style="z-index: 9999; pointer-events: auto;" title="تبديل الوضع الليلي/النهاري">
                <i class="fas fa-moon dark:hidden text-slate-600 transition-colors text-lg"></i>
                <i class="fas fa-sun hidden dark:block text-yellow-400 transition-colors text-lg"></i>
            </button>

            <!-- Notifications for authenticated users -->
            @auth
                @include('components.notification-dropdown')
            @endauth

            <div class="hidden md:flex items-center gap-4">
                @guest
                    <a href="{{ route('login') }}" class="px-4 py-2 text-primary dark:text-white border border-primary dark:border-gray-600 rounded-lg hover:bg-primary hover:text-white transition">تسجيل دخول</a>
                    <a href="{{ route('register') }}" class="px-4 py-2 gradient-bg text-white rounded-lg hover:opacity-90 transition">إنشاء حساب</a>
                @else
                    <div class="relative">
                        <button id="userMenuBtn" class="flex items-center gap-2 px-4 py-2 text-primary dark:text-white border border-primary dark:border-gray-600 rounded-lg hover:bg-primary hover:text-white transition">
                            <span>{{ Auth::user()->name }}</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                        <div id="userMenuDropdown" class="absolute left-0 mt-2 w-48 bg-white dark:bg-dark-card rounded-lg shadow-lg py-2 z-10 hidden">
                            @if(Auth::user()->isJobSeeker())
                                <a href="{{ route('job-seeker.profile') }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800">
                                    <i class="fas fa-user-circle ml-2"></i>الملف الشخصي
                                </a>
                                <a href="{{ route('job-seeker.applications') }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800">
                                    <i class="fas fa-file-alt ml-2"></i>طلباتي
                                </a>
                            @elseif(Auth::user()->isEmployer())
                                <a href="{{ route('employer.profile') }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800">
                                    <i class="fas fa-user-circle ml-2"></i>الملف الشخصي
                                </a>
                                <a href="{{ route('employer.jobs.index') }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800">
                                    <i class="fas fa-briefcase ml-2"></i>وظائفي
                                </a>
                                <a href="{{ route('employer.applications.index') }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800">
                                    <i class="fas fa-users ml-2"></i>المتقدمين
                                </a>
                                <a href="{{ route('reports.dashboard') }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800">
                                    <i class="fas fa-chart-line ml-2"></i>لوحة التقارير
                                </a>
                                <a href="{{ route('reports.job-statistics') }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800">
                                    <i class="fas fa-chart-bar ml-2"></i>إحصائيات الوظائف
                                </a>
                                <a href="{{ route('reports.applicant-analytics') }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800">
                                    <i class="fas fa-analytics ml-2"></i>تحليل المتقدمين
                                </a>
                            @endif

                            <!-- رابط الإشعارات لجميع المستخدمين -->
                            @php
                                $notificationService = app(App\Services\NotificationService::class);
                                $unreadCount = $notificationService->getUnreadCount(auth()->user());
                            @endphp
                            <a href="{{ route('notifications.index') }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800">
                                <i class="fas fa-bell ml-2"></i>الإشعارات
                                @if($unreadCount > 0)
                                    <span class="inline-flex items-center justify-center w-5 h-5 mr-2 text-xs font-bold text-white bg-red-500 rounded-full">
                                        {{ $unreadCount > 9 ? '9+' : $unreadCount }}
                                    </span>
                                @endif
                            </a>

                            <!-- رابط إعدادات الإشعارات -->
                            <a href="{{ route('notifications.settings') }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800">
                                <i class="fas fa-cog ml-2"></i>إعدادات الإشعارات
                            </a>
                            <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>
                            <a href="{{ route('logout') }}" onclick="event.preventDefault(); document.getElementById('desktop-logout-form').submit();" class="block w-full text-right px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800">
                                <i class="fas fa-sign-out-alt ml-2"></i>تسجيل الخروج
                            </a>
                            <form id="desktop-logout-form" method="POST" action="{{ route('logout') }}" class="hidden">
                                @csrf
                            </form>
                        </div>
                    </div>
                @endguest
            </div>

            <!-- Mobile Menu Button -->
            <button id="mobileMenuBtn" class="md:hidden text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-white">
                <i class="fas fa-bars text-xl"></i>
            </button>
        </div>
    </div>

    <!-- Mobile Navigation -->
    <div id="mobileMenu" class="hidden md:hidden bg-white dark:bg-dark-bg shadow-md">
        <div class="container mx-auto px-4 py-3 space-y-4">
            <a href="{{ route('home') }}" class="block text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-white font-medium">الرئيسية</a>
            <a href="{{ route('jobs.index') }}" class="block text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-white font-medium">الوظائف</a>
            <a href="{{ route('employers.index') }}" class="block text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-white font-medium">الشركات</a>
            <a href="{{ route('job-seekers.index') }}" class="block text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-white font-medium">المرشحين</a>
            @auth
                @php
                    $notificationService = app(App\Services\NotificationService::class);
                    $unreadCount = $notificationService->getUnreadCount(auth()->user());
                @endphp
                <a href="{{ route('notifications.index') }}" class="relative block text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-white font-medium">
                    الإشعارات
                    @if($unreadCount > 0)
                        <span class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                            {{ $unreadCount > 9 ? '9+' : $unreadCount }}
                        </span>
                    @endif
                </a>
            @endauth
            <a href="{{ route('about') }}" class="block text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-white font-medium">من نحن</a>

            <div class="flex items-center gap-4 pt-4 border-t dark:border-gray-700">
                @guest
                    <a href="{{ route('login') }}" class="flex-1 px-4 py-2 text-center text-primary dark:text-white border border-primary dark:border-gray-600 rounded-lg hover:bg-primary hover:text-white transition">تسجيل دخول</a>
                    <a href="{{ route('register') }}" class="flex-1 px-4 py-2 text-center gradient-bg text-white rounded-lg hover:opacity-90 transition">إنشاء حساب</a>
                @else
                    <div class="w-full">
                        <button id="mobileUserMenuBtn" class="flex w-full items-center justify-between px-4 py-2 bg-gray-100 dark:bg-gray-800 rounded-lg mb-2">
                            <span class="font-medium">{{ Auth::user()->name }}</span>
                            <i class="fas fa-user-circle text-primary"></i>
                        </button>
                        <div id="mobileUserMenuContent" class="mt-2">

                        @if(Auth::user()->isJobSeeker())
                            <a href="{{ route('job-seeker.profile') }}" class="block w-full text-right px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg mb-2">
                                <i class="fas fa-user-circle ml-2"></i>الملف الشخصي
                            </a>
                            <a href="{{ route('job-seeker.applications') }}" class="block w-full text-right px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg mb-2">
                                <i class="fas fa-file-alt ml-2"></i>طلباتي
                            </a>
                        @elseif(Auth::user()->isEmployer())
                            <a href="{{ route('employer.profile') }}" class="block w-full text-right px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg mb-2">
                                <i class="fas fa-user-circle ml-2"></i>الملف الشخصي
                            </a>
                            <a href="{{ route('employer.jobs.index') }}" class="block w-full text-right px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg mb-2">
                                <i class="fas fa-briefcase ml-2"></i>وظائفي
                            </a>
                            <a href="{{ route('employer.applications.index') }}" class="block w-full text-right px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg mb-2">
                                <i class="fas fa-users ml-2"></i>المتقدمين
                            </a>
                            <a href="{{ route('reports.dashboard') }}" class="block w-full text-right px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg mb-2">
                                <i class="fas fa-chart-line ml-2"></i>لوحة التقارير
                            </a>
                            <a href="{{ route('reports.job-statistics') }}" class="block w-full text-right px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg mb-2">
                                <i class="fas fa-chart-bar ml-2"></i>إحصائيات الوظائف
                            </a>
                            <a href="{{ route('reports.applicant-analytics') }}" class="block w-full text-right px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg mb-2">
                                <i class="fas fa-analytics ml-2"></i>تحليل المتقدمين
                            </a>
                        @endif

                        <!-- روابط الإشعارات لجميع المستخدمين -->
                        @php
                            $notificationService = app(App\Services\NotificationService::class);
                            $unreadCount = $notificationService->getUnreadCount(auth()->user());
                        @endphp
                        <a href="{{ route('notifications.index') }}" class="block w-full text-right px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg mb-2">
                            <i class="fas fa-bell ml-2"></i>الإشعارات
                            @if($unreadCount > 0)
                                <span class="inline-flex items-center justify-center w-5 h-5 mr-2 text-xs font-bold text-white bg-red-500 rounded-full">
                                    {{ $unreadCount > 9 ? '9+' : $unreadCount }}
                                </span>
                            @endif
                        </a>
                        <a href="{{ route('notifications.settings') }}" class="block w-full text-right px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg mb-2">
                            <i class="fas fa-cog ml-2"></i>إعدادات الإشعارات
                        </a>

                        <a href="{{ route('logout') }}" onclick="event.preventDefault(); document.getElementById('mobile-logout-form').submit();" class="block w-full text-right px-4 py-2 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg">
                            <i class="fas fa-sign-out-alt ml-2"></i>تسجيل الخروج
                        </a>
                        <form id="mobile-logout-form" method="POST" action="{{ route('logout') }}" class="hidden">
                            @csrf
                        </form>
                        </div>
                    </div>
                @endguest
            </div>
        </div>
    </div>
</header>

<script>
    console.log('🚀 Header script loaded');

    // نسخة من نظام الثيم الذي يعمل في layout صاحب العمل
    function setupThemeToggle() {
        console.log('🔍 Looking for dark mode toggle...');
        const darkModeToggle = document.getElementById('darkModeToggle');

        if (darkModeToggle) {
            console.log('✅ Dark mode toggle found:', darkModeToggle);
            console.log('📍 Button position:', darkModeToggle.getBoundingClientRect());
            console.log('👆 Button clickable:', !darkModeToggle.disabled, 'visible:', darkModeToggle.offsetParent !== null);

            // إزالة أي معالجات سابقة
            darkModeToggle.replaceWith(darkModeToggle.cloneNode(true));
            const newToggle = document.getElementById('darkModeToggle');

            newToggle.addEventListener('click', function(e) {
                console.log('🔄 Toggle clicked!');
                e.preventDefault();
                e.stopPropagation();

                // تبديل الثيم
                const isDarkBefore = document.documentElement.classList.contains('dark');
                document.documentElement.classList.toggle('dark');
                const isDarkAfter = document.documentElement.classList.contains('dark');

                // حفظ الإعداد
                const newTheme = isDarkAfter ? 'dark' : 'light';
                localStorage.setItem('theme', newTheme);

                console.log('🎨 Theme changed from', isDarkBefore ? 'dark' : 'light', 'to', newTheme);

                // تأثير بصري
                this.style.transform = 'scale(0.9)';
                this.style.backgroundColor = isDarkAfter ? '#374151' : '#f3f4f6';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 100);
            });

            // اختبار النقر
            newToggle.addEventListener('mousedown', function() {
                console.log('👇 Mouse down on toggle');
            });

            newToggle.addEventListener('mouseup', function() {
                console.log('👆 Mouse up on toggle');
            });

        } else {
            console.log('❌ Dark mode toggle NOT found');
            console.log('🔍 Available elements with IDs:', Array.from(document.querySelectorAll('[id]')).map(el => el.id));
        }
    }

    // Mobile Menu Toggle
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const mobileMenu = document.getElementById('mobileMenu');

    if (mobileMenuBtn && mobileMenu) {
        mobileMenuBtn.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });
    }

    // تشغيل فوري لإعداد الثيم
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', setupThemeToggle);
    } else {
        setupThemeToggle();
    }

    // تشغيل إضافي بعد ثانية للتأكد
    setTimeout(setupThemeToggle, 1000);

    // User Menu Toggle
    const userMenuBtn = document.getElementById('userMenuBtn');
    const userMenuDropdown = document.getElementById('userMenuDropdown');

    if (userMenuBtn && userMenuDropdown) {
        // Toggle menu on button click
        userMenuBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            userMenuDropdown.classList.toggle('hidden');
        });

        // Keep menu open when clicking inside the dropdown
        userMenuDropdown.addEventListener('click', (e) => {
            e.stopPropagation();
        });

        // Close menu when clicking outside
        document.addEventListener('click', () => {
            userMenuDropdown.classList.add('hidden');
        });
    }

    // Mobile User Menu Toggle
    const mobileUserMenuBtn = document.getElementById('mobileUserMenuBtn');
    const mobileUserMenuContent = document.getElementById('mobileUserMenuContent');

    if (mobileUserMenuBtn && mobileUserMenuContent) {
        // Initially hide the mobile menu content
        mobileUserMenuContent.style.display = 'none';

        // Toggle mobile menu on button click
        mobileUserMenuBtn.addEventListener('click', () => {
            if (mobileUserMenuContent.style.display === 'none') {
                mobileUserMenuContent.style.display = 'block';
            } else {
                mobileUserMenuContent.style.display = 'none';
            }
        });
    }

    // معلومات تشخيصية إضافية (نسخة من layout صاحب العمل)
    setTimeout(() => {
        console.log('🔍 Final diagnostic:');
        console.log('- Document ready state:', document.readyState);
        console.log('- Dark mode toggle exists:', !!document.getElementById('darkModeToggle'));
        console.log('- Current theme class:', document.documentElement.classList.contains('dark') ? 'dark' : 'light');
        console.log('- Saved theme:', localStorage.getItem('theme'));

        const toggle = document.getElementById('darkModeToggle');
        if (toggle) {
            console.log('- Toggle visible:', toggle.offsetParent !== null);
            console.log('- Toggle disabled:', toggle.disabled);
            console.log('- Toggle style display:', getComputedStyle(toggle).display);
            console.log('- Toggle style pointer-events:', getComputedStyle(toggle).pointerEvents);
        }
    }, 2000);
</script>
