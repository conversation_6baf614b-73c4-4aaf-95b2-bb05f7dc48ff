/**
 * نظام الثيم المحسن لموقع Hire Me
 * يحفظ إعدادات الوضع الليلي/النهاري ويطبقها عبر جميع الصفحات
 */

class ThemeSystem {
    constructor() {
        this.storageKey = 'theme';
        this.init();
    }

    /**
     * تهيئة النظام
     */
    init() {
        console.log('Initializing theme system...');
        this.applyStoredTheme();
        this.setupToggleButtons();
        this.addTransitionEffects();
        console.log('Theme system initialization complete');
    }

    /**
     * تطبيق الثيم المحفوظ
     */
    applyStoredTheme() {
        const savedTheme = localStorage.getItem(this.storageKey);
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

        // تطبيق الثيم المحفوظ أو الافتراضي
        if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
    }

    /**
     * تطبيق ثيم محدد
     */
    applyTheme(theme) {
        if (theme === 'dark') {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
        localStorage.setItem(this.storageKey, theme);

        // إرسال حدث للمكونات الأخرى
        window.dispatchEvent(new CustomEvent('themeChanged', {
            detail: { theme }
        }));
    }

    /**
     * تبديل الثيم
     */
    toggleTheme() {
        const isDark = document.documentElement.classList.contains('dark');
        const newTheme = isDark ? 'light' : 'dark';
        this.applyTheme(newTheme);
        return newTheme;
    }

    /**
     * الحصول على الثيم الحالي
     */
    getCurrentTheme() {
        return document.documentElement.classList.contains('dark') ? 'dark' : 'light';
    }

    /**
     * إعداد أزرار التبديل
     */
    setupToggleButtons() {
        // البحث عن جميع أزرار التبديل الممكنة
        const selectors = [
            '#darkModeToggle',
            '[data-theme-toggle]',
            '.theme-toggle',
            '.dark-mode-toggle'
        ];

        selectors.forEach(selector => {
            const buttons = document.querySelectorAll(selector);
            buttons.forEach(button => {
                if (button && !button.hasAttribute('data-theme-initialized')) {
                    button.setAttribute('data-theme-initialized', 'true');
                    button.addEventListener('click', (e) => {
                        e.preventDefault();
                        console.log('Theme toggle clicked');
                        const newTheme = this.toggleTheme();
                        this.addButtonEffect(button);

                        // تحديث أيقونة الزر إذا لزم الأمر
                        this.updateButtonIcon(button, newTheme);
                        console.log('Theme changed to:', newTheme);
                    });
                    console.log('Theme toggle button initialized:', selector);
                }
            });
        });

        // إعداد إضافي للأزرار التي قد تضاف لاحقاً
        setTimeout(() => {
            this.setupToggleButtons();
        }, 1000);
    }

    /**
     * إضافة تأثير بصري للزر
     */
    addButtonEffect(button) {
        button.style.transform = 'scale(0.95)';
        button.classList.add('animate-pulse');

        setTimeout(() => {
            button.style.transform = 'scale(1)';
            button.classList.remove('animate-pulse');
        }, 200);
    }

    /**
     * تحديث أيقونة الزر
     */
    updateButtonIcon(button, theme) {
        const moonIcon = button.querySelector('.fa-moon');
        const sunIcon = button.querySelector('.fa-sun');

        if (moonIcon && sunIcon) {
            if (theme === 'dark') {
                moonIcon.classList.add('hidden');
                moonIcon.classList.add('dark:hidden');
                sunIcon.classList.remove('hidden');
                sunIcon.classList.add('dark:block');
            } else {
                moonIcon.classList.remove('hidden');
                moonIcon.classList.remove('dark:hidden');
                sunIcon.classList.add('hidden');
                sunIcon.classList.remove('dark:block');
            }
            console.log('Button icon updated for theme:', theme);
        } else {
            console.log('Moon or sun icon not found in button');
        }
    }

    /**
     * إضافة تأثيرات الانتقال
     */
    addTransitionEffects() {
        // إضافة انتقال سلس للألوان
        const style = document.createElement('style');
        style.textContent = `
            * {
                transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
            }

            .theme-transition {
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * مراقبة تغييرات النظام
     */
    watchSystemChanges() {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        mediaQuery.addEventListener('change', (e) => {
            // تطبيق تغيير النظام فقط إذا لم يكن هناك إعداد محفوظ
            const savedTheme = localStorage.getItem(this.storageKey);
            if (!savedTheme) {
                this.applyTheme(e.matches ? 'dark' : 'light');
            }
        });
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.themeSystem = new ThemeSystem();
    console.log('Theme system initialized successfully');
});

// تهيئة إضافية للتأكد من عمل النظام
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initThemeSystem);
} else {
    initThemeSystem();
}

function initThemeSystem() {
    if (!window.themeSystem) {
        window.themeSystem = new ThemeSystem();
        console.log('Theme system initialized (fallback)');
    }
}

// تطبيق الثيم فوراً (قبل تحميل الصفحة كاملة)
(function() {
    const savedTheme = localStorage.getItem('theme');
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

    if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
        document.documentElement.classList.add('dark');
    } else {
        document.documentElement.classList.remove('dark');
    }
})();
