@extends('layouts.admin')

@section('title', 'تفاصيل الشركة - Hire Me')
@section('header_title', 'تفاصيل الشركة')

@section('content')
<div class="p-6">
    <!-- Company Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
            <h2 class="text-2xl font-bold">{{ $company->name ?? 'شركة التقنية المتطورة' }}</h2>
            <p class="text-gray-600 dark:text-gray-400 mt-1">
                <span>{{ $company->industry ?? 'تقنية المعلومات' }}</span> • 
                <span>{{ $company->location ?? 'الرياض، المملكة العربية السعودية' }}</span>
            </p>
        </div>
        <div class="mt-4 md:mt-0 flex gap-3">
            <a href="{{ route('admin.companies.edit', $company->id ?? 1) }}" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors flex items-center gap-2">
                <i class="fas fa-edit"></i>
                <span>تعديل</span>
            </a>
            <button id="statusToggleBtn" class="px-4 py-2 gradient-bg text-white rounded-lg hover:opacity-90 transition-colors flex items-center gap-2">
                <i class="fas fa-{{ ($company->status ?? 'active') == 'active' ? 'ban' : 'check' }}"></i>
                <span>{{ ($company->status ?? 'active') == 'active' ? 'تعطيل الحساب' : 'تفعيل الحساب' }}</span>
            </button>
        </div>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Company Details -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Company Overview -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <div class="flex items-center gap-4 mb-6">
                    <div class="w-16 h-16 bg-{{ $company->color ?? 'blue' }}-100 dark:bg-{{ $company->color ?? 'blue' }}-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-building text-{{ $company->color ?? 'blue' }}-600 dark:text-{{ $company->color ?? 'blue' }}-400 text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold">{{ $company->name ?? 'شركة التقنية المتطورة' }}</h3>
                        <div class="flex items-center gap-2 mt-1">
                            <span class="px-2.5 py-0.5 {{ ($company->status ?? 'active') == 'active' ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400' : 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400' }} rounded-full text-xs">
                                {{ ($company->status ?? 'active') == 'active' ? 'نشط' : 'غير نشط' }}
                            </span>
                            <span class="px-2.5 py-0.5 
                                @if(($company->subscription_type ?? 'premium') == 'premium') bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400
                                @elseif(($company->subscription_type ?? '') == 'standard') bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400
                                @elseif(($company->subscription_type ?? '') == 'basic') bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400
                                @else bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400 @endif rounded-full text-xs">
                                {{ ($company->subscription_type ?? 'premium') == 'premium' ? 'متميز' : (($company->subscription_type ?? '') == 'standard' ? 'قياسي' : (($company->subscription_type ?? '') == 'basic' ? 'أساسي' : 'مجاني')) }}
                            </span>
                        </div>
                    </div>
                </div>
                
                <!-- Company Description -->
                <div class="mb-6">
                    <h4 class="text-md font-semibold mb-2">نبذة عن الشركة</h4>
                    <p class="text-gray-600 dark:text-gray-400">
                        {{ $company->description ?? 'شركة رائدة في مجال تقنية المعلومات، متخصصة في تطوير البرمجيات وتقديم الحلول التقنية المبتكرة للمؤسسات والشركات. تأسست الشركة في عام 2010 ومنذ ذلك الحين نمت لتصبح من الشركات الرائدة في المنطقة. تضم الشركة أكثر من 200 موظف من ذوي الخبرة والمهارات العالية، ولديها مكاتب في الرياض وجدة ودبي.' }}
                    </p>
                </div>
                
                <!-- Company Contact Details -->
                <div class="mb-6">
                    <h4 class="text-md font-semibold mb-4">معلومات الاتصال</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="flex items-start gap-3">
                            <div class="w-10 h-10 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-envelope text-primary"></i>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500 dark:text-gray-400">البريد الإلكتروني</p>
                                <p class="font-medium">{{ $company->email ?? '<EMAIL>' }}</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start gap-3">
                            <div class="w-10 h-10 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-phone text-primary"></i>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500 dark:text-gray-400">رقم الهاتف</p>
                                <p class="font-medium">{{ $company->phone ?? '+218 21 234 5678' }}</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start gap-3">
                            <div class="w-10 h-10 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-map-marker-alt text-primary"></i>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500 dark:text-gray-400">العنوان</p>
                                <p class="font-medium">{{ $company->address ?? 'شارع الجمهورية، حي الأندلس، طرابلس، ليبيا' }}</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start gap-3">
                            <div class="w-10 h-10 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-globe text-primary"></i>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500 dark:text-gray-400">الموقع الإلكتروني</p>
                                <a href="{{ $company->website ?? 'https://www.techcompany.com' }}" target="_blank" class="font-medium text-primary hover:underline">{{ $company->website ?? 'www.techcompany.com' }}</a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Social Media Links -->
                <div>
                    <h4 class="text-md font-semibold mb-4">روابط التواصل الاجتماعي</h4>
                    <div class="flex flex-wrap gap-3">
                        @if($company->linkedin ?? true)
                            <a href="{{ $company->linkedin_url ?? 'https://www.linkedin.com/company/techcompany' }}" target="_blank" class="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center text-blue-600 dark:text-blue-400 hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                        @endif
                        
                        @if($company->twitter ?? true)
                            <a href="{{ $company->twitter_url ?? 'https://twitter.com/techcompany' }}" target="_blank" class="w-10 h-10 bg-sky-100 dark:bg-sky-900/30 rounded-full flex items-center justify-center text-sky-600 dark:text-sky-400 hover:bg-sky-200 dark:hover:bg-sky-900/50 transition-colors">
                                <i class="fab fa-twitter"></i>
                            </a>
                        @endif
                        
                        @if($company->facebook ?? true)
                            <a href="{{ $company->facebook_url ?? 'https://www.facebook.com/techcompany' }}" target="_blank" class="w-10 h-10 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center text-indigo-600 dark:text-indigo-400 hover:bg-indigo-200 dark:hover:bg-indigo-900/50 transition-colors">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                        @endif
                        
                        @if($company->instagram ?? false)
                            <a href="{{ $company->instagram_url ?? 'https://www.instagram.com/techcompany' }}" target="_blank" class="w-10 h-10 bg-pink-100 dark:bg-pink-900/30 rounded-full flex items-center justify-center text-pink-600 dark:text-pink-400 hover:bg-pink-200 dark:hover:bg-pink-900/50 transition-colors">
                                <i class="fab fa-instagram"></i>
                            </a>
                        @endif
                    </div>
                </div>
            </div>
            
            <!-- Company Jobs -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-bold">الوظائف الحالية</h3>
                    <a href="{{ route('admin.jobs.index', ['company_id' => $company->id ?? 1]) }}" class="text-primary text-sm hover:underline">عرض الكل</a>
                </div>
                
                <div class="space-y-4">
                    @forelse($company->jobs ?? [] as $job)
                        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="w-10 h-10 {{ $job->color_class ?? 'gradient-bg' }} rounded-lg flex items-center justify-center text-white flex-shrink-0">
                                    <i class="fas fa-{{ $job->icon ?? 'code' }}"></i>
                                </div>
                                <div>
                                    <a href="{{ route('admin.jobs.show', $job->id) }}" class="font-medium text-lg hover:text-primary">{{ $job->title }}</a>
                                    <div class="flex items-center text-sm text-gray-500 dark:text-gray-400 gap-4 mt-1">
                                        <span><i class="fas fa-map-marker-alt ml-1"></i>{{ $job->location }}</span>
                                        <span><i class="fas fa-clock ml-1"></i>{{ $job->type }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex justify-between items-center">
                                <div>
                                    <span class="text-sm text-gray-500 dark:text-gray-400">تاريخ النشر: {{ $job->published_at }}</span>
                                </div>
                                <div>
                                    <span class="px-2.5 py-0.5 {{ $job->status == 'active' ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400' : 'bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400' }} rounded-full text-xs">{{ $job->status == 'active' ? 'نشط' : 'غير نشط' }}</span>
                                    <span class="px-2.5 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 rounded-full text-xs mr-1">{{ $job->applications_count }} طلب</span>
                                </div>
                            </div>
                        </div>
                    @empty
                        <!-- Sample jobs data -->
                        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="w-10 h-10 gradient-bg rounded-lg flex items-center justify-center text-white flex-shrink-0">
                                    <i class="fas fa-code"></i>
                                </div>
                                <div>
                                    <a href="{{ route('admin.jobs.show', 1) }}" class="font-medium text-lg hover:text-primary">مطور واجهات أمامية</a>
                                    <div class="flex items-center text-sm text-gray-500 dark:text-gray-400 gap-4 mt-1">
                                        <span><i class="fas fa-map-marker-alt ml-1"></i>الرياض</span>
                                        <span><i class="fas fa-clock ml-1"></i>دوام كامل</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex justify-between items-center">
                                <div>
                                    <span class="text-sm text-gray-500 dark:text-gray-400">تاريخ النشر: 15 يونيو، 2023</span>
                                </div>
                                <div>
                                    <span class="px-2.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">نشط</span>
                                    <span class="px-2.5 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 rounded-full text-xs mr-1">15 طلب</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center text-white flex-shrink-0">
                                    <i class="fas fa-paint-brush"></i>
                                </div>
                                <div>
                                    <a href="{{ route('admin.jobs.show', 2) }}" class="font-medium text-lg hover:text-primary">مصمم واجهات المستخدم</a>
                                    <div class="flex items-center text-sm text-gray-500 dark:text-gray-400 gap-4 mt-1">
                                        <span><i class="fas fa-map-marker-alt ml-1"></i>الرياض</span>
                                        <span><i class="fas fa-clock ml-1"></i>دوام كامل</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex justify-between items-center">
                                <div>
                                    <span class="text-sm text-gray-500 dark:text-gray-400">تاريخ النشر: 10 يونيو، 2023</span>
                                </div>
                                <div>
                                    <span class="px-2.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">نشط</span>
                                    <span class="px-2.5 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 rounded-full text-xs mr-1">8 طلبات</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center text-white flex-shrink-0">
                                    <i class="fas fa-mobile-alt"></i>
                                </div>
                                <div>
                                    <a href="{{ route('admin.jobs.show', 3) }}" class="font-medium text-lg hover:text-primary">مطور تطبيقات الجوال</a>
                                    <div class="flex items-center text-sm text-gray-500 dark:text-gray-400 gap-4 mt-1">
                                        <span><i class="fas fa-map-marker-alt ml-1"></i>جدة</span>
                                        <span><i class="fas fa-clock ml-1"></i>عن بعد</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex justify-between items-center">
                                <div>
                                    <span class="text-sm text-gray-500 dark:text-gray-400">تاريخ النشر: 5 يونيو، 2023</span>
                                </div>
                                <div>
                                    <span class="px-2.5 py-0.5 bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400 rounded-full text-xs">غير نشط</span>
                                    <span class="px-2.5 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 rounded-full text-xs mr-1">12 طلب</span>
                                </div>
                            </div>
                        </div>
                    @endforelse
                </div>
                
                <div class="mt-4 text-center">
                    <a href="{{ route('admin.jobs.create', ['company_id' => $company->id ?? 1]) }}" class="px-4 py-2 gradient-bg text-white rounded-lg hover:opacity-90 transition-colors inline-flex items-center gap-2">
                        <i class="fas fa-plus"></i>
                        <span>إضافة وظيفة جديدة</span>
                    </a>
                </div>
            </div>
            
            <!-- Company Activity Timeline -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">النشاط</h3>
                
                <div class="space-y-6">
                    @forelse($company->activities ?? [] as $activity)
                        <div class="flex gap-4">
                            <div class="w-10 h-10 rounded-full bg-{{ $activity->color }}-100 dark:bg-{{ $activity->color }}-900/30 flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-{{ $activity->icon }} text-{{ $activity->color }}-600 dark:text-{{ $activity->color }}-400"></i>
                            </div>
                            <div>
                                <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1">
                                    <h4 class="font-medium">{{ $activity->title }}</h4>
                                    <span class="text-xs text-gray-500 dark:text-gray-400">{{ $activity->date }}</span>
                                </div>
                                <p class="text-gray-600 dark:text-gray-400 mt-1">{{ $activity->description }}</p>
                            </div>
                        </div>
                    @empty
                        <!-- Sample activity data -->
                        <div class="flex gap-4">
                            <div class="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-user-plus text-blue-600 dark:text-blue-400"></i>
                            </div>
                            <div>
                                <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1">
                                    <h4 class="font-medium">تم تسجيل الشركة</h4>
                                    <span class="text-xs text-gray-500 dark:text-gray-400">15 يناير، 2023</span>
                                </div>
                                <p class="text-gray-600 dark:text-gray-400 mt-1">تم تسجيل الشركة واعتماد حسابها بنجاح</p>
                            </div>
                        </div>
                        
                        <div class="flex gap-4">
                            <div class="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-credit-card text-purple-600 dark:text-purple-400"></i>
                            </div>
                            <div>
                                <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1">
                                    <h4 class="font-medium">ترقية الاشتراك</h4>
                                    <span class="text-xs text-gray-500 dark:text-gray-400">1 فبراير، 2023</span>
                                </div>
                                <p class="text-gray-600 dark:text-gray-400 mt-1">تمت ترقية اشتراك الشركة إلى الباقة المتميزة</p>
                            </div>
                        </div>
                        
                        <div class="flex gap-4">
                            <div class="w-10 h-10 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-briefcase text-green-600 dark:text-green-400"></i>
                            </div>
                            <div>
                                <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1">
                                    <h4 class="font-medium">إضافة وظيفة جديدة</h4>
                                    <span class="text-xs text-gray-500 dark:text-gray-400">15 يونيو، 2023</span>
                                </div>
                                <p class="text-gray-600 dark:text-gray-400 mt-1">تمت إضافة وظيفة "مطور واجهات أمامية"</p>
                            </div>
                        </div>
                        
                        <div class="flex gap-4">
                            <div class="w-10 h-10 rounded-full bg-indigo-100 dark:bg-indigo-900/30 flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-file-alt text-indigo-600 dark:text-indigo-400"></i>
                            </div>
                            <div>
                                <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1">
                                    <h4 class="font-medium">تحديث معلومات الشركة</h4>
                                    <span class="text-xs text-gray-500 dark:text-gray-400">20 يوليو، 2023</span>
                                </div>
                                <p class="text-gray-600 dark:text-gray-400 mt-1">تم تحديث معلومات الشركة والوصف</p>
                            </div>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Company Stats -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">إحصائيات الشركة</h3>
                
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                                <i class="fas fa-briefcase text-blue-600 dark:text-blue-400"></i>
                            </div>
                            <div>
                                <p class="text-gray-500 dark:text-gray-400 text-xs">الوظائف النشطة</p>
                                <p class="font-bold">{{ $company->active_jobs ?? '12' }}</p>
                            </div>
                        </div>
                        <div class="text-green-600 dark:text-green-400 text-sm">
                            <i class="fas fa-arrow-up"></i>
                            <span>{{ $company->jobs_percentage ?? '20%' }}</span>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                                <i class="fas fa-file-alt text-green-600 dark:text-green-400"></i>
                            </div>
                            <div>
                                <p class="text-gray-500 dark:text-gray-400 text-xs">إجمالي الطلبات</p>
                                <p class="font-bold">{{ $company->total_applications ?? '143' }}</p>
                            </div>
                        </div>
                        <div class="text-green-600 dark:text-green-400 text-sm">
                            <i class="fas fa-arrow-up"></i>
                            <span>{{ $company->applications_percentage ?? '15%' }}</span>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
                                <i class="fas fa-user-check text-purple-600 dark:text-purple-400"></i>
                            </div>
                            <div>
                                <p class="text-gray-500 dark:text-gray-400 text-xs">التعيينات الجديدة</p>
                                <p class="font-bold">{{ $company->new_hires ?? '5' }}</p>
                            </div>
                        </div>
                        <div class="text-green-600 dark:text-green-400 text-sm">
                            <i class="fas fa-arrow-up"></i>
                            <span>{{ $company->hires_percentage ?? '50%' }}</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Subscription Details -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">تفاصيل الاشتراك</h3>
                
                <div class="space-y-4">
                    <div class="text-center">
                        <div class="w-16 h-16 rounded-full mx-auto
                            @if(($company->subscription_type ?? 'premium') == 'premium') bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400
                            @elseif(($company->subscription_type ?? '') == 'standard') bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400
                            @elseif(($company->subscription_type ?? '') == 'basic') bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400
                            @else bg-gray-100 dark:bg-gray-900/30 text-gray-600 dark:text-gray-400 @endif
                            flex items-center justify-center mb-2">
                            <i class="fas fa-crown text-xl"></i>
                        </div>
                        <h4 class="font-semibold">
                            @if(($company->subscription_type ?? 'premium') == 'premium') الباقة المتميزة
                            @elseif(($company->subscription_type ?? '') == 'standard') الباقة القياسية
                            @elseif(($company->subscription_type ?? '') == 'basic') الباقة الأساسية
                            @else الباقة المجانية @endif
                        </h4>
                    </div>
                    
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-4 space-y-3">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500 dark:text-gray-400">تاريخ البدء:</span>
                            <span>{{ $company->subscription_start ?? '1 فبراير، 2023' }}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500 dark:text-gray-400">تاريخ الانتهاء:</span>
                            <span>{{ $company->subscription_end ?? '1 فبراير، 2024' }}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500 dark:text-gray-400">حالة الاشتراك:</span>
                            <span class="text-green-600 dark:text-green-400">{{ $company->subscription_status ?? 'نشط' }}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500 dark:text-gray-400">الوظائف المسموحة:</span>
                            <span>{{ $company->subscription_jobs_limit ?? 'غير محدود' }}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500 dark:text-gray-400">الوظائف المميزة:</span>
                            <span>{{ $company->subscription_featured_jobs ?? '5' }} وظائف</span>
                        </div>
                    </div>
                    
                    <div class="pt-2">
                        <a href="{{ route('admin.settings.payment', ['company_id' => $company->id ?? 1]) }}" class="w-full px-4 py-2 gradient-bg text-white rounded-lg hover:opacity-90 transition-colors flex items-center gap-2 justify-center">
                            <i class="fas fa-edit"></i>
                            <span>تعديل الاشتراك</span>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Contact Person -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-bold mb-4">جهة الاتصال</h3>
                
                <div class="flex items-center gap-4 mb-4">
                    <div class="w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-user text-primary text-xl"></i>
                    </div>
                    <div>
                        <h4 class="font-semibold">{{ $company->contact_name ?? 'عبدالله الأحمد' }}</h4>
                        <p class="text-gray-500 dark:text-gray-400 text-sm">{{ $company->contact_position ?? 'مدير الموارد البشرية' }}</p>
                    </div>
                </div>
                
                <div class="space-y-3">
                    <div class="flex items-center gap-3">
                        <div class="w-8 text-center text-gray-500 dark:text-gray-400">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <p>{{ $company->contact_email ?? '<EMAIL>' }}</p>
                    </div>
                    
                    <div class="flex items-center gap-3">
                        <div class="w-8 text-center text-gray-500 dark:text-gray-400">
                            <i class="fas fa-phone"></i>
                        </div>
                        <p>{{ $company->contact_phone ?? '+966 50 123 4567' }}</p>
                    </div>
                </div>
            </div>
            
            <!-- Notes & Actions -->
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold">ملاحظات</h3>
                    <button class="text-primary hover:text-primary/80" id="editNotesBtn">
                        <i class="fas fa-edit"></i>
                    </button>
                </div>
                
                <div id="notesDisplay" class="text-gray-600 dark:text-gray-400 mb-6">
                    <p>{{ $company->notes ?? 'شركة مميزة ومتعاونة. تم التواصل معهم بخصوص ترقية اشتراكهم ورفع الحد الأقصى للوظائف المميزة. يفضلون التواصل عبر البريد الإلكتروني.' }}</p>
                </div>
                
                <div id="notesForm" class="hidden mb-6">
                    <textarea class="block w-full p-2.5 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white focus:ring-primary focus:border-primary text-base" rows="4">{{ $company->notes ?? 'شركة مميزة ومتعاونة. تم التواصل معهم بخصوص ترقية اشتراكهم ورفع الحد الأقصى للوظائف المميزة. يفضلون التواصل عبر البريد الإلكتروني.' }}</textarea>
                    <div class="flex justify-end mt-2 gap-2">
                        <button id="cancelEditNotes" class="px-3 py-1 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-sm">إلغاء</button>
                        <button id="saveNotes" class="px-3 py-1 gradient-bg text-white rounded-lg hover:opacity-90 text-sm">حفظ</button>
                    </div>
                </div>
                
                <div class="space-y-3">
                    <button class="w-full px-4 py-2 border border-primary text-primary bg-white dark:bg-gray-800 rounded-lg hover:bg-primary/10 transition-colors flex items-center gap-2 justify-center">
                        <i class="fas fa-envelope"></i>
                        <span>إرسال بريد إلكتروني</span>
                    </button>
                    
                    <button class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center gap-2 justify-center">
                        <i class="fas fa-history"></i>
                        <span>سجل المدفوعات</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Toggle Confirmation Modal -->
<div id="statusModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
        </div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white dark:bg-dark-card rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white dark:bg-dark-card px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 dark:bg-yellow-900/30 sm:mx-0 sm:h-10 sm:w-10">
                        <i class="fas fa-exclamation-triangle text-yellow-600 dark:text-yellow-400"></i>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:mr-4 sm:text-right">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                            {{ ($company->status ?? 'active') == 'active' ? 'تعطيل الحساب' : 'تفعيل الحساب' }}
                        </h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                {{ ($company->status ?? 'active') == 'active' ? 
                                'هل أنت متأكد من رغبتك في تعطيل حساب هذه الشركة؟ سيتم إيقاف عرض جميع وظائفها وتعليق نشاطهم على المنصة.' : 
                                'هل أنت متأكد من رغبتك في تفعيل حساب هذه الشركة؟ سيتمكنون من استئناف نشاطهم على المنصة ونشر الوظائف.' }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="confirmStatus" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 {{ ($company->status ?? 'active') == 'active' ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700' }} text-base font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm">
                    {{ ($company->status ?? 'active') == 'active' ? 'تعطيل' : 'تفعيل' }}
                </button>
                <button type="button" id="cancelStatus" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    إلغاء
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Status Toggle Modal
    const statusToggleBtn = document.getElementById('statusToggleBtn');
    const statusModal = document.getElementById('statusModal');
    const confirmStatus = document.getElementById('confirmStatus');
    const cancelStatus = document.getElementById('cancelStatus');
    
    statusToggleBtn.addEventListener('click', () => {
        statusModal.classList.remove('hidden');
    });
    
    cancelStatus.addEventListener('click', () => {
        statusModal.classList.add('hidden');
    });
    
    confirmStatus.addEventListener('click', () => {
        // Here you would normally send an AJAX request to update the status
        console.log('Toggling company status');
        
        // For demo purposes, change UI elements
        const isActive = statusToggleBtn.querySelector('i').classList.contains('fa-ban');
        if (isActive) {
            statusToggleBtn.innerHTML = '<i class="fas fa-check"></i><span>تفعيل الحساب</span>';
            statusToggleBtn.previousElementSibling.previousElementSibling.querySelector('.rounded-full').className = 
                statusToggleBtn.previousElementSibling.previousElementSibling.querySelector('.rounded-full').className.replace('bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400', 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400');
            statusToggleBtn.previousElementSibling.previousElementSibling.querySelector('.rounded-full').textContent = 'غير نشط';
        } else {
            statusToggleBtn.innerHTML = '<i class="fas fa-ban"></i><span>تعطيل الحساب</span>';
            statusToggleBtn.previousElementSibling.previousElementSibling.querySelector('.rounded-full').className = 
                statusToggleBtn.previousElementSibling.previousElementSibling.querySelector('.rounded-full').className.replace('bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400', 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400');
            statusToggleBtn.previousElementSibling.previousElementSibling.querySelector('.rounded-full').textContent = 'نشط';
        }
        
        // Hide modal
        statusModal.classList.add('hidden');
    });
    
    // Notes Edit
    const editNotesBtn = document.getElementById('editNotesBtn');
    const notesDisplay = document.getElementById('notesDisplay');
    const notesForm = document.getElementById('notesForm');
    const cancelEditNotes = document.getElementById('cancelEditNotes');
    const saveNotes = document.getElementById('saveNotes');
    
    editNotesBtn.addEventListener('click', () => {
        notesDisplay.classList.add('hidden');
        notesForm.classList.remove('hidden');
    });
    
    cancelEditNotes.addEventListener('click', () => {
        notesDisplay.classList.remove('hidden');
        notesForm.classList.add('hidden');
    });
    
    saveNotes.addEventListener('click', () => {
        const newNotes = notesForm.querySelector('textarea').value;
        notesDisplay.textContent = newNotes;
        notesDisplay.classList.remove('hidden');
        notesForm.classList.add('hidden');
        
        // Here you would normally send an AJAX request to save the notes
        console.log('Saving notes:', newNotes);
    });
    
    // Close modals when clicking outside
    document.addEventListener('click', (e) => {
        if (e.target === statusModal) {
            statusModal.classList.add('hidden');
        }
    });
</script>
@endsection