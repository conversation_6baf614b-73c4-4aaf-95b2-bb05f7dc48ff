@extends('layouts.employer')

@section('title', 'تصدير البيانات - Hire Me')
@section('header_title', 'تصدير البيانات')

@section('content')
<!-- رسائل النجاح والخطأ -->
@if(session('success'))
    <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg">
        {{ session('success') }}
    </div>
@endif

@if(session('error'))
    <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg">
        {{ session('error') }}
    </div>
@endif
<div class="p-8 space-y-8">
    <!-- Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">تصدير البيانات</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">تصدير بيانات الوظائف والطلبات بصيغ مختلفة</p>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="stat-card p-6 rounded-xl">
            <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-users text-white"></i>
                </div>
                <div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">إجمالي الطلبات</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $exportStats['total_applications'] ?? 0 }}</p>
                </div>
            </div>
        </div>

        <div class="stat-card p-6 rounded-xl">
            <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-briefcase text-white"></i>
                </div>
                <div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">إجمالي الوظائف</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $exportStats['total_jobs'] ?? 0 }}</p>
                </div>
            </div>
        </div>

        <div class="stat-card p-6 rounded-xl">
            <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-clock text-white"></i>
                </div>
                <div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">طلبات قيد المراجعة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $exportStats['pending_applications'] ?? 0 }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Options -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Export Applications -->
        <div class="glass-card rounded-xl p-6">
            <div class="flex items-center gap-3 mb-6">
                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-users text-white"></i>
                </div>
                <div>
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">تصدير طلبات التوظيف</h2>
                    <p class="text-gray-600 dark:text-gray-400">تصدير قائمة المتقدمين مع تفاصيلهم</p>
                </div>
            </div>

            <form action="{{ route('employer.exports.applications') }}" method="POST" class="space-y-4">
                @csrf

                <div>
                    <label for="app_status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        فلترة حسب الحالة
                    </label>
                    <select id="app_status" name="status" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                        <option value="">جميع الحالات</option>
                        <option value="pending">قيد المراجعة</option>
                        <option value="reviewed">تمت المراجعة</option>
                        <option value="accepted">مقبولة</option>
                        <option value="rejected">مرفوضة</option>
                        <option value="interview">مقابلة</option>
                        <option value="hired">تم التوظيف</option>
                    </select>
                </div>

                <div>
                    <label for="app_job" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        فلترة حسب الوظيفة
                    </label>
                    <select id="app_job" name="job_id" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                        <option value="">جميع الوظائف</option>
                        @if(Auth::user()->employer)
                            @foreach(Auth::user()->employer->jobs as $job)
                                <option value="{{ $job->id }}">{{ $job->title }}</option>
                            @endforeach
                        @endif
                    </select>
                </div>

                <div>
                    <label for="app_format" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        صيغة التصدير
                    </label>
                    <select id="app_format" name="format" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                        <option value="csv">CSV (Excel)</option>
                        <option value="pdf">PDF</option>
                        <option value="json">JSON</option>
                    </select>
                </div>

                <button type="submit" class="w-full px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 flex items-center justify-center gap-2 shadow-lg">
                    <i class="fas fa-download"></i>
                    <span class="btn-text">تصدير طلبات التوظيف</span>
                    <span class="loading-text"><i class="fas fa-spinner fa-spin"></i> جاري التصدير...</span>
                </button>
            </form>
        </div>

        <!-- Export Jobs -->
        <div class="glass-card rounded-xl p-6">
            <div class="flex items-center gap-3 mb-6">
                <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-briefcase text-white"></i>
                </div>
                <div>
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">تصدير الوظائف</h2>
                    <p class="text-gray-600 dark:text-gray-400">تصدير قائمة الوظائف مع الإحصائيات</p>
                </div>
            </div>

            <form action="{{ route('employer.exports.jobs') }}" method="POST" class="space-y-4">
                @csrf

                <div>
                    <label for="job_status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        فلترة حسب الحالة
                    </label>
                    <select id="job_status" name="status" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشطة</option>
                        <option value="expired">منتهية الصلاحية</option>
                        <option value="draft">مسودات</option>
                    </select>
                </div>

                <div>
                    <label for="job_date_from" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        من تاريخ
                    </label>
                    <input type="date" id="job_date_from" name="date_from" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                </div>

                <div>
                    <label for="job_date_to" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        إلى تاريخ
                    </label>
                    <input type="date" id="job_date_to" name="date_to" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                </div>

                <div>
                    <label for="job_format" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        صيغة التصدير
                    </label>
                    <select id="job_format" name="format" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                        <option value="csv">CSV (Excel)</option>
                        <option value="pdf">PDF</option>
                        <option value="json">JSON</option>
                    </select>
                </div>

                <button type="submit" class="w-full px-6 py-3 bg-gradient-to-r from-green-600 to-teal-600 text-white rounded-xl hover:from-green-700 hover:to-teal-700 transition-all duration-300 flex items-center justify-center gap-2 shadow-lg">
                    <i class="fas fa-download"></i>
                    <span class="btn-text">تصدير الوظائف</span>
                    <span class="loading-text"><i class="fas fa-spinner fa-spin"></i> جاري التصدير...</span>
                </button>
            </form>
        </div>
    </div>

    <!-- Export History -->
    <div class="glass-card rounded-xl p-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
            <i class="fas fa-history text-purple-600"></i>
            تصدير سريع
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-4">اضغط على أزرار التحميل للحصول على البيانات فوراً</p>

        <div class="overflow-x-auto">
            <table class="w-full">
                <thead>
                    <tr class="border-b border-gray-200 dark:border-gray-700">
                        <th class="text-right py-3 px-4 font-medium text-gray-900 dark:text-white">التاريخ</th>
                        <th class="text-right py-3 px-4 font-medium text-gray-900 dark:text-white">النوع</th>
                        <th class="text-right py-3 px-4 font-medium text-gray-900 dark:text-white">الصيغة</th>
                        <th class="text-right py-3 px-4 font-medium text-gray-900 dark:text-white">عدد السجلات</th>
                        <th class="text-right py-3 px-4 font-medium text-gray-900 dark:text-white">الحالة</th>
                        <th class="text-right py-3 px-4 font-medium text-gray-900 dark:text-white">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    <!-- تصدير سريع للطلبات -->
                    <tr>
                        <td class="py-3 px-4 text-gray-700 dark:text-gray-300">{{ now()->format('Y-m-d H:i') }}</td>
                        <td class="py-3 px-4 text-gray-700 dark:text-gray-300">طلبات التوظيف</td>
                        <td class="py-3 px-4 text-gray-700 dark:text-gray-300">CSV</td>
                        <td class="py-3 px-4 text-gray-700 dark:text-gray-300">{{ $exportStats['total_applications'] ?? 0 }}</td>
                        <td class="py-3 px-4">
                            <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 rounded-full">
                                متاح
                            </span>
                        </td>
                        <td class="py-3 px-4">
                            <form action="{{ route('employer.exports.applications') }}" method="POST" class="inline">
                                @csrf
                                <input type="hidden" name="format" value="csv">
                                <button type="submit" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm download-btn">
                                    <i class="fas fa-download mr-1"></i>
                                    تحميل CSV
                                </button>
                            </form>
                        </td>
                    </tr>
                    <tr>
                        <td class="py-3 px-4 text-gray-700 dark:text-gray-300">{{ now()->format('Y-m-d H:i') }}</td>
                        <td class="py-3 px-4 text-gray-700 dark:text-gray-300">طلبات التوظيف</td>
                        <td class="py-3 px-4 text-gray-700 dark:text-gray-300">PDF</td>
                        <td class="py-3 px-4 text-gray-700 dark:text-gray-300">{{ $exportStats['total_applications'] ?? 0 }}</td>
                        <td class="py-3 px-4">
                            <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 rounded-full">
                                متاح
                            </span>
                        </td>
                        <td class="py-3 px-4">
                            <form action="{{ route('employer.exports.applications') }}" method="POST" class="inline">
                                @csrf
                                <input type="hidden" name="format" value="pdf">
                                <button type="submit" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm download-btn">
                                    <i class="fas fa-download mr-1"></i>
                                    تحميل PDF
                                </button>
                            </form>
                        </td>
                    </tr>
                    <tr>
                        <td class="py-3 px-4 text-gray-700 dark:text-gray-300">{{ now()->format('Y-m-d H:i') }}</td>
                        <td class="py-3 px-4 text-gray-700 dark:text-gray-300">الوظائف</td>
                        <td class="py-3 px-4 text-gray-700 dark:text-gray-300">CSV</td>
                        <td class="py-3 px-4 text-gray-700 dark:text-gray-300">{{ $exportStats['total_jobs'] ?? 0 }}</td>
                        <td class="py-3 px-4">
                            <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 rounded-full">
                                متاح
                            </span>
                        </td>
                        <td class="py-3 px-4">
                            <form action="{{ route('employer.exports.jobs') }}" method="POST" class="inline">
                                @csrf
                                <input type="hidden" name="format" value="csv">
                                <button type="submit" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm download-btn">
                                    <i class="fas fa-download mr-1"></i>
                                    تحميل CSV
                                </button>
                            </form>
                        </td>
                    </tr>
                    <tr>
                        <td class="py-3 px-4 text-gray-700 dark:text-gray-300">{{ now()->format('Y-m-d H:i') }}</td>
                        <td class="py-3 px-4 text-gray-700 dark:text-gray-300">الوظائف</td>
                        <td class="py-3 px-4 text-gray-700 dark:text-gray-300">PDF</td>
                        <td class="py-3 px-4 text-gray-700 dark:text-gray-300">{{ $exportStats['total_jobs'] ?? 0 }}</td>
                        <td class="py-3 px-4">
                            <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 rounded-full">
                                متاح
                            </span>
                        </td>
                        <td class="py-3 px-4">
                            <form action="{{ route('employer.exports.jobs') }}" method="POST" class="inline">
                                @csrf
                                <input type="hidden" name="format" value="pdf">
                                <button type="submit" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm download-btn">
                                    <i class="fas fa-download mr-1"></i>
                                    تحميل PDF
                                </button>
                            </form>
                        </td>
                    </tr>
                    <tr>
                        <td class="py-3 px-4 text-gray-700 dark:text-gray-300">{{ now()->format('Y-m-d H:i') }}</td>
                        <td class="py-3 px-4 text-gray-700 dark:text-gray-300">طلبات التوظيف</td>
                        <td class="py-3 px-4 text-gray-700 dark:text-gray-300">JSON</td>
                        <td class="py-3 px-4 text-gray-700 dark:text-gray-300">{{ $exportStats['total_applications'] ?? 0 }}</td>
                        <td class="py-3 px-4">
                            <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 rounded-full">
                                متاح
                            </span>
                        </td>
                        <td class="py-3 px-4">
                            <form action="{{ route('employer.exports.applications') }}" method="POST" class="inline">
                                @csrf
                                <input type="hidden" name="format" value="json">
                                <button type="submit" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm download-btn">
                                    <i class="fas fa-download mr-1"></i>
                                    تحميل JSON
                                </button>
                            </form>
                        </td>
                    </tr>
                    <tr>
                        <td class="py-3 px-4 text-gray-700 dark:text-gray-300">{{ now()->format('Y-m-d H:i') }}</td>
                        <td class="py-3 px-4 text-gray-700 dark:text-gray-300">الوظائف</td>
                        <td class="py-3 px-4 text-gray-700 dark:text-gray-300">JSON</td>
                        <td class="py-3 px-4 text-gray-700 dark:text-gray-300">{{ $exportStats['total_jobs'] ?? 0 }}</td>
                        <td class="py-3 px-4">
                            <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 rounded-full">
                                متاح
                            </span>
                        </td>
                        <td class="py-3 px-4">
                            <form action="{{ route('employer.exports.jobs') }}" method="POST" class="inline">
                                @csrf
                                <input type="hidden" name="format" value="json">
                                <button type="submit" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm download-btn">
                                    <i class="fas fa-download mr-1"></i>
                                    تحميل JSON
                                </button>
                            </form>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Export Tips -->
    <div class="glass-card rounded-xl p-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800">
        <h2 class="text-xl font-semibold text-blue-900 dark:text-blue-100 mb-4 flex items-center gap-2">
            <i class="fas fa-lightbulb text-blue-600"></i>
            نصائح للتصدير
        </h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="flex items-start gap-3">
                <div class="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mt-1">
                    <i class="fas fa-check text-blue-600 dark:text-blue-400 text-xs"></i>
                </div>
                <div>
                    <h3 class="font-medium text-blue-900 dark:text-blue-100">صيغة CSV</h3>
                    <p class="text-sm text-blue-700 dark:text-blue-300">الأفضل لفتح البيانات في Excel أو Google Sheets</p>
                </div>
            </div>

            <div class="flex items-start gap-3">
                <div class="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mt-1">
                    <i class="fas fa-check text-blue-600 dark:text-blue-400 text-xs"></i>
                </div>
                <div>
                    <h3 class="font-medium text-blue-900 dark:text-blue-100">صيغة PDF</h3>
                    <p class="text-sm text-blue-700 dark:text-blue-300">مناسبة للطباعة والمشاركة الرسمية</p>
                </div>
            </div>

            <div class="flex items-start gap-3">
                <div class="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mt-1">
                    <i class="fas fa-check text-blue-600 dark:text-blue-400 text-xs"></i>
                </div>
                <div>
                    <h3 class="font-medium text-blue-900 dark:text-blue-100">الفلترة</h3>
                    <p class="text-sm text-blue-700 dark:text-blue-300">استخدم الفلاتر للحصول على البيانات المطلوبة فقط</p>
                </div>
            </div>

            <div class="flex items-start gap-3">
                <div class="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mt-1">
                    <i class="fas fa-check text-blue-600 dark:text-blue-400 text-xs"></i>
                </div>
                <div>
                    <h3 class="font-medium text-blue-900 dark:text-blue-100">الخصوصية</h3>
                    <p class="text-sm text-blue-700 dark:text-blue-300">تأكد من حماية البيانات الشخصية للمتقدمين</p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.stat-card {
    transition: all 0.3s ease-in-out;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading .btn-text {
    display: none;
}

.loading .loading-text {
    display: inline;
}

.loading-text {
    display: none;
}

.download-btn {
    transition: all 0.3s ease;
    padding: 6px 12px;
    border-radius: 6px;
    border: 1px solid transparent;
}

.download-btn:hover {
    background-color: rgba(59, 130, 246, 0.1);
    border-color: rgba(59, 130, 246, 0.3);
    transform: translateY(-1px);
}

.download-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // معالجة نماذج التصدير
    const exportForms = document.querySelectorAll('form[action*="exports"]');

    exportForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"]');
            const btnText = submitBtn.querySelector('.btn-text') || submitBtn;
            const loadingText = submitBtn.querySelector('.loading-text');

            // إضافة حالة التحميل
            submitBtn.classList.add('loading');
            submitBtn.disabled = true;

            if (loadingText) {
                loadingText.style.display = 'inline';
            } else {
                btnText.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التصدير...';
            }

            // إزالة حالة التحميل بعد 5 ثوان (في حالة عدم إعادة تحميل الصفحة)
            setTimeout(() => {
                submitBtn.classList.remove('loading');
                submitBtn.disabled = false;
                if (loadingText) {
                    loadingText.style.display = 'none';
                } else {
                    btnText.innerHTML = '<i class="fas fa-download"></i> تصدير';
                }
            }, 5000);
        });
    });

    // إضافة تأكيد للتصدير
    const exportButtons = document.querySelectorAll('button[type="submit"]');
    exportButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            const form = btn.closest('form');
            const formatSelect = form.querySelector('select[name="format"]');
            const formatInput = form.querySelector('input[name="format"]');
            const format = formatSelect ? formatSelect.value : (formatInput ? formatInput.value : 'csv');

            if (!confirm(`هل أنت متأكد من تصدير البيانات بصيغة ${format.toUpperCase()}؟`)) {
                e.preventDefault();
            }
        });
    });

    // معالجة أزرار التحميل السريع في سجل التصدير
    const downloadButtons = document.querySelectorAll('.download-btn');
    downloadButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            const form = btn.closest('form');
            const formatInput = form.querySelector('input[name="format"]');
            const format = formatInput ? formatInput.value : 'csv';

            // إضافة حالة التحميل
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> جاري التحميل...';

            // إزالة حالة التحميل بعد 3 ثوان
            setTimeout(() => {
                btn.disabled = false;
                btn.innerHTML = `<i class="fas fa-download mr-1"></i> تحميل ${format.toUpperCase()}`;
            }, 3000);
        });
    });
});
</script>
@endpush
