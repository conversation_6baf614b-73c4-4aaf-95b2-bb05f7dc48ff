<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\CompanyRating;
use App\Models\ProfileRating;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ReviewController extends Controller
{
    /**
     * عرض قائمة المراجعات والتقييمات
     */
    public function index(Request $request)
    {
        // استعلام المراجعات للشركات
        $companyRatingsQuery = CompanyRating::with(['jobSeeker.user', 'employer'])
            ->select(
                'company_ratings.id',
                'company_ratings.rating',
                'company_ratings.comment',
                'company_ratings.created_at',
                DB::raw("'company' as type")
            );

        // استعلام المراجعات للمرشحين
        $profileRatingsQuery = ProfileRating::with(['employer.user', 'jobSeeker.user'])
            ->select(
                'profile_ratings.id',
                'profile_ratings.rating',
                'profile_ratings.comment',
                'profile_ratings.created_at',
                DB::raw("'profile' as type")
            );

        // تطبيق الفلاتر
        if ($request->has('type') && $request->type) {
            if ($request->type == 'company') {
                $profileRatingsQuery->whereRaw('1=0'); // لا تعرض مراجعات المرشحين
            } elseif ($request->type == 'candidate') {
                $companyRatingsQuery->whereRaw('1=0'); // لا تعرض مراجعات الشركات
            }
        }

        if ($request->has('rating') && $request->rating) {
            $companyRatingsQuery->where('rating', $request->rating);
            $profileRatingsQuery->where('rating', $request->rating);
        }

        // دمج نتائج الاستعلامين
        $companyRatings = $companyRatingsQuery->get();
        $profileRatings = $profileRatingsQuery->get();

        // دمج النتائج وترتيبها حسب تاريخ الإنشاء
        $reviews = $companyRatings->concat($profileRatings)
            ->sortByDesc('created_at');

        // تقسيم النتائج إلى صفحات
        $perPage = 10;
        $page = $request->input('page', 1);
        $total = $reviews->count();
        $reviews = $reviews->forPage($page, $perPage);

        return view('admin.reviews.index', [
            'reviews' => $reviews,
            'total' => $total,
            'perPage' => $perPage,
            'currentPage' => $page,
            'lastPage' => ceil($total / $perPage),
            'filters' => $request->only(['type', 'rating', 'status', 'date'])
        ]);
    }

    /**
     * عرض تفاصيل مراجعة محددة
     */
    public function show($id)
    {
        // البحث عن المراجعة في جدول مراجعات الشركات
        $companyRating = CompanyRating::with(['jobSeeker.user', 'employer'])
            ->where('id', $id)
            ->first();

        if ($companyRating) {
            return view('admin.reviews.show', [
                'review' => $companyRating,
                'type' => 'company',
                'id' => $id
            ]);
        }

        // البحث عن المراجعة في جدول مراجعات المرشحين
        $profileRating = ProfileRating::with(['employer.user', 'jobSeeker.user'])
            ->where('id', $id)
            ->first();

        if ($profileRating) {
            return view('admin.reviews.show', [
                'review' => $profileRating,
                'type' => 'profile',
                'id' => $id
            ]);
        }

        // إذا لم يتم العثور على المراجعة
        return redirect()->route('admin.reviews.index')
            ->with('error', 'لم يتم العثور على المراجعة المطلوبة');
    }

    /**
     * تحديث حالة المراجعة (موافقة أو رفض)
     */
    public function updateStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:approved,rejected',
            'type' => 'required|in:company,profile',
            'admin_notes' => 'nullable|string|max:500',
        ]);

        if ($request->type == 'company') {
            $review = CompanyRating::findOrFail($id);
        } else {
            $review = ProfileRating::findOrFail($id);
        }

        // تحديث حالة المراجعة
        $review->status = $request->status;
        $review->admin_notes = $request->admin_notes;
        $review->save();

        return redirect()->route('admin.reviews.show', $id)
            ->with('success', 'تم تحديث حالة المراجعة بنجاح');
    }

    /**
     * حذف مراجعة
     */
    public function destroy($id, Request $request)
    {
        $request->validate([
            'type' => 'required|in:company,profile',
        ]);

        if ($request->type == 'company') {
            $review = CompanyRating::findOrFail($id);
        } else {
            $review = ProfileRating::findOrFail($id);
        }

        $review->delete();

        return redirect()->route('admin.reviews.index')
            ->with('success', 'تم حذف المراجعة بنجاح');
    }
}
