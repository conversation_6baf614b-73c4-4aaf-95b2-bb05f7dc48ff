/* تحسينات خاصة بصفحة إدارة الوظائف لصاحب العمل */

/* تحسين الأزرار الرئيسية */
.admin-style-btn {
    position: relative;
    overflow: hidden;
    border: none;
    cursor: pointer;
    user-select: none;
    outline: none;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.admin-style-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.2));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.admin-style-btn:hover::before {
    opacity: 1;
}

/* تأثيرات الألوان المحسنة */
.btn-view {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.btn-view:hover {
    background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.btn-edit {
    background: linear-gradient(135deg, #10b981 0%, #047857 100%);
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.btn-edit:hover {
    background: linear-gradient(135deg, #047857 0%, #065f46 100%);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.btn-toggle {
    background: linear-gradient(135deg, #f59e0b 0%, #b45309 100%);
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.btn-toggle:hover {
    background: linear-gradient(135deg, #b45309 0%, #92400e 100%);
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
}

.btn-copy {
    background: linear-gradient(135deg, #8b5cf6 0%, #6d28d9 100%);
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
}

.btn-copy:hover {
    background: linear-gradient(135deg, #6d28d9 0%, #5b21b6 100%);
    box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
}

.btn-delete {
    background: linear-gradient(135deg, #ef4444 0%, #b91c1c 100%);
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.btn-delete:hover {
    background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

/* تحسين التولتيب */
.admin-style-btn .tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
    pointer-events: none;
}

.admin-style-btn .tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
}

.admin-style-btn:hover .tooltip {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-5px);
}

/* تحسين الرسوم المتحركة */
@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.admin-style-btn:active {
    animation: pulse 0.2s ease-in-out;
}

/* تحسين الكارد */
.job-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.job-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border-color: rgba(0, 0, 0, 0.1);
}

/* تحسين الإحصائيات */
.stat-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .stat-card {
    background: linear-gradient(135deg, rgba(31, 41, 55, 0.9), rgba(31, 41, 55, 0.7));
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* تحسين الفلاتر */
.glass-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-card {
    background: rgba(31, 41, 55, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* تحسين النافذة المنبثقة */
.modal-backdrop {
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

/* تحسين الأزرار للشاشات الصغيرة */
@media (max-width: 768px) {
    .admin-style-btn {
        width: 36px;
        height: 36px;
    }
    
    .admin-style-btn i {
        font-size: 14px;
    }
    
    .admin-style-btn .tooltip {
        display: none;
    }
}

/* تحسين الحالات */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background: linear-gradient(135deg, #dcfce7, #bbf7d0);
    color: #166534;
    border: 1px solid #86efac;
}

.status-inactive {
    background: linear-gradient(135deg, #fef2f2, #fecaca);
    color: #991b1b;
    border: 1px solid #fca5a5;
}

.status-expired {
    background: linear-gradient(135deg, #fefce8, #fef3c7);
    color: #92400e;
    border: 1px solid #fcd34d;
}

/* تحسين الرسوم المتحركة للتحميل */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* تحسين التفاعل مع الماوس */
.admin-style-btn {
    cursor: pointer;
    will-change: transform, box-shadow;
}

.admin-style-btn:focus {
    outline: none;
    ring: 2px;
    ring-color: rgba(59, 130, 246, 0.5);
    ring-offset: 2px;
}

/* تحسين الانتقالات */
* {
    scroll-behavior: smooth;
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
